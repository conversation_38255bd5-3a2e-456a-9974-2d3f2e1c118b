from rest_framework import serializers
from .models import (
    BrokerService, ServiceRequest, BrokerQuote, 
    ServiceExecution, BrokerProfile
)
from accounts.serializers import UserSerializer


class BrokerServiceSerializer(serializers.ModelSerializer):
    """Serializer for broker services"""
    
    class Meta:
        model = BrokerService
        fields = [
            'id', 'name', 'name_ar', 'service_type', 
            'description', 'description_ar', 'is_active'
        ]


class ServiceRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating service requests"""
    
    class Meta:
        model = ServiceRequest
        fields = [
            'auction', 'service', 'location_description',
            'latitude', 'longitude', 'special_instructions'
        ]
    
    def create(self, validated_data):
        validated_data['client'] = self.context['request'].user
        return super().create(validated_data)


class BrokerQuoteSerializer(serializers.ModelSerializer):
    """Serializer for broker quotes"""
    
    broker_info = UserSerializer(source='broker', read_only=True)
    broker_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = BrokerQuote
        fields = [
            'id', 'amount', 'estimated_duration', 'notes',
            'status', 'created_at', 'broker_info', 'broker_rating'
        ]
    
    def get_broker_rating(self, obj):
        if hasattr(obj.broker, 'broker_profile'):
            return float(obj.broker.broker_profile.average_rating)
        return 0.0


class BrokerQuoteCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating broker quotes"""
    
    class Meta:
        model = BrokerQuote
        fields = ['service_request', 'amount', 'estimated_duration', 'notes']
    
    def create(self, validated_data):
        validated_data['broker'] = self.context['request'].user
        quote = super().create(validated_data)

        # Send notification to client about new broker offer
        try:
            from notifications.services import NotificationService
            notification_service = NotificationService()

            notification_service.send_notification(
                quote.service_request.client,
                'broker_offer_received',
                {
                    'service_request': quote.service_request,
                    'broker': quote.broker,
                    'quote': quote,
                },
                channels=['whatsapp', 'in_app']  # Both WhatsApp and in-app
            )
        except Exception as e:
            # Don't fail quote creation if notification fails
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send broker offer notification: {e}")

        return quote


class ServiceRequestSerializer(serializers.ModelSerializer):
    """Serializer for service requests with quotes"""
    
    service_info = BrokerServiceSerializer(source='service', read_only=True)
    auction_title = serializers.CharField(source='auction.title', read_only=True)
    client_info = UserSerializer(source='client', read_only=True)
    quotes = BrokerQuoteSerializer(many=True, read_only=True)
    quotes_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceRequest
        fields = [
            'id', 'auction', 'service', 'service_info', 'auction_title', 'client_info',
            'location_description', 'latitude', 'longitude',
            'special_instructions', 'status', 'selected_quote_amount',
            'quotes', 'quotes_count', 'created_at', 'updated_at'
        ]
    
    def get_quotes_count(self, obj):
        return obj.quotes.count()


class ServiceExecutionSerializer(serializers.ModelSerializer):
    """Serializer for service execution"""
    
    service_request_info = ServiceRequestSerializer(source='service_request', read_only=True)
    broker_info = UserSerializer(source='broker', read_only=True)
    
    class Meta:
        model = ServiceExecution
        fields = [
            'id', 'service_request_info', 'broker_info',
            'started_at', 'completed_at', 'status',
            'report_text', 'report_images',
            'client_approved', 'client_feedback', 'client_rating',
            'payment_released', 'created_at', 'updated_at'
        ]


class ServiceExecutionUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating service execution"""
    
    class Meta:
        model = ServiceExecution
        fields = ['status', 'report_text', 'report_images']


class BrokerProfileSerializer(serializers.ModelSerializer):
    """Serializer for broker profiles"""
    
    user_info = UserSerializer(source='user', read_only=True)
    specializations_info = BrokerServiceSerializer(source='specializations', many=True, read_only=True)
    
    class Meta:
        model = BrokerProfile
        fields = [
            'user_info', 'service_areas', 'specializations_info',
            'total_services', 'average_rating', 'total_earnings',
            'is_available', 'max_concurrent_services',
            'current_active_services', 'can_accept_new_service'
        ]
        read_only_fields = [
            'total_services', 'average_rating', 'total_earnings',
            'current_active_services'
        ]


class ClientFeedbackSerializer(serializers.ModelSerializer):
    """Serializer for client feedback on completed services"""
    
    class Meta:
        model = ServiceExecution
        fields = ['client_feedback', 'client_rating', 'client_approved']
    
    def update(self, instance, validated_data):
        # Auto-approve when feedback is provided
        if 'client_feedback' in validated_data or 'client_rating' in validated_data:
            validated_data['client_approved'] = True
        return super().update(instance, validated_data)
