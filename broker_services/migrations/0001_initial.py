# Generated by Django 4.2.23 on 2025-06-26 17:22

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auctions', '0004_auction_is_location_live_auction_latitude_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BrokerService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(max_length=100)),
                ('service_type', models.CharField(choices=[('inspection', 'المعاينة'), ('examination', 'التفتيش'), ('pickup', 'الاستلام نيابة عن العميل'), ('loading_inspection', 'المعاينة أثناء التحميل/التنزيل'), ('shipping_assistance', 'المساعدة في تقديم خدمة الشحن')], max_length=50)),
                ('description', models.TextField()),
                ('description_ar', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ServiceRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('location_description', models.TextField(help_text='وصف الموقع')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('special_instructions', models.TextField(blank=True, help_text='تعليمات خاصة')),
                ('status', models.CharField(choices=[('pending', 'في انتظار العروض'), ('quotes_received', 'تم استلام العروض'), ('broker_selected', 'تم اختيار البروكر'), ('payment_held', 'تم حجز المبلغ'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغية')], default='pending', max_length=20)),
                ('selected_quote_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('payment_held', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('auction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_requests', to='auctions.auction')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_requests', to=settings.AUTH_USER_MODEL)),
                ('selected_broker', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='selected_services', to=settings.AUTH_USER_MODEL)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='broker_services.brokerservice')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ServiceExecution',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('started', 'بدأ التنفيذ'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('client_approved', 'موافقة العميل'), ('payment_released', 'تم تحويل المبلغ')], default='started', max_length=20)),
                ('report_text', models.TextField(blank=True, help_text='تقرير البروكر')),
                ('report_images', models.JSONField(default=list, help_text='صور التقرير')),
                ('client_approved', models.BooleanField(default=False)),
                ('client_feedback', models.TextField(blank=True)),
                ('client_rating', models.IntegerField(blank=True, help_text='تقييم من 1-5', null=True)),
                ('payment_released', models.BooleanField(default=False)),
                ('payment_released_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('broker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_executions', to=settings.AUTH_USER_MODEL)),
                ('service_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='execution', to='broker_services.servicerequest')),
            ],
        ),
        migrations.CreateModel(
            name='BrokerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_areas', models.TextField(help_text='المناطق التي يخدمها البروكر')),
                ('total_services', models.IntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('is_available', models.BooleanField(default=True)),
                ('max_concurrent_services', models.IntegerField(default=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('specializations', models.ManyToManyField(blank=True, help_text='التخصصات', to='broker_services.brokerservice')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='broker_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='BrokerQuote',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('estimated_duration', models.CharField(help_text='المدة المتوقعة', max_length=100)),
                ('notes', models.TextField(blank=True, help_text='ملاحظات إضافية')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الرد'), ('selected', 'تم الاختيار'), ('rejected', 'مرفوض')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('broker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='broker_quotes', to=settings.AUTH_USER_MODEL)),
                ('service_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quotes', to='broker_services.servicerequest')),
            ],
            options={
                'ordering': ['amount'],
                'unique_together': {('service_request', 'broker')},
            },
        ),
    ]
