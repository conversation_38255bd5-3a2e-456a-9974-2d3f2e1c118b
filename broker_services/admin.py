from django.contrib import admin
from .models import (
    BrokerService, ServiceRequest, BrokerQuote, 
    ServiceExecution, BrokerProfile
)


@admin.register(BrokerService)
class BrokerServiceAdmin(admin.ModelAdmin):
    list_display = ['name_ar', 'name', 'service_type', 'is_active']
    list_filter = ['service_type', 'is_active']
    search_fields = ['name', 'name_ar']
    ordering = ['service_type', 'name_ar']


@admin.register(ServiceRequest)
class ServiceRequestAdmin(admin.ModelAdmin):
    list_display = ['service', 'client', 'auction', 'status', 'selected_quote_amount', 'created_at']
    list_filter = ['status', 'service', 'payment_held']
    search_fields = ['client__username', 'auction__title', 'location_description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Info', {
            'fields': ('id', 'client', 'auction', 'service', 'status')
        }),
        ('Location', {
            'fields': ('location_description', 'latitude', 'longitude')
        }),
        ('Request Details', {
            'fields': ('special_instructions', 'selected_broker', 'selected_quote_amount', 'payment_held')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(BrokerQuote)
class BrokerQuoteAdmin(admin.ModelAdmin):
    list_display = ['broker', 'service_request', 'amount', 'status', 'created_at']
    list_filter = ['status', 'service_request__service']
    search_fields = ['broker__username', 'service_request__auction__title']
    ordering = ['-created_at']


@admin.register(ServiceExecution)
class ServiceExecutionAdmin(admin.ModelAdmin):
    list_display = ['broker', 'service_request', 'status', 'client_approved', 'payment_released', 'created_at']
    list_filter = ['status', 'client_approved', 'payment_released']
    search_fields = ['broker__username', 'service_request__auction__title']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Info', {
            'fields': ('id', 'service_request', 'broker', 'status')
        }),
        ('Execution Details', {
            'fields': ('started_at', 'completed_at', 'report_text', 'report_images')
        }),
        ('Client Feedback', {
            'fields': ('client_approved', 'client_feedback', 'client_rating')
        }),
        ('Payment', {
            'fields': ('payment_released', 'payment_released_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(BrokerProfile)
class BrokerProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_services', 'average_rating', 'total_earnings', 'is_available']
    list_filter = ['is_available', 'specializations']
    search_fields = ['user__username', 'service_areas']
    readonly_fields = ['total_services', 'average_rating', 'total_earnings', 'current_active_services']
    filter_horizontal = ['specializations']
    ordering = ['-total_earnings']
    
    fieldsets = (
        ('User Info', {
            'fields': ('user',)
        }),
        ('Service Details', {
            'fields': ('service_areas', 'specializations', 'is_available', 'max_concurrent_services')
        }),
        ('Statistics', {
            'fields': ('total_services', 'average_rating', 'total_earnings', 'current_active_services')
        }),
    )
