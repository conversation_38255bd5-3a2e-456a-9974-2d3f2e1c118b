from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from .models import BrokerProfile


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_broker_profile(sender, instance, created, **kwargs):
    """
    Automatically create a BrokerProfile when a new broker user is created
    """
    if created and instance.user_type == 'broker':
        BrokerProfile.objects.create(
            user=instance,
            service_areas='جميع المناطق',  # Default service areas
            is_available=True,
            max_concurrent_services=5
        )
        print(f"✅ Created broker profile for user: {instance.username}")


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def update_broker_profile_on_user_change(sender, instance, created, **kwargs):
    """
    Handle user type changes - create broker profile if user type changed to broker
    """
    if not created and instance.user_type == 'broker':
        # Check if broker profile already exists
        if not hasattr(instance, 'broker_profile'):
            BrokerProfile.objects.create(
                user=instance,
                service_areas='جميع المناطق',
                is_available=True,
                max_concurrent_services=5
            )
            print(f"✅ Created broker profile for existing user: {instance.username}")
