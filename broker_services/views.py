import uuid
import os
from rest_framework import generics, permissions, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.conf import settings
from datetime import timedelta
from decimal import Decimal

from .models import (
    BrokerService, ServiceRequest, BrokerQuote, 
    ServiceExecution, BrokerProfile
)
from .serializers import (
    BrokerServiceSerializer, ServiceRequestCreateSerializer,
    ServiceRequestSerializer, BrokerQuoteSerializer, BrokerQuoteCreateSerializer,
    ServiceExecutionSerializer, ServiceExecutionUpdateSerializer,
    BrokerProfileSerializer, ClientFeedbackSerializer
)
from auctions.models import Auction


class BrokerServiceListView(generics.ListAPIView):
    """List all available broker services"""
    
    queryset = BrokerService.objects.filter(is_active=True)
    serializer_class = BrokerServiceSerializer
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="List broker services",
        description="Get all available broker services"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ServiceRequestCreateView(generics.CreateAPIView):
    """Create a new service request"""
    
    serializer_class = ServiceRequestCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Create service request",
        description="Create a new service request for an auction (only if auction starts in 12+ hours)"
    )
    def post(self, request, *args, **kwargs):
        # Check if auction allows service requests (12+ hours before end)
        auction_id = request.data.get('auction')
        if auction_id:
            try:
                auction = Auction.objects.get(id=auction_id)
                time_until_end = auction.end_time - timezone.now()

                if time_until_end < timedelta(hours=12):
                    return Response(
                        {'error': 'Service requests are only available for auctions ending in 12+ hours'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except Auction.DoesNotExist:
                return Response(
                    {'error': 'Auction not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        return super().post(request, *args, **kwargs)


class ServiceRequestListView(generics.ListAPIView):
    """List service requests for client"""
    
    serializer_class = ServiceRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'service']
    ordering_fields = ['created_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        return ServiceRequest.objects.filter(client=self.request.user)
    
    @extend_schema(
        summary="List my service requests",
        description="Get all service requests created by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class AvailableServiceRequestsView(generics.ListAPIView):
    """List available service requests for brokers"""
    
    serializer_class = ServiceRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['service']
    ordering = ['-created_at']
    
    def get_queryset(self):
        # Only show requests that are pending and don't have a quote from this broker
        user = self.request.user
        if user.user_type != 'broker':
            return ServiceRequest.objects.none()
        
        return ServiceRequest.objects.filter(
            status='pending'
        ).exclude(
            quotes__broker=user
        ).select_related('service', 'auction', 'client')
    
    @extend_schema(
        summary="List available service requests",
        description="Get service requests available for brokers to quote on"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class BrokerQuoteCreateView(generics.CreateAPIView):
    """Create a quote for a service request"""
    
    serializer_class = BrokerQuoteCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Create broker quote",
        description="Submit a quote for a service request (brokers only)"
    )
    def post(self, request, *args, **kwargs):
        if request.user.user_type != 'broker':
            return Response(
                {'error': 'Only brokers can submit quotes'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if broker profile exists and can accept new services
        try:
            broker_profile = request.user.broker_profile
        except BrokerProfile.DoesNotExist:
            # Create default broker profile if it doesn't exist
            broker_profile = BrokerProfile.objects.create(
                user=request.user,
                service_areas='جميع المناطق',
                is_available=True,
                max_concurrent_services=5
            )

        if not broker_profile.can_accept_new_service:
            return Response(
                {'error': 'You have reached your maximum concurrent services limit'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().post(request, *args, **kwargs)


class ServiceRequestQuotesView(generics.ListAPIView):
    """List quotes for a specific service request"""
    
    serializer_class = BrokerQuoteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        service_request_id = self.kwargs['service_request_id']
        service_request = get_object_or_404(ServiceRequest, id=service_request_id)
        
        # Only the client can see quotes for their request
        if service_request.client != self.request.user:
            return BrokerQuote.objects.none()
        
        return service_request.quotes.all()
    
    @extend_schema(
        summary="List service request quotes",
        description="Get all quotes for a specific service request"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class SelectBrokerView(APIView):
    """Select a broker for a service request"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="Select broker",
        description="Select a broker quote and hold payment"
    )
    def post(self, request, service_request_id, quote_id):
        try:
            service_request = get_object_or_404(ServiceRequest, id=service_request_id)
            quote = get_object_or_404(BrokerQuote, id=quote_id, service_request=service_request)

            # Only the client can select a broker
            if service_request.client != request.user:
                return Response(
                    {'error': 'Only the service request owner can select a broker'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Check if a broker is already selected and service is in progress
            if service_request.status in ['broker_selected', 'in_progress', 'completed']:
                return Response(
                    {'error': 'تم اختيار بروكر لهذا الطلب مسبقاً'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if client has sufficient wallet balance
            if request.user.wallet_balance < quote.amount:
                return Response(
                    {'error': 'رصيد المحفظة غير كافي'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Mark payment as held (don't actually charge yet - charge when work is completed)

            service_request.selected_broker = quote.broker
            service_request.selected_quote_amount = quote.amount
            service_request.status = 'broker_selected'
            service_request.payment_held = True
            service_request.save()

            # Update quote status
            quote.status = 'selected'
            quote.save()

            # Reject other quotes
            service_request.quotes.exclude(id=quote.id).update(status='rejected')

            # Create or get service execution record
            execution, created = ServiceExecution.objects.get_or_create(
                service_request=service_request,
                defaults={
                    'broker': quote.broker,
                    'status': 'started'
                }
            )

            # If execution already exists, update it with the new broker
            if not created:
                execution.broker = quote.broker
                execution.status = 'started'
                execution.save()

            # Send notification to broker about offer acceptance
            try:
                from notifications.services import NotificationService
                notification_service = NotificationService()

                notification_service.send_notification(
                    quote.broker,
                    'broker_offer_accepted',
                    {
                        'service_request': service_request,
                        'quote': quote,
                        'client': service_request.client,
                    },
                    channels=['whatsapp', 'in_app']  # Both WhatsApp and in-app
                )
            except Exception as e:
                # Don't fail selection if notification fails
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Failed to send broker offer acceptance notification: {e}")

            print(f"✅ Created service execution with ID: {execution.id}")

            return Response({
                'message': 'Broker selected successfully',
                'service_request': ServiceRequestSerializer(service_request).data
            })

        except Exception as e:
            print(f"❌ Error in SelectBrokerView: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {'error': f'Server error: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BrokerServiceExecutionsView(generics.ListAPIView):
    """List service executions for broker"""

    serializer_class = ServiceExecutionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']
    ordering = ['-created_at']

    def get_queryset(self):
        if self.request.user.user_type != 'broker':
            return ServiceExecution.objects.none()

        return ServiceExecution.objects.filter(broker=self.request.user)

    @extend_schema(
        summary="List broker service executions",
        description="Get all service executions for the authenticated broker"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class UpdateServiceExecutionView(generics.UpdateAPIView):
    """Update service execution status and report"""

    serializer_class = ServiceExecutionUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ServiceExecution.objects.filter(broker=self.request.user)

    @extend_schema(
        summary="Update service execution",
        description="Update service execution status and submit report"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Upload report image",
    description="Upload confirmation image for service execution report",
    request={
        'multipart/form-data': {
            'type': 'object',
            'properties': {
                'report_image': {'type': 'string', 'format': 'binary'},
            },
            'required': ['report_image']
        }
    }
)
def upload_report_image(request, pk):
    """Upload confirmation image for service execution report"""
    try:
        execution = ServiceExecution.objects.get(pk=pk, broker=request.user)
    except ServiceExecution.DoesNotExist:
        return Response(
            {'error': 'Service execution not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    if 'report_image' not in request.FILES:
        return Response(
            {'error': 'No image file provided'},
            status=status.HTTP_400_BAD_REQUEST
        )

    image_file = request.FILES['report_image']

    # Validate file type
    allowed_types = ['image/jpeg', 'image/jpg', 'image/png']
    if image_file.content_type not in allowed_types:
        return Response(
            {'error': 'Invalid file type. Only JPEG and PNG images are allowed'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Validate file size (max 5MB)
    if image_file.size > 5 * 1024 * 1024:
        return Response(
            {'error': 'File size too large. Maximum size is 5MB'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Save the image and get URL
    # For now, we'll store the image path in the report_images JSON field

    # Create unique filename
    file_extension = os.path.splitext(image_file.name)[1]
    unique_filename = f"report_{uuid.uuid4()}{file_extension}"

    # Save file (you might want to use a proper file storage service)
    upload_path = os.path.join(settings.MEDIA_ROOT, 'broker_reports', unique_filename)
    os.makedirs(os.path.dirname(upload_path), exist_ok=True)

    with open(upload_path, 'wb+') as destination:
        for chunk in image_file.chunks():
            destination.write(chunk)

    # Create the URL for the image
    image_url = f"{settings.MEDIA_URL}broker_reports/{unique_filename}"

    return Response({
        'image_url': image_url,
        'message': 'Image uploaded successfully'
    }, status=status.HTTP_201_CREATED)


class ClientServiceExecutionsView(generics.ListAPIView):
    """List service executions for client"""

    serializer_class = ServiceExecutionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ServiceExecution.objects.filter(
            service_request__client=self.request.user
        )

    @extend_schema(
        summary="List client service executions",
        description="Get all service executions for the authenticated client"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class SubmitClientFeedbackView(generics.UpdateAPIView):
    """Submit client feedback for completed service"""

    serializer_class = ClientFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ServiceExecution.objects.filter(
            service_request__client=self.request.user,
            status='completed'
        )

    @extend_schema(
        summary="Submit client feedback",
        description="Submit feedback and rating for completed service"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class BrokerProfileView(generics.RetrieveUpdateAPIView):
    """Get and update broker profile"""

    serializer_class = BrokerProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        if self.request.user.user_type != 'broker':
            raise permissions.PermissionDenied("Only brokers can access this endpoint")

        profile, created = BrokerProfile.objects.get_or_create(user=self.request.user)
        return profile

    @extend_schema(
        summary="Get/Update broker profile",
        description="Get or update broker profile information"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def release_payment(request, execution_id):
    """Release payment to broker after service completion"""

    execution = get_object_or_404(ServiceExecution, id=execution_id)

    # Only client can release payment
    if execution.service_request.client != request.user:
        return Response(
            {'error': 'Only the service client can release payment'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Check if service is completed and approved
    if not execution.client_approved:
        return Response(
            {'error': 'Service must be approved before payment release'},
            status=status.HTTP_400_BAD_REQUEST
        )

    if execution.payment_released:
        return Response(
            {'error': 'Payment already released'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Release payment: charge client and pay broker
    client = execution.service_request.client
    broker = execution.broker
    amount = execution.service_request.selected_quote_amount

    # Check if client still has sufficient balance
    if client.wallet_balance < amount:
        return Response(
            {'error': 'Client has insufficient balance for payment'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Calculate platform fee (5% like auction payments)
    from decimal import Decimal
    platform_fee_percentage = Decimal('0.05')  # 5% platform fee
    platform_fee = amount * platform_fee_percentage
    broker_amount = amount - platform_fee

    # Transfer money from client to broker (minus platform fee)
    client.wallet_balance -= amount
    broker.wallet_balance += broker_amount

    client.save()
    broker.save()

    # Create wallet transactions
    from payments.models import WalletTransaction

    # Client payment transaction (negative)
    WalletTransaction.objects.create(
        user=client,
        transaction_type='payment',
        amount=-amount,
        description=f'Payment for broker service: {execution.service_request.service.name_ar}',
        status='completed',
        auction=execution.service_request.auction if hasattr(execution.service_request, 'auction') else None
    )

    # Broker payment received transaction (positive)
    WalletTransaction.objects.create(
        user=broker,
        transaction_type='payment',
        amount=broker_amount,
        description=f'Payment received for service: {execution.service_request.service.name_ar}',
        status='completed',
        auction=execution.service_request.auction if hasattr(execution.service_request, 'auction') else None
    )

    # Update execution
    execution.payment_released = True
    execution.payment_released_at = timezone.now()
    execution.status = 'payment_released'
    execution.save()

    # Update broker profile stats
    broker_profile = broker.broker_profile
    broker_profile.total_services += 1
    broker_profile.total_earnings += broker_amount  # Use broker_amount (after fee deduction)
    broker_profile.save()

    return Response({
        'message': 'Payment released successfully',
        'total_amount': amount,
        'platform_fee': platform_fee,
        'broker_amount': broker_amount,
        'broker_balance': broker.wallet_balance
    })
