from django.db import models
from django.conf import settings
from decimal import Decimal
import uuid

class BrokerService(models.Model):
    """Broker service types"""
    
    SERVICE_TYPES = [
        ('inspection', 'المعاينة'),
        ('examination', 'التفتيش'),
        ('pickup', 'الاستلام نيابة عن العميل'),
        ('loading_inspection', 'المعاينة أثناء التحميل/التنزيل'),
        ('shipping_assistance', 'المساعدة في تقديم خدمة الشحن'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100)
    service_type = models.CharField(max_length=50, choices=SERVICE_TYPES)
    description = models.TextField()
    description_ar = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.name_ar} ({self.name})"


class ServiceRequest(models.Model):
    """Client service requests for auctions"""
    
    STATUS_CHOICES = [
        ('pending', 'في انتظار العروض'),
        ('quotes_received', 'تم استلام العروض'),
        ('broker_selected', 'تم اختيار البروكر'),
        ('payment_held', 'تم حجز المبلغ'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغية'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='service_requests')
    auction = models.ForeignKey('auctions.Auction', on_delete=models.CASCADE, related_name='service_requests')
    service = models.ForeignKey(BrokerService, on_delete=models.CASCADE)
    
    # Location details
    location_description = models.TextField(help_text="وصف الموقع")
    latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    
    # Request details
    special_instructions = models.TextField(blank=True, help_text="تعليمات خاصة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Selected broker and payment
    selected_broker = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='selected_services')
    selected_quote_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    payment_held = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.service.name_ar} - {self.auction.title} - {self.client.username}"
    
    class Meta:
        ordering = ['-created_at']


class BrokerQuote(models.Model):
    """Broker quotes for service requests"""
    
    STATUS_CHOICES = [
        ('pending', 'في انتظار الرد'),
        ('selected', 'تم الاختيار'),
        ('rejected', 'مرفوض'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service_request = models.ForeignKey(ServiceRequest, on_delete=models.CASCADE, related_name='quotes')
    broker = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='broker_quotes')
    
    # Quote details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    estimated_duration = models.CharField(max_length=100, help_text="المدة المتوقعة")
    notes = models.TextField(blank=True, help_text="ملاحظات إضافية")
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.broker.username} - {self.amount} ريال - {self.service_request.service.name_ar}"
    
    class Meta:
        unique_together = ['service_request', 'broker']
        ordering = ['amount']  # Order by price (lowest first)


class ServiceExecution(models.Model):
    """Service execution and completion details"""
    
    STATUS_CHOICES = [
        ('started', 'بدأ التنفيذ'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('client_approved', 'موافقة العميل'),
        ('payment_released', 'تم تحويل المبلغ'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service_request = models.OneToOneField(ServiceRequest, on_delete=models.CASCADE, related_name='execution')
    broker = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='service_executions')
    
    # Execution details
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='started')
    
    # Report details
    report_text = models.TextField(blank=True, help_text="تقرير البروكر")
    report_images = models.JSONField(default=list, help_text="صور التقرير")
    
    # Client feedback
    client_approved = models.BooleanField(default=False)
    client_feedback = models.TextField(blank=True)
    client_rating = models.IntegerField(null=True, blank=True, help_text="تقييم من 1-5")
    
    # Payment
    payment_released = models.BooleanField(default=False)
    payment_released_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"تنفيذ {self.service_request.service.name_ar} - {self.broker.username}"


class BrokerProfile(models.Model):
    """Extended profile for brokers"""
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='broker_profile')
    
    # Service areas
    service_areas = models.TextField(help_text="المناطق التي يخدمها البروكر")
    specializations = models.ManyToManyField(BrokerService, blank=True, help_text="التخصصات")
    
    # Ratings and statistics
    total_services = models.IntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    total_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    # Availability
    is_available = models.BooleanField(default=True)
    max_concurrent_services = models.IntegerField(default=5)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"ملف {self.user.username} - البروكر"
    
    @property
    def current_active_services(self):
        return ServiceExecution.objects.filter(
            broker=self.user,
            status__in=['started', 'in_progress']
        ).count()
    
    @property
    def can_accept_new_service(self):
        return self.is_available and self.current_active_services < self.max_concurrent_services
