# Generated by Django 4.2.7 on 2025-06-15 12:50

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auctions', '0001_initial'),
        ('payments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Delivery',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('pickup_address', models.TextField()),
                ('pickup_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('pickup_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('delivery_address', models.TextField()),
                ('delivery_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('delivery_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('picked_up', 'Picked Up'), ('in_transit', 'In Transit'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('estimated_pickup_time', models.DateTimeField(blank=True, null=True)),
                ('estimated_delivery_time', models.DateTimeField(blank=True, null=True)),
                ('picked_up_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('delivery_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=8)),
                ('distance_km', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('special_instructions', models.TextField(blank=True)),
                ('tracking_number', models.CharField(blank=True, max_length=50, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('auction', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery', to='auctions.auction')),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries_as_buyer', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Deliveries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('contact_person', models.CharField(max_length=100)),
                ('phone_number', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('address', models.TextField()),
                ('service_areas', models.TextField(help_text='Comma-separated list of service areas')),
                ('base_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=8)),
                ('rate_per_km', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=6)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('total_deliveries', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Vessel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('vessel_type', models.CharField(choices=[('fishing_boat', 'Fishing Boat'), ('trawler', 'Trawler'), ('cargo_ship', 'Cargo Ship'), ('delivery_truck', 'Delivery Truck')], max_length=20)),
                ('registration_number', models.CharField(max_length=50, unique=True)),
                ('capacity', models.DecimalField(decimal_places=2, help_text='Capacity in kg', max_digits=8)),
                ('length', models.DecimalField(decimal_places=2, help_text='Length in meters', max_digits=6)),
                ('current_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('current_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('last_location_update', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vessels', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('picked_up', 'Picked Up'), ('in_transit', 'In Transit'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], max_length=20)),
                ('message', models.TextField()),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='delivery_updates/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('delivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updates', to='delivery.delivery')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeliveryRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.PositiveSmallIntegerField()),
                ('timeliness_rating', models.PositiveSmallIntegerField()),
                ('condition_rating', models.PositiveSmallIntegerField()),
                ('communication_rating', models.PositiveSmallIntegerField()),
                ('review_text', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('delivery', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rating', to='delivery.delivery')),
                ('rated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='delivery',
            name='delivery_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='delivery.deliveryprovider'),
        ),
        migrations.AddField(
            model_name='delivery',
            name='payment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery', to='payments.payment'),
        ),
        migrations.AddField(
            model_name='delivery',
            name='seller',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries_as_seller', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='delivery',
            name='vessel',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='delivery.vessel'),
        ),
    ]
