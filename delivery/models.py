from django.db import models
from django.conf import settings
from decimal import Decimal
import uuid


class DeliveryProvider(models.Model):
    """Delivery service providers"""

    name = models.CharField(max_length=100)
    contact_person = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=20)
    email = models.EmailField()
    address = models.TextField()

    # Service areas
    service_areas = models.TextField(help_text="Comma-separated list of service areas")

    # Pricing
    base_rate = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'))
    rate_per_km = models.DecimalField(max_digits=6, decimal_places=2, default=Decimal('0.00'))

    # Ratings
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    total_deliveries = models.PositiveIntegerField(default=0)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


class Vessel(models.Model):
    """Fishing vessels for tracking"""

    VESSEL_TYPES = [
        ('fishing_boat', 'Fishing Boat'),
        ('trawler', 'Trawler'),
        ('cargo_ship', 'Cargo Ship'),
        ('delivery_truck', 'Delivery Truck'),
    ]

    name = models.CharField(max_length=100)
    vessel_type = models.CharField(max_length=20, choices=VESSEL_TYPES)
    registration_number = models.CharField(max_length=50, unique=True)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='vessels')

    # Specifications
    capacity = models.DecimalField(max_digits=8, decimal_places=2, help_text="Capacity in kg")
    length = models.DecimalField(max_digits=6, decimal_places=2, help_text="Length in meters")

    # Current location
    current_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    current_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    last_location_update = models.DateTimeField(null=True, blank=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.registration_number})"

    class Meta:
        ordering = ['name']


class Delivery(models.Model):
    """Delivery tracking for auction items"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('picked_up', 'Picked Up'),
        ('in_transit', 'In Transit'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    auction = models.OneToOneField('auctions.Auction', on_delete=models.CASCADE, related_name='delivery')
    payment = models.OneToOneField('payments.Payment', on_delete=models.CASCADE, related_name='delivery')

    # Parties
    seller = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='deliveries_as_seller'
    )
    buyer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='deliveries_as_buyer'
    )
    delivery_provider = models.ForeignKey(
        DeliveryProvider,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    vessel = models.ForeignKey(Vessel, on_delete=models.SET_NULL, null=True, blank=True)

    # Addresses
    pickup_address = models.TextField()
    pickup_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    pickup_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    delivery_address = models.TextField()
    delivery_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    delivery_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    # Status and timing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    estimated_pickup_time = models.DateTimeField(null=True, blank=True)
    estimated_delivery_time = models.DateTimeField(null=True, blank=True)

    # Actual times
    picked_up_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    # Delivery details
    delivery_cost = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'))
    distance_km = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    special_instructions = models.TextField(blank=True)

    # Tracking
    tracking_number = models.CharField(max_length=50, unique=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.tracking_number:
            # Generate tracking number
            import random
            import string
            self.tracking_number = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Delivery for {self.auction.title} - {self.get_status_display()}"

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Deliveries"


class DeliveryUpdate(models.Model):
    """Delivery status updates and location tracking"""

    delivery = models.ForeignKey(Delivery, on_delete=models.CASCADE, related_name='updates')
    status = models.CharField(max_length=20, choices=Delivery.STATUS_CHOICES)
    message = models.TextField()

    # Location at time of update
    latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)

    # Photos/documents
    photo = models.ImageField(upload_to='delivery_updates/', blank=True, null=True)

    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.delivery.tracking_number} - {self.get_status_display()}"

    class Meta:
        ordering = ['-created_at']


class DeliveryRating(models.Model):
    """Ratings for delivery service"""

    delivery = models.OneToOneField(Delivery, on_delete=models.CASCADE, related_name='rating')
    rated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    # Ratings (1-5 scale)
    overall_rating = models.PositiveSmallIntegerField()
    timeliness_rating = models.PositiveSmallIntegerField()
    condition_rating = models.PositiveSmallIntegerField()
    communication_rating = models.PositiveSmallIntegerField()

    # Review
    review_text = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Rating for {self.delivery.tracking_number} - {self.overall_rating}/5"

    class Meta:
        ordering = ['-created_at']
