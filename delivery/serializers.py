from rest_framework import serializers
from django.utils import timezone
from .models import (
    DeliveryProvider, Vessel, Delivery, DeliveryUpdate, DeliveryRating
)
from accounts.serializers import UserSerializer
from auctions.serializers import AuctionListSerializer
from payments.serializers import PaymentSerializer


class DeliveryProviderSerializer(serializers.ModelSerializer):
    """Serializer for delivery providers"""
    
    class Meta:
        model = DeliveryProvider
        fields = [
            'id', 'name', 'contact_person', 'phone_number', 'email', 'address',
            'service_areas', 'base_rate', 'rate_per_km', 'average_rating',
            'total_deliveries', 'is_active'
        ]
        read_only_fields = ['average_rating', 'total_deliveries']


class VesselSerializer(serializers.ModelSerializer):
    """Serializer for vessels"""
    
    owner = UserSerializer(read_only=True)
    vessel_type_display = serializers.CharField(source='get_vessel_type_display', read_only=True)
    
    class Meta:
        model = Vessel
        fields = [
            'id', 'name', 'vessel_type', 'vessel_type_display', 'registration_number',
            'owner', 'capacity', 'length', 'current_latitude', 'current_longitude',
            'last_location_update', 'is_active', 'created_at'
        ]
        read_only_fields = ['owner', 'last_location_update']


class VesselLocationUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating vessel location"""
    
    class Meta:
        model = Vessel
        fields = ['current_latitude', 'current_longitude']
    
    def update(self, instance, validated_data):
        validated_data['last_location_update'] = timezone.now()
        return super().update(instance, validated_data)


class DeliveryUpdateSerializer(serializers.ModelSerializer):
    """Serializer for delivery updates"""
    
    created_by = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = DeliveryUpdate
        fields = [
            'id', 'delivery', 'status', 'status_display', 'message',
            'latitude', 'longitude', 'photo', 'created_by', 'created_at'
        ]
        read_only_fields = ['created_by']


class DeliveryRatingSerializer(serializers.ModelSerializer):
    """Serializer for delivery ratings"""
    
    rated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = DeliveryRating
        fields = [
            'id', 'delivery', 'rated_by', 'overall_rating', 'timeliness_rating',
            'condition_rating', 'communication_rating', 'review_text', 'created_at'
        ]
        read_only_fields = ['rated_by']
    
    def validate_overall_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value
    
    def validate_timeliness_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value
    
    def validate_condition_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value
    
    def validate_communication_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value


class DeliverySerializer(serializers.ModelSerializer):
    """Serializer for delivery (read)"""
    
    auction = AuctionListSerializer(read_only=True)
    payment = PaymentSerializer(read_only=True)
    seller = UserSerializer(read_only=True)
    buyer = UserSerializer(read_only=True)
    delivery_provider = DeliveryProviderSerializer(read_only=True)
    vessel = VesselSerializer(read_only=True)
    updates = DeliveryUpdateSerializer(many=True, read_only=True)
    rating = DeliveryRatingSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Delivery
        fields = [
            'id', 'auction', 'payment', 'seller', 'buyer', 'delivery_provider',
            'vessel', 'pickup_address', 'pickup_latitude', 'pickup_longitude',
            'delivery_address', 'delivery_latitude', 'delivery_longitude',
            'status', 'status_display', 'estimated_pickup_time', 'estimated_delivery_time',
            'picked_up_at', 'delivered_at', 'delivery_cost', 'distance_km',
            'special_instructions', 'tracking_number', 'updates', 'rating',
            'created_at', 'updated_at'
        ]


class DeliveryCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating delivery"""
    
    class Meta:
        model = Delivery
        fields = [
            'auction', 'payment', 'delivery_provider', 'vessel',
            'pickup_address', 'pickup_latitude', 'pickup_longitude',
            'delivery_address', 'delivery_latitude', 'delivery_longitude',
            'estimated_pickup_time', 'estimated_delivery_time',
            'delivery_cost', 'distance_km', 'special_instructions'
        ]
    
    def validate(self, attrs):
        auction = attrs.get('auction')
        payment = attrs.get('payment')
        
        # Validate that auction is completed and paid
        if auction and auction.status != 'ended':
            raise serializers.ValidationError("Can only create delivery for ended auctions")
        
        if payment and payment.status != 'completed':
            raise serializers.ValidationError("Payment must be completed before creating delivery")
        
        return attrs
    
    def create(self, validated_data):
        auction = validated_data['auction']
        payment = validated_data['payment']
        
        # Set seller and buyer from auction
        validated_data['seller'] = auction.seller
        validated_data['buyer'] = payment.user
        
        return super().create(validated_data)
