from django.urls import path
from .views import (
    DeliveryProviderListView, VesselListView, VesselCreateView, VesselLocationUpdateView,
    DeliveryListView, DeliveryDetailView, DeliveryCreateView, DeliveryUpdateCreateView,
    SellerDeliveryListView, update_delivery_status, DeliveryUpdateListView
)

app_name = 'delivery'

urlpatterns = [
    # Delivery providers
    path('providers/', DeliveryProviderListView.as_view(), name='provider_list'),

    # Vessels
    path('vessels/', VesselListView.as_view(), name='vessel_list'),
    path('vessels/create/', VesselCreateView.as_view(), name='vessel_create'),
    path('vessels/<int:pk>/location/', VesselLocationUpdateView.as_view(), name='vessel_location_update'),

    # Deliveries
    path('', DeliveryListView.as_view(), name='delivery_list'),
    path('<uuid:pk>/', DeliveryDetailView.as_view(), name='delivery_detail'),
    path('create/', DeliveryCreateView.as_view(), name='delivery_create'),
    path('updates/create/', DeliveryUpdateCreateView.as_view(), name='delivery_update_create'),

    # Seller-specific endpoints
    path('seller/', SellerDeliveryListView.as_view(), name='seller_delivery_list'),
    path('<uuid:delivery_id>/update-status/', update_delivery_status, name='update_delivery_status'),
    path('<uuid:pk>/updates/', DeliveryUpdateListView.as_view(), name='delivery_updates_list'),
]
