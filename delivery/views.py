from django.db import models
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .models import (
    DeliveryProvider, Vessel, Delivery, DeliveryUpdate, DeliveryRating
)
from .serializers import (
    DeliveryProviderSerializer, VesselSerializer, DeliverySerializer,
    DeliveryUpdateSerializer, DeliveryRatingSerializer, DeliveryCreateSerializer,
    VesselLocationUpdateSerializer
)


class DeliveryProviderListView(generics.ListAPIView):
    """List all delivery providers"""

    queryset = DeliveryProvider.objects.filter(is_active=True)
    serializer_class = DeliveryProviderSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="List delivery providers",
        description="Get all active delivery service providers"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class VesselListView(generics.ListAPIView):
    """List user's vessels"""

    serializer_class = VesselSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Vessel.objects.filter(owner=self.request.user, is_active=True)

    @extend_schema(
        summary="List my vessels",
        description="Get all vessels owned by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class VesselCreateView(generics.CreateAPIView):
    """Create new vessel"""

    queryset = Vessel.objects.all()
    serializer_class = VesselSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    @extend_schema(
        summary="Create vessel",
        description="Register a new vessel for tracking"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class VesselLocationUpdateView(generics.UpdateAPIView):
    """Update vessel location"""

    queryset = Vessel.objects.all()
    serializer_class = VesselLocationUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Vessel.objects.filter(owner=self.request.user)

    @extend_schema(
        summary="Update vessel location",
        description="Update the current GPS location of a vessel"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class DeliveryListView(generics.ListAPIView):
    """List deliveries for user"""

    serializer_class = DeliverySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']

    def get_queryset(self):
        user = self.request.user
        return Delivery.objects.filter(
            models.Q(buyer=user) | models.Q(seller=user)
        ).distinct()

    @extend_schema(
        summary="List my deliveries",
        description="Get all deliveries for the authenticated user (as buyer or seller)",
        parameters=[
            OpenApiParameter('status', str, description='Filter by delivery status'),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class DeliveryDetailView(generics.RetrieveAPIView):
    """Get delivery details"""

    serializer_class = DeliverySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        return Delivery.objects.filter(
            models.Q(buyer=user) | models.Q(seller=user)
        )

    @extend_schema(
        summary="Get delivery details",
        description="Get detailed information about a specific delivery"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class DeliveryCreateView(generics.CreateAPIView):
    """Create delivery for auction"""

    queryset = Delivery.objects.all()
    serializer_class = DeliveryCreateSerializer
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Create delivery",
        description="Create a delivery for a completed auction"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class DeliveryUpdateCreateView(generics.CreateAPIView):
    """Create delivery status update"""

    queryset = DeliveryUpdate.objects.all()
    serializer_class = DeliveryUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @extend_schema(
        summary="Create delivery update",
        description="Add a status update to a delivery"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class DeliveryUpdateListView(generics.ListAPIView):
    """List delivery updates for a specific delivery"""

    serializer_class = DeliveryUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        delivery_id = self.kwargs['pk']
        return DeliveryUpdate.objects.filter(
            delivery_id=delivery_id
        ).select_related('created_by').order_by('-created_at')

    @extend_schema(
        summary="Get delivery updates",
        description="Get delivery updates for a specific delivery"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class SellerDeliveryListView(generics.ListAPIView):
    """List deliveries for seller (ended auctions with successful payments)"""

    serializer_class = DeliverySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']

    def get_queryset(self):
        user = self.request.user
        # Only return deliveries where user is the seller
        return Delivery.objects.filter(seller=user).select_related(
            'auction', 'buyer', 'seller', 'payment'
        ).prefetch_related('updates').order_by('-created_at')

    @extend_schema(
        summary="List seller deliveries",
        description="Get all deliveries for auctions sold by the authenticated seller",
        parameters=[
            OpenApiParameter('status', str, description='Filter by delivery status'),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Update delivery status",
    description="Update delivery status by seller",
    request={
        'application/json': {
            'type': 'object',
            'properties': {
                'status': {'type': 'string', 'enum': ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed', 'cancelled']},
                'message': {'type': 'string'},
                'latitude': {'type': 'number'},
                'longitude': {'type': 'number'},
            },
            'required': ['status']
        }
    }
)
def update_delivery_status(request, delivery_id):
    """Update delivery status (for sellers)"""
    try:
        delivery = Delivery.objects.get(
            id=delivery_id,
            seller=request.user
        )
    except Delivery.DoesNotExist:
        return Response(
            {'error': 'Delivery not found or you do not have permission to update it'},
            status=status.HTTP_404_NOT_FOUND
        )

    new_status = request.data.get('status')
    message = request.data.get('message', f'Status updated to {new_status}')

    if not new_status:
        return Response(
            {'error': 'Status is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Update delivery status
    delivery.status = new_status

    # Update specific timestamps based on status
    from django.utils import timezone
    now = timezone.now()

    if new_status == 'picked_up' and not delivery.picked_up_at:
        delivery.picked_up_at = now
    elif new_status == 'delivered' and not delivery.delivered_at:
        delivery.delivered_at = now

    delivery.save()

    # Create delivery update record
    DeliveryUpdate.objects.create(
        delivery=delivery,
        status=new_status,
        message=message,
        created_by=request.user
    )

    # Send delivery status notification to buyer
    try:
        from notifications.services import NotificationService
        notification_service = NotificationService()

        logger.info(f"📦 Sending delivery status notification to buyer: {delivery.buyer.username}")
        logger.info(f"   Delivery ID: {delivery.id}")
        logger.info(f"   Auction: {delivery.auction.title}")
        logger.info(f"   New status: {new_status}")
        logger.info(f"   Status display: {delivery.get_status_display()}")

        # Send notification to buyer about delivery status update
        notifications = notification_service.send_notification(
            delivery.buyer,
            'delivery_status_changed',
            {
                'delivery': delivery,
                'auction': delivery.auction,
                'status': new_status,
                'message': message,
                'status_display': delivery.get_status_display(),
            },
            channels=['whatsapp', 'in_app']  # Both WhatsApp and in-app
        )

        logger.info(f"✅ Sent {len(notifications)} delivery status notifications to buyer")

    except Exception as e:
        logger.error(f"❌ Failed to send delivery notification: {e}")
        import traceback
        traceback.print_exc()

    # Return success response with updated delivery data in the format Flutter expects
    from .serializers import DeliverySerializer
    serializer = DeliverySerializer(delivery)

    return Response(serializer.data, status=status.HTTP_200_OK)
