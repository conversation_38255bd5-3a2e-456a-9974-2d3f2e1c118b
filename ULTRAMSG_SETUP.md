# UltraMsg WhatsApp Integration Setup

This guide will help you set up UltraMsg WhatsApp API integration for your Fish Auction app.

## 🚀 Quick Setup

### 1. Create UltraMsg Account
1. Go to [UltraMsg.com](https://ultramsg.com)
2. Sign up for a free account
3. Create a new WhatsApp instance

### 2. Get Your Credentials
1. Login to your [UltraMsg Dashboard](https://user.ultramsg.com)
2. Go to your instance
3. Copy your **Instance ID** and **Token**

### 3. Configure Environment Variables

Add these to your environment variables or `.env` file:

```bash
# UltraMsg WhatsApp API Configuration
ULTRAMSG_INSTANCE_ID=your_instance_id_here
ULTRAMSG_TOKEN=your_token_here
```

### 4. Authenticate Your WhatsApp
1. In your UltraMsg dashboard, go to your instance
2. Scan the QR code with your WhatsApp
3. Wait for "Auth Status: authenticated"

### 5. Test the Integration

Run the test script:
```bash
python3 test_ultramsg_integration.py
```

## 📱 WhatsApp Message Types

Your Fish Auction app will send WhatsApp notifications for:

### 🎉 Auction Events
- **Auction Won**: Congratulations message with payment details
- **Auction Ending**: Reminder when auction ends in 1 hour
- **Bid Outbid**: Alert when someone outbids you

### 💰 Payment Events  
- **Payment Reminder**: Urgent reminder before 20-minute deadline
- **Payment Received**: Confirmation for sellers

### 📋 Account Events
- **KYC Approved**: Welcome message after verification
- **KYC Rejected**: Instructions to resubmit documents

### 🚚 Delivery Events
- **Delivery Updates**: Status updates with tracking info

## 🔧 Configuration Details

### Instance Settings
- **Instance Type**: Personal or Business WhatsApp account
- **Webhook URL**: Not required for basic notifications
- **Message Format**: Text messages with emojis and formatting

### Phone Number Format
- Always use international format: `+**********`
- Include country code without spaces or dashes
- Example: `+************` (UAE), `+***********` (US)

### Message Limits
- **Free Plan**: 100 messages/month
- **Paid Plans**: Higher limits available
- **Message Length**: Up to 4096 characters

## 🧪 Testing

### Test Checklist
- [ ] UltraMsg account created
- [ ] Instance authenticated (QR scanned)
- [ ] Environment variables set
- [ ] Test script runs successfully
- [ ] Receive test WhatsApp message

### Test Commands
```bash
# Test UltraMsg service
python3 test_ultramsg_integration.py

# Create WhatsApp templates
python3 manage.py create_whatsapp_templates

# Test notification system
python3 manage.py shell
>>> from notifications.services import NotificationService
>>> # Test with your user and auction
```

## 🔍 Troubleshooting

### Common Issues

**❌ "UltraMsg not configured"**
- Check environment variables are set correctly
- Restart Django server after setting variables

**❌ "Instance not authenticated"**
- Scan QR code in UltraMsg dashboard
- Wait for "Auth Status: authenticated"
- Check WhatsApp is connected to internet

**❌ "Phone number not valid"**
- Use international format: +countrycode+number
- Remove spaces, dashes, parentheses
- Verify number has WhatsApp installed

**❌ "Message not sent"**
- Check UltraMsg account balance/limits
- Verify recipient has WhatsApp
- Check instance status in dashboard

### Debug Mode
Enable debug logging in Django settings:
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'notifications': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

## 📞 Support

### UltraMsg Support
- Email: <EMAIL>
- WhatsApp: +************
- Documentation: https://docs.ultramsg.com

### Fish Auction App
- Check logs in Django admin
- Test with `test_ultramsg_integration.py`
- Verify templates with `python3 manage.py shell`

## 🎯 Next Steps

After setup is complete:

1. **Test with Real Users**: Send test notifications to actual phone numbers
2. **Monitor Usage**: Check UltraMsg dashboard for message statistics
3. **Upgrade Plan**: Consider paid plan for higher message limits
4. **Customize Templates**: Modify WhatsApp message templates as needed
5. **Add Images**: Use UltraMsg image API for fish photos in messages

## 🔐 Security Notes

- Keep your UltraMsg token secure
- Don't commit credentials to version control
- Use environment variables for production
- Regularly rotate API tokens
- Monitor usage for suspicious activity

---

🐟 **Fish Auction Team** - Happy bidding with WhatsApp notifications!
