# 🔧 Fish Auction App - Troubleshooting Guide

Common issues and solutions for setting up the Fish Auction app.

## 🚨 Common Issues

### 1. Python Command Not Found

**Error**: `python: command not found`

**Solution for macOS**:
```bash
# Use the macOS-specific script instead:
./quick_start_macos.sh

# Or manually use python3:
python3 manage.py runserver 8000
```

**Solution for Linux/Windows**:
```bash
# Install Python 3.8+
# Ubuntu/Debian:
sudo apt update && sudo apt install python3 python3-pip

# Windows: Download from https://python.org
```

### 2. Virtual Environment Issues

**Error**: `venv/bin/activate: No such file or directory`

**Solution**:
```bash
# Remove existing venv and recreate:
rm -rf venv
python3 -m venv venv
source venv/bin/activate

# Or use the fixed script:
./quick_start_macos.sh
```

### 3. Redis Connection Failed

**Error**: `redis.exceptions.ConnectionError`

**Solution for macOS**:
```bash
# Install Redis with Homebrew:
brew install redis
brew services start redis

# Test connection:
redis-cli ping  # Should return PONG
```

**Solution for Ubuntu/Debian**:
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**Solution for Windows**:
```bash
# Option 1: Use Docker
docker run -d -p 6379:6379 redis:alpine

# Option 2: Download Redis for Windows
# https://github.com/microsoftarchive/redis/releases
```

### 4. Port Already in Use

**Error**: `Port 8000 is already in use`

**Solution**:
```bash
# Find and kill process using port 8000:
# macOS/Linux:
lsof -ti:8000 | xargs kill -9

# Or use the process ID:
lsof -i :8000
kill -9 <PID>

# Windows:
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

### 5. ngrok Not Found

**Error**: `ngrok: command not found`

**Solution**:
```bash
# macOS with Homebrew:
brew install ngrok

# Or download manually:
# 1. Go to https://ngrok.com/download
# 2. Download for your OS
# 3. Extract and add to PATH

# Or use npm:
npm install -g ngrok
```

### 6. Django Migration Errors

**Error**: `django.db.utils.OperationalError`

**Solution**:
```bash
# Reset database (development only):
rm db.sqlite3
python3 manage.py makemigrations
python3 manage.py migrate

# Create superuser:
python3 manage.py createsuperuser
```

### 7. Celery Worker Won't Start

**Error**: `celery: command not found`

**Solution**:
```bash
# Make sure virtual environment is activated:
source venv/bin/activate

# Install celery:
pip install celery

# Start worker:
celery -A fish_auction worker --loglevel=info
```

### 8. Flutter Dependencies Error

**Error**: `flutter pub get` fails

**Solution**:
```bash
# Clean Flutter cache:
flutter clean
flutter pub cache repair

# Get dependencies:
flutter pub get

# Check Flutter installation:
flutter doctor
```

### 9. WebSocket Connection Failed

**Error**: WebSocket connections not working

**Solution**:
```bash
# Check if Redis is running:
redis-cli ping

# Check Django Channels installation:
pip install channels channels-redis

# Verify ASGI configuration in fish_auction/asgi.py
# Restart Django server:
python3 manage.py runserver 8000
```

### 10. Stripe Webhook Not Receiving Events

**Error**: Webhooks not being delivered

**Solution**:
```bash
# Check ngrok is running:
curl http://localhost:4040/api/tunnels

# Test webhook endpoint:
curl -X POST https://your-ngrok-url.ngrok.io/api/payments/stripe/webhook/

# Check Stripe Dashboard:
# 1. Go to Developers → Webhooks
# 2. Click on your webhook
# 3. Check "Recent deliveries"
# 4. Look for error messages

# Verify webhook secret in .env:
STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret
```

## 🛠️ Manual Setup Steps

If the automated scripts don't work, follow these manual steps:

### 1. Setup Backend Manually

```bash
# 1. Create virtual environment
python3 -m venv venv
source venv/bin/activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Setup database
python3 manage.py migrate

# 4. Start Redis (if not running)
brew services start redis  # macOS
sudo systemctl start redis-server  # Linux

# 5. Start Django server
python3 manage.py runserver 8000

# 6. Start Celery (in separate terminals)
celery -A fish_auction worker --loglevel=info
celery -A fish_auction beat --loglevel=info

# 7. Start ngrok
ngrok http 8000
```

### 2. Setup Flutter App Manually

```bash
# 1. Navigate to Flutter app
cd fish_auction_app

# 2. Get dependencies
flutter pub get

# 3. Run app
flutter run -d web-server --web-port 8080
```

### 3. Setup Stripe Webhooks Manually

```bash
# 1. Get ngrok URL from dashboard: http://localhost:4040
# 2. Go to Stripe Dashboard: https://dashboard.stripe.com/webhooks
# 3. Add endpoint: https://your-ngrok-url.ngrok.io/api/payments/stripe/webhook/
# 4. Select events: payment_intent.succeeded, payment_intent.payment_failed
# 5. Copy signing secret to .env file
```

## 🔍 Debugging Commands

### Check Service Status
```bash
# Check if services are running:
lsof -i :8000  # Django
lsof -i :6379  # Redis
lsof -i :4040  # ngrok
lsof -i :8080  # Flutter web

# Check processes:
ps aux | grep python
ps aux | grep celery
ps aux | grep ngrok
```

### View Logs
```bash
# Django logs:
tail -f django.log

# Celery logs:
tail -f celery_worker.log
tail -f celery_beat.log

# ngrok logs:
tail -f ngrok.log
```

### Test Connections
```bash
# Test Django API:
curl http://localhost:8000/api/

# Test Redis:
redis-cli ping

# Test ngrok tunnel:
curl https://your-ngrok-url.ngrok.io/api/

# Test WebSocket:
wscat -c ws://localhost:8000/ws/auction/1/
```

## 📞 Getting Help

If you're still having issues:

1. **Check the logs** - Look at django.log, celery_worker.log for error messages
2. **Verify environment** - Make sure .env file has correct values
3. **Test components individually** - Start each service separately
4. **Check versions** - Ensure Python 3.8+, Redis, ngrok are installed
5. **Use the simplified script** - Try `./quick_start_macos.sh` for macOS

## 🎯 Quick Fix Commands

```bash
# Reset everything:
pkill -f "runserver 8000"
pkill -f "celery.*fish_auction"
pkill -f "ngrok"
rm -rf venv
./quick_start_macos.sh

# Just restart services:
pkill -f "runserver 8000"
python3 manage.py runserver 8000 &
ngrok http 8000 &

# Fix permissions:
chmod +x *.sh
```

Remember: The automated scripts handle most of these issues automatically! 🚀
