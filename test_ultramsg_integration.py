#!/usr/bin/env python3
"""
Test script for UltraMsg WhatsApp integration
This script tests the WhatsApp notification system with UltraMsg API
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from notifications.ultramsg_service import ultramsg_service
from notifications.services import NotificationService
from accounts.models import User
from auctions.models import Auction


def test_ultramsg_service():
    """Test UltraMsg service directly"""
    print("🧪 Testing UltraMsg Service")
    print("=" * 40)
    
    # Check configuration
    if not ultramsg_service.is_configured():
        print("❌ UltraMsg not configured. Please set environment variables:")
        print("   - ULTRAMSG_INSTANCE_ID")
        print("   - ULTRAMSG_TOKEN")
        return False
    
    print("✅ UltraMsg service is configured")
    
    # Test instance status
    print("\n📊 Checking instance status...")
    status_result = ultramsg_service.get_instance_status()
    
    if status_result['success']:
        status = status_result['status']
        print(f"✅ Instance Status: {status.get('accountStatus', 'Unknown')}")
        print(f"📱 Phone: {status.get('phone', 'Not connected')}")
    else:
        print(f"❌ Failed to get status: {status_result['error']}")
        return False
    
    return True


def test_phone_validation():
    """Test phone number validation"""
    print("\n🧪 Testing Phone Number Validation")
    print("=" * 40)
    
    # Test with a sample phone number (replace with your test number)
    test_phone = "+**********"  # Replace with your actual test number
    
    print(f"📞 Testing phone number: {test_phone}")
    result = ultramsg_service.check_phone_number(test_phone)
    
    if result['success']:
        print(f"✅ Phone validation successful")
        print(f"   Valid: {result['valid']}")
        print(f"   Has WhatsApp: {result['has_whatsapp']}")
    else:
        print(f"❌ Phone validation failed: {result['error']}")
    
    return result['success']


def test_send_message():
    """Test sending a WhatsApp message"""
    print("\n🧪 Testing Message Sending")
    print("=" * 40)
    
    # Get a test phone number (you should replace this with your actual test number)
    test_phone = input("Enter your WhatsApp number (with country code, e.g., +**********): ").strip()
    
    if not test_phone:
        print("❌ No phone number provided, skipping message test")
        return False
    
    test_message = """🧪 *Test Message from Fish Auction*

This is a test message to verify UltraMsg integration.

✅ If you receive this, the integration is working!

🐟 Fish Auction Team"""
    
    print(f"📱 Sending test message to: {test_phone}")
    result = ultramsg_service.send_text_message(test_phone, test_message)
    
    if result['success']:
        print(f"✅ Message sent successfully!")
        print(f"   Message ID: {result.get('message_id')}")
        return True
    else:
        print(f"❌ Failed to send message: {result['error']}")
        return False


def test_notification_system():
    """Test the notification system with WhatsApp"""
    print("\n🧪 Testing Notification System")
    print("=" * 40)
    
    # Get a test user
    test_user = User.objects.filter(user_type='buyer').first()
    if not test_user:
        print("❌ No test user found")
        return False
    
    print(f"👤 Using test user: {test_user.username}")
    
    # Check if user has phone number
    if not test_user.phone_number:
        print("❌ Test user doesn't have a phone number")
        return False
    
    print(f"📱 User phone: {test_user.phone_number}")
    
    # Get a test auction
    test_auction = Auction.objects.filter(status='live').first()
    if not test_auction:
        print("❌ No live auction found for testing")
        return False
    
    print(f"🐟 Using test auction: {test_auction.title}")
    
    # Test sending auction won notification
    service = NotificationService()
    context = {
        'auction': test_auction,
        'auction_title': test_auction.title,
        'time_remaining': '5 minutes'
    }
    
    print("\n📤 Sending test 'auction_won' notification...")
    notifications = service.send_notification(
        user=test_user,
        notification_type='auction_won',
        context=context,
        channels=['whatsapp']  # Only test WhatsApp
    )
    
    if notifications:
        for notification in notifications:
            if notification.channel == 'whatsapp':
                if notification.status == 'sent':
                    print(f"✅ WhatsApp notification sent successfully!")
                    print(f"   Title: {notification.title}")
                    print(f"   Message: {notification.message[:100]}...")
                    return True
                else:
                    print(f"❌ WhatsApp notification failed: {notification.message}")
                    return False
    
    print("❌ No WhatsApp notification was created")
    return False


def main():
    """Main test function"""
    print("🚀 UltraMsg WhatsApp Integration Test")
    print("=" * 50)
    
    # Test 1: Service configuration
    if not test_ultramsg_service():
        print("\n❌ UltraMsg service test failed")
        return False
    
    # Test 2: Phone validation (optional)
    print("\n" + "="*50)
    phone_test = test_phone_validation()
    
    # Test 3: Direct message sending
    print("\n" + "="*50)
    message_test = test_send_message()
    
    # Test 4: Notification system
    print("\n" + "="*50)
    notification_test = test_notification_system()
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Summary:")
    print(f"   ✅ Service Configuration: Passed")
    print(f"   {'✅' if phone_test else '❌'} Phone Validation: {'Passed' if phone_test else 'Failed'}")
    print(f"   {'✅' if message_test else '❌'} Direct Message: {'Passed' if message_test else 'Failed'}")
    print(f"   {'✅' if notification_test else '❌'} Notification System: {'Passed' if notification_test else 'Failed'}")
    
    overall_success = message_test or notification_test
    
    if overall_success:
        print("\n🎉 UltraMsg integration is working!")
        print("💡 You can now receive WhatsApp notifications for:")
        print("   - Auction won")
        print("   - Payment reminders")
        print("   - Auction ending alerts")
        print("   - Bid outbid notifications")
        print("   - KYC status updates")
        print("   - Delivery updates")
    else:
        print("\n❌ UltraMsg integration needs attention")
        print("💡 Please check:")
        print("   - ULTRAMSG_INSTANCE_ID and ULTRAMSG_TOKEN are set")
        print("   - Your UltraMsg instance is authenticated (QR scanned)")
        print("   - Phone numbers are in international format")
    
    return overall_success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
