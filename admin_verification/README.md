# Fish Auction - Document Verification Admin Interface

## Overview

This is a web-based admin interface for reviewing and approving user document verification requests in the Fish Auction platform. The interface allows administrators to review government ID and hunting approval documents submitted by users.

## Features

### 📊 Dashboard
- Real-time statistics of pending, approved, and rejected documents
- Total user count
- Auto-refresh every 30 seconds

### 🔍 Document Review
- View submitted documents (Government ID and Hunting Approval)
- Document preview with zoom capability
- User information display
- Review notes functionality
- Approve/Reject actions

### 🔧 Filtering & Search
- Filter by document status (Pending, Approved, Rejected)
- Filter by document type (Government ID, Hunting Approval)
- Search by username or email
- Real-time filtering

### 🔐 Security
- Admin-only access with JWT authentication
- Secure API communication
- Session management

## Access

The admin interface is accessible at:
```
http://localhost:8000/api/auth/admin/verification/
```

## Login Requirements

- **User Type**: Admin
- **Credentials**: Use any admin account created in the Django admin panel

## Document Types

### 1. Government ID
- **Purpose**: Identity verification
- **Icon**: ID Card
- **Color**: Blue
- **Accepted Formats**: PDF, JPEG, PNG (max 10MB)

### 2. Hunting Approval
- **Purpose**: Hunting license verification
- **Icon**: Certificate
- **Color**: Green
- **Accepted Formats**: PDF, JPEG, PNG (max 10MB)

## Verification Process

1. **User Submission**: Users submit documents through the mobile app
2. **Admin Review**: Admins review documents in this interface
3. **Decision**: Approve or reject with optional notes
4. **Notification**: Users receive notifications about the decision
5. **Status Update**: User verification status is updated automatically

## User Verification Status

A user is considered **fully verified** when:
- ✅ Government ID is approved
- ✅ Hunting Approval is approved

## API Endpoints Used

- `POST /api/auth/login/` - Admin authentication
- `GET /api/auth/documents/pending/` - Get pending verifications
- `PATCH /api/auth/documents/review/{id}/` - Review document

## Technical Details

### Frontend
- **Framework**: Vanilla JavaScript with Bootstrap 5
- **Icons**: Font Awesome 6
- **Responsive**: Mobile-friendly design
- **Real-time**: Auto-refresh functionality

### Backend Integration
- **Authentication**: JWT tokens
- **API**: RESTful endpoints
- **File Handling**: Secure document serving
- **Notifications**: Automatic user notifications

## Usage Instructions

### 1. Login
- Enter admin username and password
- System verifies admin privileges
- JWT token is stored for session management

### 2. Review Documents
- Click "Review" button on any pending document
- View document preview and user information
- Add review notes (optional)
- Click "Approve" or "Reject"

### 3. Filter and Search
- Use status filter to show specific document states
- Use document type filter for specific document types
- Use search box to find specific users
- Click "Refresh" to reload data

### 4. Monitor Statistics
- View real-time counts in the dashboard cards
- Statistics update automatically every 30 seconds

## Troubleshooting

### Common Issues

1. **Login Failed**
   - Ensure user has admin privileges
   - Check username/password
   - Verify backend is running

2. **Documents Not Loading**
   - Check network connection
   - Verify API endpoints are accessible
   - Check browser console for errors

3. **Document Preview Not Showing**
   - Ensure document files are accessible
   - Check file permissions
   - Verify media serving is configured

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Development

### File Structure
```
admin_verification/
├── index.html          # Main HTML interface
├── admin.js           # JavaScript functionality
└── README.md          # This documentation
```

### Customization
- Modify `admin.js` for functionality changes
- Update `index.html` for UI modifications
- Adjust API endpoints in the configuration section

## Security Considerations

- Admin interface requires authentication
- JWT tokens have expiration
- File access is controlled by Django permissions
- HTTPS recommended for production

## Future Enhancements

- [ ] Bulk approval/rejection
- [ ] Document comparison tools
- [ ] Advanced filtering options
- [ ] Export functionality
- [ ] Audit trail logging
- [ ] Email notifications to admins
