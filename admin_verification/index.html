<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مزاد الأسماك - لوحة الإدارة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* RTL Support */
        body {
            direction: rtl;
            text-align: right;
        }

        .verification-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;
        }

        .verification-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .document-preview {
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }

        .status-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }

        .navbar-brand {
            font-weight: bold;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .btn-approve {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-reject {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .loading-spinner {
            display: none;
        }

        .document-type-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .government-id {
            color: #007bff;
        }

        .hunting-approval {
            color: #28a745;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-fish"></i> إدارة مزاد الأسماك
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user"></i> <span id="adminName">المدير</span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- Navigation Tabs -->
    <div class="container mt-3">
        <ul class="nav nav-tabs" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="verification-tab" data-bs-toggle="tab"
                    data-bs-target="#verification" type="button" role="tab">
                    <i class="fas fa-id-card"></i> التحقق من الوثائق
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments" type="button"
                    role="tab">
                    <i class="fas fa-money-bill-wave"></i> إدارة المدفوعات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payment-methods-tab" data-bs-toggle="tab" data-bs-target="#payment-methods"
                    type="button" role="tab">
                    <i class="fas fa-credit-card"></i> طرق الدفع
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="withdrawals-tab" data-bs-toggle="tab" data-bs-target="#withdrawals"
                    type="button" role="tab">
                    <i class="fas fa-hand-holding-usd"></i> طلبات السحب
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions"
                    type="button" role="tab">
                    <i class="fas fa-history"></i> تاريخ المعاملات
                </button>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="tab-content" id="adminTabContent">

            <!-- Document Verification Tab -->
            <div class="tab-pane fade show active" id="verification" role="tabpanel">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 id="pendingCount">0</h4>
                                <p class="mb-0">Pending Reviews</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 id="approvedCount">0</h4>
                                <p class="mb-0">Approved Today</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h4 id="rejectedCount">0</h4>
                                <p class="mb-0">Rejected Today</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4 id="totalUsersCount">0</h4>
                                <p class="mb-0">Total Users</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="statusFilter" class="form-label">Status Filter</label>
                                        <select class="form-select" id="statusFilter" onchange="filterRequests()">
                                            <option value="">All Statuses</option>
                                            <option value="pending" selected>Pending</option>
                                            <option value="approved">Approved</option>
                                            <option value="rejected">Rejected</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="documentTypeFilter" class="form-label">Document Type</label>
                                        <select class="form-select" id="documentTypeFilter" onchange="filterRequests()">
                                            <option value="">All Types</option>
                                            <option value="government_id">Government ID</option>
                                            <option value="hunting_approval">Hunting Approval</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="searchInput" class="form-label">Search User</label>
                                        <input type="text" class="form-control" id="searchInput"
                                            placeholder="Search by username or email" onkeyup="filterRequests()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="loadVerificationRequests()">
                                            <i class="fas fa-refresh"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Spinner -->
                <div class="text-center loading-spinner" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading verification requests...</p>
                </div>

                <!-- Verification Requests -->
                <div id="verificationRequests">
                    <!-- Requests will be loaded here -->
                </div>

                <!-- No Results Message -->
                <div id="noResults" class="text-center py-5" style="display: none;">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No verification requests found</h5>
                    <p class="text-muted">Try adjusting your filters or check back later.</p>
                </div>
            </div>

            <!-- Payment Management Tab -->
            <div class="tab-pane fade" id="payments" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <h4 id="totalPaymentsCount">0</h4>
                                <p class="mb-0">Total Payments</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 id="completedPaymentsCount">0</h4>
                                <p class="mb-0">Completed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 id="pendingPaymentsCount">0</h4>
                                <p class="mb-0">Pending</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                <h4 id="totalRevenueCount">$0</h4>
                                <p class="mb-0">Total Revenue</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Filters -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="paymentStatusFilter" class="form-label">Payment Status</label>
                                        <select class="form-select" id="paymentStatusFilter"
                                            onchange="filterPayments()">
                                            <option value="">All Statuses</option>
                                            <option value="pending">Pending</option>
                                            <option value="completed">Completed</option>
                                            <option value="failed">Failed</option>
                                            <option value="refunded">Refunded</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="paymentMethodFilter" class="form-label">Payment Method</label>
                                        <select class="form-select" id="paymentMethodFilter"
                                            onchange="filterPayments()">
                                            <option value="">All Methods</option>
                                            <option value="wallet">Wallet</option>
                                            <option value="stripe">Stripe</option>
                                            <option value="paypal">PayPal</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="paymentSearchInput" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="paymentSearchInput"
                                            placeholder="Search by auction title or user" onkeyup="filterPayments()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="loadPayments()">
                                            <i class="fas fa-refresh"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payments List -->
                <div id="paymentsList">
                    <!-- Payments will be loaded here -->
                </div>
            </div>

            <!-- Payment Methods Tab -->
            <div class="tab-pane fade" id="payment-methods" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h5 class="card-title">Pending</h5>
                                <h3 id="pendingPaymentMethodsCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check fa-2x mb-2"></i>
                                <h5 class="card-title">Verified</h5>
                                <h3 id="verifiedPaymentMethodsCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-times fa-2x mb-2"></i>
                                <h5 class="card-title">Rejected</h5>
                                <h3 id="rejectedPaymentMethodsCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <h5 class="card-title">Total Methods</h5>
                                <h3 id="totalPaymentMethodsCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="paymentMethodStatusFilter">
                            <option value="">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="verified">Verified</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="paymentMethodTypeFilter">
                            <option value="">All Types</option>
                            <option value="paypal">PayPal</option>
                            <option value="stripe">Stripe</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="payoneer">Payoneer</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="paymentMethodSearch"
                            placeholder="Search by username or email...">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" onclick="loadPaymentMethods()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Payment Methods List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Payment Methods</h5>
                    </div>
                    <div class="card-body">
                        <div id="paymentMethodsLoading" class="text-center py-4" style="display: none;">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading payment methods...</p>
                        </div>
                        <div id="paymentMethodsList">
                            <!-- Payment methods will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Withdrawal Requests Tab -->
            <div class="tab-pane fade" id="withdrawals" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 id="pendingWithdrawalsCount">0</h4>
                                <p class="mb-0">Pending Requests</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 id="approvedWithdrawalsCount">0</h4>
                                <p class="mb-0">Approved Today</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h4 id="rejectedWithdrawalsCount">0</h4>
                                <p class="mb-0">Rejected Today</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-hand-holding-usd fa-2x mb-2"></i>
                                <h4 id="totalWithdrawalsAmount">$0</h4>
                                <p class="mb-0">Total Amount</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Filters -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="withdrawalStatusFilter" class="form-label">Status</label>
                                        <select class="form-select" id="withdrawalStatusFilter"
                                            onchange="filterWithdrawals()">
                                            <option value="">All Statuses</option>
                                            <option value="pending" selected>Pending</option>
                                            <option value="approved">Approved</option>
                                            <option value="processing">Processing</option>
                                            <option value="completed">Completed</option>
                                            <option value="rejected">Rejected</option>
                                            <option value="failed">Failed</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="payoutMethodFilter" class="form-label">Payout Method</label>
                                        <select class="form-select" id="payoutMethodFilter"
                                            onchange="filterWithdrawals()">
                                            <option value="">All Methods</option>
                                            <option value="paypal">PayPal</option>
                                            <option value="stripe">Stripe</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                            <option value="payoneer">Payoneer</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="withdrawalSearchInput" class="form-label">Search User</label>
                                        <input type="text" class="form-control" id="withdrawalSearchInput"
                                            placeholder="Search by username or email" onkeyup="filterWithdrawals()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="loadWithdrawals()">
                                            <i class="fas fa-refresh"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdrawals List -->
                <div id="withdrawalsList">
                    <!-- Withdrawals will be loaded here -->
                </div>
            </div>

            <!-- Transaction History Tab -->
            <div class="tab-pane fade" id="transactions" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <h4 id="totalTransactionsCount">0</h4>
                                <p class="mb-0">Total Transactions</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-up fa-2x mb-2"></i>
                                <h4 id="depositsCount">0</h4>
                                <p class="mb-0">Deposits</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-arrow-down fa-2x mb-2"></i>
                                <h4 id="withdrawalsCount">0</h4>
                                <p class="mb-0">Withdrawals</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                                <h4 id="paymentsCount">0</h4>
                                <p class="mb-0">Payments</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction Filters -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="transactionTypeFilter" class="form-label">Transaction Type</label>
                                        <select class="form-select" id="transactionTypeFilter"
                                            onchange="filterTransactions()">
                                            <option value="">All Types</option>
                                            <option value="deposit">Deposit</option>
                                            <option value="withdrawal">Withdrawal</option>
                                            <option value="payment">Payment</option>
                                            <option value="refund">Refund</option>
                                            <option value="fee">Fee</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="transactionStatusFilter" class="form-label">Status</label>
                                        <select class="form-select" id="transactionStatusFilter"
                                            onchange="filterTransactions()">
                                            <option value="">All Statuses</option>
                                            <option value="pending">Pending</option>
                                            <option value="completed">Completed</option>
                                            <option value="failed">Failed</option>
                                            <option value="cancelled">Cancelled</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="transactionSearchInput" class="form-label">Search User</label>
                                        <input type="text" class="form-control" id="transactionSearchInput"
                                            placeholder="Search by username or email" onkeyup="filterTransactions()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="loadTransactions()">
                                            <i class="fas fa-refresh"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transactions List -->
                <div id="transactionsList">
                    <!-- Transactions will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Review Modal -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Review Document Verification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>User Information</h6>
                            <p><strong>Username:</strong> <span id="modalUsername"></span></p>
                            <p><strong>Email:</strong> <span id="modalEmail"></span></p>
                            <p><strong>Document Type:</strong> <span id="modalDocumentType"></span></p>
                            <p><strong>Submitted:</strong> <span id="modalSubmittedDate"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Document Preview</h6>
                            <div class="text-center">
                                <img id="modalDocumentPreview" class="document-preview" alt="Document Preview">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="reviewNotes" class="form-label">Review Notes</label>
                            <textarea class="form-control" id="reviewNotes" rows="3"
                                placeholder="Add notes about your decision..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-reject" onclick="reviewDocument('rejected')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                    <button type="button" class="btn btn-approve" onclick="reviewDocument('approved')">
                        <i class="fas fa-check"></i> Approve
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Admin Login</h5>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        <div id="loginError" class="alert alert-danger" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="login()">Login</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Withdrawal Review Modal -->
    <div class="modal fade" id="withdrawalModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Review Withdrawal Request</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>User Information</h6>
                            <p><strong>Username:</strong> <span id="withdrawalUsername"></span></p>
                            <p><strong>Email:</strong> <span id="withdrawalEmail"></span></p>
                            <p><strong>User Type:</strong> <span id="withdrawalUserType"></span></p>
                            <p><strong>Current Balance:</strong> <span id="withdrawalCurrentBalance"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Withdrawal Details</h6>
                            <p><strong>Amount:</strong> <span id="withdrawalAmount"></span></p>
                            <p><strong>Platform Fee:</strong> <span id="withdrawalFee"></span></p>
                            <p><strong>Payout Amount:</strong> <span id="withdrawalPayoutAmount"></span></p>
                            <p><strong>Requested:</strong> <span id="withdrawalDate"></span></p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>Payout Method</h6>
                            <p><strong>Type:</strong> <span id="withdrawalPayoutType"></span></p>
                            <p><strong>Details:</strong> <span id="withdrawalPayoutDetails"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Admin Processing</h6>
                            <div class="mb-3">
                                <label for="withdrawalNotes" class="form-label">Admin Notes</label>
                                <textarea class="form-control" id="withdrawalNotes" rows="3"
                                    placeholder="Add notes about your decision..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="externalTransactionId" class="form-label">External Transaction ID
                                    (Optional)</label>
                                <input type="text" class="form-control" id="externalTransactionId"
                                    placeholder="PayPal/Stripe/Bank transaction ID">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="processWithdrawal('rejected')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                    <button type="button" class="btn btn-warning" onclick="processWithdrawal('processing')">
                        <i class="fas fa-clock"></i> Mark as Processing
                    </button>
                    <button type="button" class="btn btn-success" onclick="processWithdrawal('approved')">
                        <i class="fas fa-check"></i> Approve & Process
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Details Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Payment Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Payment Information</h6>
                            <p><strong>Payment ID:</strong> <span id="paymentId"></span></p>
                            <p><strong>Amount:</strong> <span id="paymentAmount"></span></p>
                            <p><strong>Platform Fee:</strong> <span id="paymentPlatformFee"></span></p>
                            <p><strong>Seller Amount:</strong> <span id="paymentSellerAmount"></span></p>
                            <p><strong>Method:</strong> <span id="paymentMethod"></span></p>
                            <p><strong>Status:</strong> <span id="paymentStatus"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Auction & Users</h6>
                            <p><strong>Auction:</strong> <span id="paymentAuction"></span></p>
                            <p><strong>Buyer:</strong> <span id="paymentBuyer"></span></p>
                            <p><strong>Seller:</strong> <span id="paymentSeller"></span></p>
                            <p><strong>Created:</strong> <span id="paymentCreated"></span></p>
                            <p><strong>Completed:</strong> <span id="paymentCompleted"></span></p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>External Payment IDs</h6>
                            <p><strong>Stripe Payment Intent:</strong> <span id="paymentStripeId"></span></p>
                            <p><strong>PayPal Payment ID:</strong> <span id="paymentPaypalId"></span></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-warning" onclick="refundPayment()" id="refundButton">
                        <i class="fas fa-undo"></i> Process Refund
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Details Modal -->
    <div class="modal fade" id="transactionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Transaction Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Transaction Information</h6>
                            <p><strong>Transaction ID:</strong> <span id="transactionId"></span></p>
                            <p><strong>User:</strong> <span id="transactionUser"></span></p>
                            <p><strong>Type:</strong> <span id="transactionType"></span></p>
                            <p><strong>Amount:</strong> <span id="transactionAmount"></span></p>
                            <p><strong>Status:</strong> <span id="transactionStatus"></span></p>
                            <p style="display: none;"><strong>Payout Status:</strong> <span
                                    id="transactionPayoutStatus"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Additional Details</h6>
                            <p><strong>Description:</strong> <span id="transactionDescription"></span></p>
                            <p><strong>Related Auction:</strong> <span id="transactionAuction"></span></p>
                            <p><strong>External ID:</strong> <span id="transactionExternalId"></span></p>
                            <p><strong>Created:</strong> <span id="transactionCreated"></span></p>
                            <p><strong>Updated:</strong> <span id="transactionUpdated"></span></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="admin.js"></script>
</body>

</html>