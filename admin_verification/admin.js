// Admin Verification Interface JavaScript

// Configuration
const API_BASE_URL = 'http://localhost:8000/api';
let authToken = localStorage.getItem('adminToken');
let currentRequestId = null;
let allRequests = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function () {
    if (!authToken) {
        showLoginModal();
    } else {
        initializeApp();
    }
});

// Show login modal
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// Login function
async function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');

    if (!username || !password) {
        showError(errorDiv, 'Please enter both username and password');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/auth/login/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            // Check if user is admin
            if (data.user.user_type !== 'admin') {
                showError(errorDiv, 'Access denied. Admin privileges required.');
                return;
            }

            authToken = data.tokens.access;
            localStorage.setItem('adminToken', authToken);
            localStorage.setItem('adminUser', JSON.stringify(data.user));

            // Hide login modal and initialize app
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            loginModal.hide();
            initializeApp();
        } else {
            showError(errorDiv, data.message || 'Login failed');
        }
    } catch (error) {
        showError(errorDiv, 'Network error. Please try again.');
        console.error('Login error:', error);
    }
}

// Initialize the application
function initializeApp() {
    const adminUser = JSON.parse(localStorage.getItem('adminUser') || '{}');
    document.getElementById('adminName').textContent = adminUser.username || 'Admin';

    loadVerificationRequests();
    loadStatistics();
    loadDashboardStats();

    // Add tab click listeners
    document.getElementById('payment-methods-tab')?.addEventListener('click', function () {
        setTimeout(loadPaymentMethods, 100);
    });

    // Auto-refresh every 30 seconds
    setInterval(() => {
        loadVerificationRequests();
        loadStatistics();
    }, 30000);
}

// Load verification requests and group them by user
async function loadVerificationRequests() {
    showLoading(true);

    try {
        const response = await fetch(`${API_BASE_URL}/auth/documents/pending/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            const data = await response.json();
            // Handle both direct array and paginated response
            let requests;
            if (Array.isArray(data)) {
                requests = data;
            } else if (data.results && Array.isArray(data.results)) {
                requests = data.results;
            } else {
                console.error('Unexpected response format:', data);
                requests = [];
            }

            // Group requests by user
            const userGroups = groupRequestsByUser(requests);
            allRequests = userGroups;
            displayVerificationRequests(userGroups);
        } else if (response.status === 401) {
            handleAuthError();
        } else {
            showAlert('Error loading verification requests', 'danger');
        }
    } catch (error) {
        showAlert('Network error loading requests', 'danger');
        console.error('Load requests error:', error);
    } finally {
        showLoading(false);
    }
}

// Group individual document requests by user
function groupRequestsByUser(requests) {
    const userGroups = {};

    requests.forEach(request => {
        const userId = request.user;
        if (!userGroups[userId]) {
            userGroups[userId] = {
                user_id: userId,
                user_name: request.user_name,
                user_email: request.user_email,
                documents: {},
                submitted_at: request.created_at,
                document_count: 0
            };
        }

        // Add document to user group
        userGroups[userId].documents[request.document_type] = {
            id: request.id,
            document_type: request.document_type,
            document_type_display: request.document_type_display,
            document_file: request.document_file,
            status: request.status,
            status_display: request.status_display,
            created_at: request.created_at,
        };

        userGroups[userId].document_count++;

        // Update submission date to earliest
        if (request.created_at < userGroups[userId].submitted_at) {
            userGroups[userId].submitted_at = request.created_at;
        }
    });

    // Convert to array and add completion flags
    const result = Object.values(userGroups).map(group => {
        group.has_government_id = 'government_id' in group.documents;
        group.has_hunting_approval = 'hunting_approval' in group.documents;
        group.is_complete = group.has_government_id && group.has_hunting_approval;
        return group;
    });

    // Sort by submission date (newest first)
    result.sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at));

    return result;
}

// Display verification requests (grouped by user)
function displayVerificationRequests(userGroups) {
    const container = document.getElementById('verificationRequests');
    const noResults = document.getElementById('noResults');

    if (userGroups.length === 0) {
        container.innerHTML = '';
        noResults.style.display = 'block';
        return;
    }

    noResults.style.display = 'none';

    const html = userGroups.map(userGroup => {
        const govDoc = userGroup.documents.government_id;
        const huntDoc = userGroup.documents.hunting_approval;

        return `
        <div class="verification-card card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <h5 class="card-title mb-1">
                            <i class="fas fa-user"></i> ${userGroup.user_name}
                        </h5>
                        <p class="text-muted mb-1">${userGroup.user_email}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> ${formatDate(userGroup.submitted_at)}
                        </small>
                        <br>
                        <span class="badge bg-${userGroup.user_type === 'seller' ? 'primary' : 'secondary'} mt-1">
                            ${userGroup.user_type}
                        </span>
                    </div>

                    <div class="col-md-4">
                        <div class="row">
                            <div class="col-6">
                                <div class="document-preview-small text-center">
                                    <div class="document-type-icon government_id mb-2">
                                        <i class="fas fa-id-card"></i>
                                    </div>
                                    <small class="d-block">
                                        ${govDoc ? '✅ Government ID' : '❌ Missing'}
                                    </small>
                                    ${govDoc ? `<img src="${govDoc.document_file}" class="document-thumbnail mt-1" onclick="previewDocument('${govDoc.document_file}', 'Government ID')" style="width: 60px; height: 40px; object-fit: cover; cursor: pointer; border: 1px solid #ddd; border-radius: 4px;">` : ''}
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="document-preview-small text-center">
                                    <div class="document-type-icon hunting_approval mb-2">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <small class="d-block">
                                        ${huntDoc ? '✅ Hunting Approval' : '❌ Missing'}
                                    </small>
                                    ${huntDoc ? `<img src="${huntDoc.document_file}" class="document-thumbnail mt-1" onclick="previewDocument('${huntDoc.document_file}', 'Hunting Approval')" style="width: 60px; height: 40px; object-fit: cover; cursor: pointer; border: 1px solid #ddd; border-radius: 4px;">` : ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 text-center">
                        <div class="completion-status">
                            ${userGroup.is_complete ?
                '<span class="badge bg-success"><i class="fas fa-check-circle"></i> Complete</span>' :
                '<span class="badge bg-warning"><i class="fas fa-exclamation-triangle"></i> Incomplete</span>'
            }
                        </div>
                        <small class="text-muted d-block mt-1">
                            ${userGroup.document_count}/2 documents
                        </small>
                    </div>

                    <div class="col-md-2 text-end">
                        ${userGroup.is_complete ? `
                            <button class="btn btn-success btn-sm d-block mb-2" onclick="batchReview(${userGroup.user_id}, 'approve')">
                                <i class="fas fa-check"></i> Approve All
                            </button>
                            <button class="btn btn-danger btn-sm d-block" onclick="batchReview(${userGroup.user_id}, 'reject')">
                                <i class="fas fa-times"></i> Reject All
                            </button>
                        ` : `
                            <button class="btn btn-outline-secondary btn-sm" disabled>
                                <i class="fas fa-hourglass-half"></i> Incomplete
                            </button>
                        `}
                    </div>
                </div>
            </div>
        </div>
        `;
    }).join('');

    container.innerHTML = html;
}

// Get document icon
function getDocumentIcon(documentType) {
    switch (documentType) {
        case 'government_id':
            return 'fa-id-card';
        case 'hunting_approval':
            return 'fa-certificate';
        default:
            return 'fa-file';
    }
}

// Get status color
function getStatusColor(status) {
    switch (status) {
        case 'pending':
            return 'warning';
        case 'approved':
            return 'success';
        case 'rejected':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Preview document in modal
function previewDocument(documentUrl, documentType) {
    // Create or update preview modal
    let previewModal = document.getElementById('documentPreviewModal');
    if (!previewModal) {
        // Create modal if it doesn't exist
        previewModal = document.createElement('div');
        previewModal.className = 'modal fade';
        previewModal.id = 'documentPreviewModal';
        previewModal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Document Preview</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <h6 id="previewDocumentType"></h6>
                        <img id="previewImage" class="img-fluid" style="max-height: 70vh;">
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(previewModal);
    }

    // Update modal content
    document.getElementById('previewDocumentType').textContent = documentType;
    document.getElementById('previewImage').src = documentUrl;

    // Show modal
    const modal = new bootstrap.Modal(previewModal);
    modal.show();
}

// Batch review (approve/reject all documents for a user)
async function batchReview(userId, action) {
    const notes = prompt(`Enter review notes for ${action === 'approve' ? 'approval' : 'rejection'} (optional):`);
    if (notes === null) return; // User cancelled

    try {
        // Find the user group to get document IDs
        const userGroup = allRequests.find(group => group.user_id === userId);
        if (!userGroup) {
            showAlert('User group not found', 'danger');
            return;
        }

        const documentIds = Object.values(userGroup.documents).map(doc => doc.id);
        let successCount = 0;
        let errorCount = 0;

        // Review each document individually
        for (const docId of documentIds) {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/documents/review/${docId}/`, {
                    method: 'PATCH',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: action === 'approve' ? 'approved' : 'rejected',
                        review_notes: notes || ''
                    })
                });

                if (response.ok) {
                    successCount++;
                } else {
                    errorCount++;
                    console.error(`Failed to review document ${docId}:`, await response.text());
                }
            } catch (error) {
                errorCount++;
                console.error(`Error reviewing document ${docId}:`, error);
            }
        }

        // Show result message
        if (successCount > 0 && errorCount === 0) {
            showAlert(`Successfully ${action}d ${successCount} documents for ${userGroup.user_name}`, 'success');
        } else if (successCount > 0 && errorCount > 0) {
            showAlert(`${action}d ${successCount} documents, ${errorCount} failed for ${userGroup.user_name}`, 'warning');
        } else {
            showAlert(`Failed to ${action} documents for ${userGroup.user_name}`, 'danger');
        }

        // Reload requests and statistics
        loadVerificationRequests();
        loadStatistics();

    } catch (error) {
        showAlert('Network error during batch review', 'danger');
        console.error('Batch review error:', error);
    }
}

// Review document (approve/reject)
async function reviewDocument(status) {
    const notes = document.getElementById('reviewNotes').value;

    try {
        const response = await fetch(`${API_BASE_URL}/auth/documents/review/${currentRequestId}/`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: status,
                review_notes: notes
            })
        });

        if (response.ok) {
            showAlert(`Document ${status} successfully`, 'success');

            // Hide modal
            const reviewModal = bootstrap.Modal.getInstance(document.getElementById('reviewModal'));
            reviewModal.hide();

            // Reload requests
            loadVerificationRequests();
            loadStatistics();
        } else {
            showAlert(`Error ${status === 'approved' ? 'approving' : 'rejecting'} document`, 'danger');
        }
    } catch (error) {
        showAlert('Network error during review', 'danger');
        console.error('Review error:', error);
    }
}

// Load statistics
async function loadStatistics() {
    try {
        // This would typically be a separate endpoint, but for now we'll calculate from the requests
        const pendingResponse = await fetch(`${API_BASE_URL}/auth/documents/pending/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            }
        });

        if (pendingResponse.ok) {
            const pendingRequests = await pendingResponse.json();
            document.getElementById('pendingCount').textContent = pendingRequests.length;
        }

        // You would add more API calls here for other statistics
        // For now, we'll use placeholder values
        document.getElementById('approvedCount').textContent = '12';
        document.getElementById('rejectedCount').textContent = '3';
        document.getElementById('totalUsersCount').textContent = '156';

    } catch (error) {
        console.error('Statistics error:', error);
    }
}

// Filter requests
function filterRequests() {
    const statusFilter = document.getElementById('statusFilter').value;
    const documentTypeFilter = document.getElementById('documentTypeFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();

    let filteredRequests = allRequests.filter(request => {
        const matchesStatus = !statusFilter || request.status === statusFilter;
        const matchesDocumentType = !documentTypeFilter || request.document_type === documentTypeFilter;
        const matchesSearch = !searchInput ||
            request.user_name.toLowerCase().includes(searchInput) ||
            request.user_email.toLowerCase().includes(searchInput);

        return matchesStatus && matchesDocumentType && matchesSearch;
    });

    displayVerificationRequests(filteredRequests);
}

// Download document
function downloadDocument(documentUrl) {
    window.open(documentUrl, '_blank');
}

// Utility functions
function showLoading(show) {
    document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
}

function showAlert(message, type) {
    // Create and show bootstrap alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showError(element, message) {
    element.textContent = message;
    element.style.display = 'block';
}

function showSuccess(message) {
    // Create and show a success toast
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Add to body
    document.body.insertAdjacentHTML('beforeend', alertHtml);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert-success');
        if (alert) alert.remove();
    }, 5000);
}

function handleAuthError() {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    authToken = null;
    showLoginModal();
}

function logout() {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    location.reload();
}

// ===== PAYMENT MANAGEMENT FUNCTIONS =====

// Global variables for payment management
let allPayments = [];
let allWithdrawals = [];
let allTransactions = [];
let currentWithdrawalId = null;
let currentPaymentId = null;
let currentTransactionId = null;

// Tab change handler
document.addEventListener('DOMContentLoaded', function () {
    // Add tab change listeners
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function (e) {
            const targetTab = e.target.getAttribute('data-bs-target');

            switch (targetTab) {
                case '#verification':
                    loadVerificationRequests();
                    break;
                case '#payments':
                    loadPayments();
                    break;
                case '#withdrawals':
                    loadWithdrawals();
                    break;
                case '#transactions':
                    loadTransactions();
                    break;
            }
        });
    });
});

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        const response = await fetch(`${API_BASE_URL}/payments/admin/dashboard-stats/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();

            // Update withdrawal stats
            document.getElementById('pendingWithdrawalsCount').textContent = data.withdrawals.pending;
            document.getElementById('approvedWithdrawalsCount').textContent = data.withdrawals.approved_today;
            document.getElementById('rejectedWithdrawalsCount').textContent = data.withdrawals.rejected_today;
            document.getElementById('totalWithdrawalsAmount').textContent = `$${data.withdrawals.total_amount.toFixed(2)}`;

            // Update payment stats
            document.getElementById('totalPaymentsCount').textContent = data.payments.total;
            document.getElementById('completedPaymentsCount').textContent = data.payments.completed;
            document.getElementById('pendingPaymentsCount').textContent = data.payments.pending;
            document.getElementById('totalRevenueCount').textContent = `$${data.payments.total_revenue.toFixed(2)}`;

            // Update transaction stats
            document.getElementById('totalTransactionsCount').textContent = data.transactions.total;
            document.getElementById('depositsCount').textContent = data.transactions.deposits;
            document.getElementById('withdrawalsCount').textContent = data.transactions.withdrawals;
            document.getElementById('paymentsCount').textContent = data.transactions.payments;
        }
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

// Load payments
async function loadPayments() {
    try {
        showLoading('paymentsList', 'Loading payments...');

        const response = await fetch(`${API_BASE_URL}/payments/admin/payments/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            allPayments = data.payments;
            displayPayments(allPayments);
        } else {
            showError(document.getElementById('paymentsList'), 'Failed to load payments');
        }
    } catch (error) {
        console.error('Error loading payments:', error);
        showError(document.getElementById('paymentsList'), 'Error loading payments');
    }
}

// Display payments
function displayPayments(payments) {
    const container = document.getElementById('paymentsList');

    if (payments.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No payments found</h5>
            </div>
        `;
        return;
    }

    const paymentsHtml = payments.map(payment => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="card-title">${payment.auction.title}</h6>
                        <p class="card-text">
                            <strong>Buyer:</strong> ${payment.buyer.username} (${payment.buyer.email})<br>
                            <strong>Seller:</strong> ${payment.seller.username} (${payment.seller.email})<br>
                            <strong>Amount:</strong> $${payment.amount.toFixed(2)}
                            <small class="text-muted">(Fee: $${payment.platform_fee.toFixed(2)}, Seller: $${payment.seller_amount.toFixed(2)})</small><br>
                            <strong>Method:</strong> ${payment.payment_method.charAt(0).toUpperCase() + payment.payment_method.slice(1)}<br>
                            <strong>Created:</strong> ${new Date(payment.created_at).toLocaleString()}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge ${getPaymentStatusBadgeClass(payment.payment_status)} mb-2">${payment.payment_status.charAt(0).toUpperCase() + payment.payment_status.slice(1)}</span><br>
                        <button class="btn btn-sm btn-outline-primary" onclick="showPaymentDetails('${payment.id}')">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = paymentsHtml;
}

// Get payment status badge class
function getPaymentStatusBadgeClass(status) {
    switch (status) {
        case 'completed': return 'bg-success';
        case 'pending': return 'bg-warning';
        case 'failed': return 'bg-danger';
        case 'refunded': return 'bg-info';
        default: return 'bg-secondary';
    }
}

// Show payment details
function showPaymentDetails(paymentId) {
    const payment = allPayments.find(p => p.id === paymentId);
    if (!payment) return;

    currentPaymentId = paymentId;

    // Populate modal
    document.getElementById('paymentId').textContent = payment.id;
    document.getElementById('paymentAmount').textContent = `$${payment.amount.toFixed(2)}`;
    document.getElementById('paymentPlatformFee').textContent = `$${payment.platform_fee.toFixed(2)}`;
    document.getElementById('paymentSellerAmount').textContent = `$${payment.seller_amount.toFixed(2)}`;
    document.getElementById('paymentMethod').textContent = payment.payment_method.charAt(0).toUpperCase() + payment.payment_method.slice(1);
    document.getElementById('paymentStatus').textContent = payment.payment_status.charAt(0).toUpperCase() + payment.payment_status.slice(1);
    document.getElementById('paymentAuction').textContent = payment.auction.title;
    document.getElementById('paymentBuyer').textContent = `${payment.buyer.username} (${payment.buyer.email})`;
    document.getElementById('paymentSeller').textContent = `${payment.seller.username} (${payment.seller.email})`;
    document.getElementById('paymentCreated').textContent = new Date(payment.created_at).toLocaleString();
    document.getElementById('paymentCompleted').textContent = payment.completed_at ? new Date(payment.completed_at).toLocaleString() : 'Not completed';
    document.getElementById('paymentStripeId').textContent = payment.stripe_payment_intent_id || 'N/A';
    document.getElementById('paymentPaypalId').textContent = payment.paypal_payment_id || 'N/A';

    // Show/hide refund button
    const refundButton = document.getElementById('refundButton');
    refundButton.style.display = payment.payment_status === 'completed' ? 'inline-block' : 'none';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

// Load withdrawals
async function loadWithdrawals() {
    try {
        showLoading('withdrawalsList', 'Loading withdrawal requests...');

        const response = await fetch(`${API_BASE_URL}/payments/admin/withdrawals/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            allWithdrawals = data.withdrawals;
            displayWithdrawals(allWithdrawals);
        } else {
            showError(document.getElementById('withdrawalsList'), 'Failed to load withdrawals');
        }
    } catch (error) {
        console.error('Error loading withdrawals:', error);
        showError(document.getElementById('withdrawalsList'), 'Error loading withdrawals');
    }
}

// Display withdrawals
function displayWithdrawals(withdrawals) {
    const container = document.getElementById('withdrawalsList');

    if (withdrawals.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No withdrawal requests found</h5>
            </div>
        `;
        return;
    }

    const withdrawalsHtml = withdrawals.map(withdrawal => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="card-title">${withdrawal.user.username} - $${withdrawal.amount.toFixed(2)}</h6>
                        <p class="card-text">
                            <strong>User:</strong> ${withdrawal.user.username} (${withdrawal.user.email}) - ${withdrawal.user.user_type}<br>
                            <strong>Amount:</strong> $${withdrawal.amount.toFixed(2)}
                            <small class="text-muted">(Fee: $${withdrawal.platform_fee.toFixed(2)}, Payout: $${withdrawal.payout_amount.toFixed(2)})</small><br>
                            <strong>Payout Method:</strong> ${withdrawal.payout_method.type.charAt(0).toUpperCase() + withdrawal.payout_method.type.slice(1)} - ${withdrawal.payout_method.details}<br>
                            <strong>Current Balance:</strong> $${withdrawal.user.wallet_balance.toFixed(2)}<br>
                            <strong>Requested:</strong> ${new Date(withdrawal.created_at).toLocaleString()}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge ${getWithdrawalStatusBadgeClass(withdrawal.status)} mb-2">${withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}</span><br>
                        ${withdrawal.status === 'pending' ? `
                            <button class="btn btn-sm btn-success me-1" onclick="showWithdrawalDetails('${withdrawal.id}')">
                                <i class="fas fa-check"></i> Review
                            </button>
                        ` : `
                            <button class="btn btn-sm btn-outline-primary" onclick="showWithdrawalDetails('${withdrawal.id}')">
                                <i class="fas fa-eye"></i> View Details
                            </button>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = withdrawalsHtml;
}

// Get withdrawal status badge class
function getWithdrawalStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'bg-warning';
        case 'approved': return 'bg-success';
        case 'processing': return 'bg-info';
        case 'completed': return 'bg-success';
        case 'rejected': return 'bg-danger';
        case 'failed': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

// Show withdrawal details
function showWithdrawalDetails(withdrawalId) {
    const withdrawal = allWithdrawals.find(w => w.id === withdrawalId);
    if (!withdrawal) return;

    currentWithdrawalId = withdrawalId;

    // Populate modal
    document.getElementById('withdrawalUsername').textContent = withdrawal.user.username;
    document.getElementById('withdrawalEmail').textContent = withdrawal.user.email;
    document.getElementById('withdrawalUserType').textContent = withdrawal.user.user_type.charAt(0).toUpperCase() + withdrawal.user.user_type.slice(1);
    document.getElementById('withdrawalCurrentBalance').textContent = `$${withdrawal.user.wallet_balance.toFixed(2)}`;
    document.getElementById('withdrawalAmount').textContent = `$${withdrawal.amount.toFixed(2)}`;
    document.getElementById('withdrawalFee').textContent = `$${withdrawal.platform_fee.toFixed(2)}`;
    document.getElementById('withdrawalPayoutAmount').textContent = `$${withdrawal.payout_amount.toFixed(2)}`;
    document.getElementById('withdrawalDate').textContent = new Date(withdrawal.created_at).toLocaleString();
    document.getElementById('withdrawalPayoutType').textContent = withdrawal.payout_method.type.charAt(0).toUpperCase() + withdrawal.payout_method.type.slice(1);
    document.getElementById('withdrawalPayoutDetails').textContent = withdrawal.payout_method.details;

    // Pre-fill existing notes and transaction ID
    document.getElementById('withdrawalNotes').value = withdrawal.admin_notes || '';
    document.getElementById('externalTransactionId').value = withdrawal.external_transaction_id || '';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('withdrawalModal'));
    modal.show();
}

// Process withdrawal
async function processWithdrawal(action) {
    if (!currentWithdrawalId) return;

    const adminNotes = document.getElementById('withdrawalNotes').value;
    const externalTransactionId = document.getElementById('externalTransactionId').value;

    try {
        const response = await fetch(`${API_BASE_URL}/payments/admin/withdrawals/${currentWithdrawalId}/process/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: action,
                admin_notes: adminNotes,
                external_transaction_id: externalTransactionId
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('withdrawalModal'));
            modal.hide();

            // Show success message
            showSuccessMessage(`Withdrawal request ${action} successfully`);

            // Reload data
            loadWithdrawals();
            loadDashboardStats();
        } else {
            showErrorMessage(data.error || `Failed to ${action} withdrawal request`);
        }
    } catch (error) {
        console.error('Error processing withdrawal:', error);
        showErrorMessage('Error processing withdrawal request');
    }
}

// Load transactions
async function loadTransactions() {
    try {
        showLoading('transactionsList', 'Loading transactions...');

        const response = await fetch(`${API_BASE_URL}/payments/admin/transactions/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            allTransactions = data.transactions;
            displayTransactions(allTransactions);
        } else {
            showError(document.getElementById('transactionsList'), 'Failed to load transactions');
        }
    } catch (error) {
        console.error('Error loading transactions:', error);
        showError(document.getElementById('transactionsList'), 'Error loading transactions');
    }
}

// Display transactions
function displayTransactions(transactions) {
    const container = document.getElementById('transactionsList');

    if (transactions.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
            </div>
        `;
        return;
    }

    const transactionsHtml = transactions.map(transaction => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="card-title">${transaction.user.username} - ${transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1)}</h6>
                        <p class="card-text">
                            <strong>User:</strong> ${transaction.user.username} (${transaction.user.email}) - ${transaction.user.user_type}<br>
                            <strong>Amount:</strong> <span class="${transaction.amount >= 0 ? 'text-success' : 'text-danger'}">${transaction.amount >= 0 ? '+' : ''}$${transaction.amount.toFixed(2)}</span><br>
                            <strong>Description:</strong> ${transaction.description}<br>
                            ${transaction.payout_status ? `<strong>Payout Status:</strong> <span class="badge ${getPayoutStatusBadgeClass(transaction.payout_status)}">${transaction.payout_status.charAt(0).toUpperCase() + transaction.payout_status.slice(1)}</span><br>` : ''}
                            ${transaction.auction ? `<strong>Related Auction:</strong> ${transaction.auction.title}<br>` : ''}
                            <strong>Created:</strong> ${new Date(transaction.created_at).toLocaleString()}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge ${getTransactionStatusBadgeClass(transaction.status)} mb-2">${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}</span><br>
                        <span class="badge ${getTransactionTypeBadgeClass(transaction.transaction_type)} mb-2">${transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1)}</span><br>
                        <button class="btn btn-sm btn-outline-primary" onclick="showTransactionDetails('${transaction.id}')">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = transactionsHtml;
}

// Get transaction status badge class
function getTransactionStatusBadgeClass(status) {
    switch (status) {
        case 'completed': return 'bg-success';
        case 'pending': return 'bg-warning';
        case 'failed': return 'bg-danger';
        case 'cancelled': return 'bg-secondary';
        default: return 'bg-secondary';
    }
}

// Get transaction type badge class
function getTransactionTypeBadgeClass(type) {
    switch (type) {
        case 'deposit': return 'bg-success';
        case 'withdrawal': return 'bg-danger';
        case 'payment': return 'bg-primary';
        case 'refund': return 'bg-info';
        case 'fee': return 'bg-warning';
        default: return 'bg-secondary';
    }
}

// Get payout status badge class
function getPayoutStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'bg-warning';
        case 'processing': return 'bg-info';
        case 'approved': return 'bg-success';
        case 'completed': return 'bg-success';
        case 'rejected': return 'bg-danger';
        case 'failed': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

// Show transaction details
function showTransactionDetails(transactionId) {
    const transaction = allTransactions.find(t => t.id === transactionId);
    if (!transaction) return;

    currentTransactionId = transactionId;

    // Populate modal
    document.getElementById('transactionId').textContent = transaction.id;
    document.getElementById('transactionUser').textContent = `${transaction.user.username} (${transaction.user.email})`;
    document.getElementById('transactionType').textContent = transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1);
    document.getElementById('transactionAmount').textContent = `${transaction.amount >= 0 ? '+' : ''}$${transaction.amount.toFixed(2)}`;
    document.getElementById('transactionStatus').textContent = transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1);
    document.getElementById('transactionDescription').textContent = transaction.description;
    document.getElementById('transactionAuction').textContent = transaction.auction ? transaction.auction.title : 'N/A';
    document.getElementById('transactionExternalId').textContent = transaction.external_transaction_id || 'N/A';
    document.getElementById('transactionCreated').textContent = new Date(transaction.created_at).toLocaleString();
    document.getElementById('transactionUpdated').textContent = new Date(transaction.updated_at).toLocaleString();

    // Show payout status if it exists (for withdrawal transactions)
    const payoutStatusElement = document.getElementById('transactionPayoutStatus');
    if (transaction.payout_status) {
        payoutStatusElement.textContent = transaction.payout_status.charAt(0).toUpperCase() + transaction.payout_status.slice(1);
        payoutStatusElement.parentElement.style.display = 'block';
    } else {
        payoutStatusElement.parentElement.style.display = 'none';
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('transactionModal'));
    modal.show();
}

// Filter functions
function filterPayments() {
    const statusFilter = document.getElementById('paymentStatusFilter').value;
    const methodFilter = document.getElementById('paymentMethodFilter').value;
    const searchFilter = document.getElementById('paymentSearchInput').value.toLowerCase();

    const filtered = allPayments.filter(payment => {
        const matchesStatus = !statusFilter || payment.payment_status === statusFilter;
        const matchesMethod = !methodFilter || payment.payment_method === methodFilter;
        const matchesSearch = !searchFilter ||
            payment.auction.title.toLowerCase().includes(searchFilter) ||
            payment.buyer.username.toLowerCase().includes(searchFilter) ||
            payment.seller.username.toLowerCase().includes(searchFilter);

        return matchesStatus && matchesMethod && matchesSearch;
    });

    displayPayments(filtered);
}

function filterWithdrawals() {
    const statusFilter = document.getElementById('withdrawalStatusFilter').value;
    const methodFilter = document.getElementById('payoutMethodFilter').value;
    const searchFilter = document.getElementById('withdrawalSearchInput').value.toLowerCase();

    const filtered = allWithdrawals.filter(withdrawal => {
        const matchesStatus = !statusFilter || withdrawal.status === statusFilter;
        const matchesMethod = !methodFilter || withdrawal.payout_method.type === methodFilter;
        const matchesSearch = !searchFilter ||
            withdrawal.user.username.toLowerCase().includes(searchFilter) ||
            withdrawal.user.email.toLowerCase().includes(searchFilter);

        return matchesStatus && matchesMethod && matchesSearch;
    });

    displayWithdrawals(filtered);
}

function filterTransactions() {
    const typeFilter = document.getElementById('transactionTypeFilter').value;
    const statusFilter = document.getElementById('transactionStatusFilter').value;
    const searchFilter = document.getElementById('transactionSearchInput').value.toLowerCase();

    const filtered = allTransactions.filter(transaction => {
        const matchesType = !typeFilter || transaction.transaction_type === typeFilter;
        const matchesStatus = !statusFilter || transaction.status === statusFilter;
        const matchesSearch = !searchFilter ||
            transaction.user.username.toLowerCase().includes(searchFilter) ||
            transaction.user.email.toLowerCase().includes(searchFilter);

        return matchesType && matchesStatus && matchesSearch;
    });

    displayTransactions(filtered);
}

// Utility functions
function showLoading(containerId, message) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.warn(`Container with ID '${containerId}' not found`);
        return;
    }
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">${message}</p>
        </div>
    `;
}

function showSuccessMessage(message) {
    // Create and show a toast or alert
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Add to top of container
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert-success');
        if (alert) alert.remove();
    }, 5000);
}

function showErrorMessage(message) {
    // Create and show a toast or alert
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Add to top of container
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert-danger');
        if (alert) alert.remove();
    }, 5000);
}

// Dashboard stats are now loaded in initializeApp()

// Payment Methods Management
async function loadPaymentMethods() {
    showPaymentMethodsLoading(true);

    try {
        const statusFilter = document.getElementById('paymentMethodStatusFilter').value;
        const typeFilter = document.getElementById('paymentMethodTypeFilter').value;
        const search = document.getElementById('paymentMethodSearch').value;

        let url = `${API_BASE_URL}/payments/admin/payment-methods/`;
        const params = new URLSearchParams();

        if (statusFilter) params.append('status', statusFilter);
        if (typeFilter) params.append('type', typeFilter);
        if (search) params.append('search', search);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (response.ok) {
            displayPaymentMethods(data.payment_methods);
            updatePaymentMethodsStats(data);
        } else {
            showError(document.getElementById('paymentMethodsList'), data.error || 'Failed to load payment methods');
        }
    } catch (error) {
        showError(document.getElementById('paymentMethodsList'), 'Network error loading payment methods');
        console.error('Error loading payment methods:', error);
    } finally {
        showPaymentMethodsLoading(false);
    }
}

function showPaymentMethodsLoading(show) {
    const element = document.getElementById('paymentMethodsLoading');
    if (element) {
        element.style.display = show ? 'block' : 'none';
    }
}

function updatePaymentMethodsStats(data) {
    const pendingEl = document.getElementById('pendingPaymentMethodsCount');
    const verifiedEl = document.getElementById('verifiedPaymentMethodsCount');
    const rejectedEl = document.getElementById('rejectedPaymentMethodsCount');
    const totalEl = document.getElementById('totalPaymentMethodsCount');

    if (pendingEl) pendingEl.textContent = data.pending_count || 0;
    if (verifiedEl) verifiedEl.textContent = data.verified_count || 0;
    if (rejectedEl) rejectedEl.textContent = data.rejected_count || 0;
    if (totalEl) totalEl.textContent = data.total_count || 0;
}

function displayPaymentMethods(paymentMethods) {
    const container = document.getElementById('paymentMethodsList');
    if (!container) {
        console.warn('Payment methods list container not found');
        return;
    }

    if (!paymentMethods || paymentMethods.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No payment methods found</h5>
                <p class="text-muted">No payment methods match your current filters.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = paymentMethods.map(method => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="text-center">
                            <i class="fas ${getPaymentMethodIcon(method.payout_type)} fa-2x text-primary mb-2"></i>
                            <h6 class="mb-0">${getPaymentMethodTitle(method.payout_type)}</h6>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1">${method.user.username}</h6>
                        <small class="text-muted">${method.user.email}</small>
                        <br>
                        <small class="text-muted">User Type: ${method.user.user_type}</small>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1">Payment Details</h6>
                        <small class="text-muted">${getPaymentMethodDetails(method)}</small>
                        <br>
                        <small class="text-muted">Added: ${new Date(method.created_at).toLocaleDateString()}</small>
                    </div>
                    <div class="col-md-2">
                        <span class="badge ${getStatusBadgeClass(method.verification_status)} fs-6">
                            ${method.verification_status.toUpperCase()}
                        </span>
                        ${method.is_default ? '<br><span class="badge bg-info mt-1">Default</span>' : ''}
                    </div>
                    <div class="col-md-2">
                        ${method.verification_status === 'pending' ? `
                            <button class="btn btn-success btn-sm mb-1 w-100" onclick="reviewPaymentMethod('${method.id}', 'approve')">
                                <i class="fas fa-check"></i> Approve
                            </button>
                            <button class="btn btn-danger btn-sm w-100" onclick="reviewPaymentMethod('${method.id}', 'reject')">
                                <i class="fas fa-times"></i> Reject
                            </button>
                        ` : `
                            <button class="btn btn-outline-secondary btn-sm w-100" onclick="viewPaymentMethodDetails('${method.id}')">
                                <i class="fas fa-eye"></i> View Details
                            </button>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function getPaymentMethodIcon(type) {
    switch (type) {
        case 'paypal': return 'fa-paypal';
        case 'stripe': return 'fa-credit-card';
        case 'bank_transfer': return 'fa-university';
        case 'payoneer': return 'fa-wallet';
        default: return 'fa-credit-card';
    }
}

function getPaymentMethodTitle(type) {
    switch (type) {
        case 'paypal': return 'PayPal';
        case 'stripe': return 'Stripe';
        case 'bank_transfer': return 'Bank Transfer';
        case 'payoneer': return 'Payoneer';
        default: return type.toUpperCase();
    }
}

function getPaymentMethodDetails(method) {
    switch (method.payout_type) {
        case 'paypal':
            return method.paypal_email || 'No email provided';
        case 'stripe':
            return `Account: ${method.stripe_account_id || 'Not set'}`;
        case 'bank_transfer':
            return `${method.bank_name || 'Bank'} - ${method.account_number ? '****' + method.account_number.slice(-4) : 'No account'}`;
        case 'payoneer':
            return method.payoneer_email || 'No email provided';
        default:
            return 'Payment method details';
    }
}

function getStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'bg-warning';
        case 'verified': return 'bg-success';
        case 'rejected': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

async function reviewPaymentMethod(methodId, action) {
    const notes = prompt(`Enter review notes for this ${action} action (optional):`);

    try {
        const response = await fetch(`${API_BASE_URL}/payments/admin/payment-methods/${methodId}/review/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: action,
                notes: notes || ''
            })
        });

        const data = await response.json();

        if (response.ok) {
            showSuccess(`Payment method ${action}d successfully!`);
            loadPaymentMethods(); // Reload the list
        } else {
            showErrorMessage(data.error || `Failed to ${action} payment method`);
        }
    } catch (error) {
        showErrorMessage(`Network error: ${error.message}`);
        console.error(`Error ${action}ing payment method:`, error);
    }
}

function viewPaymentMethodDetails(methodId) {
    // This could open a modal with detailed information
    alert('Payment method details view - to be implemented');
}

// Add event listeners for payment method filters
document.addEventListener('DOMContentLoaded', function () {
    const statusFilter = document.getElementById('paymentMethodStatusFilter');
    const typeFilter = document.getElementById('paymentMethodTypeFilter');
    const searchInput = document.getElementById('paymentMethodSearch');

    if (statusFilter) statusFilter.addEventListener('change', loadPaymentMethods);
    if (typeFilter) typeFilter.addEventListener('change', loadPaymentMethods);
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function () {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(loadPaymentMethods, 500);
        });
    }
});
