# 🚀 Fish Auction App - Quick Start

Get your Fish Auction app running with WebSockets and Stripe webhooks in minutes!

## ⚡ One-Command Setup

### For macOS (Recommended):
```bash
./quick_start_macos.sh
```

### For Linux:
```bash
./start_with_webhooks.sh
```

### For Windows:
```bash
start_with_webhooks.bat
```

### What This Does:
- ✅ Checks all requirements (Python, Redis, ngrok)
- ✅ Starts Django backend server
- ✅ Starts Celery workers for background tasks
- ✅ Starts Redis for WebSocket support
- ✅ Creates ngrok tunnel for webhooks
- ✅ Displays Stripe webhook setup instructions
- ✅ Provides all necessary URLs and logs

## 📋 Prerequisites

### Install These First:

1. **Python 3.8+**: https://python.org/downloads/
2. **Redis**: 
   - macOS: `brew install redis`
   - Ubuntu: `sudo apt install redis-server`
   - Windows: Download from GitHub releases
3. **ngrok** (FREE): https://ngrok.com/download
   - Or: `npm install -g ngrok`
4. **Flutter SDK**: https://flutter.dev/docs/get-started/install

## 🎯 After Running the Script

### 1. Setup Stripe Webhooks (FREE)
The script will show you:
```
📍 Your webhook URL: https://abc123.ngrok.io/api/payments/stripe/webhook/

Steps to complete setup:
1. Go to Stripe Dashboard: https://dashboard.stripe.com/
2. Navigate to: Developers → Webhooks
3. Click 'Add endpoint'
4. Enter the webhook URL shown above
5. Select events: payment_intent.succeeded, payment_intent.payment_failed
6. Copy the signing secret to your .env file
```

### 2. Start Flutter App
```bash
cd fish_auction_app
flutter pub get
flutter run -d web-server --web-port 8080
```

### 3. Test Everything
- **Backend**: http://localhost:8000
- **Flutter App**: http://localhost:8080
- **ngrok Dashboard**: http://localhost:4040

## 🧪 Test Real-time Features

1. Open the app in two browser tabs
2. Login with different accounts
3. Create an auction
4. Start bidding from both tabs
5. Watch real-time updates! 🎉

## 🔧 Manual Setup (Alternative)

If you prefer manual setup, see the complete guide: [COMPLETE_SETUP_GUIDE.md](COMPLETE_SETUP_GUIDE.md)

## 🆘 Troubleshooting

### Redis Not Running
```bash
# macOS
brew services start redis

# Linux
sudo systemctl start redis-server

# Test
redis-cli ping  # Should return PONG
```

### ngrok Not Found
```bash
# Install ngrok
npm install -g ngrok
# Or download from: https://ngrok.com/download
```

### Port Already in Use
```bash
# Kill processes on port 8000
# macOS/Linux:
lsof -ti:8000 | xargs kill -9

# Windows:
netstat -ano | findstr :8000
taskkill /PID <PID> /F
```

### Django Server Won't Start
```bash
# Check Python and dependencies
python --version
pip install -r requirements.txt
python manage.py check
```

## 📱 App Features to Test

### Real-time Bidding
- Multiple users can bid simultaneously
- Instant bid updates across all connected clients
- Auto-bidding with customizable limits

### Payment System
- Stripe integration with test cards
- Wallet top-up functionality
- Secure payment processing

### WebSocket Features
- Live auction updates
- Real-time notifications
- Delivery tracking
- Chat-style bidding history

## 🎉 You're Ready!

Your Fish Auction app now supports:
- ✅ Real-time bidding with WebSockets
- ✅ Stripe payments with webhook integration
- ✅ Multi-language support (English/Arabic)
- ✅ Complete auction lifecycle management
- ✅ Mobile and web compatibility

Happy coding! 🐟💻
