#!/usr/bin/env python3
"""
Test script to simulate frontend auction close and verify notifications
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.test import Client
from auctions.models import Auction
from accounts.models import User
from notifications.models import Notification
import json

def test_auction_close_notifications():
    """Test that closing auction sends proper notifications"""
    
    print("🧪 Testing Auction Close Notifications")
    print("=" * 50)
    
    try:
        # Get test data
        auction = Auction.objects.get(title__icontains='samakat kos ibrahim')
        seller = auction.seller
        buyer = User.objects.get(username='rabie4')
        
        print(f"🎣 Auction: {auction.title}")
        print(f"👤 Seller: {seller.username}")
        print(f"👤 Buyer: {buyer.username}")
        print(f"💰 Current price: ${auction.current_price}")
        print(f"📊 Status: {auction.status}")
        print()
        
        # Count existing notifications
        initial_notifications = Notification.objects.filter(recipient=buyer).count()
        print(f"📱 Initial notifications for buyer: {initial_notifications}")
        
        # Create test client and login as seller
        client = Client()
        login_success = client.login(username=seller.username, password='97152897Aa')
        
        if not login_success:
            print("❌ Failed to login as seller")
            return False
        
        print("✅ Seller logged in successfully")
        
        # Call the close auction API
        url = f'/api/auctions/{auction.id}/close-early/'
        print(f"🔄 Calling API: POST {url}")
        
        response = client.post(url, content_type='application/json')
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Auction closed successfully!")
            
            response_data = response.json()
            print(f"📄 Response message: {response_data.get('message', 'N/A')}")
            
            # Check if auction status changed
            auction.refresh_from_db()
            print(f"📊 New auction status: {auction.status}")
            print(f"🏆 Winner: {auction.winner.username if auction.winner else 'None'}")
            
            # Check notifications
            new_notifications = Notification.objects.filter(recipient=buyer).count()
            notifications_added = new_notifications - initial_notifications
            
            print(f"📱 New notifications count: {new_notifications}")
            print(f"📨 Notifications added: {notifications_added}")
            
            # Show recent notifications
            recent_notifications = Notification.objects.filter(
                recipient=buyer
            ).order_by('-created_at')[:3]
            
            print("\n📱 Recent notifications:")
            for notif in recent_notifications:
                print(f"   {notif.created_at.strftime('%H:%M:%S')}: {notif.title} ({notif.channel}) - {notif.status}")
            
            # Check for auction_won notifications specifically
            auction_won_notifications = Notification.objects.filter(
                recipient=buyer,
                template__notification_type='auction_won'
            ).order_by('-created_at')[:2]
            
            print(f"\n🏆 Auction won notifications: {auction_won_notifications.count()}")
            for notif in auction_won_notifications:
                print(f"   {notif.channel}: {notif.status} - {notif.title}")
                if notif.channel == 'whatsapp':
                    print(f"      WhatsApp message: {notif.message[:80]}...")
            
            if notifications_added > 0:
                print("\n🎉 SUCCESS: Notifications were sent!")
                return True
            else:
                print("\n❌ ISSUE: No new notifications were created")
                return False
                
        else:
            print(f"❌ API call failed!")
            print(f"Response: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_auction_close_notifications()
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
