from rest_framework import generics, permissions, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .models import (
    ServiceCategory, MarketplaceListing, MarketplaceOrder,
    MarketplaceReview, MarketplaceWishlist
)
from .serializers import (
    ServiceCategorySerializer, MarketplaceListingSerializer,
    MarketplaceListingCreateSerializer, MarketplaceOrderSerializer,
    MarketplaceReviewSerializer
)


class ServiceCategoryListView(generics.ListAPIView):
    """List all service categories"""

    queryset = ServiceCategory.objects.filter(is_active=True)
    serializer_class = ServiceCategorySerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="List service categories",
        description="Get all active service categories for marketplace"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class MarketplaceListingListView(generics.ListAPIView):
    """List marketplace listings with filtering"""

    queryset = MarketplaceListing.objects.filter(status='active')
    serializer_class = MarketplaceListingSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['listing_type', 'category', 'fish_type']
    search_fields = ['title', 'description', 'fish_type']
    ordering_fields = ['created_at', 'price', 'views_count']
    ordering = ['-created_at']

    @extend_schema(
        summary="List marketplace listings",
        description="Get all active marketplace listings with filtering options",
        parameters=[
            OpenApiParameter('listing_type', str, description='Filter by listing type'),
            OpenApiParameter('category', int, description='Filter by category ID'),
            OpenApiParameter('fish_type', str, description='Filter by fish type'),
            OpenApiParameter('search', str, description='Search in title and description'),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class MarketplaceListingDetailView(generics.RetrieveAPIView):
    """Get marketplace listing details"""

    queryset = MarketplaceListing.objects.filter(status='active')
    serializer_class = MarketplaceListingSerializer
    permission_classes = [permissions.AllowAny]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment view count
        instance.views_count += 1
        instance.save(update_fields=['views_count'])
        return super().retrieve(request, *args, **kwargs)

    @extend_schema(
        summary="Get marketplace listing details",
        description="Get detailed information about a marketplace listing"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class MarketplaceListingCreateView(generics.CreateAPIView):
    """Create new marketplace listing"""

    queryset = MarketplaceListing.objects.all()
    serializer_class = MarketplaceListingCreateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(seller=self.request.user)

    @extend_schema(
        summary="Create marketplace listing",
        description="Create a new marketplace listing"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class MyMarketplaceListingsView(generics.ListAPIView):
    """List user's marketplace listings"""

    serializer_class = MarketplaceListingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return MarketplaceListing.objects.filter(seller=self.request.user)

    @extend_schema(
        summary="Get my marketplace listings",
        description="Get all marketplace listings created by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class MarketplaceOrderCreateView(generics.CreateAPIView):
    """Create marketplace order"""

    queryset = MarketplaceOrder.objects.all()
    serializer_class = MarketplaceOrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        listing = serializer.validated_data['listing']
        serializer.save(
            buyer=self.request.user,
            seller=listing.seller,
            unit_price=listing.price
        )

    @extend_schema(
        summary="Create marketplace order",
        description="Place an order for a marketplace listing"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class MyMarketplaceOrdersView(generics.ListAPIView):
    """List user's marketplace orders"""

    serializer_class = MarketplaceOrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return MarketplaceOrder.objects.filter(buyer=self.request.user)

    @extend_schema(
        summary="Get my marketplace orders",
        description="Get all marketplace orders placed by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
