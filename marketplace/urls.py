from django.urls import path
from .views import (
    ServiceCategoryListView, MarketplaceListingListView, MarketplaceListingDetailView,
    MarketplaceListingCreateView, MyMarketplaceListingsView, MarketplaceOrderCreateView,
    MyMarketplaceOrdersView
)

app_name = 'marketplace'

urlpatterns = [
    # Categories
    path('categories/', ServiceCategoryListView.as_view(), name='service_categories'),

    # Listings
    path('listings/', MarketplaceListingListView.as_view(), name='listing_list'),
    path('listings/create/', MarketplaceListingCreateView.as_view(), name='listing_create'),
    path('listings/<uuid:pk>/', MarketplaceListingDetailView.as_view(), name='listing_detail'),
    path('my-listings/', MyMarketplaceListingsView.as_view(), name='my_listings'),

    # Orders
    path('orders/create/', MarketplaceOrderCreateView.as_view(), name='order_create'),
    path('my-orders/', MyMarketplaceOrdersView.as_view(), name='my_orders'),
]
