from rest_framework import serializers
from .models import (
    ServiceCategory, MarketplaceListing, ListingImage, MarketplaceOrder,
    MarketplaceReview, MarketplaceWishlist, MarketplaceMessage
)
from accounts.serializers import UserSerializer


class ServiceCategorySerializer(serializers.ModelSerializer):
    """Serializer for service categories"""
    
    class Meta:
        model = ServiceCategory
        fields = ['id', 'name', 'description', 'icon']


class ListingImageSerializer(serializers.ModelSerializer):
    """Serializer for listing images"""
    
    class Meta:
        model = ListingImage
        fields = ['id', 'image', 'caption', 'order']


class MarketplaceListingSerializer(serializers.ModelSerializer):
    """Serializer for marketplace listings (read)"""
    
    seller = UserSerializer(read_only=True)
    category = ServiceCategorySerializer(read_only=True)
    images = ListingImageSerializer(many=True, read_only=True)
    listing_type_display = serializers.CharField(source='get_listing_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = MarketplaceListing
        fields = [
            'id', 'seller', 'title', 'description', 'listing_type', 'listing_type_display',
            'category', 'price', 'is_negotiable', 'fish_type', 'weight', 'quantity',
            'catch_date', 'catch_location', 'service_duration', 'service_area',
            'main_image', 'images', 'status', 'status_display', 'views_count',
            'featured', 'available_from', 'available_until', 'created_at', 'updated_at'
        ]


class MarketplaceListingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating marketplace listings"""
    
    class Meta:
        model = MarketplaceListing
        fields = [
            'title', 'description', 'listing_type', 'category', 'price', 'is_negotiable',
            'fish_type', 'weight', 'quantity', 'catch_date', 'catch_location',
            'service_duration', 'service_area', 'main_image', 'available_from',
            'available_until'
        ]
    
    def validate(self, attrs):
        listing_type = attrs.get('listing_type')
        
        # Validate fish-specific fields
        if listing_type == 'fish':
            if not attrs.get('fish_type'):
                raise serializers.ValidationError("Fish type is required for fish listings")
            if not attrs.get('weight'):
                raise serializers.ValidationError("Weight is required for fish listings")
        
        # Validate service-specific fields
        elif listing_type == 'service':
            if not attrs.get('service_duration'):
                raise serializers.ValidationError("Service duration is required for service listings")
            if not attrs.get('service_area'):
                raise serializers.ValidationError("Service area is required for service listings")
        
        return attrs


class MarketplaceOrderSerializer(serializers.ModelSerializer):
    """Serializer for marketplace orders"""
    
    listing = MarketplaceListingSerializer(read_only=True)
    listing_id = serializers.UUIDField(write_only=True)
    buyer = UserSerializer(read_only=True)
    seller = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = MarketplaceOrder
        fields = [
            'id', 'listing', 'listing_id', 'buyer', 'seller', 'quantity',
            'unit_price', 'total_amount', 'status', 'status_display',
            'special_instructions', 'order_date', 'confirmed_at', 'completed_at'
        ]
        read_only_fields = ['unit_price', 'total_amount', 'seller']
    
    def validate_listing_id(self, value):
        try:
            listing = MarketplaceListing.objects.get(id=value, status='active')
            return listing
        except MarketplaceListing.DoesNotExist:
            raise serializers.ValidationError("Listing not found or not active")
    
    def validate(self, attrs):
        listing = attrs.get('listing_id')
        quantity = attrs.get('quantity', 1)
        
        if listing and quantity > listing.quantity:
            raise serializers.ValidationError("Requested quantity exceeds available quantity")
        
        return attrs


class MarketplaceReviewSerializer(serializers.ModelSerializer):
    """Serializer for marketplace reviews"""
    
    reviewer = UserSerializer(read_only=True)
    reviewed_user = UserSerializer(read_only=True)
    order = MarketplaceOrderSerializer(read_only=True)
    
    class Meta:
        model = MarketplaceReview
        fields = [
            'id', 'order', 'reviewer', 'reviewed_user', 'rating', 'title',
            'review_text', 'quality_rating', 'communication_rating',
            'delivery_rating', 'created_at'
        ]
    
    def validate_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value


class MarketplaceWishlistSerializer(serializers.ModelSerializer):
    """Serializer for marketplace wishlist"""
    
    listing = MarketplaceListingSerializer(read_only=True)
    
    class Meta:
        model = MarketplaceWishlist
        fields = ['id', 'listing', 'added_at']


class MarketplaceMessageSerializer(serializers.ModelSerializer):
    """Serializer for marketplace messages"""
    
    sender = UserSerializer(read_only=True)
    recipient = UserSerializer(read_only=True)
    
    class Meta:
        model = MarketplaceMessage
        fields = [
            'id', 'order', 'sender', 'recipient', 'message', 'attachment',
            'is_read', 'read_at', 'created_at'
        ]
        read_only_fields = ['sender', 'is_read', 'read_at']
