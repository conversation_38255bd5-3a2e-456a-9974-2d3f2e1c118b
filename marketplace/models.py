from django.db import models
from django.conf import settings
from decimal import Decimal
import uuid


class ServiceCategory(models.Model):
    """Categories for marketplace services"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.ImageField(upload_to='service_categories/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Service Categories"
        ordering = ['name']


class MarketplaceListing(models.Model):
    """Marketplace listings for fish and services"""

    LISTING_TYPES = [
        ('fish', 'Fish Product'),
        ('service', 'Service'),
        ('equipment', 'Equipment'),
        ('supplies', 'Supplies'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('sold', 'Sold'),
        ('expired', 'Expired'),
        ('suspended', 'Suspended'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    seller = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='marketplace_listings')

    # Basic information
    title = models.CharField(max_length=200)
    description = models.TextField()
    listing_type = models.CharField(max_length=20, choices=LISTING_TYPES)
    category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE, related_name='listings')

    # Pricing
    price = models.DecimalField(max_digits=10, decimal_places=2)
    is_negotiable = models.BooleanField(default=False)

    # Fish-specific fields
    fish_type = models.CharField(max_length=100, blank=True)
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Weight in kg")
    quantity = models.PositiveIntegerField(default=1)
    catch_date = models.DateField(null=True, blank=True)
    catch_location = models.CharField(max_length=200, blank=True)

    # Service-specific fields
    service_duration = models.CharField(max_length=100, blank=True, help_text="e.g., '2 hours', '1 day'")
    service_area = models.CharField(max_length=200, blank=True)

    # Media
    main_image = models.ImageField(upload_to='marketplace_listings/')

    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    views_count = models.PositiveIntegerField(default=0)
    featured = models.BooleanField(default=False)

    # Availability
    available_from = models.DateTimeField(null=True, blank=True)
    available_until = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.get_listing_type_display()}"

    class Meta:
        ordering = ['-created_at']


class ListingImage(models.Model):
    """Additional images for marketplace listings"""

    listing = models.ForeignKey(MarketplaceListing, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='marketplace_images/')
    caption = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']


class MarketplaceOrder(models.Model):
    """Orders for marketplace items"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    listing = models.ForeignKey(MarketplaceListing, on_delete=models.CASCADE, related_name='orders')
    buyer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='marketplace_orders')
    seller = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='marketplace_sales'
    )

    # Order details
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Special requirements
    special_instructions = models.TextField(blank=True)

    # Timestamps
    order_date = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.total_amount:
            self.total_amount = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Order {str(self.id)[:8]} - {self.listing.title}"

    class Meta:
        ordering = ['-order_date']


class MarketplaceReview(models.Model):
    """Reviews for marketplace transactions"""

    order = models.OneToOneField(MarketplaceOrder, on_delete=models.CASCADE, related_name='review')
    reviewer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='marketplace_reviews_given')
    reviewed_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='marketplace_reviews_received'
    )

    # Rating (1-5 scale)
    rating = models.PositiveSmallIntegerField()

    # Review details
    title = models.CharField(max_length=200)
    review_text = models.TextField()

    # Specific ratings
    quality_rating = models.PositiveSmallIntegerField(null=True, blank=True)
    communication_rating = models.PositiveSmallIntegerField(null=True, blank=True)
    delivery_rating = models.PositiveSmallIntegerField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Review by {self.reviewer.username} for {self.reviewed_user.username} - {self.rating}/5"

    class Meta:
        ordering = ['-created_at']


class MarketplaceWishlist(models.Model):
    """User's marketplace wishlist"""

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='marketplace_wishlist')
    listing = models.ForeignKey(MarketplaceListing, on_delete=models.CASCADE, related_name='wishlisted_by')
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'listing']
        ordering = ['-added_at']


class MarketplaceMessage(models.Model):
    """Messages between buyers and sellers"""

    order = models.ForeignKey(MarketplaceOrder, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_marketplace_messages')
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='received_marketplace_messages')

    message = models.TextField()
    attachment = models.FileField(upload_to='marketplace_messages/', blank=True, null=True)

    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Message from {self.sender.username} to {self.recipient.username}"

    class Meta:
        ordering = ['created_at']
