#!/bin/bash

# Quick Flutter Permission Fix
# Simple workaround for the immediate permission issue

echo "🚀 Quick Flutter Fix"
echo "==================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}Attempting to fix Flutter permission issue...${NC}"

# Method 1: Try to make the cache directory writable
FLUTTER_CACHE="/Users/<USER>/Public/flutter/bin/cache"
if [ -d "$FLUTTER_CACHE" ]; then
    echo -e "${BLUE}🔧 Attempting to fix cache permissions...${NC}"
    
    # Try to make specific files writable
    chmod u+w "$FLUTTER_CACHE/engine.stamp" 2>/dev/null && echo -e "${GREEN}✅ Fixed engine.stamp${NC}"
    chmod u+w "$FLUTTER_CACHE/lockfile" 2>/dev/null && echo -e "${GREEN}✅ Fixed lockfile${NC}"
    chmod u+w "$FLUTTER_CACHE"/*.stamp 2>/dev/null && echo -e "${GREEN}✅ Fixed stamp files${NC}"
fi

# Method 2: Set Flutter to use a different cache location
echo -e "${BLUE}🔧 Setting up user-specific Flutter cache...${NC}"
export FLUTTER_STORAGE_BASE_URL="https://storage.googleapis.com"
export PUB_CACHE="$HOME/.pub-cache"
export FLUTTER_ROOT="/Users/<USER>/Public/flutter"

# Create user cache directory
mkdir -p "$HOME/.flutter"
mkdir -p "$HOME/.pub-cache"

echo -e "${BLUE}🧪 Testing Flutter...${NC}"

# Test Flutter with environment variables
if FLUTTER_STORAGE_BASE_URL="https://storage.googleapis.com" PUB_CACHE="$HOME/.pub-cache" flutter --version 2>/dev/null; then
    echo -e "${GREEN}✅ Flutter is working with workaround!${NC}"
    
    # Create a wrapper script
    cat > flutter_wrapper.sh << 'EOF'
#!/bin/bash
export FLUTTER_STORAGE_BASE_URL="https://storage.googleapis.com"
export PUB_CACHE="$HOME/.pub-cache"
/Users/<USER>/Public/flutter/bin/flutter "$@"
EOF
    
    chmod +x flutter_wrapper.sh
    echo -e "${GREEN}✅ Created flutter_wrapper.sh${NC}"
    echo -e "${BLUE}💡 Use ./flutter_wrapper.sh instead of flutter command${NC}"
    
else
    echo -e "${YELLOW}⚠️ Quick fix didn't work. Trying alternative...${NC}"
    
    # Method 3: Copy Flutter to user directory
    echo -e "${BLUE}📦 Copying Flutter to your home directory...${NC}"
    
    if [ ! -d "$HOME/flutter" ]; then
        echo -e "${BLUE}📥 This may take a few minutes...${NC}"
        cp -r /Users/<USER>/Public/flutter "$HOME/" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Flutter copied successfully${NC}"
            echo -e "${BLUE}🔧 Add to your PATH: export PATH=\"\$HOME/flutter/bin:\$PATH\"${NC}"
            
            # Test the copied version
            if "$HOME/flutter/bin/flutter" --version >/dev/null 2>&1; then
                echo -e "${GREEN}✅ Copied Flutter is working!${NC}"
                
                # Update PATH for current session
                export PATH="$HOME/flutter/bin:$PATH"
                
                echo -e "${BLUE}💡 Flutter is now available at: $HOME/flutter/bin/flutter${NC}"
            else
                echo -e "${YELLOW}⚠️ Copied Flutter needs setup${NC}"
            fi
        else
            echo -e "${RED}❌ Failed to copy Flutter${NC}"
            echo -e "${BLUE}💡 You may need to run: ./fix_flutter_permissions.sh${NC}"
        fi
    else
        echo -e "${BLUE}Flutter already exists in home directory${NC}"
        export PATH="$HOME/flutter/bin:$PATH"
    fi
fi

# Final test
echo -e "${BLUE}🔍 Final Flutter test...${NC}"
if flutter --version >/dev/null 2>&1; then
    echo -e "${GREEN}🎉 Flutter is working!${NC}"
    flutter --version
    
    echo ""
    echo -e "${GREEN}✅ You can now run:${NC}"
    echo "cd fish_auction_app"
    echo "flutter pub get"
    echo "flutter run -d ios"
    
else
    echo -e "${RED}❌ Flutter still has issues${NC}"
    echo ""
    echo -e "${BLUE}🔧 Manual solutions:${NC}"
    echo "1. Run: ./fix_flutter_permissions.sh"
    echo "2. Or install Flutter in your home directory"
    echo "3. Or ask system admin to fix permissions"
    echo ""
    echo -e "${YELLOW}⚠️ Current issue: Permission denied on Flutter cache${NC}"
    echo -e "${BLUE}💡 The Flutter installation is owned by 'safe' but you're 'aug'${NC}"
fi
