#!/usr/bin/env python3
"""
Debug script to check the jamin fish auction visibility issue
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction
from accounts.models import User
from django.utils import timezone

def debug_jamin_auction():
    print("🔍 Debugging Jamin Fish Auction Visibility")
    print("=" * 50)
    
    # Find the jamin fish auction
    jamin_auction = Auction.objects.filter(title__icontains='jamin').first()
    
    if jamin_auction:
        print(f"✅ Found auction: {jamin_auction.title}")
        print(f"📊 Status: {jamin_auction.status}")
        print(f"👤 Seller: {jamin_auction.seller.username} (ID: {jamin_auction.seller.id}, Type: {jamin_auction.seller.user_type})")
        print(f"⏰ Start time: {jamin_auction.start_time}")
        print(f"⏰ End time: {jamin_auction.end_time}")
        print(f"💰 Current price: ${jamin_auction.current_price}")
        print(f"🔴 Is live property: {jamin_auction.is_live}")
        print(f"📅 Created at: {jamin_auction.created_at}")
        print(f"🎯 Auction type: {jamin_auction.auction_type}")
        print(f"🐟 Fish type: {jamin_auction.fish_type}")
        print()
        
        # Check current time vs auction times
        now = timezone.now()
        print(f"🕐 Current time: {now}")
        print(f"⏰ Start <= Now <= End: {jamin_auction.start_time <= now <= jamin_auction.end_time}")
        print(f"📊 Status == 'live': {jamin_auction.status == 'live'}")
        print()
        
        # Check what makes an auction visible to buyers
        print("👁️ Visibility Analysis:")
        print(f"   - Not draft: {jamin_auction.status != 'draft'}")
        print(f"   - Not ended: {jamin_auction.status != 'ended'}")
        print(f"   - Is live: {jamin_auction.status == 'live'}")
        print(f"   - Is scheduled: {jamin_auction.status == 'scheduled'}")
        print()
        
    else:
        print("❌ No auction found with 'jamin' in title")
    
    # Check all users and their types
    print("👥 All users:")
    for user in User.objects.all():
        print(f"   {user.username} - {user.user_type} (ID: {user.id})")
    print()
    
    # Check all auctions and their visibility
    print("🏛️ All auctions:")
    for auction in Auction.objects.all():
        visible_to_buyers = auction.status not in ['draft', 'ended']
        print(f"   {auction.title} - {auction.status} - Seller: {auction.seller.username} - Visible to buyers: {visible_to_buyers}")
    print()
    
    # Test API endpoint response
    print("🌐 Testing API endpoint...")
    try:
        from auctions.serializers import AuctionListSerializer
        from rest_framework.request import Request
        from django.test import RequestFactory
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.get('/api/auctions/')
        
        # Get all auctions as API would return them
        auctions = Auction.objects.all()
        serializer = AuctionListSerializer(auctions, many=True, context={'request': Request(request)})
        
        print(f"📊 API would return {len(serializer.data)} auctions:")
        for auction_data in serializer.data:
            print(f"   {auction_data['title']} - {auction_data['status']} - Seller: {auction_data['seller']['username']}")
        
    except Exception as e:
        print(f"❌ Error testing API: {e}")
    
    print()
    print("🔍 Possible Issues:")
    print("1. Auction status might not be 'live'")
    print("2. Auction times might be incorrect")
    print("3. Frontend filtering might be hiding the auction")
    print("4. User permissions might be blocking visibility")
    print("5. API endpoint might have additional filtering")

if __name__ == "__main__":
    debug_jamin_auction()
