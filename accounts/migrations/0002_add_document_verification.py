# Generated by Django 4.2.23 on 2025-06-24 12:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='government_id_document',
            field=models.FileField(blank=True, null=True, upload_to='verification_documents/government_id/'),
        ),
        migrations.AddField(
            model_name='user',
            name='government_id_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('not_submitted', 'Not Submitted')], default='not_submitted', max_length=20),
        ),
        migrations.AddField(
            model_name='user',
            name='hunting_approval_document',
            field=models.FileField(blank=True, null=True, upload_to='verification_documents/hunting_approval/'),
        ),
        migrations.AddField(
            model_name='user',
            name='hunting_approval_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('not_submitted', 'Not Submitted')], default='not_submitted', max_length=20),
        ),
        migrations.AddField(
            model_name='user',
            name='verification_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='verified_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='DocumentVerificationRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('government_id', 'Government ID'), ('hunting_approval', 'Hunting Approval')], max_length=20)),
                ('document_file', models.FileField(upload_to='verification_documents/')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('review_notes', models.TextField(blank=True, null=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_verifications', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='verification_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'accounts_document_verification',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'document_type')},
            },
        ),
    ]
