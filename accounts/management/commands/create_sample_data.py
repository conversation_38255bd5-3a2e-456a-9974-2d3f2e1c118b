from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from accounts.models import User
from auctions.models import FishCategory, Auction
from notifications.models import NotificationTemplate
from marketplace.models import ServiceCategory
import random


class Command(BaseCommand):
    help = 'Create sample data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create users
        self.create_users()
        
        # Create fish categories
        self.create_fish_categories()
        
        # Create service categories
        self.create_service_categories()
        
        # Create notification templates
        self.create_notification_templates()
        
        # Create sample auctions
        self.create_sample_auctions()
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))

    def create_users(self):
        """Create sample users"""
        users_data = [
            {
                'username': 'seller1',
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'user_type': 'seller',
                'phone_number': '+1234567890',
                'wallet_balance': Decimal('1000.00')
            },
            {
                'username': 'seller2',
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'user_type': 'seller',
                'phone_number': '+1234567891',
                'wallet_balance': Decimal('1500.00')
            },
            {
                'username': 'buyer1',
                'email': '<EMAIL>',
                'first_name': 'David',
                'last_name': 'Smith',
                'user_type': 'buyer',
                'phone_number': '+1234567892',
                'wallet_balance': Decimal('500.00')
            },
            {
                'username': 'buyer2',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'user_type': 'buyer',
                'phone_number': '+1234567893',
                'wallet_balance': Decimal('750.00')
            },
            {
                'username': 'broker1',
                'email': '<EMAIL>',
                'first_name': 'Michael',
                'last_name': 'Trade',
                'user_type': 'broker',
                'phone_number': '+1234567894',
                'wallet_balance': Decimal('2000.00')
            }
        ]
        
        for user_data in users_data:
            if not User.objects.filter(username=user_data['username']).exists():
                user = User.objects.create_user(
                    password='testpass123',
                    **user_data
                )
                self.stdout.write(f'Created user: {user.username}')

    def create_fish_categories(self):
        """Create fish categories"""
        categories = [
            {'name': 'Tuna', 'description': 'Fresh tuna fish'},
            {'name': 'Salmon', 'description': 'Atlantic and Pacific salmon'},
            {'name': 'Cod', 'description': 'Fresh cod fish'},
            {'name': 'Mackerel', 'description': 'Fresh mackerel'},
            {'name': 'Sardines', 'description': 'Fresh sardines'},
            {'name': 'Shrimp', 'description': 'Fresh shrimp and prawns'},
            {'name': 'Lobster', 'description': 'Fresh lobster'},
            {'name': 'Crab', 'description': 'Fresh crab'},
        ]
        
        for cat_data in categories:
            category, created = FishCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created fish category: {category.name}')

    def create_service_categories(self):
        """Create service categories"""
        categories = [
            {'name': 'Fishing Equipment', 'description': 'Nets, rods, and fishing gear'},
            {'name': 'Boat Services', 'description': 'Boat rental and maintenance'},
            {'name': 'Processing Services', 'description': 'Fish cleaning and processing'},
            {'name': 'Storage & Ice', 'description': 'Cold storage and ice supply'},
            {'name': 'Transportation', 'description': 'Fish delivery services'},
        ]
        
        for cat_data in categories:
            category, created = ServiceCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created service category: {category.name}')

    def create_notification_templates(self):
        """Create notification templates"""
        templates = [
            {
                'name': 'Auction Won',
                'notification_type': 'auction_won',
                'email_subject': 'Congratulations! You won the auction for {{ auction_title }}',
                'email_body': 'Dear {{ user_name }}, you have won the auction for {{ auction_title }}. Please complete payment within 10 minutes.',
                'whatsapp_message': 'Congratulations! You won {{ auction_title }}. Complete payment in 10 minutes.',
                'push_title': 'Auction Won!',
                'push_body': 'You won {{ auction_title }}'
            },
            {
                'name': 'Payment Reminder',
                'notification_type': 'payment_reminder',
                'email_subject': 'Payment Reminder for {{ auction_title }}',
                'email_body': 'Dear {{ user_name }}, your payment for {{ auction_title }} is overdue. Please complete payment to avoid cancellation.',
                'whatsapp_message': 'Payment reminder: {{ auction_title }} payment is overdue.',
                'push_title': 'Payment Overdue',
                'push_body': 'Complete payment for {{ auction_title }}'
            },
            {
                'name': 'Auction Ending Soon',
                'notification_type': 'auction_ending',
                'email_subject': '{{ auction_title }} ending soon!',
                'email_body': 'The auction for {{ auction_title }} is ending in less than 1 hour. Place your final bids now!',
                'whatsapp_message': '{{ auction_title }} ending soon! Place your bids now.',
                'push_title': 'Auction Ending Soon',
                'push_body': '{{ auction_title }} ends in 1 hour'
            }
        ]
        
        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'Created notification template: {template.name}')

    def create_sample_auctions(self):
        """Create sample auctions"""
        sellers = User.objects.filter(user_type='seller')
        categories = FishCategory.objects.all()
        
        if not sellers.exists() or not categories.exists():
            self.stdout.write('No sellers or categories found. Skipping auction creation.')
            return
        
        fish_types = ['Bluefin Tuna', 'Atlantic Salmon', 'Fresh Cod', 'King Mackerel', 'Tiger Shrimp']
        locations = ['Pacific Ocean', 'Atlantic Ocean', 'Mediterranean Sea', 'North Sea', 'Gulf of Mexico']
        
        for i in range(10):
            seller = random.choice(sellers)
            category = random.choice(categories)
            fish_type = random.choice(fish_types)
            location = random.choice(locations)
            
            start_time = timezone.now() + timedelta(hours=random.randint(1, 24))
            end_time = start_time + timedelta(hours=random.randint(2, 12))
            
            auction_data = {
                'seller': seller,
                'title': f'Fresh {fish_type} - Premium Quality',
                'description': f'High-quality {fish_type} caught fresh from {location}. Perfect for restaurants and fish markets.',
                'fish_category': category,
                'fish_type': fish_type,
                'weight': Decimal(str(random.uniform(5.0, 50.0))),
                'quantity': random.randint(1, 10),
                'catch_date': timezone.now().date() - timedelta(days=random.randint(0, 2)),
                'catch_location': location,
                'auction_type': random.choice(['live', 'timed']),
                'starting_price': Decimal(str(random.uniform(10.0, 100.0))),
                'reserve_price': Decimal(str(random.uniform(50.0, 150.0))),
                'buy_now_price': Decimal(str(random.uniform(100.0, 200.0))),
                'bid_increment': Decimal('5.00'),
                'start_time': start_time,
                'end_time': end_time,
                'status': random.choice(['draft', 'scheduled', 'live'])
            }
            
            auction = Auction.objects.create(**auction_data)
            self.stdout.write(f'Created auction: {auction.title}')
