from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from decimal import Decimal

class User(AbstractUser):
    """Custom User model with additional fields for Fish Auction platform"""

    USER_TYPE_CHOICES = [
        ('buyer', 'Buyer'),
        ('seller', 'Seller'),
        ('broker', 'Broker'),
        ('service_provider', 'Service Provider'),
        ('admin', 'Admin'),
    ]

    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='buyer')
    phone_number = models.CharField(
        max_length=17,
        validators=[RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")],
        blank=True,
        null=True
    )
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)

    # KYC and verification
    is_verified = models.BooleanField(default=False)
    kyc_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('not_submitted', 'Not Submitted'),
        ],
        default='not_submitted'
    )
    kyc_document = models.FileField(upload_to='kyc_documents/', blank=True, null=True)

    # Enhanced document verification
    government_id_document = models.FileField(upload_to='verification_documents/government_id/', blank=True, null=True)
    hunting_approval_document = models.FileField(upload_to='verification_documents/hunting_approval/', blank=True, null=True)
    government_id_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('not_submitted', 'Not Submitted'),
        ],
        default='not_submitted'
    )
    hunting_approval_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('not_submitted', 'Not Submitted'),
        ],
        default='not_submitted'
    )
    verification_notes = models.TextField(blank=True, null=True)
    verified_at = models.DateTimeField(blank=True, null=True)

    # Wallet
    wallet_balance = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Preferences
    enable_notifications = models.BooleanField(default=True)
    enable_whatsapp_notifications = models.BooleanField(default=True)
    enable_email_notifications = models.BooleanField(default=True)
    preferred_language = models.CharField(max_length=10, default='en')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} ({self.get_user_type_display()})"

    class Meta:
        db_table = 'accounts_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'


class UserProfile(models.Model):
    """Extended profile information for users"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(max_length=500, blank=True)
    website = models.URLField(blank=True)
    company_name = models.CharField(max_length=200, blank=True)
    business_license = models.CharField(max_length=100, blank=True)
    tax_id = models.CharField(max_length=50, blank=True)

    # Rating and reviews
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    total_reviews = models.PositiveIntegerField(default=0)

    # Statistics
    total_auctions_participated = models.PositiveIntegerField(default=0)
    total_auctions_won = models.PositiveIntegerField(default=0)
    total_auctions_created = models.PositiveIntegerField(default=0)
    total_amount_spent = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_amount_earned = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Profile"

    class Meta:
        db_table = 'accounts_user_profile'


class DocumentVerificationRequest(models.Model):
    """Document verification requests for enhanced KYC"""

    DOCUMENT_TYPE_CHOICES = [
        ('government_id', 'Government ID'),
        ('hunting_approval', 'Hunting Approval'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verification_requests')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPE_CHOICES)
    document_file = models.FileField(upload_to='verification_documents/')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Admin review fields
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_verifications'
    )
    review_notes = models.TextField(blank=True, null=True)
    reviewed_at = models.DateTimeField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.get_document_type_display()} - {self.get_status_display()}"

    class Meta:
        db_table = 'accounts_document_verification'
        unique_together = ['user', 'document_type']
        ordering = ['-created_at']
