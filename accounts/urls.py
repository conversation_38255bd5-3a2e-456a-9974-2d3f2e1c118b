from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from .views import (
    UserRegistrationView, UserLoginView, UserProfileView,
    PasswordChangeView, KYCSubmissionView, KYCPendingListView,
    KYCVerificationView, DocumentSubmissionView, BatchDocumentSubmissionView,
    UserDocumentRequestsView, PendingDocumentVerificationsView,
    DocumentVerificationReviewView, admin_verification_interface, admin_verification_js,
    admin_verification_stats, admin_verification_grouped, admin_batch_review
)

app_name = 'accounts'

urlpatterns = [
    path('register/', UserRegistrationView.as_view(), name='register'),
    path('login/', UserLoginView.as_view(), name='login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('profile/', UserProfileView.as_view(), name='profile'),
    path('change-password/', PasswordChangeView.as_view(), name='change_password'),
    path('kyc/submit/', KYCSubmissionView.as_view(), name='kyc_submit'),
    path('kyc/pending/', KYCPendingListView.as_view(), name='kyc_pending'),
    path('kyc/verify/<int:pk>/', KYCVerificationView.as_view(), name='kyc_verify'),

    # Enhanced document verification
    path('documents/submit/', DocumentSubmissionView.as_view(), name='document_submit'),
    path('documents/submit-batch/', BatchDocumentSubmissionView.as_view(), name='batch_document_submit'),
    path('documents/my-requests/', UserDocumentRequestsView.as_view(), name='user_document_requests'),
    path('documents/pending/', PendingDocumentVerificationsView.as_view(), name='pending_document_verifications'),
    path('documents/review/<int:pk>/', DocumentVerificationReviewView.as_view(), name='document_verification_review'),

    # Admin verification interface
    path('admin/verification/', admin_verification_interface, name='admin_verification_interface'),
    path('admin/verification/admin.js', admin_verification_js, name='admin_verification_js'),
    path('admin/verification/stats/', admin_verification_stats, name='admin_verification_stats'),
    path('admin/verification/grouped/', admin_verification_grouped, name='admin_verification_grouped'),
    path('admin/verification/batch-review/', admin_batch_review, name='admin_batch_review'),
]
