from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.shortcuts import render
from django.http import HttpResponse
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .models import User, UserProfile, DocumentVerificationRequest
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    UserUpdateSerializer, UserProfileSerializer, PasswordChangeSerializer,
    KYCSubmissionSerializer, KYCVerificationSerializer,
    DocumentVerificationRequestSerializer, DocumentSubmissionSerializer,
    DocumentVerificationReviewSerializer
)


class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint"""

    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Register a new user",
        description="Create a new user account with the provided information",
        responses={201: UserSerializer}
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                'user': UserSerializer(user).data,
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLoginView(APIView):
    """User login endpoint"""

    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="User login",
        description="Authenticate user and return JWT tokens",
        request=UserLoginSerializer,
        responses={200: UserSerializer}
    )
    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            refresh = RefreshToken.for_user(user)
            return Response({
                'user': UserSerializer(user).data,
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile view and update"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

    @extend_schema(
        summary="Get user profile",
        description="Retrieve the authenticated user's profile information"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Update user profile",
        description="Update the authenticated user's profile information",
        request=UserUpdateSerializer
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return UserUpdateSerializer
        return UserSerializer


class PasswordChangeView(APIView):
    """Password change endpoint"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Change password",
        description="Change the authenticated user's password",
        request=PasswordChangeSerializer
    )
    def post(self, request):
        serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = request.user
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            return Response({'message': 'Password changed successfully'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class KYCSubmissionView(generics.UpdateAPIView):
    """KYC document submission endpoint"""

    serializer_class = KYCSubmissionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

    @extend_schema(
        summary="Submit KYC document",
        description="Submit KYC document for verification"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class KYCPendingListView(generics.ListAPIView):
    """List pending KYC submissions (admin only)"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAdminUser]

    def get_queryset(self):
        return User.objects.filter(kyc_status='pending')

    @extend_schema(
        summary="List pending KYC submissions",
        description="Get all users with pending KYC verification (admin only)"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class KYCVerificationView(generics.UpdateAPIView):
    """KYC verification endpoint (admin only)"""

    serializer_class = KYCVerificationSerializer
    permission_classes = [permissions.IsAdminUser]
    queryset = User.objects.all()

    @extend_schema(
        summary="Verify KYC document",
        description="Approve or reject KYC document (admin only)"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class DocumentSubmissionView(generics.CreateAPIView):
    """Submit verification documents"""

    serializer_class = DocumentSubmissionSerializer
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Submit verification document",
        description="Submit government ID or hunting approval document for verification"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class BatchDocumentSubmissionView(APIView):
    """Submit multiple verification documents at once"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Submit multiple verification documents",
        description="Submit both government ID and hunting approval documents for verification",
        request={
            'multipart/form-data': {
                'type': 'object',
                'properties': {
                    'government_id': {
                        'type': 'string',
                        'format': 'binary',
                        'description': 'Government ID document file'
                    },
                    'hunting_approval': {
                        'type': 'string',
                        'format': 'binary',
                        'description': 'Hunting approval document file'
                    }
                },
                'required': ['government_id', 'hunting_approval']
            }
        }
    )
    def post(self, request, *args, **kwargs):
        user = request.user
        submitted_documents = []
        errors = {}

        # Document types to process
        document_types = {
            'government_id': request.FILES.get('government_id'),
            'hunting_approval': request.FILES.get('hunting_approval')
        }

        # Debug: Print file information
        print("🔍 Batch Document Submission Debug:")
        for doc_type, file in document_types.items():
            if file:
                print(f"   {doc_type}: {file.name}, Content-Type: {file.content_type}, Size: {file.size}")
            else:
                print(f"   {doc_type}: No file provided")

        # Validate that both documents are provided
        for doc_type, file in document_types.items():
            if not file:
                errors[doc_type] = ['This field is required.']

        if errors:
            return Response(errors, status=status.HTTP_400_BAD_REQUEST)

        # Process each document
        for doc_type, file in document_types.items():
            try:
                # Check if user already has a pending/approved request for this document type
                existing_request = DocumentVerificationRequest.objects.filter(
                    user=user,
                    document_type=doc_type
                ).exclude(status='rejected').first()

                if existing_request:
                    errors[doc_type] = [f'{doc_type.replace("_", " ").title()} is already {existing_request.status}']
                    continue

                # Create document verification request
                serializer = DocumentSubmissionSerializer(data={
                    'document_type': doc_type,
                    'document_file': file
                }, context={'request': request})

                if serializer.is_valid():
                    document_request = serializer.save()
                    submitted_documents.append({
                        'id': document_request.id,
                        'document_type': document_request.document_type,
                        'status': document_request.status,
                        'submitted_at': document_request.created_at
                    })
                    print(f"✅ {doc_type} submitted successfully")
                else:
                    print(f"❌ Validation error for {doc_type}: {serializer.errors}")
                    errors[doc_type] = serializer.errors.get('document_file', serializer.errors)

            except Exception as e:
                errors[doc_type] = [str(e)]

        # Return response
        if errors and not submitted_documents:
            return Response(errors, status=status.HTTP_400_BAD_REQUEST)
        elif errors and submitted_documents:
            return Response({
                'submitted_documents': submitted_documents,
                'errors': errors,
                'message': 'Some documents were submitted successfully, but others failed.'
            }, status=status.HTTP_207_MULTI_STATUS)
        else:
            return Response({
                'submitted_documents': submitted_documents,
                'message': 'All documents submitted successfully for verification.'
            }, status=status.HTTP_201_CREATED)


class UserDocumentRequestsView(generics.ListAPIView):
    """List user's document verification requests"""

    serializer_class = DocumentVerificationRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return DocumentVerificationRequest.objects.filter(user=self.request.user)

    @extend_schema(
        summary="List my document requests",
        description="Get all document verification requests for the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class PendingDocumentVerificationsView(generics.ListAPIView):
    """List pending document verifications (admin only)"""

    serializer_class = DocumentVerificationRequestSerializer
    permission_classes = [permissions.IsAdminUser]
    pagination_class = None  # Disable pagination for admin interface

    def get_queryset(self):
        return DocumentVerificationRequest.objects.filter(status='pending').order_by('-created_at')

    @extend_schema(
        summary="List pending document verifications",
        description="Get all pending document verification requests (admin only)"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class DocumentVerificationReviewView(generics.UpdateAPIView):
    """Review document verification (admin only)"""

    serializer_class = DocumentVerificationReviewSerializer
    permission_classes = [permissions.IsAdminUser]
    queryset = DocumentVerificationRequest.objects.all()

    def perform_update(self, serializer):
        # Save the document review
        instance = serializer.save(reviewed_by=self.request.user, reviewed_at=timezone.now())

        # Check if user should be verified after this update
        if instance.status == 'approved':
            user = instance.user
            # Only sellers need verification - buyers and brokers don't need document verification
            if user.user_type == 'seller':
                # Check if user has both required documents approved
                approved_gov_id = DocumentVerificationRequest.objects.filter(
                    user=user,
                    document_type='government_id',
                    status='approved'
                ).exists()
                approved_hunting = DocumentVerificationRequest.objects.filter(
                    user=user,
                    document_type='hunting_approval',
                    status='approved'
                ).exists()

                if approved_gov_id and approved_hunting:
                    user.is_verified = True
                    user.save()

                    # Send account activation notification
                    from notifications.services import NotificationService
                    notification_service = NotificationService()
                    notification_service.send_notification(
                        user,
                        'account_activated',
                        {'user': user},
                        channels=['whatsapp', 'in_app']  # Both WhatsApp and in-app
                    )

    @extend_schema(
        summary="Review document verification",
        description="Approve or reject document verification request (admin only)"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def admin_verification_stats(request):
    """Get verification statistics for admin dashboard"""

    stats = {
        'pending_count': DocumentVerificationRequest.objects.filter(status='pending').count(),
        'approved_count': DocumentVerificationRequest.objects.filter(status='approved').count(),
        'rejected_count': DocumentVerificationRequest.objects.filter(status='rejected').count(),
        'total_users': User.objects.count(),
        'verified_users': User.objects.filter(is_verified=True).count(),
    }

    return Response(stats)


@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def admin_verification_grouped(request):
    """Get verification requests grouped by user"""

    # Get all pending document requests
    pending_requests = DocumentVerificationRequest.objects.filter(
        status='pending'
    ).select_related('user').order_by('user__username', 'document_type')

    # Group by user
    user_groups = {}
    for request in pending_requests:
        user_id = request.user.id
        if user_id not in user_groups:
            user_groups[user_id] = {
                'user_id': user_id,
                'user_name': request.user.username,
                'user_email': request.user.email,
                'user_type': request.user.user_type,
                'documents': {},
                'submitted_at': None
            }

        # Add document to user group
        user_groups[user_id]['documents'][request.document_type] = {
            'id': request.id,
            'document_type': request.document_type,
            'document_type_display': request.get_document_type_display(),
            'document_file': request.document_file.url if request.document_file else None,
            'status': request.status,
            'status_display': request.get_status_display(),
            'created_at': request.created_at,
        }

        # Set the earliest submission date
        if not user_groups[user_id]['submitted_at'] or request.created_at < user_groups[user_id]['submitted_at']:
            user_groups[user_id]['submitted_at'] = request.created_at

    # Convert to list and add completion status
    result = []
    for user_data in user_groups.values():
        user_data['has_government_id'] = 'government_id' in user_data['documents']
        user_data['has_hunting_approval'] = 'hunting_approval' in user_data['documents']
        user_data['is_complete'] = user_data['has_government_id'] and user_data['has_hunting_approval']
        user_data['document_count'] = len(user_data['documents'])
        result.append(user_data)

    # Sort by submission date (newest first)
    result.sort(key=lambda x: x['submitted_at'], reverse=True)

    return Response(result)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def admin_batch_review(request):
    """Review multiple documents for a user at once"""

    user_id = request.data.get('user_id')
    action = request.data.get('action')  # 'approve' or 'reject'
    review_notes = request.data.get('review_notes', '')

    if not user_id or action not in ['approve', 'reject']:
        return Response(
            {'error': 'user_id and action (approve/reject) are required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Get all pending documents for this user
        pending_docs = DocumentVerificationRequest.objects.filter(
            user_id=user_id,
            status='pending'
        )

        if not pending_docs.exists():
            return Response(
                {'error': 'No pending documents found for this user'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Update all documents
        updated_docs = []
        for doc in pending_docs:
            doc.status = 'approved' if action == 'approve' else 'rejected'
            doc.reviewed_by = request.user
            doc.review_notes = review_notes
            doc.reviewed_at = timezone.now()
            doc.save()

            updated_docs.append({
                'id': doc.id,
                'document_type': doc.document_type,
                'status': doc.status
            })

        # Update user verification status if all documents are approved
        if action == 'approve':
            user = User.objects.get(id=user_id)
            # Only sellers need verification - buyers and brokers don't need document verification
            if user.user_type == 'seller':
                # Check if user has both required documents approved
                approved_gov_id = DocumentVerificationRequest.objects.filter(
                    user=user,
                    document_type='government_id',
                    status='approved'
                ).exists()
                approved_hunting = DocumentVerificationRequest.objects.filter(
                    user=user,
                    document_type='hunting_approval',
                    status='approved'
                ).exists()

                if approved_gov_id and approved_hunting:
                    user.is_verified = True
                    user.save()

                    # Send account activation notification
                    from notifications.services import NotificationService
                    notification_service = NotificationService()
                    notification_service.send_notification(
                        user,
                        'account_activated',
                        {'user': user},
                        channels=['whatsapp', 'in_app']  # Both WhatsApp and in-app
                    )

        return Response({
            'message': f'Successfully {action}d {len(updated_docs)} documents',
            'updated_documents': updated_docs,
            'user_verified': action == 'approve' and User.objects.get(id=user_id).is_verified
        })

    except User.DoesNotExist:
        return Response(
            {'error': 'User not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': f'Error processing review: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def admin_verification_interface(request):
    """Serve the admin verification interface"""
    import os
    from django.conf import settings

    # Read the HTML file
    admin_html_path = os.path.join(settings.BASE_DIR, 'admin_verification', 'index.html')

    try:
        with open(admin_html_path, 'r', encoding='utf-8') as file:
            html_content = file.read()
        return HttpResponse(html_content, content_type='text/html')
    except FileNotFoundError:
        return HttpResponse(
            '<h1>Admin Interface Not Found</h1><p>Please ensure the admin verification interface files are properly installed.</p>',
            content_type='text/html',
            status=404
        )


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def admin_verification_js(request):
    """Serve the admin verification JavaScript"""
    import os
    from django.conf import settings

    # Read the JS file
    admin_js_path = os.path.join(settings.BASE_DIR, 'admin_verification', 'admin.js')

    try:
        with open(admin_js_path, 'r', encoding='utf-8') as file:
            js_content = file.read()
        return HttpResponse(js_content, content_type='application/javascript')
    except FileNotFoundError:
        return HttpResponse(
            '// Admin JavaScript Not Found',
            content_type='application/javascript',
            status=404
        )
