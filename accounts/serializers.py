from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, UserProfile, DocumentVerificationRequest


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'phone_number', 'user_type',
            'address', 'city', 'state', 'country', 'postal_code'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()

        # UserProfile is created automatically by signal

        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include username and password')
        
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile"""
    
    class Meta:
        model = UserProfile
        fields = [
            'bio', 'website', 'company_name', 'business_license', 'tax_id',
            'average_rating', 'total_reviews', 'total_auctions_participated',
            'total_auctions_won', 'total_auctions_created', 'total_amount_spent',
            'total_amount_earned'
        ]
        read_only_fields = [
            'average_rating', 'total_reviews', 'total_auctions_participated',
            'total_auctions_won', 'total_auctions_created', 'total_amount_spent',
            'total_amount_earned'
        ]


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user details"""
    
    profile = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone_number', 'user_type', 'profile_picture', 'date_of_birth',
            'address', 'city', 'state', 'country', 'postal_code',
            'is_verified', 'kyc_status', 'wallet_balance',
            'government_id_status', 'hunting_approval_status', 'verified_at',
            'enable_notifications', 'enable_whatsapp_notifications',
            'enable_email_notifications', 'preferred_language',
            'date_joined', 'profile'
        ]
        read_only_fields = [
            'id', 'username', 'is_verified', 'kyc_status', 'wallet_balance',
            'government_id_status', 'hunting_approval_status', 'verified_at', 'date_joined'
        ]


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user information"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'phone_number', 'profile_picture',
            'date_of_birth', 'address', 'city', 'state', 'country', 'postal_code',
            'enable_notifications', 'enable_whatsapp_notifications',
            'enable_email_notifications', 'preferred_language'
        ]


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change"""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class KYCSubmissionSerializer(serializers.ModelSerializer):
    """Serializer for KYC document submission"""

    class Meta:
        model = User
        fields = ['kyc_document']

    def validate_kyc_document(self, value):
        if not value:
            raise serializers.ValidationError("KYC document is required")

        # Validate file size (max 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 10MB")

        # Validate file type
        allowed_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/jpg',
            'image/pjpeg',
            'image/x-png',  # Alternative PNG type
            'application/octet-stream',  # Generic binary type (common on mobile)
        ]

        # Check file extension as fallback if content type is generic
        if value.content_type == 'application/octet-stream':
            file_extension = value.name.lower().split('.')[-1] if '.' in value.name else ''
            if file_extension in ['jpg', 'jpeg', 'png', 'pdf']:
                return value

        if value.content_type not in allowed_types:
            raise serializers.ValidationError(f"Only PDF, JPEG, PNG files are allowed. Received: {value.content_type}")

        return value

    def update(self, instance, validated_data):
        instance.kyc_document = validated_data.get('kyc_document', instance.kyc_document)
        instance.kyc_status = 'pending'
        instance.save()

        # Send notification about KYC submission
        from notifications.services import NotificationService
        notification_service = NotificationService()
        notification_service.send_notification(
            'kyc_submitted',
            instance,
            {'user': instance}
        )

        return instance


class KYCVerificationSerializer(serializers.ModelSerializer):
    """Serializer for KYC verification (admin only)"""

    verification_notes = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = ['kyc_status', 'is_verified', 'verification_notes']

    def validate_kyc_status(self, value):
        if value not in ['approved', 'rejected']:
            raise serializers.ValidationError("KYC status must be 'approved' or 'rejected'")
        return value

    def update(self, instance, validated_data):
        kyc_status = validated_data.get('kyc_status')
        verification_notes = validated_data.get('verification_notes', '')

        instance.kyc_status = kyc_status
        if kyc_status == 'approved':
            instance.is_verified = True

        instance.save()

        # Send notification about KYC decision
        from notifications.services import NotificationService
        notification_service = NotificationService()

        if kyc_status == 'approved':
            notification_service.send_notification(
                'kyc_approved',
                instance,
                {'user': instance, 'notes': verification_notes}
            )
        else:
            notification_service.send_notification(
                'kyc_rejected',
                instance,
                {'user': instance, 'notes': verification_notes}
            )

        return instance


class DocumentVerificationRequestSerializer(serializers.ModelSerializer):
    """Serializer for document verification requests"""

    user_name = serializers.CharField(source='user.username', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reviewed_by_name = serializers.CharField(source='reviewed_by.username', read_only=True)

    class Meta:
        model = DocumentVerificationRequest
        fields = [
            'id', 'user', 'user_name', 'user_email', 'document_type',
            'document_type_display', 'document_file', 'status', 'status_display',
            'reviewed_by', 'reviewed_by_name', 'review_notes', 'reviewed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'reviewed_by', 'reviewed_at', 'created_at', 'updated_at']


class DocumentSubmissionSerializer(serializers.ModelSerializer):
    """Serializer for submitting verification documents"""

    class Meta:
        model = DocumentVerificationRequest
        fields = ['document_type', 'document_file']

    def validate_document_file(self, value):
        if not value:
            raise serializers.ValidationError("Document file is required")

        # Validate file size (max 10MB)
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError("File size must be less than 10MB")

        # Validate file type
        allowed_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/jpg',
            'image/pjpeg',
            'image/x-png',  # Alternative PNG type
            'application/octet-stream',  # Generic binary type (common on mobile)
        ]

        # Debug: Print the actual content type
        print(f"🔍 File validation - Name: {value.name}, Content-Type: {value.content_type}, Size: {value.size}")

        # Check file extension as fallback if content type is generic
        if value.content_type == 'application/octet-stream':
            file_extension = value.name.lower().split('.')[-1] if '.' in value.name else ''
            if file_extension in ['jpg', 'jpeg', 'png', 'pdf']:
                print(f"✅ Allowing file based on extension: {file_extension}")
                return value

        # Allow the file if content type is in allowed list
        if value.content_type in allowed_types:
            print(f"✅ Allowing file with content type: {value.content_type}")
            return value

        # If we get here, the file type is not allowed
        print(f"❌ Rejected content type: {value.content_type}")
        raise serializers.ValidationError(f"Only PDF, JPEG, PNG files are allowed. Received: {value.content_type}")

        return value

    def create(self, validated_data):
        user = self.context['request'].user
        document_type = validated_data['document_type']

        # Check if user already has a pending or approved request for this document type
        existing_request = DocumentVerificationRequest.objects.filter(
            user=user,
            document_type=document_type
        ).first()

        if existing_request:
            if existing_request.status == 'approved':
                raise serializers.ValidationError(
                    f"{existing_request.get_document_type_display()} is already approved"
                )
            elif existing_request.status == 'pending':
                # Update existing pending request
                existing_request.document_file = validated_data['document_file']
                existing_request.save()
                return existing_request

        # Create new request
        request = DocumentVerificationRequest.objects.create(
            user=user,
            **validated_data
        )

        # Send notification about document submission
        from notifications.services import NotificationService
        notification_service = NotificationService()
        notification_service.send_notification(
            'document_submitted',
            user,
            {
                'user': user,
                'document_type': request.get_document_type_display()
            }
        )

        return request


class DocumentVerificationReviewSerializer(serializers.ModelSerializer):
    """Serializer for admin review of document verification"""

    class Meta:
        model = DocumentVerificationRequest
        fields = ['status', 'review_notes']

    def validate_status(self, value):
        if value not in ['approved', 'rejected']:
            raise serializers.ValidationError("Status must be 'approved' or 'rejected'")
        return value

    def update(self, instance, validated_data):
        from django.utils import timezone

        instance.status = validated_data.get('status')
        instance.review_notes = validated_data.get('review_notes', '')
        instance.reviewed_by = self.context['request'].user
        instance.reviewed_at = timezone.now()
        instance.save()

        # Update user's document status
        user = instance.user
        if instance.document_type == 'government_id':
            user.government_id_status = instance.status
        elif instance.document_type == 'hunting_approval':
            user.hunting_approval_status = instance.status

        # Check if user is fully verified (both documents approved)
        if (user.government_id_status == 'approved' and
            user.hunting_approval_status == 'approved'):
            user.is_verified = True
            user.verified_at = timezone.now()
        else:
            user.is_verified = False
            user.verified_at = None

        user.save()

        # Send notification about verification decision
        from notifications.services import NotificationService
        notification_service = NotificationService()

        if instance.status == 'approved':
            notification_service.send_notification(
                'document_approved',
                user,
                {
                    'user': user,
                    'document_type': instance.get_document_type_display(),
                    'notes': instance.review_notes
                }
            )
        else:
            notification_service.send_notification(
                'document_rejected',
                user,
                {
                    'user': user,
                    'document_type': instance.get_document_type_display(),
                    'notes': instance.review_notes
                }
            )

        return instance
