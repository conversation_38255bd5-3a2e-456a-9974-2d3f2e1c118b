from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for User model"""

    list_display = ['username', 'email', 'user_type', 'is_verified', 'kyc_status', 'wallet_balance', 'date_joined']
    list_filter = ['user_type', 'is_verified', 'kyc_status', 'is_active', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name', 'phone_number']
    ordering = ['-date_joined']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Info', {
            'fields': (
                'user_type', 'phone_number', 'profile_picture', 'date_of_birth',
                'address', 'city', 'state', 'country', 'postal_code'
            )
        }),
        ('Verification', {
            'fields': ('is_verified', 'kyc_status', 'kyc_document')
        }),
        ('Wallet', {
            'fields': ('wallet_balance',)
        }),
        ('Preferences', {
            'fields': (
                'enable_notifications', 'enable_whatsapp_notifications',
                'enable_email_notifications', 'preferred_language'
            )
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Additional Info', {
            'fields': ('user_type', 'phone_number', 'email')
        }),
    )


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for UserProfile model"""

    list_display = ['user', 'company_name', 'average_rating', 'total_reviews', 'total_auctions_participated']
    list_filter = ['user__user_type', 'average_rating']
    search_fields = ['user__username', 'user__email', 'company_name', 'business_license']
    readonly_fields = [
        'average_rating', 'total_reviews', 'total_auctions_participated',
        'total_auctions_won', 'total_auctions_created', 'total_amount_spent',
        'total_amount_earned', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('Basic Info', {
            'fields': ('user', 'bio', 'website', 'company_name')
        }),
        ('Business Info', {
            'fields': ('business_license', 'tax_id')
        }),
        ('Statistics', {
            'fields': (
                'average_rating', 'total_reviews', 'total_auctions_participated',
                'total_auctions_won', 'total_auctions_created', 'total_amount_spent',
                'total_amount_earned'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
