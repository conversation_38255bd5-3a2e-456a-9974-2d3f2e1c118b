#!/usr/bin/env python3
"""
Test insufficient balance error message
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceRequest, BrokerQuote
from accounts.models import User
import json


def test_insufficient_balance():
    """Test insufficient balance error message"""
    print("🧪 Testing Insufficient Balance Error")
    print("=" * 50)
    
    # Find a service request with pending quotes
    service_request = ServiceRequest.objects.filter(
        status='pending',
        quotes__status='pending'
    ).first()
    
    if not service_request:
        print("❌ No service request with pending quotes found")
        return
    
    print(f"📋 Service Request: {service_request.id}")
    print(f"   Client: {service_request.client.username}")
    print(f"   Status: {service_request.status}")
    
    # Get a pending quote
    quote = service_request.quotes.filter(status='pending').first()
    if not quote:
        print("❌ No pending quotes found")
        return
    
    print(f"📝 Quote: {quote.id}")
    print(f"   Broker: {quote.broker.username}")
    print(f"   Amount: {quote.amount}")
    
    # Check and modify client wallet balance to be insufficient
    client = service_request.client
    original_balance = client.wallet_balance
    print(f"💰 Original wallet balance: {original_balance}")
    
    # Set balance to be less than quote amount
    insufficient_balance = float(quote.amount) - 1.0
    client.wallet_balance = insufficient_balance
    client.save()
    
    print(f"💰 Set wallet balance to: {insufficient_balance} (insufficient)")
    
    # Create API client and authenticate as client
    api_client = APIClient()
    refresh = RefreshToken.for_user(client)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as client: {client.username}")
    
    # Make API request to select broker (should fail with insufficient balance)
    url = f'/api/broker/requests/{service_request.id}/select-broker/{quote.id}/'
    print(f"\n📤 POST {url}")
    
    response = api_client.post(url, data={})
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    
    if hasattr(response, 'data'):
        print(f"   Data: {response.data}")
        
        # Check specific fields
        if isinstance(response.data, dict):
            print(f"\n🔍 Response Fields:")
            for key, value in response.data.items():
                print(f"   {key}: {value}")
    else:
        print(f"   Content: {response.content.decode()}")
    
    # Restore original balance
    client.wallet_balance = original_balance
    client.save()
    print(f"\n💰 Restored wallet balance to: {original_balance}")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_insufficient_balance()
