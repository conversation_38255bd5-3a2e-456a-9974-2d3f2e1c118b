{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}Invoice {{ invoice.invoice_number }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .invoice-detail {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .invoice-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #007cba;
        padding-bottom: 20px;
    }
    .invoice-title {
        font-size: 2.5em;
        color: #007cba;
        margin: 0;
    }
    .invoice-number {
        font-size: 1.2em;
        color: #666;
        margin: 10px 0;
    }
    .invoice-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }
    .info-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
    }
    .info-section h3 {
        margin: 0 0 15px 0;
        color: #007cba;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
    }
    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }
    .info-label {
        font-weight: bold;
        color: #333;
    }
    .info-value {
        color: #666;
    }
    .auction-details {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
    }
    .auction-details h3 {
        margin: 0 0 15px 0;
        color: #007cba;
    }
    .payment-summary {
        background: #e8f4f8;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
    }
    .payment-summary h3 {
        margin: 0 0 15px 0;
        color: #007cba;
    }
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 5px 0;
    }
    .summary-total {
        border-top: 2px solid #007cba;
        padding-top: 10px;
        font-weight: bold;
        font-size: 1.2em;
    }
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        text-transform: uppercase;
        display: inline-block;
    }
    .status-draft { background: #e9ecef; color: #495057; }
    .status-sent { background: #cce5ff; color: #0066cc; }
    .status-paid { background: #d4edda; color: #155724; }
    .status-overdue { background: #f8d7da; color: #721c24; }
    .status-cancelled { background: #f1f3f4; color: #5f6368; }
    .action-buttons {
        text-align: center;
        margin-top: 30px;
    }
    .btn {
        padding: 12px 24px;
        margin: 0 10px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        font-weight: bold;
    }
    .btn-primary { background: #007cba; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    .btn-secondary { background: #6c757d; color: white; }
    .back-link {
        margin-bottom: 20px;
    }
    .back-link a {
        color: #007cba;
        text-decoration: none;
    }
    .back-link a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="back-link">
    <a href="{% url 'admin:invoice_management' %}">&larr; Back to Invoice Management</a>
</div>

<div class="invoice-detail">
    <!-- Header -->
    <div class="invoice-header">
        <h1 class="invoice-title">Fish Auction App</h1>
        <div class="invoice-number">Invoice #{{ invoice.invoice_number }}</div>
        <div>
            <span class="status-badge status-{{ invoice.status }}">
                {{ invoice.get_status_display }}
            </span>
        </div>
    </div>

    <!-- Invoice Information -->
    <div class="invoice-info">
        <div class="info-section">
            <h3>Invoice Details</h3>
            <div class="info-row">
                <span class="info-label">Issue Date:</span>
                <span class="info-value">{{ invoice.issue_date|date:"F d, Y" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Due Date:</span>
                <span class="info-value">{{ invoice.due_date|date:"F d, Y" }}</span>
            </div>
            {% if invoice.paid_date %}
            <div class="info-row">
                <span class="info-label">Paid Date:</span>
                <span class="info-value">{{ invoice.paid_date|date:"F d, Y H:i" }}</span>
            </div>
            {% endif %}
            <div class="info-row">
                <span class="info-label">Payment Method:</span>
                <span class="info-value">{{ payment.get_payment_method_display }}</span>
            </div>
        </div>

        <div class="info-section">
            <h3>Billing Information</h3>
            <div style="margin-bottom: 15px;">
                <strong>Bill To:</strong><br>
                {{ invoice.buyer_name }}<br>
                {{ invoice.buyer_email }}<br>
                {% if invoice.buyer_address %}{{ invoice.buyer_address }}{% endif %}
            </div>
            <div>
                <strong>Bill From:</strong><br>
                {{ invoice.seller_name }}<br>
                {{ invoice.seller_email }}<br>
                {% if invoice.seller_address %}{{ invoice.seller_address }}{% endif %}
            </div>
        </div>
    </div>

    <!-- Auction Details -->
    <div class="auction-details">
        <h3>Auction Details</h3>
        <div class="info-row">
            <span class="info-label">Title:</span>
            <span class="info-value">{{ auction.title }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Category:</span>
            <span class="info-value">{{ auction.fish_category.name|default:"N/A" }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Type:</span>
            <span class="info-value">{{ auction.fish_type|default:"N/A" }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Weight:</span>
            <span class="info-value">{{ auction.weight }} kg</span>
        </div>
        <div class="info-row">
            <span class="info-label">Quantity:</span>
            <span class="info-value">{{ auction.quantity }}</span>
        </div>
        {% if auction.catch_date %}
        <div class="info-row">
            <span class="info-label">Catch Date:</span>
            <span class="info-value">{{ auction.catch_date|date:"F d, Y" }}</span>
        </div>
        {% endif %}
        {% if auction.catch_location %}
        <div class="info-row">
            <span class="info-label">Catch Location:</span>
            <span class="info-value">{{ auction.catch_location }}</span>
        </div>
        {% endif %}
    </div>

    <!-- Payment Summary -->
    <div class="payment-summary">
        <h3>Payment Summary</h3>
        <div class="summary-row">
            <span>Subtotal:</span>
            <span>${{ invoice.subtotal }}</span>
        </div>
        <div class="summary-row">
            <span>Platform Fee:</span>
            <span>${{ payment.platform_fee }}</span>
        </div>
        {% if invoice.tax_amount > 0 %}
        <div class="summary-row">
            <span>Tax:</span>
            <span>${{ invoice.tax_amount }}</span>
        </div>
        {% endif %}
        <div class="summary-row summary-total">
            <span>Total Amount:</span>
            <span>${{ invoice.total_amount }}</span>
        </div>
        <div class="summary-row">
            <span>Seller Receives:</span>
            <span>${{ payment.seller_amount }}</span>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        {% if invoice.pdf_file %}
            <a href="{% url 'admin:download_invoice' invoice.id %}" class="btn btn-success">Download PDF</a>
        {% else %}
            <button onclick="generatePDF()" class="btn btn-warning">Generate PDF</button>
        {% endif %}
        
        <button onclick="resendWhatsApp()" class="btn btn-primary">Resend WhatsApp</button>
        
        {% if invoice.status != 'paid' %}
            <button onclick="markAsPaid()" class="btn btn-success">Mark as Paid</button>
        {% endif %}
    </div>
</div>

<script>
function generatePDF() {
    if (confirm('Generate PDF for this invoice?')) {
        fetch('{% url "admin:bulk_invoice_actions" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                action: 'regenerate_pdf',
                invoice_ids: ['{{ invoice.id }}']
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('PDF generated successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function resendWhatsApp() {
    if (confirm('Resend WhatsApp notifications for this invoice?')) {
        fetch('{% url "admin:bulk_invoice_actions" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                action: 'resend_whatsapp',
                invoice_ids: ['{{ invoice.id }}']
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('WhatsApp notifications sent successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function markAsPaid() {
    if (confirm('Mark this invoice as paid?')) {
        fetch('{% url "admin:bulk_invoice_actions" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                action: 'mark_paid',
                invoice_ids: ['{{ invoice.id }}']
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Invoice marked as paid!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}
</script>
{% endblock %}
