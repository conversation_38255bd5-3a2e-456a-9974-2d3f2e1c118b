{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block title %}Invoice Management{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .invoice-filters {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .filter-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }
    .filter-group {
        flex: 1;
        min-width: 200px;
    }
    .filter-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #333;
    }
    .filter-group input, .filter-group select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .btn-group {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    .btn-primary { background: #007cba; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: black; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-secondary { background: #6c757d; color: white; }
    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    .invoice-table th, .invoice-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    .invoice-table th {
        background: #f8f9fa;
        font-weight: bold;
    }
    .invoice-table tr:hover {
        background: #f5f5f5;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    .status-draft { background: #e9ecef; color: #495057; }
    .status-sent { background: #cce5ff; color: #0066cc; }
    .status-paid { background: #d4edda; color: #155724; }
    .status-overdue { background: #f8d7da; color: #721c24; }
    .status-cancelled { background: #f1f3f4; color: #5f6368; }
    .pagination {
        display: flex;
        justify-content: center;
        gap: 5px;
        margin: 20px 0;
    }
    .pagination a, .pagination span {
        padding: 8px 12px;
        border: 1px solid #ddd;
        text-decoration: none;
        color: #007cba;
    }
    .pagination .current {
        background: #007cba;
        color: white;
    }
    .stats-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    .stat-card {
        flex: 1;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #007cba;
    }
    .stat-label {
        color: #666;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<h1>Invoice Management</h1>

<!-- Statistics Row -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-number">{{ total_invoices }}</div>
        <div class="stat-label">Total Invoices</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" id="week-count">-</div>
        <div class="stat-label">This Week</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" id="month-count">-</div>
        <div class="stat-label">This Month</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" id="total-revenue">-</div>
        <div class="stat-label">Total Revenue</div>
    </div>
</div>

<!-- Filters -->
<div class="invoice-filters">
    <form method="get" id="filter-form">
        <div class="filter-row">
            <div class="filter-group">
                <label for="search">Search:</label>
                <input type="text" id="search" name="search" value="{{ search_query }}" 
                       placeholder="Invoice number, buyer, seller, or auction title">
            </div>
            <div class="filter-group">
                <label for="seller">Seller:</label>
                <select id="seller" name="seller">
                    <option value="">All Sellers</option>
                    {% for seller in sellers %}
                        <option value="{{ seller }}" {% if seller == seller_filter %}selected{% endif %}>{{ seller }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-group">
                <label for="buyer">Buyer:</label>
                <select id="buyer" name="buyer">
                    <option value="">All Buyers</option>
                    {% for buyer in buyers %}
                        <option value="{{ buyer }}" {% if buyer == buyer_filter %}selected{% endif %}>{{ buyer }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="filter-row">
            <div class="filter-group">
                <label for="status">Status:</label>
                <select id="status" name="status">
                    <option value="">All Statuses</option>
                    {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_code == status_filter %}selected{% endif %}>{{ status_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-group">
                <label for="date_from">Date From:</label>
                <input type="date" id="date_from" name="date_from" value="{{ date_from }}">
            </div>
            <div class="filter-group">
                <label for="date_to">Date To:</label>
                <input type="date" id="date_to" name="date_to" value="{{ date_to }}">
            </div>
            <div class="filter-group" style="display: flex; align-items: end;">
                <button type="submit" class="btn btn-primary">Filter</button>
            </div>
        </div>
    </form>
</div>

<!-- Action Buttons -->
<div class="btn-group">
    <button type="button" class="btn btn-success" onclick="bulkAction('regenerate_pdf')">Regenerate PDFs</button>
    <button type="button" class="btn btn-warning" onclick="bulkAction('mark_paid')">Mark as Paid</button>
    <button type="button" class="btn btn-primary" onclick="bulkAction('resend_whatsapp')">Resend WhatsApp</button>
    <a href="{% url 'admin:export_invoices' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-secondary">Export CSV</a>
</div>

<!-- Invoice Table -->
<table class="invoice-table">
    <thead>
        <tr>
            <th><input type="checkbox" id="select-all"></th>
            <th>Invoice #</th>
            <th>Buyer</th>
            <th>Seller</th>
            <th>Auction</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Issue Date</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for invoice in page_obj %}
        <tr>
            <td><input type="checkbox" class="invoice-checkbox" value="{{ invoice.id }}"></td>
            <td><strong>{{ invoice.invoice_number }}</strong></td>
            <td>{{ invoice.buyer_name }}</td>
            <td>{{ invoice.seller_name }}</td>
            <td>{{ invoice.payment.auction.title|truncatechars:30 }}</td>
            <td>${{ invoice.total_amount }}</td>
            <td>
                <span class="status-badge status-{{ invoice.status }}">
                    {{ invoice.get_status_display }}
                </span>
            </td>
            <td>{{ invoice.issue_date|date:"M d, Y H:i" }}</td>
            <td>
                <a href="{% url 'admin:invoice_detail' invoice.id %}" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px;">View</a>
                {% if invoice.pdf_file %}
                    <a href="{% url 'admin:download_invoice' invoice.id %}" class="btn btn-success" style="font-size: 12px; padding: 4px 8px;">Download</a>
                {% else %}
                    <span style="font-size: 12px; color: #999;">No PDF</span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" style="text-align: center; padding: 40px; color: #999;">
                No invoices found matching your criteria.
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="pagination">
    {% if page_obj.has_previous %}
        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page=1">&laquo; First</a>
        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.previous_page_number }}">Previous</a>
    {% endif %}
    
    <span class="current">
        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
    </span>
    
    {% if page_obj.has_next %}
        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.next_page_number }}">Next</a>
        <a href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
    {% endif %}
</div>
{% endif %}

<script>
// Load statistics
fetch('{% url "admin:invoice_stats" %}')
    .then(response => response.json())
    .then(data => {
        document.getElementById('week-count').textContent = data.this_week;
        document.getElementById('month-count').textContent = data.this_month;
        document.getElementById('total-revenue').textContent = '$' + data.total_revenue.toFixed(2);
    });

// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.invoice-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

// Bulk actions
function bulkAction(action) {
    const selectedIds = Array.from(document.querySelectorAll('.invoice-checkbox:checked'))
                            .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Please select at least one invoice.');
        return;
    }
    
    if (!confirm(`Are you sure you want to ${action.replace('_', ' ')} ${selectedIds.length} invoice(s)?`)) {
        return;
    }
    
    fetch('{% url "admin:bulk_invoice_actions" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            action: action,
            invoice_ids: selectedIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error: ' + error);
    });
}
</script>
{% endblock %}
