#!/usr/bin/env python3
"""
Test broker offer notifications
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.utils import timezone
from datetime import timed<PERSON>ta
from accounts.models import User
from broker_services.models import BrokerService, ServiceRequest, BrokerQuote
from auctions.models import Auction
from notifications.services import NotificationService
from notifications.models import Notification


def create_test_broker_offer():
    """Create a test broker offer to trigger notification"""
    print("🤝 Testing Broker Offer Notifications")
    print("=" * 50)
    
    # Get users
    client = User.objects.filter(user_type='buyer').first()
    broker = User.objects.filter(user_type='broker').first()
    seller = User.objects.filter(user_type='seller').first()
    
    if not client or not broker or not seller:
        print("❌ Need buyer, broker, and seller users")
        return
    
    print(f"👤 Client: {client.username} ({client.phone_number})")
    print(f"🤝 Broker: {broker.username} ({broker.phone_number})")
    print(f"🐟 Seller: {seller.username} ({seller.phone_number})")
    
    # Get an existing broker service
    service = BrokerService.objects.filter(service_type='inspection').first()
    if not service:
        service = BrokerService.objects.create(
            name='Fish Inspection Service',
            name_ar='خدمة فحص الأسماك',
            service_type='inspection',
            description='Professional fish inspection service',
            description_ar='خدمة فحص الأسماك المهنية',
            is_active=True
        )
    
    # Use an existing auction or create a simple one
    auction = Auction.objects.filter(status='live').first()
    if not auction:
        print("❌ No live auctions found, using any auction")
        auction = Auction.objects.first()

    if not auction:
        print("❌ No auctions found in database")
        return
    
    # Create a service request
    service_request, created = ServiceRequest.objects.get_or_create(
        client=client,
        service=service,
        auction=auction,
        defaults={
            'location_description': 'Test Location - Please inspect this fish before I bid',
            'special_instructions': 'Please provide detailed inspection report',
            'status': 'pending'
        }
    )
    
    print(f"📋 Service Request: {service_request.id}")
    
    # Create a broker quote (this should trigger the notification)
    quote, created = BrokerQuote.objects.get_or_create(
        service_request=service_request,
        broker=broker,
        defaults={
            'amount': 45.00,
            'estimated_duration': '2 hours',
            'notes': 'I will provide detailed inspection report with photos',
            'status': 'pending'
        }
    )
    
    if created:
        print(f"✅ Created new broker quote: {quote.id}")
        
        # The notification should be sent automatically via the serializer
        # But let's also test it manually
        service = NotificationService()
        result = service.send_notification(
            client,
            'broker_offer_received',
            {
                'service_request': service_request,
                'broker': broker,
                'quote': quote,
                'user_name': client.first_name or client.username
            },
            channels=['whatsapp', 'in_app']
        )
        
        print(f"📱 Manual notification sent: {len(result)} notifications")
        
    else:
        print(f"ℹ️ Quote already exists: {quote.id}")
    
    # Check recent notifications for the client
    print(f"\n📬 Recent notifications for {client.username}:")
    recent_notifications = Notification.objects.filter(
        recipient=client
    ).order_by('-created_at')[:5]
    
    for notif in recent_notifications:
        status_icon = "✅" if notif.status == 'sent' else "❌" if notif.status == 'failed' else "⏳"
        print(f"   {status_icon} {notif.template.notification_type} - {notif.status}")
        if notif.status == 'failed':
            print(f"      Error: Failed notification")
    
    return quote


def test_broker_offer_acceptance():
    """Test broker offer acceptance notification"""
    print(f"\n✅ Testing Broker Offer Acceptance")
    print("=" * 40)
    
    # Find a pending quote
    quote = BrokerQuote.objects.filter(status='pending').first()
    if not quote:
        print("❌ No pending quotes found")
        return
    
    # Accept the quote (this should trigger acceptance notification)
    quote.status = 'selected'
    quote.save()
    
    # Send acceptance notification manually
    service = NotificationService()
    result = service.send_notification(
        quote.broker,
        'broker_offer_accepted',
        {
            'service_request': quote.service_request,
            'quote': quote,
            'client': quote.service_request.client,
            'user_name': quote.broker.first_name or quote.broker.username
        },
        channels=['whatsapp', 'in_app']
    )
    
    print(f"📱 Acceptance notification sent: {len(result)} notifications")
    
    # Check recent notifications for the broker
    print(f"\n📬 Recent notifications for {quote.broker.username}:")
    recent_notifications = Notification.objects.filter(
        recipient=quote.broker
    ).order_by('-created_at')[:3]
    
    for notif in recent_notifications:
        status_icon = "✅" if notif.status == 'sent' else "❌" if notif.status == 'failed' else "⏳"
        print(f"   {status_icon} {notif.template.notification_type} - {notif.status}")


if __name__ == '__main__':
    quote = create_test_broker_offer()
    if quote:
        test_broker_offer_acceptance()
    
    print(f"\n🎉 Broker offer notification test completed!")
    print(f"📱 Check WhatsApp for messages!")
