#!/bin/bash

# Fix VS Code Flutter Extension Configuration
# Updates VS Code settings to use the correct Flutter SDK path

echo "🔧 VS Code Flutter Configuration Fix"
echo "===================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# VS Code settings paths
VSCODE_USER_SETTINGS="$HOME/Library/Application Support/Code/User/settings.json"
VSCODE_WORKSPACE_SETTINGS=".vscode/settings.json"

# New Flutter SDK path
NEW_FLUTTER_PATH="/Users/<USER>/flutter"

echo -e "${BLUE}🔍 Checking Flutter installation...${NC}"
if [ -d "$NEW_FLUTTER_PATH" ]; then
    echo -e "${GREEN}✅ Flutter found at: $NEW_FLUTTER_PATH${NC}"
else
    echo -e "${RED}❌ Flutter not found at: $NEW_FLUTTER_PATH${NC}"
    exit 1
fi

# Function to update JSON file
update_flutter_path() {
    local file_path="$1"
    local backup_path="${file_path}.backup"
    
    if [ -f "$file_path" ]; then
        echo -e "${BLUE}📝 Updating: $file_path${NC}"
        
        # Create backup
        cp "$file_path" "$backup_path"
        echo -e "${BLUE}💾 Backup created: $backup_path${NC}"
        
        # Update Flutter SDK path using Python
        python3 -c "
import json
import sys

try:
    with open('$file_path', 'r') as f:
        settings = json.load(f)
    
    # Update Flutter paths
    settings['dart.flutterSdkPath'] = '$NEW_FLUTTER_PATH'
    settings['dart.sdkPath'] = '$NEW_FLUTTER_PATH/bin/cache/dart-sdk'
    settings['flutter.sdkPath'] = '$NEW_FLUTTER_PATH'
    
    with open('$file_path', 'w') as f:
        json.dump(settings, f, indent=4)
    
    print('✅ Successfully updated Flutter paths')
except Exception as e:
    print(f'❌ Error updating file: {e}')
    sys.exit(1)
"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Updated Flutter paths in: $file_path${NC}"
        else
            echo -e "${RED}❌ Failed to update: $file_path${NC}"
            # Restore backup
            mv "$backup_path" "$file_path"
        fi
    else
        echo -e "${YELLOW}⚠️ File not found: $file_path${NC}"
        
        # Create new settings file
        echo -e "${BLUE}📝 Creating new settings file...${NC}"
        mkdir -p "$(dirname "$file_path")"
        cat > "$file_path" << EOF
{
    "dart.flutterSdkPath": "$NEW_FLUTTER_PATH",
    "dart.sdkPath": "$NEW_FLUTTER_PATH/bin/cache/dart-sdk",
    "flutter.sdkPath": "$NEW_FLUTTER_PATH"
}
EOF
        echo -e "${GREEN}✅ Created new settings file: $file_path${NC}"
    fi
}

echo -e "${BLUE}🔧 Updating VS Code settings...${NC}"

# Update global VS Code settings
echo -e "${BLUE}1. Updating global VS Code settings...${NC}"
update_flutter_path "$VSCODE_USER_SETTINGS"

# Update workspace settings
echo -e "${BLUE}2. Updating workspace settings...${NC}"
update_flutter_path "$VSCODE_WORKSPACE_SETTINGS"

# Verify Flutter path
echo -e "${BLUE}🧪 Verifying Flutter installation...${NC}"
export PATH="$NEW_FLUTTER_PATH/bin:$PATH"
flutter --version

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Flutter is working correctly${NC}"
else
    echo -e "${RED}❌ Flutter verification failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 VS Code Flutter configuration updated successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "1. Restart VS Code completely"
echo "2. Open the fish_auction_app folder in VS Code"
echo "3. Press Cmd+Shift+P and run 'Flutter: Reload'"
echo "4. Check that Flutter extension shows the correct SDK path"
echo "5. Use F5 or Run → Start Debugging to run the app"
echo ""
echo -e "${YELLOW}💡 VS Code Flutter Extension Commands:${NC}"
echo "• Cmd+Shift+P → 'Flutter: Launch Emulator'"
echo "• Cmd+Shift+P → 'Flutter: Select Device'"
echo "• Cmd+Shift+P → 'Flutter: Hot Reload'"
echo "• F5 → Start Debugging"
echo "• Shift+F5 → Stop Debugging"
echo ""
echo -e "${BLUE}🔍 To verify in VS Code:${NC}"
echo "• Open Command Palette (Cmd+Shift+P)"
echo "• Type 'Flutter: Change SDK'"
echo "• Should show: $NEW_FLUTTER_PATH"
