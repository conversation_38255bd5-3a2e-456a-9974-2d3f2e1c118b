#!/usr/bin/env python3
"""
Fix the Jamin fish auction by changing its status to live
"""

import os
import sys
import django
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction
from django.utils import timezone

def fix_jamin_auction():
    print("🔧 Fixing Jamin Fish Auction")
    print("=" * 30)
    
    # Find the jamin fish auction with failed status
    jamin_auction = Auction.objects.filter(title__icontains='jamin', status='failed').first()
    
    if not jamin_auction:
        print("❌ No failed Jamin fish auction found")
        return
    
    print(f"✅ Found auction: {jamin_auction.title}")
    print(f"📊 Current status: {jamin_auction.status}")
    print(f"⏰ Start time: {jamin_auction.start_time}")
    print(f"⏰ End time: {jamin_auction.end_time}")
    print()
    
    # Update the auction to make it live
    now = timezone.now()
    
    # Set start time to now and end time to 24 hours from now
    jamin_auction.start_time = now
    jamin_auction.end_time = now + timedelta(hours=24)
    jamin_auction.status = 'live'
    
    # Save the changes
    jamin_auction.save()
    
    print("🔧 Applied fixes:")
    print(f"   ✅ Status changed to: {jamin_auction.status}")
    print(f"   ✅ Start time set to: {jamin_auction.start_time}")
    print(f"   ✅ End time set to: {jamin_auction.end_time}")
    print(f"   ✅ Is live property: {jamin_auction.is_live}")
    print()
    
    # Verify the fix
    print("🧪 Verification:")
    visible_to_buyers = jamin_auction.status not in ['draft', 'ended']
    print(f"   ✅ Visible to buyers: {visible_to_buyers}")
    print(f"   ✅ Is live: {jamin_auction.is_live}")
    print(f"   ✅ Time remaining: {jamin_auction.time_remaining}")
    print()
    
    print("🎉 Jamin fish auction is now live and visible to all users!")
    print()
    print("📱 Users should now be able to:")
    print("   - See the auction in the live auctions list")
    print("   - Place bids on the auction")
    print("   - View auction details")
    print()
    print("🔄 Refresh your Flutter app to see the changes")

if __name__ == "__main__":
    fix_jamin_auction()
