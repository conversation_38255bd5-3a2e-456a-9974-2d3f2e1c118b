#!/usr/bin/env python3
"""
Switch Flutter app to use local WebSocket for testing
"""

import re

def switch_to_local_websocket():
    """Switch Flutter app to use local WebSocket"""
    print("🔄 Switching Flutter App to Local WebSocket")
    print("=" * 45)
    
    flutter_constants_file = 'fish_auction_app/lib/constants/app_constants.dart'
    
    try:
        # Read current file
        with open(flutter_constants_file, 'r') as f:
            content = f.read()
        
        # Backup current file
        with open(flutter_constants_file + '.backup', 'w') as f:
            f.write(content)
        print("✅ Created backup of Flutter constants")
        
        # Extract current URLs
        base_url_match = re.search(r"baseUrl = '([^']+)'", content)
        ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
        
        if base_url_match and ws_url_match:
            current_base_url = base_url_match.group(1)
            current_ws_url = ws_url_match.group(1)
            
            print(f"📋 Current URLs:")
            print(f"   API: {current_base_url}")
            print(f"   WebSocket: {current_ws_url}")
            
            # Update to local URLs
            new_content = content
            new_content = re.sub(
                r"baseUrl = '[^']+';",
                "baseUrl = 'http://localhost:8000/api';",
                new_content
            )
            new_content = re.sub(
                r"wsUrl = '[^']+';",
                "wsUrl = 'ws://localhost:8000/ws';",
                new_content
            )
            
            # Write updated file
            with open(flutter_constants_file, 'w') as f:
                f.write(new_content)
            
            print(f"✅ Updated URLs:")
            print(f"   API: http://localhost:8000/api")
            print(f"   WebSocket: ws://localhost:8000/ws")
            
        else:
            print("❌ Could not find URLs in Flutter constants")
            return False
            
    except Exception as e:
        print(f"❌ Error updating Flutter constants: {e}")
        return False
    
    return True

def create_test_instructions():
    """Create testing instructions"""
    print("\n📋 Local WebSocket Testing Instructions")
    print("=" * 40)
    
    print("1. 🚀 Start Flutter App:")
    print("   cd fish_auction_app")
    print("   flutter run -d web-server --web-port 8080")
    print()
    
    print("2. 🌐 Open in Browser:")
    print("   http://localhost:8080")
    print()
    
    print("3. 🔑 Login:")
    print("   Username: testuser")
    print("   Password: testpass123")
    print()
    
    print("4. 🧪 Test Real-time Updates:")
    print("   a. Navigate to 'Jamin fish' auction")
    print("   b. Open browser Developer Tools (F12)")
    print("   c. Go to Network tab, filter by 'WS'")
    print("   d. Check if WebSocket connection is established")
    print("   e. Place a bid or run test script")
    print()
    
    print("5. 🔍 What to Look For:")
    print("   ✅ WebSocket connection in Network tab")
    print("   ✅ Console logs: '📨 WebSocket message received'")
    print("   ✅ Real-time price updates without page refresh")
    print("   ✅ Bid notifications appearing")
    print()
    
    print("6. 🧪 Test Script (run in another terminal):")
    print("   python3 test_websocket_simple.py")
    print("   # This will send test WebSocket messages")

def create_restore_script():
    """Create script to restore ngrok URLs"""
    restore_script = '''#!/usr/bin/env python3
"""
Restore Flutter app to use ngrok WebSocket
"""

import re
import requests

def restore_ngrok_websocket():
    """Restore Flutter app to use ngrok WebSocket"""
    print("🔄 Restoring Flutter App to Ngrok WebSocket")
    print("=" * 45)
    
    # Get current ngrok URL
    try:
        ngrok_response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        ngrok_data = ngrok_response.json()
        ngrok_url = None
        
        for tunnel in ngrok_data.get('tunnels', []):
            if tunnel.get('proto') == 'https':
                ngrok_url = tunnel['public_url']
                break
        
        if not ngrok_url:
            print("❌ No ngrok tunnel found")
            return False
            
        print(f"✅ Found ngrok URL: {ngrok_url}")
        
    except Exception as e:
        print(f"❌ Could not get ngrok URL: {e}")
        return False
    
    flutter_constants_file = 'fish_auction_app/lib/constants/app_constants.dart'
    
    try:
        # Read current file
        with open(flutter_constants_file, 'r') as f:
            content = f.read()
        
        # Update to ngrok URLs
        ngrok_host = ngrok_url.replace('https://', '')
        new_content = content
        new_content = re.sub(
            r"baseUrl = '[^']+';",
            f"baseUrl = 'https://{ngrok_host}/api';",
            new_content
        )
        new_content = re.sub(
            r"wsUrl = '[^']+';",
            f"wsUrl = 'wss://{ngrok_host}/ws';",
            new_content
        )
        
        # Write updated file
        with open(flutter_constants_file, 'w') as f:
            f.write(new_content)
        
        print(f"✅ Restored URLs:")
        print(f"   API: https://{ngrok_host}/api")
        print(f"   WebSocket: wss://{ngrok_host}/ws")
        
        return True
        
    except Exception as e:
        print(f"❌ Error restoring Flutter constants: {e}")
        return False

if __name__ == "__main__":
    restore_ngrok_websocket()
'''
    
    with open('restore_ngrok_websocket.py', 'w') as f:
        f.write(restore_script)
    
    print("\n✅ Created restore_ngrok_websocket.py")
    print("   Run this script to switch back to ngrok URLs")

if __name__ == "__main__":
    if switch_to_local_websocket():
        create_test_instructions()
        create_restore_script()
        
        print("\n🎯 Summary:")
        print("✅ Flutter app switched to local WebSocket")
        print("✅ Backup created: app_constants.dart.backup")
        print("✅ Restore script created: restore_ngrok_websocket.py")
        print()
        print("🚀 Now test the Flutter app locally to verify WebSocket works!")
    else:
        print("❌ Failed to switch to local WebSocket")
