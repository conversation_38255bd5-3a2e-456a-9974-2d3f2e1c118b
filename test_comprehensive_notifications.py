#!/usr/bin/env python3
"""
Comprehensive test script for all notification scenarios in the fish auction app
"""

import os
import django
from decimal import Decimal
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from auctions.models import Auction, Bid, FishCategory
from payments.models import Payment
from delivery.models import Delivery
from broker_services.models import BrokerService, ServiceRequest, BrokerQuote, ServiceExecution
from notifications.models import Notification, NotificationTemplate
from notifications.services import NotificationService
from accounts.models import DocumentVerificationRequest

User = get_user_model()

class NotificationTester:
    def __init__(self):
        self.notification_service = NotificationService()
        self.test_results = []
        
    def log_result(self, test_name, success, message=""):
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append(f"{status} {test_name}: {message}")
        print(f"{status} {test_name}: {message}")
    
    def setup_test_data(self):
        """Create test users and data"""
        print("🔧 Setting up test data...")
        
        # Create test users
        self.seller = User.objects.get_or_create(
            username='test_seller',
            defaults={
                'email': '<EMAIL>',
                'phone_number': '+**********',
                'user_type': 'seller',
                'wallet_balance': Decimal('1000.00')
            }
        )[0]
        
        self.buyer = User.objects.get_or_create(
            username='test_buyer',
            defaults={
                'email': '<EMAIL>',
                'phone_number': '+1234567891',
                'user_type': 'buyer',
                'wallet_balance': Decimal('1000.00')
            }
        )[0]
        
        self.broker = User.objects.get_or_create(
            username='test_broker',
            defaults={
                'email': '<EMAIL>',
                'phone_number': '+1234567892',
                'user_type': 'broker',
                'wallet_balance': Decimal('1000.00')
            }
        )[0]
        
        # Create test category
        self.category = FishCategory.objects.get_or_create(
            name='Test Fish',
            defaults={'name_ar': 'سمك تجريبي'}
        )[0]
        
        print("✅ Test data setup complete")
    
    def test_1_auction_ending_soon(self):
        """Test 1: Auction ending soon notification (1h before)"""
        print("\n📋 Test 1: Auction ending soon notification")
        
        try:
            # Create auction ending in 1 hour
            auction = Auction.objects.create(
                title="Test Auction Ending Soon",
                description="Test auction for ending soon notification",
                seller=self.seller,
                fish_category=self.category,
                starting_price=Decimal('100.00'),
                current_price=Decimal('100.00'),
                bid_increment=Decimal('10.00'),
                status='live',
                auction_type='timed',
                start_time=timezone.now() - timedelta(hours=1),
                end_time=timezone.now() + timedelta(hours=1),  # Ends in 1 hour
                fish_type='Tuna',
                weight=Decimal('10.0'),
                quantity=1,
                catch_date=timezone.now().date(),
                catch_location='Test Location'
            )
            
            # Create a bid from buyer
            bid = Bid.objects.create(
                auction=auction,
                bidder=self.buyer,
                amount=Decimal('150.00')
            )
            auction.current_price = bid.amount
            auction.save()
            
            # Test the notification
            notifications = self.notification_service.send_notification(
                self.buyer,
                'auction_ending_soon',
                {
                    'auction': auction,
                    'user_bid_amount': bid.amount,
                    'time_remaining': auction.end_time - timezone.now()
                },
                channels=['whatsapp', 'in_app']
            )
            
            self.log_result("Auction ending soon notification", 
                          len(notifications) > 0, 
                          f"Sent {len(notifications)} notifications")
            
        except Exception as e:
            self.log_result("Auction ending soon notification", False, str(e))
    
    def test_2_scheduled_auction_started(self):
        """Test 2: Scheduled auction started notification"""
        print("\n📋 Test 2: Scheduled auction started notification")
        
        try:
            # Test the notification
            auction = Auction.objects.create(
                title="Test Scheduled Auction",
                description="Test scheduled auction",
                seller=self.seller,
                fish_fish_category=self.category,
                starting_price=Decimal('200.00',
                catch_date=timezone.now().date(),
                catch_location="Test Location"),
                current_price=Decimal('200.00'),
                bid_increment=Decimal('10.00'),
                status='live',  # Simulating it just started
                auction_type='timed',
                start_time=timezone.now(),
                end_time=timezone.now() + timedelta(hours=2),
                fish_type='Salmon',
                weight=Decimal('5.0'),
                quantity=1,
                catch_date=timezone.now().date(),
                catch_location='Test Location'
            )
            
            notifications = self.notification_service.send_notification(
                self.seller,
                'auction_started',
                {'auction': auction},
                channels=['whatsapp', 'in_app']
            )
            
            self.log_result("Scheduled auction started notification", 
                          len(notifications) > 0, 
                          f"Sent {len(notifications)} notifications")
            
        except Exception as e:
            self.log_result("Scheduled auction started notification", False, str(e))
    
    def test_3_auction_ended_with_winner(self):
        """Test 3: Auction ended with winner notification"""
        print("\n📋 Test 3: Auction ended with winner notification")
        
        try:
            # Create ended auction with winner
            auction = Auction.objects.create(
                title="Test Ended Auction",
                description="Test ended auction",
                seller=self.seller,
                fish_category=self.category,
                starting_price=Decimal('100.00',
                catch_date=timezone.now().date(),
                catch_location="Test Location"),
                current_price=Decimal('250.00'),
                bid_increment=Decimal('10.00'),
                status='ended',
                auction_type='timed',
                start_time=timezone.now() - timedelta(hours=2),
                end_time=timezone.now() - timedelta(minutes=5),
                winner=self.buyer,
                fish_type='Tuna',
                quantity=Decimal('8.0'),
                unit='kg',
                quality_grade='A'
            )
            
            winning_bid = Bid.objects.create(
                auction=auction,
                bidder=self.buyer,
                amount=Decimal('250.00'),
                is_winning_bid=True
            )
            
            notifications = self.notification_service.send_notification(
                self.seller,
                'auction_ended_with_winner',
                {
                    'auction': auction,
                    'winner': self.buyer,
                    'winning_bid': winning_bid
                },
                channels=['whatsapp', 'in_app']
            )
            
            self.log_result("Auction ended with winner notification", 
                          len(notifications) > 0, 
                          f"Sent {len(notifications)} notifications")
            
        except Exception as e:
            self.log_result("Auction ended with winner notification", False, str(e))
    
    def test_4_auction_won_payment_timer(self):
        """Test 4: Auction won with payment timer notification"""
        print("\n📋 Test 4: Auction won with payment timer notification")
        
        try:
            # Create auction with payment deadline
            auction = Auction.objects.create(
                title="Test Won Auction",
                description="Test won auction",
                seller=self.seller,
                fish_category=self.category,
                starting_price=Decimal('100.00',
                catch_date=timezone.now().date(),
                catch_location="Test Location"),
                current_price=Decimal('300.00'),
                bid_increment=Decimal('10.00'),
                status='ended',
                auction_type='timed',
                start_time=timezone.now() - timedelta(hours=2),
                end_time=timezone.now() - timedelta(minutes=5),
                winner=self.buyer,
                payment_deadline=timezone.now() + timedelta(minutes=20),
                fish_type='Salmon',
                quantity=Decimal('12.0'),
                unit='kg',
                quality_grade='A'
            )
            
            winning_bid = Bid.objects.create(
                auction=auction,
                bidder=self.buyer,
                amount=Decimal('300.00'),
                is_winning_bid=True
            )
            
            notifications = self.notification_service.send_notification(
                self.buyer,
                'auction_won',
                {
                    'auction': auction,
                    'winning_bid': winning_bid,
                    'payment_deadline': auction.payment_deadline
                },
                channels=['whatsapp', 'in_app']
            )
            
            self.log_result("Auction won with payment timer notification", 
                          len(notifications) > 0, 
                          f"Sent {len(notifications)} notifications")
            
        except Exception as e:
            self.log_result("Auction won with payment timer notification", False, str(e))

    def test_5_payment_success(self):
        """Test 5: Payment success notifications"""
        print("\n📋 Test 5: Payment success notifications")

        try:
            # Create auction and payment
            auction = Auction.objects.create(
                title="Test Payment Auction",
                description="Test payment auction",
                seller=self.seller,
                fish_category=self.category,
                starting_price=Decimal('100.00',
                catch_date=timezone.now().date(),
                catch_location="Test Location"),
                current_price=Decimal('200.00'),
                bid_increment=Decimal('10.00'),
                status='ended',
                auction_type='timed',
                start_time=timezone.now() - timedelta(hours=2),
                end_time=timezone.now() - timedelta(hours=1),
                winner=self.buyer,
                fish_type='Tuna',
                quantity=Decimal('5.0'),
                unit='kg',
                quality_grade='A'
            )

            payment = Payment.objects.create(
                auction=auction,
                buyer=self.buyer,
                seller=self.seller,
                amount=Decimal('200.00'),
                platform_fee=Decimal('20.00'),
                seller_amount=Decimal('180.00'),
                payment_method='wallet',
                status='completed',
                payment_deadline=timezone.now() + timedelta(minutes=20),
                paid_at=timezone.now()
            )

            # Test buyer notification
            buyer_notifications = self.notification_service.send_notification(
                self.buyer,
                'payment_success',
                {
                    'payment': payment,
                    'auction': auction,
                    'seller': self.seller,
                    'amount': payment.amount,
                },
                channels=['whatsapp', 'in_app']
            )

            # Test seller notification
            seller_notifications = self.notification_service.send_notification(
                self.seller,
                'payment_received',
                {
                    'payment': payment,
                    'auction': auction,
                    'buyer': self.buyer,
                    'amount': payment.amount,
                    'seller_amount': payment.seller_amount,
                },
                channels=['whatsapp', 'in_app']
            )

            total_notifications = len(buyer_notifications) + len(seller_notifications)
            self.log_result("Payment success notifications",
                          total_notifications > 0,
                          f"Sent {total_notifications} notifications (buyer: {len(buyer_notifications)}, seller: {len(seller_notifications)})")

        except Exception as e:
            self.log_result("Payment success notifications", False, str(e))

    def test_6_delivery_status_update(self):
        """Test 6: Delivery status update notifications"""
        print("\n📋 Test 6: Delivery status update notifications")

        try:
            # Create auction and delivery
            auction = Auction.objects.create(
                title="Test Delivery Auction",
                description="Test delivery auction",
                seller=self.seller,
                fish_category=self.category,
                starting_price=Decimal('100.00',
                catch_date=timezone.now().date(),
                catch_location="Test Location"),
                current_price=Decimal('150.00'),
                bid_increment=Decimal('10.00'),
                status='ended',
                auction_type='timed',
                start_time=timezone.now() - timedelta(hours=3),
                end_time=timezone.now() - timedelta(hours=2),
                winner=self.buyer,
                fish_type='Salmon',
                quantity=Decimal('7.0'),
                unit='kg',
                quality_grade='A'
            )

            payment = Payment.objects.create(
                auction=auction,
                buyer=self.buyer,
                seller=self.seller,
                amount=Decimal('150.00'),
                platform_fee=Decimal('15.00'),
                seller_amount=Decimal('135.00'),
                payment_method='wallet',
                status='completed',
                payment_deadline=timezone.now() - timedelta(hours=1),
                paid_at=timezone.now() - timedelta(hours=1)
            )

            delivery = Delivery.objects.create(
                auction=auction,
                payment=payment,
                seller=self.seller,
                buyer=self.buyer,
                pickup_address="Test Pickup Address",
                delivery_address="Test Delivery Address",
                status='in_transit'
            )

            notifications = self.notification_service.send_notification(
                self.buyer,
                'delivery_status_changed',
                {
                    'delivery': delivery,
                    'auction': auction,
                    'status': 'in_transit',
                    'message': 'Your order is now in transit',
                    'status_display': 'In Transit',
                },
                channels=['whatsapp', 'in_app']
            )

            self.log_result("Delivery status update notification",
                          len(notifications) > 0,
                          f"Sent {len(notifications)} notifications")

        except Exception as e:
            self.log_result("Delivery status update notification", False, str(e))

    def test_7_account_activation(self):
        """Test 7: Account activation notification"""
        print("\n📋 Test 7: Account activation notification")

        try:
            # Create a new seller for activation test
            new_seller = User.objects.get_or_create(
                username='test_new_seller',
                defaults={
                    'email': '<EMAIL>',
                    'phone_number': '+**********',
                    'user_type': 'seller',
                    'is_verified': True  # Simulating just got verified
                }
            )[0]

            notifications = self.notification_service.send_notification(
                new_seller,
                'account_activated',
                {'user': new_seller},
                channels=['whatsapp', 'in_app']
            )

            self.log_result("Account activation notification",
                          len(notifications) > 0,
                          f"Sent {len(notifications)} notifications")

        except Exception as e:
            self.log_result("Account activation notification", False, str(e))

    def test_8_broker_offer_received(self):
        """Test 8: Broker offer received notification"""
        print("\n📋 Test 8: Broker offer received notification")

        try:
            # Create broker service
            broker_service = BrokerService.objects.get_or_create(
                name='Inspection Service',
                defaults={
                    'name_ar': 'خدمة المعاينة',
                    'service_type': 'inspection',
                    'description': 'Fish inspection service',
                    'description_ar': 'خدمة معاينة الأسماك'
                }
            )[0]

            # Create auction for service request
            auction = Auction.objects.create(
                title="Test Broker Service Auction",
                description="Test auction for broker service",
                seller=self.seller,
                fish_category=self.category,
                starting_price=Decimal('100.00',
                catch_date=timezone.now().date(),
                catch_location="Test Location"),
                current_price=Decimal('100.00'),
                bid_increment=Decimal('10.00'),
                status='live',
                auction_type='timed',
                start_time=timezone.now(),
                end_time=timezone.now() + timedelta(hours=2),
                fish_type='Tuna',
                quantity=Decimal('10.0'),
                unit='kg',
                quality_grade='A'
            )

            # Create service request
            service_request = ServiceRequest.objects.create(
                client=self.buyer,
                auction=auction,
                service=broker_service,
                location_description="Test location",
                latitude=Decimal('25.276987'),
                longitude=Decimal('55.296249'),
                special_instructions="Test instructions"
            )

            # Create broker quote
            quote = BrokerQuote.objects.create(
                service_request=service_request,
                broker=self.broker,
                amount=Decimal('50.00'),
                estimated_duration="2 hours",
                notes="Professional inspection service"
            )

            notifications = self.notification_service.send_notification(
                self.buyer,
                'broker_offer_received',
                {
                    'service_request': service_request,
                    'broker': self.broker,
                    'quote': quote,
                },
                channels=['whatsapp', 'in_app']
            )

            self.log_result("Broker offer received notification",
                          len(notifications) > 0,
                          f"Sent {len(notifications)} notifications")

        except Exception as e:
            self.log_result("Broker offer received notification", False, str(e))

    def test_9_broker_offer_accepted(self):
        """Test 9: Broker offer accepted notification"""
        print("\n📋 Test 9: Broker offer accepted notification")

        try:
            # Use existing service request and quote from previous test
            service_request = ServiceRequest.objects.filter(client=self.buyer).first()
            if not service_request:
                # Create if doesn't exist
                broker_service = BrokerService.objects.first()
                auction = Auction.objects.filter(seller=self.seller).first()
                service_request = ServiceRequest.objects.create(
                    client=self.buyer,
                    auction=auction,
                    service=broker_service,
                    location_description="Test location",
                    latitude=Decimal('25.276987'),
                    longitude=Decimal('55.296249')
                )

            quote = BrokerQuote.objects.filter(service_request=service_request, broker=self.broker).first()
            if not quote:
                quote = BrokerQuote.objects.create(
                    service_request=service_request,
                    broker=self.broker,
                    amount=Decimal('50.00'),
                    estimated_duration="2 hours"
                )

            # Simulate offer acceptance
            service_request.selected_broker = self.broker
            service_request.selected_quote_amount = quote.amount
            service_request.status = 'broker_selected'
            service_request.save()

            quote.status = 'selected'
            quote.save()

            notifications = self.notification_service.send_notification(
                self.broker,
                'broker_offer_accepted',
                {
                    'service_request': service_request,
                    'quote': quote,
                    'client': self.buyer,
                },
                channels=['whatsapp', 'in_app']
            )

            self.log_result("Broker offer accepted notification",
                          len(notifications) > 0,
                          f"Sent {len(notifications)} notifications")

        except Exception as e:
            self.log_result("Broker offer accepted notification", False, str(e))

    def test_10_broker_service_status_update(self):
        """Test 10: Broker service status update notification"""
        print("\n📋 Test 10: Broker service status update notification")

        try:
            # Create service execution
            service_request = ServiceRequest.objects.filter(client=self.buyer).first()
            if not service_request:
                # Create if doesn't exist
                broker_service = BrokerService.objects.first()
                auction = Auction.objects.filter(seller=self.seller).first()
                service_request = ServiceRequest.objects.create(
                    client=self.buyer,
                    auction=auction,
                    service=broker_service,
                    location_description="Test location"
                )

            service_execution = ServiceExecution.objects.get_or_create(
                service_request=service_request,
                broker=self.broker,
                defaults={
                    'status': 'in_progress',
                    'started_at': timezone.now()
                }
            )[0]

            notifications = self.notification_service.send_notification(
                self.buyer,
                'broker_service_status_update',
                {
                    'service_execution': service_execution,
                    'old_status': 'started',
                    'new_status': 'in_progress',
                },
                channels=['whatsapp', 'in_app']
            )

            self.log_result("Broker service status update notification",
                          len(notifications) > 0,
                          f"Sent {len(notifications)} notifications")

        except Exception as e:
            self.log_result("Broker service status update notification", False, str(e))

    def run_all_tests(self):
        """Run all notification tests"""
        print("🚀 Starting comprehensive notification tests...")
        print("=" * 60)
        
        self.setup_test_data()
        
        # Run all tests
        self.test_1_auction_ending_soon()
        self.test_2_scheduled_auction_started()
        self.test_3_auction_ended_with_winner()
        self.test_4_auction_won_payment_timer()
        self.test_5_payment_success()
        self.test_6_delivery_status_update()
        self.test_7_account_activation()
        self.test_8_broker_offer_received()
        self.test_9_broker_offer_accepted()
        self.test_10_broker_service_status_update()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if "✅ PASS" in result)
        failed = sum(1 for result in self.test_results if "❌ FAIL" in result)
        
        for result in self.test_results:
            print(result)
        
        print(f"\n📈 Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All tests passed!")
        else:
            print("⚠️ Some tests failed. Check the logs above.")
        
        return failed == 0

if __name__ == '__main__':
    tester = NotificationTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
