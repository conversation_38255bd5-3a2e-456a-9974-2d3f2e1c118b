#!/usr/bin/env python3
"""
Comprehensive fix for real-time bidding updates
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json

def diagnose_realtime_issues():
    print("🔍 Diagnosing Real-time Update Issues")
    print("=" * 45)
    
    # 1. Check WebSocket configuration
    print("1. 📡 WebSocket Configuration:")
    
    # Check Django settings
    from django.conf import settings
    print(f"   ASGI Application: {getattr(settings, 'ASGI_APPLICATION', 'Not set')}")
    print(f"   Channel Layers: {getattr(settings, 'CHANNEL_LAYERS', 'Not set')}")
    
    # Check if Redis is working
    channel_layer = get_channel_layer()
    print(f"   Channel Layer: {type(channel_layer).__name__}")
    
    try:
        # Test Redis connection
        async_to_sync(channel_layer.group_send)(
            'test_group',
            {'type': 'test_message', 'message': 'test'}
        )
        print("   ✅ Redis connection working")
    except Exception as e:
        print(f"   ❌ Redis connection failed: {e}")
    
    print()
    
    # 2. Check auction and bid data
    print("2. 🏛️ Auction Data:")
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if auction:
        print(f"   ✅ Live auction found: {auction.title}")
        print(f"   📊 Current price: ${auction.current_price}")
        print(f"   📊 Total bids: {auction.total_bids}")
        print(f"   ⏰ Time remaining: {auction.time_remaining}")
        
        # Check recent bids
        recent_bids = Bid.objects.filter(auction=auction).order_by('-timestamp')[:5]
        print(f"   📋 Recent bids ({len(recent_bids)}):")
        for bid in recent_bids:
            print(f"      ${bid.amount} by {bid.bidder.username} ({bid.bid_type}) at {bid.timestamp}")
    else:
        print("   ❌ No live auction found")
    
    print()
    
    # 3. Check Flutter WebSocket URL
    print("3. 📱 Flutter WebSocket Configuration:")
    try:
        with open('fish_auction_app/lib/constants/app_constants.dart', 'r') as f:
            content = f.read()
            import re
            
            # Extract WebSocket URL
            ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
            if ws_url_match:
                flutter_ws_url = ws_url_match.group(1)
                print(f"   📱 Flutter WebSocket URL: {flutter_ws_url}")
                
                # Check URL format
                if flutter_ws_url.startswith('wss://') and 'ngrok-free.app' in flutter_ws_url:
                    print("   ✅ WebSocket URL format looks correct")
                else:
                    print("   ⚠️ WebSocket URL format might need checking")
            else:
                print("   ❌ WebSocket URL not found in Flutter constants")
                
    except Exception as e:
        print(f"   ❌ Error reading Flutter constants: {e}")
    
    print()
    
    # 4. Test WebSocket message sending
    print("4. 🧪 Testing WebSocket Message Sending:")
    if auction:
        try:
            # Send a test bid update
            test_bid_data = {
                'auction_id': auction.id,
                'bid_id': 999,
                'amount': str(auction.current_price + 1),
                'bidder': 'test_user',
                'timestamp': '2025-06-22T19:30:00Z',
                'current_price': str(auction.current_price + 1),
                'total_bids': auction.total_bids + 1,
                'bid_type': 'test'
            }
            
            async_to_sync(channel_layer.group_send)(
                f'auction_{auction.id}',
                {
                    'type': 'bid_update',
                    'bid_data': test_bid_data
                }
            )
            print("   ✅ Test WebSocket message sent successfully")
            print(f"   📤 Sent to group: auction_{auction.id}")
            print(f"   📋 Message data: {json.dumps(test_bid_data, indent=6)}")
            
        except Exception as e:
            print(f"   ❌ Error sending test message: {e}")
    
    print()
    
    # 5. Provide solutions
    print("5. 🔧 Potential Solutions:")
    print()
    
    print("   A. WebSocket Connection Issues:")
    print("      - ngrok free tier might have WebSocket limitations")
    print("      - Try using ngrok Pro or test locally")
    print("      - Check browser console for WebSocket errors")
    print()
    
    print("   B. Flutter App Issues:")
    print("      - Ensure WebSocket service is properly initialized")
    print("      - Check if auction detail screen is listening to updates")
    print("      - Verify setState is called on bid updates")
    print()
    
    print("   C. Immediate Fixes to Try:")
    print("      1. Add more logging to Flutter WebSocket service")
    print("      2. Implement fallback polling for bid updates")
    print("      3. Force refresh auction data after placing bids")
    print("      4. Check if WebSocket reconnection is working")
    print()
    
    print("6. 🚀 Quick Fix Implementation:")
    print("   I'll create a fallback mechanism that polls for updates")
    print("   when WebSocket updates aren't received.")

def create_fallback_polling_fix():
    print()
    print("🔧 Creating Fallback Polling Fix...")
    
    # This will be implemented in the Flutter app
    fallback_code = '''
// Add this to auction_detail_screen.dart

Timer? _fallbackTimer;

void _startFallbackPolling() {
  _fallbackTimer = Timer.periodic(Duration(seconds: 3), (timer) {
    if (mounted && _currentAuction.isLive) {
      _refreshAuctionData();
    }
  });
}

void _stopFallbackPolling() {
  _fallbackTimer?.cancel();
  _fallbackTimer = null;
}

Future<void> _refreshAuctionData() async {
  try {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadAuctionDetails(_currentAuction.id);
    
    if (mounted) {
      setState(() {
        _currentAuction = auctionProvider.selectedAuction ?? _currentAuction;
      });
    }
  } catch (e) {
    print('Error refreshing auction data: $e');
  }
}

// Call _startFallbackPolling() in initState()
// Call _stopFallbackPolling() in dispose()
'''
    
    print("✅ Fallback polling code generated")
    print("📋 This code should be added to the auction detail screen")
    print("   to poll for updates every 3 seconds as a backup")

if __name__ == "__main__":
    diagnose_realtime_issues()
    create_fallback_polling_fix()
