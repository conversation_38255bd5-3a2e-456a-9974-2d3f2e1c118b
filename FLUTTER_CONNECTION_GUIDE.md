# Flutter App Connection Guide

This guide helps you connect your Flutter app to the Django backend through ngrok.

## 🚀 Quick Start

### 1. Start Backend Services
```bash
sh quick_start_macos.sh
```

The script will automatically:
- ✅ Start Django server with WebSocket support
- ✅ Start Celery workers for background tasks
- ✅ Start ngrok tunnel
- ✅ Update Flutter app configuration with new ngrok URL
- ✅ Update Django ALLOWED_HOSTS

### 2. Start Flutter App
```bash
cd fish_auction_app
flutter pub get
flutter run -d web-server --web-port 8080
```

### 3. Test Login
- **Username**: `testuser`
- **Password**: `testpass123`

## 🔍 Troubleshooting

### Issue: "Unable to login" or "Connection failed"

#### Check 1: Verify ngrok URL is updated
```bash
python3 test_flutter_connection.py
```

This will test:
- ✅ Basic connectivity
- ✅ Login endpoint
- ✅ Authenticated API calls
- ✅ User profile endpoint

#### Check 2: Manual URL update (if needed)
If the quick start script didn't update the Flutter app:

1. **Get current ngrok URL:**
   ```bash
   curl -s http://localhost:4040/api/tunnels | python3 -c "
   import sys, json
   data = json.load(sys.stdin)
   for tunnel in data.get('tunnels', []):
       if tunnel.get('proto') == 'https':
           print(tunnel['public_url'])
           break
   "
   ```

2. **Update Flutter constants manually:**
   Edit `fish_auction_app/lib/constants/app_constants.dart`:
   ```dart
   static const String baseUrl = 'https://YOUR-NGROK-URL.ngrok-free.app/api';
   static const String wsUrl = 'wss://YOUR-NGROK-URL.ngrok-free.app/ws';
   ```

3. **Update Django ALLOWED_HOSTS:**
   Edit `fish_auction/settings.py`:
   ```python
   ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', 'YOUR-NGROK-URL.ngrok-free.app', '*']
   ```

#### Check 3: Verify services are running
```bash
# Check Django
curl -s http://localhost:8000/admin/ | head -5

# Check ngrok
curl -s http://localhost:4040/api/tunnels

# Check processes
ps aux | grep -E "(python|celery|ngrok)"
```

### Issue: "ngrok URL changes frequently"

ngrok free tier generates new URLs each time. Solutions:

1. **Use the quick start script** - it auto-updates URLs
2. **Use the update script**:
   ```bash
   ./update_ngrok_url.sh
   ```
3. **Get ngrok Pro** for persistent URLs

### Issue: "WebSocket connection failed"

1. **Check WebSocket URL** in Flutter constants
2. **Verify Django is using Daphne** (not runserver)
3. **Check Redis is running**:
   ```bash
   redis-cli ping
   ```

### Issue: "Auto-bidding not working"

1. **Check Celery workers**:
   ```bash
   tail -f celery_worker.log
   ```

2. **Check Celery beat**:
   ```bash
   tail -f celery_beat.log
   ```

3. **Test auto-bidding manually**:
   ```bash
   python3 test_auto_bidding_fix.py
   ```

## 📱 Flutter Development Tips

### Hot Reload
After updating API URLs, restart Flutter app:
```bash
# Stop current app (Ctrl+C)
flutter run -d web-server --web-port 8080
```

### Debug Network Issues
Add to Flutter app for debugging:
```dart
// In your API service
print('Making request to: $url');
print('Response: ${response.body}');
```

### Test on Physical Device
For iOS/Android testing, use your computer's IP:
```dart
static const String baseUrl = 'http://YOUR-COMPUTER-IP:8000/api';
```

## 🔧 Advanced Configuration

### Custom ngrok Domain
If you have ngrok Pro:
```bash
ngrok http 8000 --domain=your-domain.ngrok.app
```

### Environment-based URLs
Create different configurations:
```dart
class AppConstants {
  static const bool isDevelopment = true;
  
  static const String baseUrl = isDevelopment 
    ? 'https://your-ngrok-url.ngrok-free.app/api'
    : 'https://your-production-domain.com/api';
}
```

### CORS Issues
If you encounter CORS errors, verify in `fish_auction/settings.py`:
```python
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
```

## 📊 Monitoring

### Check Logs
```bash
# Django logs
tail -f django.log

# Celery worker logs
tail -f celery_worker.log

# Celery beat logs
tail -f celery_beat.log

# ngrok logs
tail -f ngrok.log
```

### ngrok Dashboard
Visit: http://localhost:4040

### Django Admin
Visit: https://your-ngrok-url.ngrok-free.app/admin/

## 🆘 Emergency Reset

If everything breaks:

1. **Stop all services**: Ctrl+C in terminal
2. **Kill remaining processes**:
   ```bash
   pkill -f "python.*manage.py"
   pkill -f "celery"
   pkill -f "ngrok"
   ```
3. **Restart**: `sh quick_start_macos.sh`
4. **Test connection**: `python3 test_flutter_connection.py`

---

🐟 **Happy coding!** Your Fish Auction app should now connect seamlessly!
