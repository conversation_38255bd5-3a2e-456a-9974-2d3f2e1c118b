#!/usr/bin/env python3
"""
Test the real-time update fix by creating bids and monitoring updates
"""

import os
import sys
import django
import time
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

def test_realtime_fix():
    print("🧪 Testing Real-time Update Fix")
    print("=" * 35)
    
    # Find the Jamin fish auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live Jamin fish auction found")
        return
    
    print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
    print(f"📊 Current price: ${auction.current_price}")
    print(f"📊 Total bids: {auction.total_bids}")
    print()
    
    # Get test users
    buyers = User.objects.filter(user_type='buyer')[:3]
    if len(buyers) < 2:
        print("❌ Need at least 2 buyers for testing")
        return
    
    print("👥 Test buyers:")
    for buyer in buyers:
        print(f"   {buyer.username}")
    print()
    
    # Test sequence
    print("🧪 Testing Real-time Update Sequence:")
    print("1. Creating manual bids")
    print("2. Sending WebSocket updates")
    print("3. Monitoring Flutter app response")
    print()
    
    channel_layer = get_channel_layer()
    
    for i, buyer in enumerate(buyers[:2]):  # Test with 2 buyers
        try:
            # Create bid
            new_amount = auction.current_price + auction.bid_increment
            
            bid = Bid.objects.create(
                auction=auction,
                bidder=buyer,
                amount=new_amount,
                bid_type='manual'
            )
            
            # Update auction
            auction.current_price = new_amount
            auction.total_bids += 1
            auction.save()
            
            print(f"✅ Bid #{i+1}: ${bid.amount} by {buyer.username}")
            
            # Send WebSocket update
            bid_data = {
                'auction_id': auction.id,
                'bid_id': bid.id,
                'amount': str(bid.amount),
                'bidder': bid.bidder.username,
                'timestamp': bid.timestamp.isoformat(),
                'current_price': str(auction.current_price),
                'total_bids': auction.total_bids,
                'bid_type': 'manual'
            }
            
            async_to_sync(channel_layer.group_send)(
                f'auction_{auction.id}',
                {
                    'type': 'bid_update',
                    'bid_data': bid_data
                }
            )
            
            print(f"📤 WebSocket update sent for bid #{i+1}")
            print(f"   Group: auction_{auction.id}")
            print(f"   Data: {bid_data}")
            print()
            
            # Wait between bids
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ Error creating bid #{i+1}: {e}")
    
    print("🔍 What to check in Flutter app:")
    print("1. Open auction detail screen for 'Jamin fish'")
    print("2. Watch for real-time price updates")
    print("3. Check browser console for WebSocket logs")
    print("4. Verify fallback polling is working (every 3 seconds)")
    print()
    
    print("📱 Expected behavior:")
    print("✅ Price should update immediately (WebSocket)")
    print("✅ OR price should update within 3 seconds (fallback)")
    print("✅ Bid count should increase")
    print("✅ Console should show WebSocket messages or polling")
    print()
    
    print("🔧 If still not working:")
    print("1. Check browser console for errors")
    print("2. Verify ngrok WebSocket support")
    print("3. Test locally without ngrok")
    print("4. Check if fallback polling logs appear")
    
    # Final status
    auction.refresh_from_db()
    print()
    print(f"📊 Final auction status:")
    print(f"   Current price: ${auction.current_price}")
    print(f"   Total bids: {auction.total_bids}")
    print(f"   Last updated: {auction.updated_at}")

if __name__ == "__main__":
    test_realtime_fix()
