#!/usr/bin/env python3
"""
Test script to debug notification issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from accounts.models import User
from auctions.models import Auction, Bid
from notifications.models import Notification, NotificationTemplate
from notifications.services import NotificationService
from notifications.tasks import send_auction_ending_reminders
from auctions.tasks import end_expired_auctions


def test_notification_system():
    """Test the notification system comprehensively"""
    print("🧪 Testing Notification System")
    print("=" * 50)
    
    # 1. Check notification templates
    print("\n📋 1. Checking Notification Templates:")
    templates = NotificationTemplate.objects.all()
    for template in templates:
        print(f"   ✅ {template.notification_type}: {template.name}")
    
    if templates.count() == 0:
        print("   ❌ No notification templates found!")
        return False
    
    # 2. Check UltraMsg configuration
    print("\n📱 2. Checking UltraMsg Configuration:")
    from django.conf import settings
    instance_id = getattr(settings, 'ULTRAMSG_INSTANCE_ID', None)
    token = getattr(settings, 'ULTRAMSG_TOKEN', None)
    
    print(f"   Instance ID: {instance_id or 'NOT SET'}")
    print(f"   Token: {'SET' if token else 'NOT SET'}")
    print(f"   WhatsApp Enabled: {getattr(settings, 'WHATSAPP_ENABLED', False)}")
    
    if not instance_id or not token:
        print("   ❌ UltraMsg not properly configured!")
        return False
    
    # 3. Test user phone numbers
    print("\n👥 3. Checking User Phone Numbers:")
    users = User.objects.filter(phone_number__isnull=False).exclude(phone_number='')
    for user in users[:5]:  # Show first 5 users
        print(f"   📞 {user.username}: {user.phone_number}")
    
    if users.count() == 0:
        print("   ❌ No users with phone numbers found!")
        return False
    
    # 4. Test notification sending
    print("\n📤 4. Testing Notification Sending:")
    test_user = users.first()
    service = NotificationService()
    
    try:
        result = service.send_notification(
            test_user,
            'auction_won',
            {
                'auction': {
                    'title': 'Test Auction',
                    'current_price': 100,
                    'payment_deadline': timezone.now() + timedelta(minutes=20)
                }
            },
            channels=['whatsapp', 'in_app']
        )
        print(f"   ✅ Notification sent: {len(result)} notifications created")
        
        # Check the latest notification
        latest = Notification.objects.filter(recipient=test_user).order_by('-created_at').first()
        if latest:
            print(f"   📨 Latest notification status: {latest.status}")
            print(f"   📝 Message preview: {latest.message[:50]}...")
        
    except Exception as e:
        print(f"   ❌ Failed to send notification: {e}")
        return False
    
    # 5. Check live auctions
    print("\n🎣 5. Checking Live Auctions:")
    live_auctions = Auction.objects.filter(status='live')
    print(f"   📊 Live auctions: {live_auctions.count()}")
    
    for auction in live_auctions[:3]:  # Show first 3
        bids_count = auction.bids.count()
        print(f"   🐟 {auction.title}: {bids_count} bids, ends {auction.end_time}")
    
    # 6. Check auctions ending soon
    print("\n⏰ 6. Checking Auctions Ending Soon:")
    now = timezone.now()
    ending_soon = now + timedelta(hours=1)
    
    soon_auctions = Auction.objects.filter(
        status='live',
        end_time__lte=ending_soon,
        end_time__gt=now
    )
    print(f"   ⏳ Auctions ending in 1 hour: {soon_auctions.count()}")
    
    # 7. Test auction ending reminders
    print("\n📢 7. Testing Auction Ending Reminders:")
    try:
        result = send_auction_ending_reminders()
        print(f"   ✅ Reminder task result: {result}")
    except Exception as e:
        print(f"   ❌ Failed to send reminders: {e}")
    
    # 8. Check recent notifications
    print("\n📬 8. Recent Notifications (last 10):")
    recent_notifications = Notification.objects.order_by('-created_at')[:10]
    
    for notif in recent_notifications:
        status_icon = "✅" if notif.status == 'sent' else "❌" if notif.status == 'failed' else "⏳"
        print(f"   {status_icon} {notif.recipient.username} - {notif.template.notification_type} - {notif.status}")
    
    print("\n🎉 Notification system test completed!")
    return True


def test_broker_notifications():
    """Test broker-related notifications"""
    print("\n🤝 Testing Broker Notifications:")
    
    from broker_services.models import ServiceExecution
    
    # Check completed service executions
    completed_executions = ServiceExecution.objects.filter(status='completed')
    print(f"   📊 Completed service executions: {completed_executions.count()}")
    
    for execution in completed_executions[:3]:
        print(f"   🔧 Service: {execution.service_request.service.name_ar}")
        print(f"      Client: {execution.service_request.client.username}")
        print(f"      Broker: {execution.broker.username}")
        print(f"      Report images: {len(execution.report_images)}")


if __name__ == '__main__':
    print("🚀 Starting Notification Debug Test")
    print("=" * 60)
    
    success = test_notification_system()
    test_broker_notifications()
    
    if success:
        print("\n✅ All tests passed! Notification system is working.")
    else:
        print("\n❌ Some tests failed. Check the issues above.")
