# Fish Auction App - Development Requirements
# Simplified requirements for local development (no PostgreSQL)

# Core Django
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-environ==0.11.2

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-oauth-toolkit==1.7.1
cryptography==41.0.7

# Real-time & WebSockets
channels==4.0.0
channels-redis==4.1.0
daphne==4.0.0

# Payment Integration
stripe==7.8.0

# Notifications
twilio==8.10.0

# Background Tasks
celery==5.3.4
redis==5.0.1

# File Handling
Pillow==10.1.0
python-magic==0.4.27

# API Documentation
drf-spectacular==0.26.5

# Development Tools
django-debug-toolbar==4.2.0

# Utilities
python-dateutil==2.8.2
requests==2.31.0

# Note: Removed psycopg2-binary for development
# Use SQLite database for local development
# For production, install: psycopg2-binary==2.9.7
