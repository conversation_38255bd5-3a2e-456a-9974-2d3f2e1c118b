#!/usr/bin/env python3
"""
Script to create notification templates for all notification scenarios
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from notifications.models import NotificationTemplate

def create_notification_templates():
    """Create all notification templates"""
    
    templates = [
        # 1. Auction ending soon (1h before) - for bidders
        {
            'name': 'Auction Ending Soon - Bidder Alert',
            'notification_type': 'auction_ending_soon',
            'email_subject': 'Auction Ending Soon - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

The auction "{{auction.title}}" that you have bid on is ending in 1 hour!

Current Price: ${{auction.current_price}}
Your Bid: ${{user_bid_amount}}
Ends At: {{auction.end_time}}

Don't miss out - place your final bid now!

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🐟 *Auction Ending Soon!*

"{{auction.title}}" ends in 1 hour!

💰 Current Price: ${{auction.current_price}}
🎯 Your Bid: ${{user_bid_amount}}
⏰ Ends: {{auction.end_time}}

Place your final bid now! 🏃‍♂️''',
            'push_title': 'Auction Ending Soon!',
            'push_body': '{{auction.title}} ends in 1 hour. Current price: ${{auction.current_price}}'
        },
        
        # 2. Scheduled auction started - for seller
        {
            'name': 'Scheduled Auction Started - Seller',
            'notification_type': 'auction_started',
            'email_subject': 'Your Auction is Now Live - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

Great news! Your scheduled auction "{{auction.title}}" is now live and accepting bids.

Starting Price: ${{auction.starting_price}}
Ends At: {{auction.end_time}}

Monitor your auction and watch the bids come in!

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🎉 *Your Auction is Live!*

"{{auction.title}}" is now accepting bids!

💰 Starting Price: ${{auction.starting_price}}
⏰ Ends: {{auction.end_time}}

Good luck with your sale! 🍀''',
            'push_title': 'Auction Started!',
            'push_body': 'Your auction {{auction.title}} is now live and accepting bids'
        },
        
        # 3. Auction ended with winner - for seller
        {
            'name': 'Auction Ended with Winner - Seller',
            'notification_type': 'auction_ended_with_winner',
            'email_subject': 'Auction Sold - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

Congratulations! Your auction "{{auction.title}}" has ended successfully.

Winner: {{winner.get_full_name}} ({{winner.username}})
Final Price: ${{auction.current_price}}
Total Bids: {{auction.total_bids}}

The winner has 20 minutes to complete payment. You'll be notified once payment is received.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🎉 *Auction Sold!*

"{{auction.title}}" has ended!

🏆 Winner: {{winner.get_full_name}}
💰 Final Price: ${{auction.current_price}}
📊 Total Bids: {{auction.total_bids}}

Winner has 20 minutes to pay. You'll be notified when payment is received! 💳''',
            'push_title': 'Auction Sold!',
            'push_body': '{{auction.title}} sold to {{winner.get_full_name}} for ${{auction.current_price}}'
        },
        
        # 4. Auction won - for buyer with payment timer
        {
            'name': 'Auction Won - Buyer Payment Required',
            'notification_type': 'auction_won',
            'email_subject': 'Congratulations! You Won - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

Congratulations! You have won the auction for "{{auction.title}}".

Your Winning Bid: ${{winning_bid.amount}}
Payment Deadline: {{auction.payment_deadline}}

⚠️ IMPORTANT: You have 20 minutes to complete payment or the auction will be reassigned to the next highest bidder.

Please complete your payment immediately.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🎉 *Congratulations! You Won!*

"{{auction.title}}"

💰 Your Winning Bid: ${{winning_bid.amount}}
⏰ Payment Deadline: {{auction.payment_deadline}}

⚠️ *URGENT: Pay within 20 minutes or lose the auction!*

Complete payment now! 💳''',
            'push_title': 'You Won! Payment Required',
            'push_body': 'You won {{auction.title}} for ${{winning_bid.amount}}. Pay within 20 minutes!'
        },
        
        # 5. Payment success - for buyer
        {
            'name': 'Payment Success - Buyer',
            'notification_type': 'payment_success',
            'email_subject': 'Payment Confirmed - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

Your payment for "{{auction.title}}" has been successfully processed.

Amount Paid: ${{payment.amount}}
Transaction ID: {{payment.id}}
Seller: {{auction.seller.get_full_name}}

The seller will now prepare your fish for delivery. You'll receive updates on the delivery status.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''✅ *Payment Successful!*

"{{auction.title}}"

💳 Amount: ${{payment.amount}}
🆔 Transaction: {{payment.id}}
👤 Seller: {{auction.seller.get_full_name}}

Your fish is being prepared for delivery! 📦''',
            'push_title': 'Payment Successful!',
            'push_body': 'Payment confirmed for {{auction.title}}. Delivery preparation started.'
        },

        # 6. Payment success - for seller
        {
            'name': 'Payment Received - Seller',
            'notification_type': 'payment_received',
            'email_subject': 'Payment Received - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

Great news! Payment has been received for your auction "{{auction.title}}".

Buyer: {{payment.buyer.get_full_name}}
Amount Received: ${{payment.seller_amount}} (after platform fee)
Transaction ID: {{payment.id}}

Please prepare the fish for delivery and update the delivery status in the app.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''💰 *Payment Received!*

"{{auction.title}}"

👤 Buyer: {{payment.buyer.get_full_name}}
💵 Amount: ${{payment.seller_amount}}
🆔 Transaction: {{payment.id}}

Please prepare fish for delivery! 📦''',
            'push_title': 'Payment Received!',
            'push_body': 'Payment received for {{auction.title}}. Prepare for delivery.'
        },

        # 7. Delivery status update - for buyer
        {
            'name': 'Delivery Status Update - Buyer',
            'notification_type': 'delivery_status_changed',
            'email_subject': 'Delivery Update - {{auction.title}}',
            'email_body': '''
Dear {{user_name}},

Your delivery status has been updated for "{{auction.title}}".

New Status: {{delivery.get_status_display}}
{% if delivery.tracking_number %}Tracking Number: {{delivery.tracking_number}}{% endif %}
{% if delivery.estimated_delivery_time %}Estimated Delivery: {{delivery.estimated_delivery_time}}{% endif %}

You can track your delivery in the app.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''📦 *Delivery Update*

"{{auction.title}}"

📋 Status: {{delivery.get_status_display}}
{% if delivery.tracking_number %}🔢 Tracking: {{delivery.tracking_number}}{% endif %}
{% if delivery.estimated_delivery_time %}⏰ ETA: {{delivery.estimated_delivery_time}}{% endif %}

Track in app for more details! 📱''',
            'push_title': 'Delivery Update',
            'push_body': '{{auction.title}} delivery status: {{delivery.get_status_display}}'
        },

        # 8. Account activated - for seller
        {
            'name': 'Account Activated - Seller',
            'notification_type': 'account_activated',
            'email_subject': 'Account Approved - Welcome to Fish Auction!',
            'email_body': '''
Dear {{user_name}},

Congratulations! Your seller account has been approved and activated.

You can now:
- Create and manage auctions
- Sell your fish to buyers
- Access seller dashboard
- Manage deliveries

Start creating your first auction today!

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🎉 *Account Approved!*

Welcome to Fish Auction, {{user_name}}!

✅ Your seller account is now active
🐟 Start creating auctions
💰 Begin selling your fish
📊 Access seller dashboard

Ready to start selling! 🚀''',
            'push_title': 'Account Approved!',
            'push_body': 'Your seller account has been activated. Start creating auctions!'
        },

        # 9. Broker offer received - for user
        {
            'name': 'Broker Offer Received - User',
            'notification_type': 'broker_offer_received',
            'email_subject': 'Broker Offer Received - {{service_request.service.name}}',
            'email_body': '''
Dear {{user_name}},

You have received a new broker offer for your service request.

Service: {{service_request.service.name_ar}}
Auction: {{service_request.auction.title}}
Broker: {{broker.get_full_name}}
Quoted Amount: ${{quote.quoted_amount}}

Service Details:
{{quote.service_description}}

You can review and accept this offer in the app.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🤝 *New Broker Offer!*

Service: {{service_request.service.name_ar}}
Auction: {{service_request.auction.title}}

👤 Broker: {{broker.get_full_name}}
💰 Quote: ${{quote.quoted_amount}}

📋 Details: {{quote.service_description}}

Review offer in app! 📱''',
            'push_title': 'New Broker Offer',
            'push_body': '{{broker.get_full_name}} offered ${{quote.quoted_amount}} for {{service_request.service.name_ar}}'
        },

        # 10. Broker offer accepted - for broker
        {
            'name': 'Broker Offer Accepted - Broker',
            'notification_type': 'broker_offer_accepted',
            'email_subject': 'Offer Accepted - {{service_request.service.name}}',
            'email_body': '''
Dear {{user_name}},

Great news! Your broker offer has been accepted.

Service: {{service_request.service.name_ar}}
Client: {{service_request.client.get_full_name}}
Auction: {{service_request.auction.title}}
Your Quote: ${{service_request.selected_quote_amount}}

The client has selected you as their broker. Payment is being held and will be released upon service completion.

Please contact the client to coordinate the service.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🎉 *Offer Accepted!*

Service: {{service_request.service.name_ar}}
👤 Client: {{service_request.client.get_full_name}}
🐟 Auction: {{service_request.auction.title}}
💰 Amount: ${{service_request.selected_quote_amount}}

💳 Payment held - released on completion
📞 Contact client to coordinate

Start your service! 🚀''',
            'push_title': 'Offer Accepted!',
            'push_body': 'Your offer for {{service_request.service.name_ar}} was accepted by {{service_request.client.get_full_name}}'
        },

        # 11. Broker service status update - for user
        {
            'name': 'Broker Service Status Update - User',
            'notification_type': 'broker_service_status_update',
            'email_subject': 'Service Update - {{service_execution.service_request.service.name}}',
            'email_body': '''
Dear {{user_name}},

Your broker service status has been updated.

Service: {{service_execution.service_request.service.name_ar}}
Broker: {{service_execution.broker.get_full_name}}
Status: {{service_execution.get_status_display}}

{% if service_execution.progress_notes %}
Progress Notes:
{{service_execution.progress_notes}}
{% endif %}

You can view full details in the app.

Best regards,
Fish Auction Team
            ''',
            'whatsapp_message': '''🔄 *Service Update*

Service: {{service_execution.service_request.service.name_ar}}
👤 Broker: {{service_execution.broker.get_full_name}}
📋 Status: {{service_execution.get_status_display}}

{% if service_execution.progress_notes %}
📝 Notes: {{service_execution.progress_notes}}
{% endif %}

Check app for details! 📱''',
            'push_title': 'Service Update',
            'push_body': '{{service_execution.service_request.service.name_ar}} status: {{service_execution.get_status_display}}'
        }
    ]

    # Clean up existing templates first
    print("🧹 Cleaning up existing templates...")
    NotificationTemplate.objects.all().delete()

    # Create templates
    created_count = 0
    for template_data in templates:
        template = NotificationTemplate.objects.create(**template_data)
        created_count += 1
        print(f"✅ Created template: {template.name}")

    print(f"\n🎉 Created {created_count} notification templates")
    return created_count

if __name__ == '__main__':
    create_notification_templates()
