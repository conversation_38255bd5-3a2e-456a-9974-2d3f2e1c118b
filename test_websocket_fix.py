#!/usr/bin/env python3
"""
Test the WebSocket null fix by sending test messages
"""

import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json

def test_websocket_after_fix():
    """Test WebSocket functionality after null fix"""
    print("🧪 Testing WebSocket After Null Fix")
    print("=" * 40)
    
    # Find auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live Jamin fish auction found")
        return
    
    print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
    print(f"📊 Current price: ${auction.current_price}")
    
    # Get buyer
    buyer = User.objects.filter(user_type='buyer').first()
    if not buyer:
        print("❌ No buyer found")
        return
    
    print(f"✅ Test buyer: {buyer.username}")
    
    # Send multiple test messages to simulate real bidding
    channel_layer = get_channel_layer()
    
    for i in range(3):
        try:
            # Create test bid data
            new_amount = auction.current_price + (auction.bid_increment * (i + 1))
            
            bid_data = {
                'auction_id': auction.id,
                'bid_id': 1000 + i,
                'amount': str(new_amount),
                'bidder': buyer.username,
                'timestamp': '2025-06-22T20:30:00Z',
                'current_price': str(new_amount),
                'total_bids': auction.total_bids + i + 1,
                'bid_type': 'test'
            }
            
            # Send WebSocket message
            async_to_sync(channel_layer.group_send)(
                f'auction_{auction.id}',
                {
                    'type': 'bid_update',
                    'bid_data': bid_data
                }
            )
            
            print(f"✅ Sent test bid #{i+1}: ${new_amount}")
            
            # Wait between messages
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error sending test bid #{i+1}: {e}")
    
    print()
    print("📱 Flutter App Testing:")
    print("1. Open auction detail screen for 'Jamin fish'")
    print("2. Check WiFi icon in app bar (should be green)")
    print("3. Watch for real-time price updates")
    print("4. Check browser console for WebSocket logs")
    print("5. No more null check errors should appear")
    print()
    
    print("🔍 Expected Console Logs:")
    print("✅ '📨 WebSocket message received: bid_update'")
    print("✅ '💰 Processing bid update: {...}'")
    print("✅ '🔌 WebSocket connection status: Connected'")
    print("❌ No more '❌ Reconnection attempt X failed: Null check operator'")
    print()
    
    print("🎯 If you still see null errors:")
    print("1. Restart the Flutter app")
    print("2. Check that Django server is running")
    print("3. Verify WebSocket URL in Flutter constants")
    print("4. Look for any remaining null assertion operators (!)")

if __name__ == "__main__":
    test_websocket_after_fix()
