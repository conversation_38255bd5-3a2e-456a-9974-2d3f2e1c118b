#!/usr/bin/env python3
"""
Restore Flutter app to use ngrok WebSocket
"""

import re
import requests

def restore_ngrok_websocket():
    """Restore Flutter app to use ngrok WebSocket"""
    print("🔄 Restoring Flutter App to Ngrok WebSocket")
    print("=" * 45)
    
    # Get current ngrok URL
    try:
        ngrok_response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        ngrok_data = ngrok_response.json()
        ngrok_url = None
        
        for tunnel in ngrok_data.get('tunnels', []):
            if tunnel.get('proto') == 'https':
                ngrok_url = tunnel['public_url']
                break
        
        if not ngrok_url:
            print("❌ No ngrok tunnel found")
            return False
            
        print(f"✅ Found ngrok URL: {ngrok_url}")
        
    except Exception as e:
        print(f"❌ Could not get ngrok URL: {e}")
        return False
    
    flutter_constants_file = 'fish_auction_app/lib/constants/app_constants.dart'
    
    try:
        # Read current file
        with open(flutter_constants_file, 'r') as f:
            content = f.read()
        
        # Update to ngrok URLs
        ngrok_host = ngrok_url.replace('https://', '')
        new_content = content
        new_content = re.sub(
            r"baseUrl = '[^']+';",
            f"baseUrl = 'https://{ngrok_host}/api';",
            new_content
        )
        new_content = re.sub(
            r"wsUrl = '[^']+';",
            f"wsUrl = 'wss://{ngrok_host}/ws';",
            new_content
        )
        
        # Write updated file
        with open(flutter_constants_file, 'w') as f:
            f.write(new_content)
        
        print(f"✅ Restored URLs:")
        print(f"   API: https://{ngrok_host}/api")
        print(f"   WebSocket: wss://{ngrok_host}/ws")
        
        return True
        
    except Exception as e:
        print(f"❌ Error restoring Flutter constants: {e}")
        return False

if __name__ == "__main__":
    restore_ngrok_websocket()
