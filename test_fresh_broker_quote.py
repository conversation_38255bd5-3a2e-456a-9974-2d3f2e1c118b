#!/usr/bin/env python3
"""
Test broker quote creation with a fresh service request to trigger notifications
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from accounts.models import User
from broker_services.models import BrokerService, ServiceRequest, BrokerQuote
from auctions.models import Auction
from notifications.models import Notification
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken


def test_fresh_broker_quote():
    """Test broker quote creation with fresh service request"""
    print("🧪 Testing Fresh Broker Quote Creation")
    print("=" * 50)
    
    # Get test users
    client_user = User.objects.filter(user_type='buyer', phone_number__isnull=False).exclude(phone_number='').first()
    broker_user = User.objects.filter(user_type='broker', phone_number__isnull=False).exclude(phone_number='').first()
    seller_user = User.objects.filter(user_type='seller').first()
    
    if not client_user or not broker_user or not seller_user:
        print("❌ Need buyer, broker, and seller users with phone numbers")
        return
    
    print(f"👤 Client: {client_user.username} ({client_user.phone_number})")
    print(f"🤝 Broker: {broker_user.username} ({broker_user.phone_number})")
    print(f"🐟 Seller: {seller_user.username}")
    
    # Get a broker service
    service = BrokerService.objects.filter(is_active=True).first()
    if not service:
        print("❌ No active broker service found")
        return
    
    # Get an auction
    auction = Auction.objects.first()
    if not auction:
        print("❌ No auction found")
        return
    
    # Create a fresh service request
    service_request = ServiceRequest.objects.create(
        client=client_user,
        service=service,
        auction=auction,
        location_description='Fresh test location for notification testing',
        special_instructions='Please test the notification system',
        status='pending'
    )
    
    print(f"📋 Created fresh service request: {service_request.id}")
    print(f"   Service: {service_request.service.name_ar}")
    print(f"   Client: {service_request.client.username}")
    
    # Count notifications before
    notifications_before = Notification.objects.count()
    broker_notifications_before = Notification.objects.filter(
        template__notification_type='broker_offer_received'
    ).count()
    client_notifications_before = Notification.objects.filter(
        recipient=client_user
    ).count()
    
    print(f"\n📊 Before quote creation:")
    print(f"   Total notifications: {notifications_before}")
    print(f"   Broker offer notifications: {broker_notifications_before}")
    print(f"   Client notifications: {client_notifications_before}")
    
    # Create API client and authenticate as broker
    api_client = APIClient()
    refresh = RefreshToken.for_user(broker_user)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    # Prepare quote data
    quote_data = {
        'service_request': str(service_request.id),
        'amount': 85.75,
        'estimated_duration': '4 hours',
        'notes': 'Fresh quote for notification testing - will provide detailed report'
    }
    
    print(f"\n📤 Creating broker quote via API...")
    print(f"   Data: {quote_data}")
    
    # Make API request
    url = '/api/broker/quotes/create/'
    response = api_client.post(url, data=quote_data, format='json')
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 201:
        print("✅ Quote created successfully!")
        
        if hasattr(response, 'data'):
            print(f"   Quote ID: {response.data.get('id', 'N/A')}")
        
        # Check notifications after
        notifications_after = Notification.objects.count()
        broker_notifications_after = Notification.objects.filter(
            template__notification_type='broker_offer_received'
        ).count()
        client_notifications_after = Notification.objects.filter(
            recipient=client_user
        ).count()
        
        print(f"\n📊 After quote creation:")
        print(f"   Total notifications: {notifications_after} (+{notifications_after - notifications_before})")
        print(f"   Broker offer notifications: {broker_notifications_after} (+{broker_notifications_after - broker_notifications_before})")
        print(f"   Client notifications: {client_notifications_after} (+{client_notifications_after - client_notifications_before})")
        
        if broker_notifications_after > broker_notifications_before:
            print("\n✅ SUCCESS: Broker offer notification was created!")
            
            # Show the notification details
            latest_notification = Notification.objects.filter(
                template__notification_type='broker_offer_received',
                recipient=client_user
            ).order_by('-created_at').first()
            
            if latest_notification:
                print(f"\n📨 Notification Details:")
                print(f"   Recipient: {latest_notification.recipient.username}")
                print(f"   Title: {latest_notification.title}")
                print(f"   Channel: {latest_notification.channel}")
                print(f"   Status: {latest_notification.status}")
                print(f"   Created: {latest_notification.created_at}")
                print(f"   Message: {latest_notification.message[:150]}...")
                
                # Check WhatsApp notification
                whatsapp_notification = Notification.objects.filter(
                    template__notification_type='broker_offer_received',
                    recipient=client_user,
                    channel='whatsapp'
                ).order_by('-created_at').first()
                
                if whatsapp_notification:
                    print(f"\n📱 WhatsApp Notification:")
                    print(f"   Status: {whatsapp_notification.status}")
                    print(f"   Phone: {client_user.phone_number}")
                    if whatsapp_notification.status == 'sent':
                        print("   ✅ WhatsApp message sent successfully!")
                    else:
                        print("   ❌ WhatsApp message failed")
        else:
            print("\n❌ FAILED: No broker offer notification was created!")
            print("   Check the serializer create() method")
            
    else:
        print(f"❌ Quote creation failed!")
        if hasattr(response, 'data'):
            print(f"   Error: {response.data}")
        else:
            print(f"   Content: {response.content.decode()}")
    
    # Verify quote was created in database
    quote = BrokerQuote.objects.filter(
        service_request=service_request,
        broker=broker_user
    ).first()
    
    if quote:
        print(f"\n📋 Quote in database:")
        print(f"   ID: {quote.id}")
        print(f"   Amount: {quote.amount}")
        print(f"   Status: {quote.status}")
        print(f"   Notes: {quote.notes}")
    else:
        print(f"\n❌ No quote found in database")
    
    print(f"\n🎉 Test completed!")
    print(f"📱 Check WhatsApp {client_user.phone_number} for messages!")


if __name__ == '__main__':
    test_fresh_broker_quote()
