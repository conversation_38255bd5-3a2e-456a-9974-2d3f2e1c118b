#!/usr/bin/env python3
"""
Script to fix auction creation in test file
"""

import re

# Read the test file
with open('test_comprehensive_notifications.py', 'r') as f:
    content = f.read()

# Define the pattern to match auction creation
auction_pattern = r'Auction\.objects\.create\((.*?)\)'

def fix_auction_fields(match):
    """Fix auction field names in the match"""
    auction_content = match.group(1)
    
    # Replace field names
    auction_content = re.sub(r'title_ar="[^"]*",?\s*', '', auction_content)
    auction_content = re.sub(r'description_ar="[^"]*",?\s*', '', auction_content)
    auction_content = re.sub(r'category=', 'fish_category=', auction_content)
    auction_content = re.sub(r'quantity=Decimal\([\'"][0-9.]+[\'"]\)', 'weight=Decimal("10.0"),\n                quantity=1', auction_content)
    auction_content = re.sub(r'unit=[\'"][^\'\"]*[\'"],?\s*', '', auction_content)
    auction_content = re.sub(r'quality_grade=[\'"][^\'\"]*[\'"],?\s*', '', auction_content)
    
    # Add required fields if not present
    if 'catch_date=' not in auction_content:
        auction_content += ',\n                catch_date=timezone.now().date()'
    if 'catch_location=' not in auction_content:
        auction_content += ',\n                catch_location="Test Location"'
    
    return f'Auction.objects.create({auction_content})'

# Fix all auction creations
content = re.sub(auction_pattern, fix_auction_fields, content, flags=re.DOTALL)

# Write back to file
with open('test_comprehensive_notifications.py', 'w') as f:
    f.write(content)

print("✅ Fixed auction field names in test file")
