#!/usr/bin/env python3
"""
Test script to verify payment methods API functionality
"""

import requests
import json
from django.contrib.auth import get_user_model
from django.test import Client
from rest_framework_simplejwt.tokens import RefreshToken

def test_payment_methods_api():
    print("=== TESTING PAYMENT METHODS API ===")
    
    # Import Django models
    import os
    import sys
    import django
    
    # Setup Django
    sys.path.append('/Users/<USER>/Downloads/fish-main')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
    django.setup()
    
    from django.contrib.auth import get_user_model
    from rest_framework_simplejwt.tokens import RefreshToken
    from payments.models import PayoutMethod
    
    User = get_user_model()
    
    # Get broker cherif
    broker = User.objects.filter(username='cherif', user_type='broker').first()
    if not broker:
        print("❌ Broker cherif not found")
        return
    
    print(f"✅ Found broker: {broker.username}")
    
    # Check payment methods in database
    payout_methods = PayoutMethod.objects.filter(user=broker)
    print(f"📊 Payment methods in DB: {payout_methods.count()}")
    for method in payout_methods:
        print(f"   - {method.get_payout_type_display()}: {method.paypal_email}")
    
    # Generate JWT token for the broker
    refresh = RefreshToken.for_user(broker)
    access_token = str(refresh.access_token)
    print(f"🔑 Generated JWT token: {access_token[:20]}...")
    
    # Test API endpoints with proper authentication
    base_url = "http://localhost:8000/api/payments"
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Test 1: Check payout methods
    print("\n1. Testing /payout-methods/check/ endpoint:")
    try:
        response = requests.get(f"{base_url}/payout-methods/check/", headers=headers, timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: List payout methods
    print("\n2. Testing /payout-methods/ endpoint:")
    try:
        response = requests.get(f"{base_url}/payout-methods/", headers=headers, timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data)} payment methods")
            for method in data:
                print(f"     - {method.get('payout_type')}: {method.get('paypal_email', 'N/A')}")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Create a new payment method
    print("\n3. Testing payment method creation:")
    try:
        new_method_data = {
            'payout_type': 'paypal',
            'paypal_email': '<EMAIL>'
        }
        response = requests.post(f"{base_url}/payout-methods/", 
                               headers=headers, 
                               json=new_method_data, 
                               timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            print("   ✅ Payment method created successfully")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_payment_methods_api()
