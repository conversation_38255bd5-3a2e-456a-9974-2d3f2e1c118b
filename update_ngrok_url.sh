#!/bin/bash

# Script to automatically update Flutter app with current ngrok URL
# This helps when ngrok URL changes and you need to update the Flutter app

echo "🔍 Checking current ngrok tunnel..."

# Get current ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    tunnels = data.get('tunnels', [])
    if tunnels:
        url = tunnels[0]['public_url']
        print(url.replace('https://', '').replace('http://', ''))
    else:
        print('NO_TUNNEL')
except:
    print('ERROR')
")

if [ "$NGROK_URL" = "NO_TUNNEL" ]; then
    echo "❌ No ngrok tunnel found. Please start ngrok first:"
    echo "   ngrok http 8000"
    exit 1
elif [ "$NGROK_URL" = "ERROR" ]; then
    echo "❌ Error getting ngrok status. Is ngrok running?"
    exit 1
fi

echo "✅ Current ngrok URL: $NGROK_URL"

# Update Flutter app constants
FLUTTER_CONSTANTS_FILE="fish_auction_app/lib/constants/app_constants.dart"

if [ ! -f "$FLUTTER_CONSTANTS_FILE" ]; then
    echo "❌ Flutter constants file not found: $FLUTTER_CONSTANTS_FILE"
    exit 1
fi

echo "🔄 Updating Flutter app configuration..."

# Backup original file
cp "$FLUTTER_CONSTANTS_FILE" "$FLUTTER_CONSTANTS_FILE.backup"

# Update the URLs in the Flutter constants file
sed -i '' "s|static const String baseUrl = 'https://.*\.ngrok-free\.app/api';|static const String baseUrl = 'https://$NGROK_URL/api';|g" "$FLUTTER_CONSTANTS_FILE"
sed -i '' "s|static const String wsUrl = 'wss://.*\.ngrok-free\.app/ws';|static const String wsUrl = 'wss://$NGROK_URL/ws';|g" "$FLUTTER_CONSTANTS_FILE"

echo "✅ Updated Flutter app configuration"

# Update Django ALLOWED_HOSTS
DJANGO_SETTINGS_FILE="fish_auction/settings.py"

if [ -f "$DJANGO_SETTINGS_FILE" ]; then
    echo "🔄 Updating Django ALLOWED_HOSTS..."
    
    # Backup original file
    cp "$DJANGO_SETTINGS_FILE" "$DJANGO_SETTINGS_FILE.backup"
    
    # Update ALLOWED_HOSTS to include new ngrok URL
    python3 -c "
import re

with open('$DJANGO_SETTINGS_FILE', 'r') as f:
    content = f.read()

# Update ALLOWED_HOSTS line
pattern = r\"ALLOWED_HOSTS = \[.*?\]\"
new_hosts = \"ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', '$NGROK_URL', '*']\"
content = re.sub(pattern, new_hosts, content)

with open('$DJANGO_SETTINGS_FILE', 'w') as f:
    f.write(content)
"
    echo "✅ Updated Django ALLOWED_HOSTS"
else
    echo "⚠️  Django settings file not found: $DJANGO_SETTINGS_FILE"
fi

# Test the connection
echo "🧪 Testing connection..."
sleep 2

HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "https://$NGROK_URL/api/auth/login/" \
    -H "Content-Type: application/json" \
    -H "ngrok-skip-browser-warning: true" \
    -d '{"username":"test","password":"test"}')

if [ "$HTTP_CODE" = "400" ]; then
    echo "✅ Connection test successful! (HTTP 400 expected for invalid credentials)"
    echo ""
    echo "📱 Your Flutter app is now configured with:"
    echo "   🌐 API URL: https://$NGROK_URL/api"
    echo "   🔗 WebSocket URL: wss://$NGROK_URL/ws"
    echo ""
    echo "🚀 You can now test your Flutter app with the simulator!"
    echo ""
    echo "💡 Test credentials:"
    echo "   Username: testuser"
    echo "   Password: testpass123"
else
    echo "❌ Connection test failed (HTTP $HTTP_CODE)"
    echo "   Please check if Django server is running"
fi

echo ""
echo "📋 Backup files created:"
echo "   - $FLUTTER_CONSTANTS_FILE.backup"
echo "   - $DJANGO_SETTINGS_FILE.backup"
