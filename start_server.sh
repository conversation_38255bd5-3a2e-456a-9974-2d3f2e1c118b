#!/bin/bash

# Fish Auction App - Start Server
echo "🐟 Starting Fish Auction Server..."

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Kill any existing processes on common ports
echo -e "${BLUE}🔄 Cleaning up existing processes...${NC}"
lsof -ti:8000 | xargs kill -9 2>/dev/null || true
lsof -ti:8001 | xargs kill -9 2>/dev/null || true
lsof -ti:8002 | xargs kill -9 2>/dev/null || true

# Activate virtual environment
if [ -d "venv" ]; then
    echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
    source venv/bin/activate
    echo -e "${GREEN}✅ Virtual environment activated${NC}"
else
    echo -e "${RED}❌ Virtual environment not found. Please run: python3 -m venv venv${NC}"
    exit 1
fi

# Find available port
PORT=8000
for p in 8000 8001 8002 8003 8004; do
    if ! lsof -i :$p >/dev/null 2>&1; then
        PORT=$p
        break
    fi
done

echo -e "${GREEN}✅ Using port: $PORT${NC}"

# Start Django server
echo -e "${BLUE}🚀 Starting Django server on http://localhost:$PORT${NC}"
echo -e "${YELLOW}💡 Press Ctrl+C to stop the server${NC}"
echo ""

# Start the server with error handling
python manage.py runserver 0.0.0.0:$PORT || {
    echo -e "${RED}❌ Server failed to start${NC}"
    echo -e "${YELLOW}💡 Try running: python manage.py migrate${NC}"
    echo -e "${YELLOW}💡 Or check for syntax errors in your code${NC}"
    exit 1
}
