#!/bin/bash

# Quick Fix for Fish Auction App Setup Issues
# This script fixes the PostgreSQL dependency and daphne installation issues

echo "🔧 Fish Auction App - Quick Fix"
echo "==============================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Make sure we're in virtual environment
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo -e "${YELLOW}⚠️ Virtual environment not activated. Activating...${NC}"
    if [ -d "venv" ]; then
        source venv/bin/activate
        echo -e "${GREEN}✅ Virtual environment activated${NC}"
    else
        echo -e "${BLUE}💡 Creating virtual environment...${NC}"
        python3 -m venv venv
        source venv/bin/activate
        echo -e "${GREEN}✅ Virtual environment created and activated${NC}"
    fi
else
    echo -e "${GREEN}✅ Virtual environment already active${NC}"
fi

# Install core dependencies first (without PostgreSQL)
echo -e "${BLUE}📦 Installing core dependencies...${NC}"
pip install --upgrade pip

# Install Django and basic dependencies
pip install Django==4.2.7
pip install djangorestframework==3.14.0
pip install django-cors-headers==4.3.1
pip install django-environ==0.11.2

# Install WebSocket dependencies
echo -e "${BLUE}🌐 Installing WebSocket dependencies...${NC}"
pip install channels==4.0.0
pip install channels-redis==4.1.0
pip install daphne==4.0.0

# Install other essential dependencies
echo -e "${BLUE}⚡ Installing other dependencies...${NC}"
pip install celery==5.3.4
pip install redis==5.0.1
pip install stripe==7.8.0
pip install Pillow==10.1.0
pip install python-dateutil==2.8.2
pip install requests==2.31.0
pip install djangorestframework-simplejwt==5.3.0

# Test daphne installation
echo -e "${BLUE}🧪 Testing daphne installation...${NC}"
if command -v daphne >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Daphne installed successfully${NC}"
    daphne --version
else
    echo -e "${RED}❌ Daphne installation failed. Trying alternative...${NC}"
    pip install daphne --force-reinstall
fi

# Check Redis
echo -e "${BLUE}🔍 Checking Redis...${NC}"
if command -v redis-cli >/dev/null 2>&1; then
    if redis-cli ping >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis is running${NC}"
    else
        echo -e "${YELLOW}⚠️ Redis not running. Starting...${NC}"
        brew services start redis 2>/dev/null || echo -e "${YELLOW}Please start Redis manually${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Redis not installed. Installing...${NC}"
    brew install redis 2>/dev/null || echo -e "${YELLOW}Please install Redis manually${NC}"
fi

# Run migrations
echo -e "${BLUE}🗄️ Setting up database...${NC}"
python manage.py migrate

echo -e "${GREEN}🎉 Setup complete!${NC}"
echo "==============================="
echo -e "${BLUE}🚀 Now you can start the server:${NC}"
echo "daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application"
echo ""
echo -e "${BLUE}📱 Or use the development server (limited WebSocket support):${NC}"
echo "python manage.py runserver 8000"
echo ""
echo -e "${BLUE}🧪 Test WebSocket connection:${NC}"
echo "wscat -c ws://localhost:8000/ws/auction/1/"
