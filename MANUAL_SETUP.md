# 🔧 Manual Setup Guide - Fish Auction App

If the automated scripts aren't working, follow these step-by-step manual instructions.

## 🚀 Quick Manual Setup (5 minutes)

### Step 1: Setup Python Environment
```bash
# 1. Create virtual environment
python3 -m venv venv

# 2. Activate virtual environment
source venv/bin/activate

# 3. Upgrade pip
pip install --upgrade pip

# 4. Install dependencies
pip install -r requirements.txt

# 5. Verify Django is installed
python -c "import django; print(f'Django {django.get_version()} installed')"
```

### Step 2: Setup Database
```bash
# Run database migrations
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser
```

### Step 3: Start Redis
```bash
# Install Redis (if not installed)
brew install redis

# Start Redis
brew services start redis

# Test Redis
redis-cli ping  # Should return PONG
```

### Step 4: Start Backend Services

**Terminal 1 - Django Server with WebSocket Support:**
```bash
# Make sure virtual environment is activated
source venv/bin/activate

# Start Django server with ASGI (for WebSocket support)
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application

# Alternative: Use uvicorn (if daphne not available)
# uvicorn fish_auction.asgi:application --host 0.0.0.0 --port 8000

# Note: Don't use regular runserver for production WebSockets
# python manage.py runserver 8000  # This won't support WebSockets properly
```

**Terminal 2 - Celery Worker:**
```bash
# Make sure virtual environment is activated
source venv/bin/activate

# Start Celery worker
celery -A fish_auction worker --loglevel=info
```

**Terminal 3 - Celery Beat:**
```bash
# Make sure virtual environment is activated
source venv/bin/activate

# Start Celery beat
celery -A fish_auction beat --loglevel=info
```

**Terminal 4 - ngrok Tunnel:**
```bash
# Install ngrok (if not installed)
brew install ngrok

# Start ngrok tunnel
ngrok http 8000
```

### Step 5: Setup Stripe Webhooks

1. **Get ngrok URL:**
   - Go to http://localhost:4040
   - Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)

2. **Setup Stripe webhook:**
   - Go to https://dashboard.stripe.com/webhooks
   - Click "Add endpoint"
   - Enter URL: `https://your-ngrok-url.ngrok.io/api/payments/stripe/webhook/`
   - Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
   - Copy the signing secret

3. **Update .env file:**
   ```bash
   STRIPE_WEBHOOK_SECRET=whsec_your_signing_secret_here
   ```

### Step 6: Start Flutter App

**Terminal 5 - Flutter App:**
```bash
# Navigate to Flutter app
cd fish_auction_app

# Get dependencies
flutter pub get

# Run on web
flutter run -d web-server --web-port 8080
```

## 🧪 Test Your Setup

### 1. Test Backend
- Django: http://localhost:8000
- API: http://localhost:8000/api/
- Admin: http://localhost:8000/admin/

### 2. Test ngrok
- Dashboard: http://localhost:4040
- Public URL: https://your-ngrok-url.ngrok.io

### 3. Test Flutter App
- Web app: http://localhost:8080

### 4. Test Real-time Features
1. Open app in two browser tabs
2. Register different users
3. Create an auction
4. Test bidding from both tabs

## 🔍 Troubleshooting Commands

### Check if services are running:
```bash
# Check Django (port 8000)
lsof -i :8000

# Check Redis (port 6379)
lsof -i :6379

# Check ngrok (port 4040)
lsof -i :4040

# Check Flutter (port 8080)
lsof -i :8080
```

### Kill processes if needed:
```bash
# Kill Django
pkill -f "runserver 8000"

# Kill Celery
pkill -f "celery.*fish_auction"

# Kill ngrok
pkill -f "ngrok"
```

### Reset virtual environment:
```bash
# Remove and recreate
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## 📋 Environment Variables Checklist

Make sure your `.env` file contains:

```bash
# Django
DEBUG=True
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DATABASE_URL=sqlite:///db.sqlite3

# Redis
REDIS_URL=redis://localhost:6379/0

# Stripe (get from https://dashboard.stripe.com/)
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# JWT
JWT_SECRET_KEY=your-jwt-secret
```

## 🎯 Verification Steps

1. **Backend running**: Visit http://localhost:8000
2. **API working**: Visit http://localhost:8000/api/
3. **Redis connected**: `redis-cli ping` returns PONG
4. **Celery working**: Check terminal for "ready" message
5. **ngrok tunnel**: Visit http://localhost:4040
6. **Flutter app**: Visit http://localhost:8080
7. **Webhooks**: Test in Stripe Dashboard

## 🚀 Quick Commands Summary

```bash
# Setup (run once)
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
brew install redis ngrok

# Start services (run each time)
brew services start redis
source venv/bin/activate

# Terminal 1: Django with WebSocket support
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application

# Terminal 2: Celery Worker
celery -A fish_auction worker --loglevel=info

# Terminal 3: Celery Beat
celery -A fish_auction beat --loglevel=info

# Terminal 4: ngrok
ngrok http 8000

# Terminal 5: Flutter
cd fish_auction_app
flutter run -d web-server --web-port 8080
```

## 🎉 Success!

When everything is working, you should see:
- ✅ Django server at http://localhost:8000
- ✅ Flutter app at http://localhost:8080
- ✅ ngrok tunnel with public HTTPS URL
- ✅ Real-time bidding working between browser tabs
- ✅ Stripe webhooks receiving events

Your Fish Auction app is now ready for development! 🐟
