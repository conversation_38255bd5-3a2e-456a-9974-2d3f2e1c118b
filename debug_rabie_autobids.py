#!/usr/bin/env python3
"""
Debug why rabie is auto-bidding against himself
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User

def debug_rabie_issue():
    print("🔍 Debugging Rabie Auto-bid Issue...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    print(f"🎯 Auction: {live_auction.title} (ID: {live_auction.id})")
    print(f"   Current price: ${live_auction.current_price}")
    
    # Find rabie user
    rabie_users = User.objects.filter(username__icontains='rabie')
    print(f"\n👤 Rabie users found: {rabie_users.count()}")
    for user in rabie_users:
        print(f"   - {user.username} (ID: {user.id})")
    
    # Check rabie's auto-bids on this auction
    for rabie in rabie_users:
        auto_bids = AutoBid.objects.filter(auction=live_auction, bidder=rabie)
        print(f"\n🤖 Auto-bids for {rabie.username}:")
        for ab in auto_bids:
            status = "✅ Active" if ab.is_active else "❌ Inactive"
            print(f"   - Max: ${ab.max_amount}, Current: ${ab.current_bid_amount} ({status})")
    
    # Check recent bids
    recent_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')[:10]
    print(f"\n💰 Recent 10 bids:")
    for i, bid in enumerate(recent_bids, 1):
        bid_type = "🤖 Auto" if bid.bid_type == 'auto' else "👤 Manual"
        print(f"   {i}. {bid.bidder.username}: ${bid.amount} ({bid_type}) - {bid.timestamp.strftime('%H:%M:%S')}")
    
    # Check the last bidder logic
    last_bid = Bid.objects.filter(auction=live_auction).order_by('-timestamp').first()
    if last_bid:
        print(f"\n🎯 Last bidder: {last_bid.bidder.username} (${last_bid.amount})")
        
        # Check what auto-bids would be excluded
        excluded_auto_bids = AutoBid.objects.filter(
            auction=live_auction,
            is_active=True,
            bidder=last_bid.bidder
        )
        print(f"   Auto-bids that SHOULD be excluded: {excluded_auto_bids.count()}")
        for ab in excluded_auto_bids:
            print(f"     - {ab.bidder.username}: max ${ab.max_amount}")
        
        # Check what auto-bids would be eligible
        eligible_auto_bids = AutoBid.objects.filter(
            auction=live_auction,
            is_active=True,
            max_amount__gt=live_auction.current_price
        ).exclude(bidder=last_bid.bidder).order_by('-max_amount')
        
        print(f"   Auto-bids that ARE eligible: {eligible_auto_bids.count()}")
        for ab in eligible_auto_bids:
            print(f"     - {ab.bidder.username}: max ${ab.max_amount}")

if __name__ == "__main__":
    debug_rabie_issue()
