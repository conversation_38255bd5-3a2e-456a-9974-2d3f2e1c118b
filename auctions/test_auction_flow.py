from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import Auction, FishCategory, Bid, AutoBid
from payments.models import Payment

User = get_user_model()


class AuctionFlowIntegrationTest(APITestCase):
    """Integration test for complete auction flow"""
    
    def setUp(self):
        """Set up test data"""
        # Create fish category
        self.fish_category = FishCategory.objects.create(
            name="Tuna",
            description="Fresh tuna fish"
        )
        
        # Create users
        self.seller = User.objects.create_user(
            username='seller_test',
            email='<EMAIL>',
            password='testpass123',
            user_type='seller',
            wallet_balance=Decimal('1000.00')
        )
        
        self.buyer1 = User.objects.create_user(
            username='buyer1_test',
            email='<EMAIL>',
            password='testpass123',
            user_type='buyer',
            wallet_balance=Decimal('500.00')
        )
        
        self.buyer2 = User.objects.create_user(
            username='buyer2_test',
            email='<EMAIL>',
            password='testpass123',
            user_type='buyer',
            wallet_balance=Decimal('750.00')
        )
        
        # Create auction
        self.auction = Auction.objects.create(
            seller=self.seller,
            title="Fresh Bluefin Tuna",
            description="Premium quality bluefin tuna",
            fish_category=self.fish_category,
            fish_type="Bluefin Tuna",
            weight=Decimal('25.5'),
            quantity=1,
            catch_date=timezone.now().date(),
            catch_location="Pacific Ocean",
            auction_type='live',
            starting_price=Decimal('50.00'),
            reserve_price=Decimal('100.00'),
            buy_now_price=Decimal('200.00'),
            bid_increment=Decimal('5.00'),
            start_time=timezone.now() - timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            status='live'
        )
    
    def test_complete_auction_flow(self):
        """Test complete auction flow from bidding to payment"""
        
        # 1. Test auction listing
        response = self.client.get('/api/auctions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        
        # 2. Test auction detail
        response = self.client.get(f'/api/auctions/{self.auction.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Fresh Bluefin Tuna')
        
        # 3. Test bidding (buyer1 places first bid)
        self.client.force_authenticate(user=self.buyer1)
        bid_data = {'amount': '55.00'}
        response = self.client.post(f'/api/auctions/{self.auction.id}/bid/', bid_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify auction current price updated
        self.auction.refresh_from_db()
        self.assertEqual(self.auction.current_price, Decimal('55.00'))
        self.assertEqual(self.auction.total_bids, 1)
        
        # 4. Test automatic bidding setup (buyer2)
        self.client.force_authenticate(user=self.buyer2)
        auto_bid_data = {
            'max_amount': '150.00',
            'increment': '5.00'
        }
        response = self.client.post(f'/api/auctions/{self.auction.id}/auto-bid/', auto_bid_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 5. Test another manual bid (buyer1)
        self.client.force_authenticate(user=self.buyer1)
        bid_data = {'amount': '65.00'}
        response = self.client.post(f'/api/auctions/{self.auction.id}/bid/', bid_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 6. Verify auto-bid would trigger (simulate)
        auto_bid = AutoBid.objects.get(auction=self.auction, bidder=self.buyer2)
        self.assertTrue(auto_bid.is_active)
        self.assertEqual(auto_bid.max_amount, Decimal('150.00'))
        
        # 7. Test auction ending (simulate)
        self.auction.status = 'ended'
        self.auction.winner = self.buyer1  # Assume buyer1 won
        self.auction.payment_deadline = timezone.now() + timedelta(minutes=10)
        self.auction.save()
        
        # 8. Test payment processing
        self.client.force_authenticate(user=self.buyer1)
        payment_data = {'payment_method': 'wallet'}
        response = self.client.post(f'/api/payments/process/{self.auction.id}/', payment_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify payment created
        payment = Payment.objects.get(auction=self.auction)
        self.assertEqual(payment.buyer, self.buyer1)
        self.assertEqual(payment.seller, self.seller)
        self.assertEqual(payment.status, 'completed')
        
        # 9. Test wallet balance updates
        self.buyer1.refresh_from_db()
        self.seller.refresh_from_db()
        
        # Buyer's balance should be reduced
        self.assertLess(self.buyer1.wallet_balance, Decimal('500.00'))
        
        # Seller's balance should be increased (minus platform fee)
        self.assertGreater(self.seller.wallet_balance, Decimal('1000.00'))
    
    def test_auction_watchlist(self):
        """Test auction watchlist functionality"""
        self.client.force_authenticate(user=self.buyer1)
        
        # Add to watchlist
        response = self.client.post('/api/auctions/watchlist/', {
            'auction_id': self.auction.id
        })
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Get watchlist
        response = self.client.get('/api/auctions/watchlist/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        
        # Remove from watchlist
        response = self.client.delete(f'/api/auctions/{self.auction.id}/watchlist/remove/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_user_auctions_and_bids(self):
        """Test user's auctions and bids endpoints"""
        
        # Test seller's auctions
        self.client.force_authenticate(user=self.seller)
        response = self.client.get('/api/auctions/my-auctions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        
        # Place a bid first
        self.client.force_authenticate(user=self.buyer1)
        bid_data = {'amount': '55.00'}
        self.client.post(f'/api/auctions/{self.auction.id}/bid/', bid_data)
        
        # Test buyer's bids
        response = self.client.get('/api/auctions/my-bids/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_wallet_operations(self):
        """Test wallet-related operations"""
        self.client.force_authenticate(user=self.buyer1)
        
        # Test wallet balance
        response = self.client.get('/api/payments/wallet/balance/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Decimal(response.data['balance']), Decimal('500.00'))
        
        # Test wallet transactions
        response = self.client.get('/api/payments/wallet/transactions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_invalid_bid_scenarios(self):
        """Test invalid bidding scenarios"""
        self.client.force_authenticate(user=self.buyer1)
        
        # Test bid lower than current price
        bid_data = {'amount': '40.00'}  # Lower than starting price
        response = self.client.post(f'/api/auctions/{self.auction.id}/bid/', bid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test seller bidding on own auction
        self.client.force_authenticate(user=self.seller)
        bid_data = {'amount': '60.00'}
        response = self.client.post(f'/api/auctions/{self.auction.id}/bid/', bid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_auction_creation_permissions(self):
        """Test auction creation permissions"""
        
        # Test buyer trying to create auction (should fail)
        self.client.force_authenticate(user=self.buyer1)
        auction_data = {
            'title': 'Test Auction',
            'description': 'Test description',
            'fish_category': self.fish_category.id,
            'fish_type': 'Test Fish',
            'weight': '10.0',
            'quantity': 1,
            'catch_date': timezone.now().date().isoformat(),
            'catch_location': 'Test Location',
            'auction_type': 'live',
            'starting_price': '50.00',
            'bid_increment': '5.00',
            'start_time': (timezone.now() + timedelta(hours=1)).isoformat(),
            'end_time': (timezone.now() + timedelta(hours=3)).isoformat(),
        }
        response = self.client.post('/api/auctions/create/', auction_data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Test seller creating auction (should succeed)
        self.client.force_authenticate(user=self.seller)
        response = self.client.post('/api/auctions/create/', auction_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
