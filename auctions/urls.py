from django.urls import path
from .views import (
    FishCategoryListView, AuctionListView, AuctionDetailView, AuctionCreateView,
    PlaceBidView, AutoBidView, AuctionWatchlistView, RemoveFromWatchlistView,
    MyAuctionsView, MyBidsView, EarnedAuctionsView, AuctionBidsView, MakeAuctionLiveView, AuctionUpdateView,
    close_auction_early, cancel_auction, UpdateSellerLocationView, GetSellerLocationView,
    ToggleLocationSharingView, AuctionLocationView
)

app_name = 'auctions'

urlpatterns = [
    # Fish categories
    path('categories/', FishCategoryListView.as_view(), name='fish_categories'),
    
    # Auctions
    path('', AuctionListView.as_view(), name='auction_list'),
    path('create/', AuctionCreateView.as_view(), name='auction_create'),
    path('<int:pk>/', AuctionDetailView.as_view(), name='auction_detail'),
    path('<int:pk>/update/', AuctionUpdateView.as_view(), name='auction_update'),
    path('<int:auction_id>/make-live/', MakeAuctionLiveView.as_view(), name='make_auction_live'),
    path('<int:auction_id>/close-early/', close_auction_early, name='close_auction_early'),
    path('<int:auction_id>/cancel/', cancel_auction, name='cancel_auction'),
    
    # Bidding
    path('<int:auction_id>/bid/', PlaceBidView.as_view(), name='place_bid'),
    path('<int:auction_id>/bids/', AuctionBidsView.as_view(), name='auction_bids'),
    path('<int:auction_id>/auto-bid/', AutoBidView.as_view(), name='auto_bid'),
    
    # Watchlist
    path('watchlist/', AuctionWatchlistView.as_view(), name='watchlist'),
    path('<int:auction_id>/watchlist/remove/', RemoveFromWatchlistView.as_view(), name='remove_from_watchlist'),
    
    # User auctions and bids
    path('my-auctions/', MyAuctionsView.as_view(), name='my_auctions'),
    path('my-bids/', MyBidsView.as_view(), name='my_bids'),
    path('earned/', EarnedAuctionsView.as_view(), name='earned_auctions'),

    # Location tracking
    path('location/update/', UpdateSellerLocationView.as_view(), name='update_seller_location'),
    path('<int:auction_id>/location/', GetSellerLocationView.as_view(), name='get_seller_location'),
    path('<int:auction_id>/location/toggle/', ToggleLocationSharingView.as_view(), name='toggle_location_sharing'),
    path('<int:auction_id>/location-data/', AuctionLocationView.as_view(), name='auction_location_data'),
]
