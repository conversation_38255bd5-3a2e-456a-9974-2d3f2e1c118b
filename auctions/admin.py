from django.contrib import admin
from .models import FishCategory, Auction, AuctionImage, Bid, AutoBid, AuctionWatchlist


@admin.register(FishCategory)
class FishCategoryAdmin(admin.ModelAdmin):
    """Admin configuration for FishCategory model"""

    list_display = ['name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']


class AuctionImageInline(admin.TabularInline):
    """Inline admin for auction images"""
    model = AuctionImage
    extra = 1


@admin.register(Auction)
class AuctionAdmin(admin.ModelAdmin):
    """Admin configuration for Auction model"""

    list_display = [
        'title', 'fish_type', 'seller', 'status', 'current_price',
        'total_bids', 'start_time', 'end_time'
    ]
    list_filter = ['status', 'auction_type', 'fish_category', 'created_at']
    search_fields = ['title', 'description', 'fish_type', 'catch_location']
    readonly_fields = ['current_price', 'total_bids', 'total_bidders', 'views_count', 'created_at', 'updated_at']
    inlines = [AuctionImageInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('seller', 'title', 'description', 'fish_category', 'main_image')
        }),
        ('Fish Details', {
            'fields': ('fish_type', 'weight', 'quantity', 'catch_date', 'catch_location')
        }),
        ('Auction Settings', {
            'fields': (
                'auction_type', 'starting_price', 'reserve_price', 'buy_now_price',
                'bid_increment', 'start_time', 'end_time'
            )
        }),
        ('Status & Results', {
            'fields': ('status', 'winner', 'payment_deadline', 'current_price')
        }),
        ('Statistics', {
            'fields': ('total_bids', 'total_bidders', 'views_count')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(Bid)
class BidAdmin(admin.ModelAdmin):
    """Admin configuration for Bid model"""

    list_display = ['auction', 'bidder', 'amount', 'bid_type', 'timestamp', 'is_winning']
    list_filter = ['bid_type', 'is_winning', 'timestamp']
    search_fields = ['auction__title', 'bidder__username']
    readonly_fields = ['timestamp']
    ordering = ['-timestamp']


@admin.register(AutoBid)
class AutoBidAdmin(admin.ModelAdmin):
    """Admin configuration for AutoBid model"""

    list_display = ['auction', 'bidder', 'max_amount', 'current_bid_amount', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['auction__title', 'bidder__username']
    readonly_fields = ['current_bid_amount', 'created_at', 'updated_at']


@admin.register(AuctionWatchlist)
class AuctionWatchlistAdmin(admin.ModelAdmin):
    """Admin configuration for AuctionWatchlist model"""

    list_display = ['user', 'auction', 'added_at']
    list_filter = ['added_at']
    search_fields = ['user__username', 'auction__title']
    readonly_fields = ['added_at']
