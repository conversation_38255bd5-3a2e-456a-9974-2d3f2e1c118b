from celery import shared_task
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
from decimal import Decimal
import time
import logging
import time

from .models import Auction, Bid, AutoBid
from notifications.services import NotificationService
from payments.models import Payment
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

logger = logging.getLogger(__name__)


@shared_task
def start_scheduled_auctions():
    """Start auctions that are scheduled to begin"""
    try:
        now = timezone.now()
        scheduled_auctions = Auction.objects.filter(
            status='scheduled',
            start_time__lte=now
        )
        
        count = 0
        for auction in scheduled_auctions:
            # Check if auction should still be live (not already expired)
            if auction.end_time and auction.end_time <= now:
                # Auction has already expired, mark as ended instead
                auction.status = 'ended'
                auction.save()
                logger.info(f"Auction {auction.title} expired before starting - marked as ended")
                continue

            # Convert to live
            auction.status = 'live'
            auction.save()

            # Send notifications to watchers
            notification_service = NotificationService()
            for watcher in auction.watchers.all():
                notification_service.send_notification(
                    'auction_started',
                    watcher.user,
                    {'auction': auction}
                )

            count += 1
            logger.info(f"Started auction: {auction.title}")
        
        logger.info(f"Started {count} scheduled auctions")
        return f"Started {count} auctions"
        
    except Exception as e:
        logger.error(f"Error starting scheduled auctions: {str(e)}")
        raise


@shared_task
def end_expired_auctions():
    """End auctions that have expired"""
    try:
        now = timezone.now()
        live_auctions = Auction.objects.filter(
            status='live',
            end_time__lte=now
        )
        
        count = 0
        for auction in live_auctions:
            with transaction.atomic():
                auction.status = 'ended'
                auction.save()
                
                # Get winning bid
                winning_bid = auction.bids.order_by('-amount').first()
                
                if winning_bid:
                    # Mark as winner
                    winning_bid.is_winning_bid = True
                    winning_bid.save()
                    
                    # Send winner notification
                    notification_service = NotificationService()
                    notification_service.send_notification(
                        'auction_won',
                        winning_bid.bidder,
                        {'auction': auction, 'bid': winning_bid}
                    )
                    
                    # Send seller notification
                    notification_service.send_notification(
                        'auction_sold',
                        auction.seller,
                        {'auction': auction, 'bid': winning_bid}
                    )
                    
                    # Schedule payment reminder
                    schedule_payment_reminder.apply_async(
                        args=[auction.id, winning_bid.id],
                        countdown=300  # 5 minutes
                    )
                else:
                    # No bids - notify seller
                    notification_service = NotificationService()
                    notification_service.send_notification(
                        'auction_no_bids',
                        auction.seller,
                        {'auction': auction}
                    )
                
                count += 1
                logger.info(f"Ended auction: {auction.title}")
        
        logger.info(f"Ended {count} expired auctions")
        return f"Ended {count} auctions"
        
    except Exception as e:
        logger.error(f"Error ending expired auctions: {str(e)}")
        raise


@shared_task
def process_auto_bids_for_auction(auction_id, triggering_bid_id=None):
    """
    Process auto-bids for an auction after a manual bid
    This runs in the background and doesn't require user to be online
    """
    try:
        auction = Auction.objects.get(id=auction_id)

        if auction.status != 'live':
            logger.info(f"Auction {auction_id} is not live, skipping auto-bids")
            return 0

        # Get the triggering bid to exclude that bidder
        triggering_bidder = None
        if triggering_bid_id:
            try:
                triggering_bid = Bid.objects.get(id=triggering_bid_id)
                triggering_bidder = triggering_bid.bidder
            except Bid.DoesNotExist:
                pass

        auto_bids_placed = 0
        max_iterations = 20  # Prevent infinite loops
        iteration = 0

        logger.info(f"Starting auto-bid processing for auction {auction_id}")

        while iteration < max_iterations:
            iteration += 1

            # Refresh auction data
            auction.refresh_from_db()

            # Get the CURRENT last bidder (could be manual or auto) - refresh each iteration
            current_last_bid = Bid.objects.filter(auction=auction).order_by('-timestamp').first()
            current_last_bidder = current_last_bid.bidder if current_last_bid else triggering_bidder

            logger.info(f"Iteration {iteration}: Current last bidder is {current_last_bidder.username if current_last_bidder else 'None'}")

            # Get active auto-bids for this auction (excluding the CURRENT last bidder)
            # Fix: Check if max_amount can cover the NEXT bid amount, not just current price
            next_bid_amount = auction.current_price + auction.bid_increment
            auto_bids = AutoBid.objects.filter(
                auction=auction,
                is_active=True,
                max_amount__gte=next_bid_amount  # Changed from __gt to __gte and use next_bid_amount
            ).exclude(bidder=current_last_bidder).order_by('-max_amount')

            if not auto_bids.exists():
                logger.info(f"No more auto-bids available after {iteration-1} iterations")
                break

            # Get the highest auto-bid that can still bid
            auto_bid = auto_bids.first()
            # next_bid_amount already calculated above for the filter

            logger.info(f"Processing auto-bid by {auto_bid.bidder.username} with max ${auto_bid.max_amount} vs next bid ${next_bid_amount}")

            # This condition should always be true now due to our filter, but keep for safety
            if next_bid_amount <= auto_bid.max_amount:
                with transaction.atomic():
                    # Double-check that this bidder is not the last bidder (race condition protection)
                    latest_bid = Bid.objects.filter(auction=auction).order_by('-timestamp').first()
                    if latest_bid and latest_bid.bidder == auto_bid.bidder:
                        logger.info(f"🚫 Skipping auto-bid for {auto_bid.bidder.username} - they are the last bidder")
                        continue

                    # Check if there's already a bid at this amount (prevent duplicates)
                    existing_bid = Bid.objects.filter(
                        auction=auction,
                        amount=next_bid_amount,
                        bidder=auto_bid.bidder
                    ).exists()

                    if existing_bid:
                        logger.info(f"🚫 Skipping auto-bid for {auto_bid.bidder.username} - bid at ${next_bid_amount} already exists")
                        continue

                    # Place auto-bid
                    auto_bid_obj = Bid.objects.create(
                        auction=auction,
                        bidder=auto_bid.bidder,
                        amount=next_bid_amount,
                        bid_type='auto'
                    )

                    # Update auction
                    auction.current_price = next_bid_amount
                    auction.total_bids += 1
                    auction.save()

                    # Update auto-bid current amount
                    auto_bid.current_bid_amount = next_bid_amount
                    auto_bid.save()

                    auto_bids_placed += 1

                    # Send real-time update via WebSocket
                    send_auto_bid_websocket_update(auction, auto_bid_obj)

                    logger.info(f"Background auto-bid #{iteration}: ${next_bid_amount} by {auto_bid.bidder.username}")

                    # Check if target price reached
                    if auction.target_price and auction.current_price >= auction.target_price:
                        send_target_price_reached_notification(auction)

                    # Longer delay to prevent bid spam and allow users to see updates
                    # This creates a queue system with 5-8 second delays between auto-bids
                    time.sleep(6)

                    # Continue to next iteration
                    continue
            else:
                # Max amount reached, deactivate auto-bid
                auto_bid.is_active = False
                auto_bid.save()
                logger.info(f"Auto-bid max reached for {auto_bid.bidder.username}")

                # Continue to check other auto-bids
                continue

        logger.info(f"Auto-bid processing complete for auction {auction_id}. Placed {auto_bids_placed} auto-bids")
        return auto_bids_placed

    except Auction.DoesNotExist:
        logger.error(f"Auction {auction_id} not found")
        return 0
    except Exception as e:
        logger.error(f"Error processing auto-bids for auction {auction_id}: {e}")
        return 0


def send_auto_bid_websocket_update(auction, bid):
    """Send WebSocket update for auto-bid"""
    try:
        channel_layer = get_channel_layer()
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                f'auction_{auction.id}',
                {
                    'type': 'bid_update',
                    'bid_data': {
                        'auction_id': auction.id,
                        'bid_id': bid.id,
                        'amount': str(bid.amount),
                        'bidder': bid.bidder.username,
                        'timestamp': bid.timestamp.isoformat(),
                        'current_price': str(auction.current_price),
                        'total_bids': auction.total_bids,
                        'bid_type': 'auto'
                    }
                }
            )
    except Exception as e:
        logger.error(f"Failed to send auto-bid WebSocket update: {e}")


def send_target_price_reached_notification(auction):
    """Send notification when target price is reached"""
    try:
        channel_layer = get_channel_layer()
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                f'auction_{auction.id}',
                {
                    'type': 'target_price_reached',
                    'auction_data': {
                        'auction_id': auction.id,
                        'target_price': str(auction.target_price),
                        'current_price': str(auction.current_price),
                        'can_close_early': auction.can_close_early()
                    }
                }
            )
    except Exception as e:
        logger.error(f"Failed to send target price notification: {e}")


@shared_task
def process_auto_bids():
    """Legacy function - redirects to new implementation"""
    try:
        live_auctions = Auction.objects.filter(status='live')
        processed_count = 0

        for auction in live_auctions:
            # Check if there are any active auto-bids that can cover the next bid
            next_bid_amount = auction.current_price + auction.bid_increment
            active_auto_bids = AutoBid.objects.filter(
                auction=auction,
                is_active=True,
                max_amount__gte=next_bid_amount  # Fixed: use next_bid_amount instead of current_price
            )

            if active_auto_bids.exists():
                # Trigger the new auto-bid processing
                result = process_auto_bids_for_auction.delay(auction.id)
                processed_count += 1

        logger.info(f"Triggered auto-bid processing for {processed_count} auctions")
        return f"Triggered processing for {processed_count} auctions"

    except Exception as e:
        logger.error(f"Error in legacy auto-bid processing: {str(e)}")
        raise


@shared_task
def monitor_auction_auto_bids():
    """
    Periodic task to monitor and process auto-bids continuously
    Runs every 5 seconds to ensure auto-bids compete with each other
    """
    try:
        # Get all live auctions with active auto-bids
        live_auctions = Auction.objects.filter(
            status='live',
            auto_bids__is_active=True
        ).distinct()

        processed_count = 0

        for auction in live_auctions:
            # Get the last bid
            last_bid = auction.bids.order_by('-timestamp').first()
            last_bidder = last_bid.bidder if last_bid else None

            # Check if there are any auto-bids that should trigger (excluding last bidder)
            next_bid_amount = auction.current_price + auction.bid_increment
            active_auto_bids = AutoBid.objects.filter(
                auction=auction,
                is_active=True,
                max_amount__gte=next_bid_amount  # Fixed: use next_bid_amount instead of current_price
            )

            if last_bidder:
                active_auto_bids = active_auto_bids.exclude(bidder=last_bidder)

            if active_auto_bids.exists():
                # Always trigger auto-bid processing if there are eligible auto-bids
                # This ensures continuous competition between auto-bids
                logger.info(f"🔄 Triggering auto-bid competition for auction {auction.id}")
                process_auto_bids_for_auction.delay(auction.id)
                processed_count += 1

        if processed_count > 0:
            logger.info(f"Monitored {live_auctions.count()} auctions, triggered {processed_count} auto-bid competitions")

        return f"Monitored {live_auctions.count()} auctions, triggered {processed_count} auto-bid competitions"

    except Exception as e:
        logger.error(f"Error monitoring auto-bids: {e}")
        return f"Error monitoring auto-bids: {e}"


@shared_task
def schedule_payment_reminder(auction_id, bid_id):
    """Send payment reminder to auction winner"""
    try:
        auction = Auction.objects.get(id=auction_id)
        bid = Bid.objects.get(id=bid_id)
        
        # Check if payment has been made
        payment_exists = Payment.objects.filter(
            auction=auction,
            user=bid.bidder,
            status='completed'
        ).exists()
        
        if not payment_exists:
            # Send payment reminder
            notification_service = NotificationService()
            notification_service.send_notification(
                'payment_reminder',
                bid.bidder,
                {'auction': auction, 'bid': bid}
            )
            
            # Schedule another reminder in 5 minutes if still not paid
            schedule_payment_timeout.apply_async(
                args=[auction_id, bid_id],
                countdown=300  # 5 minutes
            )
        
        logger.info(f"Payment reminder sent for auction {auction.title}")
        
    except Exception as e:
        logger.error(f"Error sending payment reminder: {str(e)}")
        raise


@shared_task
def schedule_payment_timeout(auction_id, bid_id):
    """Handle payment timeout - reassign auction to next highest bidder"""
    try:
        auction = Auction.objects.get(id=auction_id)
        bid = Bid.objects.get(id=bid_id)
        
        # Check if payment has been made
        payment_exists = Payment.objects.filter(
            auction=auction,
            user=bid.bidder,
            status='completed'
        ).exists()
        
        if not payment_exists:
            with transaction.atomic():
                # Mark current bid as failed
                bid.is_winning_bid = False
                bid.save()
                
                # Find next highest bidder
                next_bid = auction.bids.filter(
                    amount__lt=bid.amount
                ).order_by('-amount').first()
                
                if next_bid:
                    # Reassign to next highest bidder
                    next_bid.is_winning_bid = True
                    next_bid.save()
                    
                    # Send notifications
                    notification_service = NotificationService()
                    
                    # Notify original winner about timeout
                    notification_service.send_notification(
                        'payment_timeout',
                        bid.bidder,
                        {'auction': auction, 'bid': bid}
                    )
                    
                    # Notify new winner
                    notification_service.send_notification(
                        'auction_won_reassigned',
                        next_bid.bidder,
                        {'auction': auction, 'bid': next_bid}
                    )
                    
                    # Schedule payment reminder for new winner
                    schedule_payment_reminder.apply_async(
                        args=[auction.id, next_bid.id],
                        countdown=300  # 5 minutes
                    )
                    
                    logger.info(f"Auction {auction.title} reassigned to {next_bid.bidder.username}")
                
                else:
                    # No other bidders, auction failed
                    auction.status = 'failed'
                    auction.save()
                    
                    # Notify seller
                    notification_service = NotificationService()
                    notification_service.send_notification(
                        'auction_failed',
                        auction.seller,
                        {'auction': auction}
                    )
                    
                    logger.info(f"Auction {auction.title} failed - no payment and no other bidders")
        
    except Exception as e:
        logger.error(f"Error handling payment timeout: {str(e)}")
        raise


@shared_task
def send_auction_ending_reminders():
    """Send reminders for auctions ending soon"""
    try:
        now = timezone.now()
        ending_soon = now + timedelta(minutes=30)  # 30 minutes warning
        
        auctions = Auction.objects.filter(
            status='live',
            end_time__lte=ending_soon,
            end_time__gt=now
        )
        
        notification_service = NotificationService()
        count = 0
        
        for auction in auctions:
            # Send to all watchers
            for watcher in auction.watchers.all():
                notification_service.send_notification(
                    'auction_ending_soon',
                    watcher.user,
                    {'auction': auction}
                )
            
            # Send to all bidders
            bidders = auction.bids.values_list('bidder', flat=True).distinct()
            for bidder_id in bidders:
                from accounts.models import User
                bidder = User.objects.get(id=bidder_id)
                notification_service.send_notification(
                    'auction_ending_soon',
                    bidder,
                    {'auction': auction}
                )
            
            count += 1
        
        logger.info(f"Sent ending reminders for {count} auctions")
        return f"Sent reminders for {count} auctions"
        
    except Exception as e:
        logger.error(f"Error sending auction ending reminders: {str(e)}")
        raise
