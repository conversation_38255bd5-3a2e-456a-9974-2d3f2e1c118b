# Generated by Django 5.2.3 on 2025-06-22 19:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auctions', '0003_auction_target_price'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='auction',
            name='is_location_live',
            field=models.BooleanField(default=False, help_text='Whether seller is sharing live location'),
        ),
        migrations.AddField(
            model_name='auction',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=8, help_text='Current seller latitude', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='auction',
            name='location_updated_at',
            field=models.DateTimeField(blank=True, help_text='Last location update timestamp', null=True),
        ),
        migrations.AddField(
            model_name='auction',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=8, help_text='Current seller longitude', max_digits=11, null=True),
        ),
        migrations.AlterField(
            model_name='auction',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('live', 'Live'), ('ended', 'Ended'), ('cancelled', 'Cancelled'), ('failed', 'Failed'), ('payment_pending', 'Payment Pending'), ('completed', 'Completed')], default='draft', max_length=20),
        ),
        migrations.CreateModel(
            name='SellerLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('latitude', models.DecimalField(decimal_places=8, help_text='Seller latitude', max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=8, help_text='Seller longitude', max_digits=11)),
                ('accuracy', models.FloatField(blank=True, help_text='Location accuracy in meters', null=True)),
                ('altitude', models.FloatField(blank=True, help_text='Altitude in meters', null=True)),
                ('heading', models.FloatField(blank=True, help_text='Heading in degrees', null=True)),
                ('speed', models.FloatField(blank=True, help_text='Speed in m/s', null=True)),
                ('status', models.CharField(choices=[('offline', 'Offline'), ('online', 'Online'), ('fishing', 'Fishing'), ('returning', 'Returning'), ('at_dock', 'At Dock')], default='online', max_length=20)),
                ('address', models.CharField(blank=True, help_text='Reverse geocoded address', max_length=500)),
                ('is_active', models.BooleanField(default=True, help_text='Whether location tracking is active')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('auction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='seller_locations', to='auctions.auction')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='location_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['seller', '-timestamp'], name='auctions_se_seller__14fdae_idx'), models.Index(fields=['auction', '-timestamp'], name='auctions_se_auction_747284_idx')],
            },
        ),
    ]
