# Generated by Django 4.2.7 on 2025-06-15 12:42

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Auction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('auction_type', models.CharField(choices=[('live', 'Live Auction'), ('timed', 'Timed Auction'), ('buy_now', 'Buy Now')], default='live', max_length=20)),
                ('starting_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reserve_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('buy_now_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('current_price', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('bid_increment', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10)),
                ('fish_type', models.CharField(max_length=100)),
                ('weight', models.DecimalField(decimal_places=2, help_text='Weight in kg', max_digits=8)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('catch_date', models.DateField()),
                ('catch_location', models.CharField(max_length=200)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('live', 'Live'), ('ended', 'Ended'), ('cancelled', 'Cancelled'), ('payment_pending', 'Payment Pending'), ('completed', 'Completed')], default='draft', max_length=20)),
                ('payment_deadline', models.DateTimeField(blank=True, null=True)),
                ('main_image', models.ImageField(upload_to='auction_images/')),
                ('total_bids', models.PositiveIntegerField(default=0)),
                ('total_bidders', models.PositiveIntegerField(default=0)),
                ('views_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FishCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='fish_categories/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Fish Categories',
            },
        ),
        migrations.CreateModel(
            name='AuctionImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='auction_images/')),
                ('caption', models.CharField(blank=True, max_length=200)),
                ('order', models.PositiveIntegerField(default=0)),
                ('auction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='auctions.auction')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='auction',
            name='fish_category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auctions', to='auctions.fishcategory'),
        ),
        migrations.AddField(
            model_name='auction',
            name='seller',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auctions_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='auction',
            name='winner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='auctions_won', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Bid',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('bid_type', models.CharField(choices=[('manual', 'Manual'), ('auto', 'Automatic')], default='manual', max_length=10)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('is_winning', models.BooleanField(default=False)),
                ('auction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bids', to='auctions.auction')),
                ('bidder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bids', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'unique_together': {('auction', 'bidder', 'timestamp')},
            },
        ),
        migrations.CreateModel(
            name='AutoBid',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('increment', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('current_bid_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('auction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auto_bids', to='auctions.auction')),
                ('bidder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auto_bids', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('auction', 'bidder')},
            },
        ),
        migrations.CreateModel(
            name='AuctionWatchlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('auction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='watchers', to='auctions.auction')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='watchlist', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'auction')},
            },
        ),
    ]
