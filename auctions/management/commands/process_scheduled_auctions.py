from django.core.management.base import BaseCommand
from django.utils import timezone
from auctions.models import Auction


class Command(BaseCommand):
    help = 'Convert scheduled auctions to live when their start time is reached'

    def handle(self, *args, **options):
        now = timezone.now()
        
        # Find scheduled auctions whose start time has passed
        scheduled_auctions = Auction.objects.filter(
            status='scheduled',
            start_time__lte=now
        )
        
        count = 0
        for auction in scheduled_auctions:
            # Only convert timed auctions (live auctions start immediately)
            if auction.auction_type == 'timed':
                auction.status = 'live'
                auction.save()
                count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Converted auction "{auction.title}" (ID: {auction.id}) to live'
                    )
                )
        
        if count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully converted {count} scheduled auctions to live')
            )
        else:
            self.stdout.write('No scheduled auctions to convert')
