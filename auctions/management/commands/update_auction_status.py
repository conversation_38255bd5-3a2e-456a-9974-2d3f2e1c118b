from django.core.management.base import BaseCommand
from django.utils import timezone
from auctions.models import Auction


class Command(BaseCommand):
    help = 'Update auction statuses for expired auctions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        now = timezone.now()
        
        # Find expired live auctions
        expired_auctions = Auction.objects.filter(
            status='live',
            end_time__lte=now
        )
        
        count = expired_auctions.count()
        
        if count == 0:
            self.stdout.write(
                self.style.SUCCESS('No expired auctions found.')
            )
            return
        
        self.stdout.write(f'Found {count} expired auctions:')
        
        for auction in expired_auctions:
            self.stdout.write(f'  - {auction.title} (ended at {auction.end_time})')
            
            if not dry_run:
                auction.status = 'ended'
                auction.save()
                self.stdout.write(f'    ✓ Updated to "ended" status')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: Would update {count} auctions. Use --dry-run=false to apply changes.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {count} auction statuses.')
            )
