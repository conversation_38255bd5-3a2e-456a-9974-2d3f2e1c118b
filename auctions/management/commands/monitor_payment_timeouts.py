from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from auctions.models import Auction
from django.db.models import Q


class Command(BaseCommand):
    help = 'Monitor payment timeouts and show current status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--watch',
            action='store_true',
            help='Watch mode - continuously monitor every 30 seconds',
        )
        parser.add_argument(
            '--show-all',
            action='store_true',
            help='Show all auctions with payment deadlines',
        )

    def handle(self, *args, **options):
        if options['watch']:
            self.watch_mode()
        else:
            self.show_status(show_all=options['show_all'])

    def show_status(self, show_all=False):
        """Show current payment timeout status"""
        now = timezone.now()
        
        # Get auctions with payment deadlines
        query = Q(
            status='ended',
            winner__isnull=False,
            payment_deadline__isnull=False,
            payment_received=False
        )
        
        if not show_all:
            # Only show auctions with deadlines in the next hour or already expired
            query &= Q(payment_deadline__lte=now + timedelta(hours=1))
        
        auctions = Auction.objects.filter(query).order_by('payment_deadline')
        
        if not auctions.exists():
            self.stdout.write(
                self.style.SUCCESS('No auctions with pending payment deadlines')
            )
            return
        
        self.stdout.write(f'\n=== Payment Timeout Status ({now.strftime("%Y-%m-%d %H:%M:%S")}) ===\n')
        
        expired_count = 0
        warning_count = 0
        
        for auction in auctions:
            time_diff = auction.payment_deadline - now
            
            if time_diff.total_seconds() < 0:
                # Expired
                status = self.style.ERROR('EXPIRED')
                time_str = f'{abs(time_diff)} ago'
                expired_count += 1
            elif time_diff.total_seconds() < 300:  # Less than 5 minutes
                # Warning
                status = self.style.WARNING('WARNING')
                time_str = f'{time_diff} remaining'
                warning_count += 1
            else:
                # OK
                status = self.style.SUCCESS('OK')
                time_str = f'{time_diff} remaining'
            
            self.stdout.write(
                f'Auction {auction.id:4d} | {auction.title[:30]:30s} | '
                f'Winner: {auction.winner.username:15s} | '
                f'Amount: ${auction.current_price:8.2f} | '
                f'Deadline: {auction.payment_deadline.strftime("%H:%M:%S")} | '
                f'Status: {status} | {time_str}'
            )
        
        # Summary
        self.stdout.write(f'\n=== Summary ===')
        self.stdout.write(f'Total auctions: {auctions.count()}')
        self.stdout.write(f'Expired: {expired_count}')
        self.stdout.write(f'Warning (< 5 min): {warning_count}')
        self.stdout.write(f'OK: {auctions.count() - expired_count - warning_count}')
        
        # Show next actions
        if expired_count > 0:
            self.stdout.write(
                self.style.ERROR(
                    f'\n⚠️  {expired_count} auction(s) have expired payment deadlines!'
                )
            )
            self.stdout.write('Run: python manage.py test_payment_timeout --run-timeout-check')
        
        if warning_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f'\n⏰ {warning_count} auction(s) have payment deadlines expiring soon!'
                )
            )
            self.stdout.write('Run: python manage.py test_payment_timeout --send-reminders')

    def watch_mode(self):
        """Continuously monitor payment timeouts"""
        import time
        
        self.stdout.write('Starting payment timeout monitoring (Ctrl+C to stop)...\n')
        
        try:
            while True:
                # Clear screen (works on most terminals)
                import os
                os.system('clear' if os.name == 'posix' else 'cls')
                
                self.show_status()
                
                self.stdout.write('\n' + '='*80)
                self.stdout.write('Refreshing in 30 seconds... (Ctrl+C to stop)')
                
                time.sleep(30)
                
        except KeyboardInterrupt:
            self.stdout.write('\n\nMonitoring stopped.')

    def get_auction_summary(self):
        """Get a summary of auction payment statuses"""
        now = timezone.now()
        
        # Count different categories
        total_pending = Auction.objects.filter(
            status='ended',
            winner__isnull=False,
            payment_deadline__isnull=False,
            payment_received=False
        ).count()
        
        expired = Auction.objects.filter(
            status='ended',
            winner__isnull=False,
            payment_deadline__lt=now,
            payment_received=False
        ).count()
        
        warning = Auction.objects.filter(
            status='ended',
            winner__isnull=False,
            payment_deadline__gte=now,
            payment_deadline__lte=now + timedelta(minutes=5),
            payment_received=False
        ).count()
        
        return {
            'total_pending': total_pending,
            'expired': expired,
            'warning': warning,
            'ok': total_pending - expired - warning
        }
