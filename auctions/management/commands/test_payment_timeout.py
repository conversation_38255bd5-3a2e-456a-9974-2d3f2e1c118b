from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from auctions.models import Auction, Bid
from accounts.models import User
from notifications.tasks import handle_payment_timeouts, send_payment_deadline_reminders


class Command(BaseCommand):
    help = 'Test the 20-minute payment timeout system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-auction',
            action='store_true',
            help='Create a test auction with expired payment deadline',
        )
        parser.add_argument(
            '--run-timeout-check',
            action='store_true',
            help='Run the payment timeout check manually',
        )
        parser.add_argument(
            '--send-reminders',
            action='store_true',
            help='Send payment deadline reminders',
        )
        parser.add_argument(
            '--auction-id',
            type=int,
            help='Specific auction ID to test with',
        )

    def handle(self, *args, **options):
        if options['create_test_auction']:
            self.create_test_auction()
        
        if options['run_timeout_check']:
            self.run_timeout_check()
        
        if options['send_reminders']:
            self.send_reminders()
        
        if options['auction_id']:
            self.test_specific_auction(options['auction_id'])

    def create_test_auction(self):
        """Create a test auction with expired payment deadline"""
        self.stdout.write('Creating test auction with expired payment deadline...')
        
        try:
            # Get or create test users
            seller, _ = User.objects.get_or_create(
                username='test_seller',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'Seller',
                    'user_type': 'seller',
                }
            )
            
            buyer1, _ = User.objects.get_or_create(
                username='test_buyer1',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'Buyer1',
                    'user_type': 'buyer',
                }
            )
            
            buyer2, _ = User.objects.get_or_create(
                username='test_buyer2',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'Buyer2',
                    'user_type': 'buyer',
                }
            )
            
            # Create test auction
            auction = Auction.objects.create(
                title='Test Fish Auction - Payment Timeout',
                description='This is a test auction for payment timeout testing',
                fish_type='Salmon',
                quantity=10.0,
                quality_grade='A',
                starting_price=50.0,
                current_price=100.0,
                seller=seller,
                status='ended',
                winner=buyer1,
                payment_deadline=timezone.now() - timedelta(minutes=5),  # Expired 5 minutes ago
                payment_received=False,
                end_time=timezone.now() - timedelta(minutes=25),  # Ended 25 minutes ago
            )
            
            # Create bids
            Bid.objects.create(
                auction=auction,
                bidder=buyer1,
                amount=100.0,
                created_at=timezone.now() - timedelta(minutes=30),
            )
            
            Bid.objects.create(
                auction=auction,
                bidder=buyer2,
                amount=90.0,
                created_at=timezone.now() - timedelta(minutes=32),
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Created test auction {auction.id} with expired payment deadline'
                )
            )
            self.stdout.write(f'Winner: {buyer1.username} (payment deadline expired)')
            self.stdout.write(f'Next bidder: {buyer2.username} (should be reassigned)')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create test auction: {str(e)}')
            )

    def run_timeout_check(self):
        """Run the payment timeout check manually"""
        self.stdout.write('Running payment timeout check...')
        
        try:
            result = handle_payment_timeouts.apply()
            self.stdout.write(
                self.style.SUCCESS(f'Payment timeout check completed: {result.result}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to run timeout check: {str(e)}')
            )

    def send_reminders(self):
        """Send payment deadline reminders"""
        self.stdout.write('Sending payment deadline reminders...')
        
        try:
            result = send_payment_deadline_reminders.apply()
            self.stdout.write(
                self.style.SUCCESS(f'Payment reminders sent: {result.result}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to send reminders: {str(e)}')
            )

    def test_specific_auction(self, auction_id):
        """Test payment timeout for a specific auction"""
        self.stdout.write(f'Testing payment timeout for auction {auction_id}...')
        
        try:
            auction = Auction.objects.get(id=auction_id)
            
            self.stdout.write(f'Auction: {auction.title}')
            self.stdout.write(f'Status: {auction.status}')
            self.stdout.write(f'Winner: {auction.winner.username if auction.winner else "None"}')
            self.stdout.write(f'Payment deadline: {auction.payment_deadline}')
            self.stdout.write(f'Payment received: {auction.payment_received}')
            
            if auction.payment_deadline:
                now = timezone.now()
                if auction.payment_deadline < now:
                    self.stdout.write(
                        self.style.WARNING(
                            f'Payment deadline expired {now - auction.payment_deadline} ago'
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Payment deadline in {auction.payment_deadline - now}'
                        )
                    )
            
            # Show bids
            bids = auction.bids.all()[:5]
            self.stdout.write(f'\nTop 5 bids:')
            for i, bid in enumerate(bids, 1):
                self.stdout.write(f'{i}. {bid.bidder.username}: ${bid.amount}')
            
        except Auction.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Auction {auction_id} not found')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to test auction: {str(e)}')
            )
