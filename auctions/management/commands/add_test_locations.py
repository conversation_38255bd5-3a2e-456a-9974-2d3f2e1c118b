from django.core.management.base import BaseCommand
from django.utils import timezone
from auctions.models import Auction, SellerLocation
import random


class Command(BaseCommand):
    help = 'Add test location data to existing auctions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of auctions to add location data to'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        # Dubai fishing locations (realistic coordinates)
        dubai_locations = [
            {
                'name': 'Dubai Marina',
                'latitude': 25.0772,
                'longitude': 55.1395,
                'address': 'Dubai Marina, Dubai, UAE'
            },
            {
                'name': 'Jumeirah Beach',
                'latitude': 25.2048,
                'longitude': 55.2708,
                'address': 'Jumeirah Beach, Dubai, UAE'
            },
            {
                'name': 'Al Mamzar Beach',
                'latitude': 25.2867,
                'longitude': 55.3458,
                'address': 'Al Mamzar Beach, Dubai, UAE'
            },
            {
                'name': 'Kite Beach',
                'latitude': 25.2285,
                'longitude': 55.2530,
                'address': 'Kite Beach, Dubai, UAE'
            },
            {
                'name': 'Dubai Creek',
                'latitude': 25.2697,
                'longitude': 55.3095,
                'address': 'Dubai Creek, Dubai, UAE'
            },
            {
                'name': 'Jebel Ali Beach',
                'latitude': 24.9792,
                'longitude': 55.0326,
                'address': 'Jebel Ali Beach, Dubai, UAE'
            },
            {
                'name': 'Umm Suqeim Beach',
                'latitude': 25.1424,
                'longitude': 55.1848,
                'address': 'Umm Suqeim Beach, Dubai, UAE'
            },
            {
                'name': 'Al Sufouh Beach',
                'latitude': 25.1186,
                'longitude': 55.1694,
                'address': 'Al Sufouh Beach, Dubai, UAE'
            },
            {
                'name': 'Dubai Harbour',
                'latitude': 25.0969,
                'longitude': 55.1376,
                'address': 'Dubai Harbour, Dubai, UAE'
            },
            {
                'name': 'La Mer Beach',
                'latitude': 25.2285,
                'longitude': 55.2530,
                'address': 'La Mer Beach, Dubai, UAE'
            }
        ]

        # Get auctions without location data
        auctions = Auction.objects.filter(
            latitude__isnull=True,
            longitude__isnull=True
        )[:count]

        if not auctions:
            self.stdout.write(
                self.style.WARNING('No auctions found without location data')
            )
            return

        updated_count = 0
        location_records_created = 0

        for auction in auctions:
            # Select a random location
            location = random.choice(dubai_locations)
            
            # Add some random variation to coordinates (within ~500m radius)
            lat_variation = random.uniform(-0.005, 0.005)
            lng_variation = random.uniform(-0.005, 0.005)
            
            final_lat = location['latitude'] + lat_variation
            final_lng = location['longitude'] + lng_variation

            # Update auction with location data
            auction.latitude = final_lat
            auction.longitude = final_lng
            auction.location_updated_at = timezone.now()
            auction.is_location_live = random.choice([True, False])
            auction.save(update_fields=[
                'latitude', 'longitude', 'location_updated_at', 'is_location_live'
            ])

            # Create seller location record
            seller_location = SellerLocation.objects.create(
                seller=auction.seller,
                auction=auction,
                latitude=final_lat,
                longitude=final_lng,
                accuracy=random.uniform(5.0, 15.0),  # 5-15 meters accuracy
                altitude=random.uniform(0.0, 10.0),  # 0-10 meters altitude
                heading=random.uniform(0.0, 360.0),  # Random heading
                speed=random.uniform(0.0, 5.0),      # 0-5 m/s speed
                status=random.choice(['online', 'fishing', 'returning', 'at_dock']),
                address=location['address'],
                is_active=auction.is_location_live
            )

            updated_count += 1
            location_records_created += 1

            self.stdout.write(
                f'✅ Updated auction "{auction.title}" with location: {location["name"]}'
            )

        # Create additional historical location records for some auctions
        for auction in auctions[:count//2]:
            # Create 2-5 historical location records
            num_records = random.randint(2, 5)
            
            for i in range(num_records):
                # Select a random location
                location = random.choice(dubai_locations)
                
                # Add variation
                lat_variation = random.uniform(-0.01, 0.01)
                lng_variation = random.uniform(-0.01, 0.01)
                
                # Create historical record
                historical_time = timezone.now() - timezone.timedelta(
                    hours=random.randint(1, 72)  # 1-72 hours ago
                )
                
                SellerLocation.objects.create(
                    seller=auction.seller,
                    auction=auction,
                    latitude=location['latitude'] + lat_variation,
                    longitude=location['longitude'] + lng_variation,
                    accuracy=random.uniform(5.0, 20.0),
                    altitude=random.uniform(0.0, 15.0),
                    heading=random.uniform(0.0, 360.0),
                    speed=random.uniform(0.0, 8.0),
                    status=random.choice(['online', 'fishing', 'returning', 'at_dock']),
                    address=location['address'],
                    is_active=False,  # Historical records are not active
                    timestamp=historical_time
                )
                
                location_records_created += 1

        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 Successfully updated {updated_count} auctions with location data'
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                f'📍 Created {location_records_created} location records'
            )
        )
        
        # Show summary
        total_auctions = Auction.objects.count()
        auctions_with_location = Auction.objects.filter(
            latitude__isnull=False,
            longitude__isnull=False
        ).count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Summary:'
                f'\n   Total auctions: {total_auctions}'
                f'\n   Auctions with location: {auctions_with_location}'
                f'\n   Live location sharing: {Auction.objects.filter(is_location_live=True).count()}'
                f'\n   Total location records: {SellerLocation.objects.count()}'
            )
        )
