#!/usr/bin/env python3

import os
import sys
import django
import requests
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from auctions.models import Auction, Bid, FishCategory
from payments.models import Payment
from delivery.models import Delivery
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile

User = get_user_model()

class DeliveryTrackingTester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.session = requests.Session()
        self.test_user = None
        self.seller_user = None
        self.token = None
        
    def setup_test_users(self):
        """Create or get test users"""
        try:
            self.test_user = User.objects.get(username='delivery_test_buyer')
            print(f"✅ Using existing buyer: {self.test_user.username}")
        except User.DoesNotExist:
            self.test_user = User.objects.create_user(
                username='delivery_test_buyer',
                email='<EMAIL>',
                password='testpass123',
                wallet_balance=Decimal('300.00')
            )
            print(f"✅ Created buyer: {self.test_user.username}")
        
        try:
            self.seller_user = User.objects.get(username='delivery_test_seller')
            print(f"✅ Using existing seller: {self.seller_user.username}")
        except User.DoesNotExist:
            self.seller_user = User.objects.create_user(
                username='delivery_test_seller',
                email='<EMAIL>',
                password='testpass123',
                wallet_balance=Decimal('50.00')
            )
            print(f"✅ Created seller: {self.seller_user.username}")
    
    def authenticate(self):
        """Authenticate test user"""
        try:
            response = self.session.post(f"{self.base_url}/auth/login/", json={
                'username': 'delivery_test_buyer',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                data = response.json()
                self.token = data['tokens']['access']
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def create_test_auction_with_payment(self):
        """Create a test auction, make payment, and check delivery creation"""
        try:
            # Get or create fish category
            fish_category, _ = FishCategory.objects.get_or_create(
                name="Tuna",
                defaults={'name_ar': 'تونة'}
            )
            
            # Create a simple test image
            test_image = SimpleUploadedFile(
                "test_tuna.jpg",
                b"fake image content",
                content_type="image/jpeg"
            )
            
            # Create auction
            auction = Auction.objects.create(
                title="Test Tuna for Delivery Tracking",
                description="Test auction for delivery tracking testing",
                fish_category=fish_category,
                fish_type="Tuna",
                quantity=1,
                weight=Decimal('8.0'),
                catch_date=timezone.now().date() - timedelta(days=1),
                catch_location="Test Harbor",
                starting_price=Decimal('30.00'),
                current_price=Decimal('30.00'),
                target_price=Decimal('60.00'),
                seller=self.seller_user,
                status='ended',  # Set as ended
                start_time=timezone.now() - timedelta(hours=3),
                end_time=timezone.now() - timedelta(minutes=45),
                winner=self.test_user,  # Set buyer as winner
                payment_deadline=timezone.now() + timedelta(minutes=10),  # 10 minutes to pay
                payment_received=False,
                main_image=test_image
            )
            
            # Create a winning bid
            bid = Bid.objects.create(
                auction=auction,
                bidder=self.test_user,
                amount=Decimal('55.00'),
                timestamp=timezone.now() - timedelta(minutes=50)
            )
            
            # Update auction current price
            auction.current_price = bid.amount
            auction.save()
            
            print(f"✅ Created test auction: {auction.title} (ID: {auction.id})")
            print(f"   Winner: {auction.winner.username}")
            print(f"   Final price: ${auction.current_price}")
            
            # Process payment to trigger delivery creation
            print(f"\n💳 Processing payment to trigger delivery creation...")
            payment_response = self.session.post(f"{self.base_url}/payments/process/{auction.id}/", json={
                'payment_method': 'wallet'
            })
            
            if payment_response.status_code == 200:
                print(f"✅ Payment processed successfully")
                
                # Check if delivery was created
                auction.refresh_from_db()
                if hasattr(auction, 'delivery'):
                    delivery = auction.delivery
                    print(f"✅ Delivery created: {delivery.tracking_number}")
                    print(f"   Status: {delivery.status}")
                    print(f"   Pickup: {delivery.pickup_address}")
                    return auction, delivery
                else:
                    print(f"❌ No delivery created for auction")
                    return auction, None
            else:
                print(f"❌ Payment failed: {payment_response.text}")
                return auction, None
            
        except Exception as e:
            print(f"❌ Failed to create test auction with payment: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def test_delivery_api(self, auction_id):
        """Test delivery API endpoints"""
        try:
            print(f"\n📦 Testing delivery API endpoints...")
            
            # Test get deliveries
            response = self.session.get(f"{self.base_url}/delivery/")
            print(f"Get deliveries response: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                deliveries = data.get('results', data) if isinstance(data, dict) and 'results' in data else data
                print(f"✅ Found {len(deliveries)} deliveries")
                
                # Find delivery for our auction
                auction_delivery = None
                for delivery in deliveries:
                    if delivery['auction']['id'] == auction_id:
                        auction_delivery = delivery
                        break
                
                if auction_delivery:
                    print(f"✅ Found delivery for auction {auction_id}")
                    print(f"   Tracking number: {auction_delivery['tracking_number']}")
                    print(f"   Status: {auction_delivery['status']}")
                    print(f"   Estimated delivery: {auction_delivery.get('estimated_delivery_time', 'N/A')}")
                    
                    # Test get delivery details
                    delivery_id = auction_delivery['id']
                    detail_response = self.session.get(f"{self.base_url}/delivery/{delivery_id}/")
                    
                    if detail_response.status_code == 200:
                        print(f"✅ Retrieved delivery details successfully")
                        return True
                    else:
                        print(f"❌ Failed to get delivery details: {detail_response.status_code}")
                        return False
                else:
                    print(f"❌ No delivery found for auction {auction_id}")
                    return False
            else:
                print(f"❌ Failed to get deliveries: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Delivery API test error: {e}")
            return False
    
    def run_tests(self):
        """Run all delivery tracking tests"""
        print("🚀 Starting Delivery Tracking Tests")
        print("=" * 50)
        
        # Setup
        self.setup_test_users()
        if not self.authenticate():
            return
        
        # Create test auction with payment
        auction, delivery = self.create_test_auction_with_payment()
        if not auction:
            return
        
        # Test cases
        test_results = []
        
        # Test 1: Check delivery creation
        print("\n📝 Test 1: Check delivery creation")
        result1 = delivery is not None
        test_results.append(("Delivery creation", result1))
        
        # Test 2: Test delivery API
        print("\n📝 Test 2: Test delivery API endpoints")
        result2 = self.test_delivery_api(auction.id)
        test_results.append(("Delivery API", result2))
        
        # Results summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            print("🎉 All delivery tracking tests PASSED!")
        else:
            print("⚠️ Some delivery tracking tests FAILED!")

if __name__ == "__main__":
    tester = DeliveryTrackingTester()
    tester.run_tests()
