#!/bin/bash

# Simple Backend Starter - Skips problematic dependencies
# Starts the Fish Auction backend with all working components

echo "🐟 Fish Auction Backend - Simple Start"
echo "======================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Setup environment paths
export PATH="/opt/homebrew/bin:$PATH"
export PATH="/Users/<USER>/flutter/bin:$PATH"
eval "$(/opt/homebrew/bin/brew shellenv)" 2>/dev/null || true

echo -e "${GREEN}✅ Environment paths configured${NC}"

# Activate virtual environment
echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Test Django setup
echo -e "${BLUE}🔍 Testing Django setup...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
print('✅ Django setup successful')
"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Django setup failed${NC}"
    exit 1
fi

# Run migrations
echo -e "${BLUE}🗄️ Running database migrations...${NC}"
python manage.py migrate

# Kill any existing processes
echo -e "${BLUE}🔄 Cleaning up existing processes...${NC}"
pkill -f "runserver 8000" 2>/dev/null || true
pkill -f "daphne.*fish_auction" 2>/dev/null || true
pkill -f "celery.*fish_auction" 2>/dev/null || true
pkill -f "ngrok http 8000" 2>/dev/null || true

# Start Django server
echo -e "${BLUE}🚀 Starting Django server...${NC}"
python manage.py runserver 8000 --verbosity=2 &
DJANGO_PID=$!

# Wait for Django to start
echo -e "${BLUE}⏳ Waiting for Django server...${NC}"
sleep 5

if ! lsof -i :8000 >/dev/null 2>&1; then
    echo -e "${RED}❌ Django server failed to start${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Django server running on port 8000${NC}"

# Start Celery worker
echo -e "${BLUE}🔄 Starting Celery worker...${NC}"
celery -A fish_auction worker --loglevel=info &
CELERY_WORKER_PID=$!

# Start Celery beat
echo -e "${BLUE}⏰ Starting Celery beat...${NC}"
celery -A fish_auction beat --loglevel=info &
CELERY_BEAT_PID=$!

# Start ngrok
echo -e "${BLUE}🌐 Starting ngrok tunnel...${NC}"
ngrok http 8000 &
NGROK_PID=$!

# Wait for ngrok
sleep 5

# Get ngrok URL
NGROK_URL=""
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | python -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data.get('tunnels', []):
        if tunnel.get('proto') == 'https':
            print(tunnel.get('public_url', ''))
            break
except:
    pass
" 2>/dev/null)
    
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    sleep 1
done

if [ -z "$NGROK_URL" ]; then
    echo -e "${YELLOW}⚠️ Could not get ngrok URL automatically${NC}"
    NGROK_URL="https://[your-ngrok-url].ngrok.io"
else
    echo -e "${GREEN}✅ ngrok tunnel: $NGROK_URL${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Backend is running!${NC}"
echo "================================"
echo "📱 Django: http://localhost:8000"
echo "🌐 Public: $NGROK_URL"
echo "📊 ngrok: http://localhost:4040"
echo ""
echo -e "${BLUE}📋 API Endpoints:${NC}"
echo "• Auth: $NGROK_URL/api/auth/"
echo "• Auctions: $NGROK_URL/api/auctions/"
echo "• Payments: $NGROK_URL/api/payments/"
echo "• Admin: $NGROK_URL/api/auth/admin/verification/"
echo ""
echo -e "${GREEN}📱 Ready for Flutter app!${NC}"
echo "cd fish_auction_app"
echo "flutter run -d ios --verbose"
echo ""
echo "🛑 Press Ctrl+C to stop all services"

# Cleanup function
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping all services...${NC}"
    
    [ ! -z "$DJANGO_PID" ] && kill $DJANGO_PID 2>/dev/null
    [ ! -z "$CELERY_WORKER_PID" ] && kill $CELERY_WORKER_PID 2>/dev/null
    [ ! -z "$CELERY_BEAT_PID" ] && kill $CELERY_BEAT_PID 2>/dev/null
    [ ! -z "$NGROK_PID" ] && kill $NGROK_PID 2>/dev/null
    
    pkill -f "runserver 8000" 2>/dev/null || true
    pkill -f "celery.*fish_auction" 2>/dev/null || true
    pkill -f "ngrok http 8000" 2>/dev/null || true
    
    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

trap cleanup INT TERM

# Keep running
while true; do
    sleep 1
done
