#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from payments.models import WalletTransaction

User = get_user_model()

def debug_withdrawal():
    print("🔍 Debugging Withdrawal Functionality")
    print("=" * 50)
    
    try:
        # Get test user
        user = User.objects.get(username='withdraw_test_user')
        print(f"✅ Found user: {user.username}")
        print(f"   Current balance: ${user.wallet_balance}")
        print(f"   Balance type: {type(user.wallet_balance)}")
        
        # Test withdrawal logic
        amount = 25.0
        decimal_amount = Decimal(str(amount))
        print(f"\n💰 Testing withdrawal of ${amount}")
        print(f"   Float amount: {amount}")
        print(f"   Decimal amount: {decimal_amount}")
        print(f"   Sufficient balance: {user.wallet_balance >= decimal_amount}")
        
        # Test transaction creation
        print(f"\n📝 Testing transaction creation...")
        try:
            transaction = WalletTransaction.objects.create(
                user=user,
                transaction_type='withdrawal',
                amount=decimal_amount,
                status='completed',
                description="Debug test withdrawal"
            )
            print(f"✅ Transaction created successfully: ID {transaction.id}")
            
            # Test balance update
            print(f"\n💳 Testing balance update...")
            original_balance = user.wallet_balance
            user.wallet_balance -= decimal_amount
            user.save()
            user.refresh_from_db()
            
            print(f"   Original balance: ${original_balance}")
            print(f"   New balance: ${user.wallet_balance}")
            print(f"   Expected balance: ${original_balance - decimal_amount}")
            
            if user.wallet_balance == original_balance - decimal_amount:
                print("✅ Balance update successful")
            else:
                print("❌ Balance update failed")
                
        except Exception as e:
            print(f"❌ Transaction creation failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Check all transactions for this user
        print(f"\n📊 User's transaction history:")
        transactions = WalletTransaction.objects.filter(user=user).order_by('-created_at')
        for i, tx in enumerate(transactions[:5]):
            print(f"   {i+1}. {tx.transaction_type}: ${tx.amount} - {tx.status} - {tx.description}")
            
    except User.DoesNotExist:
        print("❌ Test user not found")
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_withdrawal()
