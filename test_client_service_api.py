#!/usr/bin/env python3
"""
Test client service executions API to see what data the client receives
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceExecution
from accounts.models import User
import json


def test_client_service_api():
    """Test what the client sees when fetching service executions"""
    print("🧪 Testing Client Service Executions API")
    print("=" * 50)
    
    # Find a service execution with an image
    execution = ServiceExecution.objects.filter(report_images__isnull=False).exclude(report_images=[]).first()
    if not execution:
        print("❌ No service execution with images found")
        return
    
    print(f"📋 Service Execution: {execution.id}")
    print(f"   Status: {execution.status}")
    print(f"   Broker: {execution.broker.username}")
    print(f"   Client: {execution.service_request.client.username}")
    print(f"   Report Text: '{execution.report_text}'")
    print(f"   Report Images: {execution.report_images}")
    
    # Get the client
    client = execution.service_request.client
    
    # Create API client and authenticate as client
    api_client = APIClient()
    refresh = RefreshToken.for_user(client)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as client: {client.username}")
    
    # Test client service executions API
    url = '/api/broker/executions/client/'
    print(f"\n📤 GET {url}")
    
    response = api_client.get(url)
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API call successful!")
        
        if hasattr(response, 'data'):
            data = response.data
            
            if isinstance(data, dict) and 'results' in data:
                # Paginated response
                results = data['results']
                print(f"   Results count: {len(results)}")
                
                for i, execution_data in enumerate(results):
                    if execution_data.get('id') == str(execution.id):
                        print(f"\n   📋 Found our execution (#{i+1}):")
                        print(f"      ID: {execution_data.get('id')}")
                        print(f"      Status: {execution_data.get('status')}")
                        print(f"      Report Text: '{execution_data.get('report_text')}'")
                        print(f"      Report Images: {execution_data.get('report_images')}")
                        
                        # Check image URLs
                        report_images = execution_data.get('report_images', [])
                        if report_images:
                            print(f"      📸 Image URLs for client:")
                            for j, image_url in enumerate(report_images):
                                print(f"         {j+1}. {image_url}")
                                
                                # Check what the Flutter app would construct
                                if image_url.startswith('/'):
                                    full_url = f"http://localhost:8001{image_url}"
                                    print(f"            Flutter would use: {full_url}")
                        else:
                            print(f"      ❌ No images in API response")
                        break
                else:
                    print(f"   ❌ Our execution not found in results")
                    
            elif isinstance(data, list):
                # Direct list response
                print(f"   Results count: {len(data)}")
                # Similar processing for list format
            else:
                print(f"   Unexpected data format: {type(data)}")
        else:
            print(f"   Content: {response.content.decode()}")
    else:
        print(f"❌ API call failed!")
        if hasattr(response, 'data'):
            print(f"   Error: {response.data}")
        else:
            print(f"   Content: {response.content.decode()}")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_client_service_api()
