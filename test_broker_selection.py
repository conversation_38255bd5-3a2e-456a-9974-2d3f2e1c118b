#!/usr/bin/env python3
"""
Test broker selection API endpoint
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceRequest, BrokerQuote
from accounts.models import User
from notifications.models import Notification


def test_broker_selection():
    """Test the broker selection endpoint"""
    print("🧪 Testing Broker Selection API")
    print("=" * 50)
    
    # Find a service request with quotes
    service_request = ServiceRequest.objects.filter(quotes__isnull=False).first()
    if not service_request:
        print("❌ No service request with quotes found")
        return
    
    print(f"📋 Service Request: {service_request.id}")
    print(f"   Client: {service_request.client.username}")
    print(f"   Status: {service_request.status}")
    
    # Get a quote to select
    quote = service_request.quotes.filter(status='pending').first()
    if not quote:
        print("❌ No pending quotes found")
        return
    
    print(f"📝 Quote to select: {quote.id}")
    print(f"   Broker: {quote.broker.username}")
    print(f"   Amount: {quote.amount}")
    
    # Check client wallet balance
    client = service_request.client
    print(f"💰 Client wallet balance: {client.wallet_balance}")
    
    # Create API client and authenticate as client
    api_client = APIClient()
    refresh = RefreshToken.for_user(client)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as client: {client.username}")
    
    # Count notifications before
    notifications_before = Notification.objects.count()
    broker_notifications_before = Notification.objects.filter(
        recipient=quote.broker,
        template__notification_type='broker_offer_accepted'
    ).count()
    
    print(f"\n📊 Before selection:")
    print(f"   Total notifications: {notifications_before}")
    print(f"   Broker acceptance notifications: {broker_notifications_before}")
    
    # Make API request to select broker
    url = f'/api/broker/requests/{service_request.id}/select-broker/{quote.id}/'
    print(f"\n📤 POST {url}")
    
    response = api_client.post(url, data={})
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Broker selected successfully!")
        
        if hasattr(response, 'data'):
            print(f"   Message: {response.data.get('message', 'N/A')}")
        
        # Check notifications after
        notifications_after = Notification.objects.count()
        broker_notifications_after = Notification.objects.filter(
            recipient=quote.broker,
            template__notification_type='broker_offer_accepted'
        ).count()
        
        print(f"\n📊 After selection:")
        print(f"   Total notifications: {notifications_after} (+{notifications_after - notifications_before})")
        print(f"   Broker acceptance notifications: {broker_notifications_after} (+{broker_notifications_after - broker_notifications_before})")
        
        if broker_notifications_after > broker_notifications_before:
            print("\n✅ SUCCESS: Broker acceptance notification sent!")
            
            # Show notification details
            latest_notification = Notification.objects.filter(
                recipient=quote.broker,
                template__notification_type='broker_offer_accepted'
            ).order_by('-created_at').first()
            
            if latest_notification:
                print(f"\n📨 Notification Details:")
                print(f"   Recipient: {latest_notification.recipient.username}")
                print(f"   Channel: {latest_notification.channel}")
                print(f"   Status: {latest_notification.status}")
                print(f"   Title: {latest_notification.title}")
        else:
            print("\n❌ No broker acceptance notification was sent")
        
        # Check service request status
        service_request.refresh_from_db()
        quote.refresh_from_db()
        
        print(f"\n📋 Updated Status:")
        print(f"   Service Request Status: {service_request.status}")
        print(f"   Selected Broker: {service_request.selected_broker.username if service_request.selected_broker else 'None'}")
        print(f"   Quote Status: {quote.status}")
        print(f"   Payment Held: {service_request.payment_held}")
        
    else:
        print(f"❌ Broker selection failed!")
        if hasattr(response, 'data'):
            print(f"   Error: {response.data}")
        else:
            print(f"   Content: {response.content.decode()}")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_broker_selection()
