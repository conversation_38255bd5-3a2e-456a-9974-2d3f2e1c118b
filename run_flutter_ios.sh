#!/bin/bash

# Flutter iOS Runner Script
# Simplified script to run Flutter app on iOS device

echo "📱 Flutter iOS Runner"
echo "===================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check if we're in the right directory
if [ ! -d "fish_auction_app" ]; then
    echo -e "${RED}❌ fish_auction_app directory not found${NC}"
    echo -e "${BLUE}💡 Please run this script from the project root directory${NC}"
    exit 1
fi

# Navigate to Flutter app directory
cd fish_auction_app

# Check Flutter installation
if ! command -v flutter >/dev/null 2>&1; then
    echo -e "${RED}❌ Flutter not found${NC}"
    echo -e "${BLUE}💡 Install Flutter: https://flutter.dev/docs/get-started/install${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Flutter found${NC}"

# Check Flutter permissions
echo -e "${BLUE}🔍 Checking Flutter permissions...${NC}"
if ! flutter --version >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ Flutter permission issues detected${NC}"
    echo -e "${BLUE}💡 Run ./fix_flutter_permissions.sh to fix this${NC}"
    echo -e "${BLUE}🤔 Continue anyway? (y/n)${NC}"
    read -n 1 CONTINUE
    echo ""
    if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo -e "${GREEN}✅ Flutter permissions OK${NC}"
fi

# Check Xcode installation
if ! command -v xcodebuild >/dev/null 2>&1; then
    echo -e "${RED}❌ Xcode not found${NC}"
    echo -e "${BLUE}💡 Install Xcode from App Store for iOS development${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Xcode found${NC}"

# Run flutter doctor to check setup
echo -e "${BLUE}🔍 Checking Flutter setup...${NC}"
flutter doctor --verbose

# Get dependencies
echo -e "${BLUE}📦 Getting Flutter dependencies...${NC}"
flutter pub get

# Clean build
echo -e "${BLUE}🧹 Cleaning previous builds...${NC}"
flutter clean

# Check for connected devices
echo -e "${BLUE}📱 Checking for connected devices...${NC}"
flutter devices

# List available devices
DEVICES=$(flutter devices --machine | jq -r '.[] | select(.category == "mobile") | .id')

if [ -z "$DEVICES" ]; then
    echo -e "${YELLOW}⚠️ No mobile devices found${NC}"
    echo -e "${BLUE}💡 Connect your iOS device via USB and trust this computer${NC}"
    echo -e "${BLUE}💡 Or use iOS Simulator: open -a Simulator${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Mobile devices found${NC}"

# Check if backend is running
echo -e "${BLUE}🔍 Checking if backend is running...${NC}"
if curl -s http://localhost:8000/api/ >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend is running${NC}"
else
    echo -e "${YELLOW}⚠️ Backend not detected${NC}"
    echo -e "${BLUE}💡 Make sure to run ./quick_start_macos.sh first${NC}"
    echo -e "${BLUE}🤔 Continue anyway? (y/n)${NC}"
    read -n 1 CONTINUE
    echo ""
    if [[ ! $CONTINUE =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Ask which device to use
echo -e "${BLUE}📱 Available devices:${NC}"
flutter devices

echo ""
echo -e "${BLUE}🎯 Choose deployment option:${NC}"
echo "1. iOS Device (auto-detect)"
echo "2. iOS Simulator"
echo "3. Specific device ID"
echo "4. Debug mode with verbose output"

read -p "Enter choice (1-4): " CHOICE

case $CHOICE in
    1)
        echo -e "${BLUE}🚀 Running on iOS device...${NC}"
        flutter run -d ios --verbose
        ;;
    2)
        echo -e "${BLUE}🚀 Running on iOS Simulator...${NC}"
        # Open simulator if not already open
        open -a Simulator
        sleep 3
        flutter run -d ios --simulator --verbose
        ;;
    3)
        echo -e "${BLUE}📱 Enter device ID:${NC}"
        read DEVICE_ID
        echo -e "${BLUE}🚀 Running on device: $DEVICE_ID${NC}"
        flutter run -d "$DEVICE_ID" --verbose
        ;;
    4)
        echo -e "${BLUE}🐛 Running in debug mode with maximum verbosity...${NC}"
        flutter run -d ios --verbose --debug
        ;;
    *)
        echo -e "${RED}❌ Invalid choice${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}✅ Flutter app deployment complete${NC}"
echo -e "${BLUE}💡 Check your iOS device/simulator for the app${NC}"
