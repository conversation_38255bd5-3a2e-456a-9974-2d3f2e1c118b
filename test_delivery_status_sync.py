#!/usr/bin/env python3
"""
Test script to verify delivery status synchronization between seller and buyer
"""

import os
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from delivery.models import Delivery, DeliveryUpdate
from accounts.models import User

def test_delivery_status_sync():
    """Test that buyer sees the same status as seller updates"""
    
    print("🧪 Testing Delivery Status Synchronization")
    print("=" * 50)
    
    # Get the test delivery
    try:
        delivery = Delivery.objects.get(id='e0688293-1e58-448b-b766-fb2c57b0aea2')
        seller = User.objects.get(username='azmi')
        buyer = User.objects.get(username='rabie4')
        
        print(f"📦 Delivery: {delivery.id}")
        print(f"👤 Seller: {seller.username}")
        print(f"👤 Buyer: {buyer.username}")
        print(f"🏷️ Auction: {delivery.auction.title}")
        print()
        
    except Exception as e:
        print(f"❌ Error getting test data: {e}")
        return
    
    # Reset to clean state
    print("🔄 Resetting delivery to clean state...")
    delivery.status = 'pending'
    delivery.picked_up_at = None
    delivery.delivered_at = None
    delivery.save()
    
    # Clear old updates
    DeliveryUpdate.objects.filter(delivery=delivery).delete()
    
    # Add initial update
    DeliveryUpdate.objects.create(
        delivery=delivery,
        status='pending',
        message='Payment confirmed. Seller will prepare your order for delivery.',
        created_by=seller
    )
    
    print(f"✅ Reset to status: {delivery.status}")
    print()
    
    # Test status progression
    status_flow = [
        ('picked_up', 'Package picked up by seller'),
        ('in_transit', 'Package is now in transit'),
        ('out_for_delivery', 'Package is out for delivery'),
        ('delivered', 'Package delivered successfully')
    ]
    
    for new_status, message in status_flow:
        print(f"🔄 Seller updating status to: {new_status}")
        
        # Update delivery status (simulating seller action)
        delivery.status = new_status
        
        # Update timestamps
        from django.utils import timezone
        now = timezone.now()
        if new_status == 'picked_up' and not delivery.picked_up_at:
            delivery.picked_up_at = now
        elif new_status == 'delivered' and not delivery.delivered_at:
            delivery.delivered_at = now
        
        delivery.save()
        
        # Create delivery update
        update = DeliveryUpdate.objects.create(
            delivery=delivery,
            status=new_status,
            message=message,
            created_by=seller
        )
        
        print(f"✅ Status updated to: {delivery.status}")
        print(f"📝 Update created: {update.message}")
        
        # Simulate what buyer would see
        buyer_delivery = Delivery.objects.get(id=delivery.id)
        buyer_updates = DeliveryUpdate.objects.filter(delivery=buyer_delivery).order_by('-created_at')
        
        print(f"👁️ Buyer sees:")
        print(f"   Status: {buyer_delivery.status}")
        print(f"   Status Display: {buyer_delivery.get_status_display()}")
        print(f"   Latest Update: {buyer_updates.first().message if buyer_updates.exists() else 'None'}")
        print(f"   Total Updates: {buyer_updates.count()}")
        print()
        
        # Small delay to simulate real-world timing
        time.sleep(1)
    
    print("🎉 Test completed! Buyer should see all seller status updates.")
    print()
    
    # Show final state
    print("📊 Final State:")
    print(f"   Delivery Status: {delivery.status}")
    print(f"   Picked Up At: {delivery.picked_up_at}")
    print(f"   Delivered At: {delivery.delivered_at}")
    print(f"   Total Updates: {DeliveryUpdate.objects.filter(delivery=delivery).count()}")
    
    print()
    print("🔍 All Updates (newest first):")
    updates = DeliveryUpdate.objects.filter(delivery=delivery).order_by('-created_at')
    for i, update in enumerate(updates, 1):
        print(f"   {i}. {update.status} - {update.message} ({update.created_at.strftime('%H:%M:%S')})")

if __name__ == '__main__':
    test_delivery_status_sync()
