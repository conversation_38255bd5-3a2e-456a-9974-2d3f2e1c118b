#!/usr/bin/env python3
"""
Test that the quick start script properly sets up WebSocket support
"""

import subprocess
import time
import requests
import json

def check_daphne_running():
    """Check if Daphne ASGI server is running"""
    print("🔍 Checking Daphne ASGI Server")
    print("=" * 30)
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        
        if 'daphne' in result.stdout and 'fish_auction.asgi' in result.stdout:
            print("✅ Daphne ASGI server is running")
            
            # Extract process details
            lines = result.stdout.split('\n')
            for line in lines:
                if 'daphne' in line and 'fish_auction.asgi' in line:
                    parts = line.split()
                    pid = parts[1] if len(parts) > 1 else "unknown"
                    print(f"   Process ID: {pid}")
                    print(f"   Command: daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application")
                    break
            
            return True
        else:
            print("❌ Daphne ASGI server not found")
            
            # Check if regular runserver is running instead
            if 'runserver' in result.stdout and '8000' in result.stdout:
                print("⚠️ Django runserver detected instead of Daphne")
                print("   This may cause WebSocket connection issues")
                return False
            else:
                print("❌ No Django server found running on port 8000")
                return False
                
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return False

def test_http_endpoint():
    """Test HTTP endpoint"""
    print("\n🧪 Testing HTTP Endpoint")
    print("=" * 25)
    
    try:
        response = requests.get('http://localhost:8000/admin/', timeout=5)
        print(f"✅ HTTP endpoint responding (status: {response.status_code})")
        return True
    except Exception as e:
        print(f"❌ HTTP endpoint failed: {e}")
        return False

def test_websocket_backend():
    """Test WebSocket backend functionality"""
    print("\n🧪 Testing WebSocket Backend")
    print("=" * 30)
    
    try:
        # Import Django and test channel layer
        import os
        import sys
        import django
        
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
        django.setup()
        
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        print(f"✅ Channel layer: {type(channel_layer).__name__}")
        
        # Test Redis connection
        async_to_sync(channel_layer.group_send)(
            'test_group',
            {'type': 'test_message', 'message': 'WebSocket test'}
        )
        print("✅ Redis channel layer working")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket backend test failed: {e}")
        return False

def test_ngrok_websocket_config():
    """Test ngrok WebSocket configuration"""
    print("\n🧪 Testing Ngrok WebSocket Config")
    print("=" * 35)
    
    try:
        # Get ngrok tunnels
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        data = response.json()
        
        https_tunnel = None
        for tunnel in data.get('tunnels', []):
            if tunnel.get('proto') == 'https':
                https_tunnel = tunnel['public_url']
                break
        
        if https_tunnel:
            print(f"✅ Ngrok HTTPS tunnel: {https_tunnel}")
            
            # Check if Flutter app is configured with this URL
            try:
                with open('fish_auction_app/lib/constants/app_constants.dart', 'r') as f:
                    content = f.read()
                
                import re
                ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
                
                if ws_url_match:
                    flutter_ws_url = ws_url_match.group(1)
                    expected_ws_url = https_tunnel.replace('https://', 'wss://') + '/ws'
                    
                    if flutter_ws_url == expected_ws_url:
                        print("✅ Flutter WebSocket URL matches ngrok tunnel")
                    else:
                        print("⚠️ Flutter WebSocket URL mismatch")
                        print(f"   Expected: {expected_ws_url}")
                        print(f"   Actual: {flutter_ws_url}")
                else:
                    print("❌ WebSocket URL not found in Flutter constants")
                    
            except Exception as e:
                print(f"⚠️ Could not check Flutter constants: {e}")
                
        else:
            print("❌ No HTTPS ngrok tunnel found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Ngrok test failed: {e}")
        return False

def provide_summary():
    """Provide summary and next steps"""
    print("\n🎯 Quick Start WebSocket Summary")
    print("=" * 35)
    
    print("✅ Updated quick_start_macos.sh features:")
    print("   • Uses Daphne ASGI server for WebSocket support")
    print("   • Automatically updates Flutter WebSocket URLs")
    print("   • Verifies WebSocket backend functionality")
    print("   • Proper cleanup of Daphne processes")
    print()
    
    print("📱 To test the complete setup:")
    print("1. Stop current services (Ctrl+C)")
    print("2. Run: sh quick_start_macos.sh")
    print("3. Start Flutter app: flutter run -d web-server --web-port 8080")
    print("4. Test real-time bidding on Jamin fish auction")
    print()
    
    print("🔍 WebSocket indicators:")
    print("✅ Green WiFi icon in Flutter app = Connected")
    print("✅ Console logs: '📨 WebSocket message received'")
    print("✅ Real-time price updates without refresh")

if __name__ == "__main__":
    print("🧪 Quick Start WebSocket Tester")
    print("=" * 35)
    
    all_tests_passed = True
    
    # Test Daphne
    if not check_daphne_running():
        all_tests_passed = False
    
    # Test HTTP
    if not test_http_endpoint():
        all_tests_passed = False
    
    # Test WebSocket backend
    if not test_websocket_backend():
        all_tests_passed = False
    
    # Test ngrok config
    if not test_ngrok_websocket_config():
        all_tests_passed = False
    
    # Provide summary
    provide_summary()
    
    if all_tests_passed:
        print("\n🎉 All WebSocket tests passed!")
        print("Your quick_start_macos.sh is properly configured for WebSocket support!")
    else:
        print("\n⚠️ Some tests failed")
        print("Consider restarting with: sh quick_start_macos.sh")
