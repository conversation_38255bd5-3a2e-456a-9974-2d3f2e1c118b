#!/bin/bash

# Fish Auction App - Quick Start Test (without Redis)
# For testing purposes - uses InMemoryChannelLayer

echo "🐟 Fish Auction App - Quick Start Test"
echo "======================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Detect Python command
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
else
    echo -e "${RED}❌ Python not found. Please install Python 3.8+${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Using Python: $PYTHON_CMD${NC}"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${BLUE}💡 Creating virtual environment...${NC}"
    $PYTHON_CMD -m venv venv
fi

# Activate virtual environment
echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Install dependencies
echo -e "${BLUE}📦 Installing dependencies...${NC}"
pip install -r requirements.txt

# Install WebSocket dependencies
echo -e "${BLUE}📡 Installing WebSocket dependencies...${NC}"
pip install channels daphne

# Temporarily switch to InMemoryChannelLayer for testing
echo -e "${BLUE}🔧 Configuring for test mode (InMemory channels)...${NC}"
python -c "
import re
with open('fish_auction/settings.py', 'r') as f:
    content = f.read()

# Replace Redis channel layer with InMemory for testing
content = re.sub(
    r\"CHANNEL_LAYERS = \{[^}]+\}\",
    '''CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}''',
    content,
    flags=re.DOTALL
)

with open('fish_auction/settings.py', 'w') as f:
    f.write(content)
print('✅ Configured InMemoryChannelLayer for testing')
"

# Run migrations
echo -e "${BLUE}🗄️ Setting up database...${NC}"
python manage.py migrate

# Kill any existing processes
echo -e "${BLUE}🔄 Cleaning up existing processes...${NC}"
pkill -f "runserver 8000" 2>/dev/null || true
pkill -f "daphne.*8000" 2>/dev/null || true
pkill -f "celery.*fish_auction" 2>/dev/null || true

# Start Django server with WebSocket support (ASGI required for WebSockets)
echo -e "${BLUE}🚀 Starting Django server with WebSocket support...${NC}"
echo -e "${YELLOW}📡 Using Daphne ASGI server for WebSocket support${NC}"
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application > django.log 2>&1 &
DJANGO_PID=$!

# Wait and check if Django started
sleep 3
if ! lsof -i :8000 >/dev/null 2>&1; then
    echo -e "${RED}❌ Django server failed to start${NC}"
    echo -e "${YELLOW}📋 Error log:${NC}"
    cat django.log
    exit 1
fi
echo -e "${GREEN}✅ Django server running with WebSocket support${NC}"

# Start Celery worker
echo -e "${BLUE}🔄 Starting Celery worker...${NC}"
celery -A fish_auction worker --loglevel=info > celery_worker.log 2>&1 &
CELERY_WORKER_PID=$!

# Start Celery beat
echo -e "${BLUE}⏰ Starting Celery beat...${NC}"
celery -A fish_auction beat --loglevel=info > celery_beat.log 2>&1 &
CELERY_BEAT_PID=$!

# Wait for services to start
sleep 5

# Test WebSocket support
echo -e "${BLUE}🧪 Testing WebSocket support...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
from channels.layers import get_channel_layer
layer = get_channel_layer()
print(f'✅ Channel layer: {layer.__class__.__name__}')
print('✅ WebSocket support ready')
"

# Test auto-bidding system
echo -e "${BLUE}🧪 Testing auto-bidding system...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
from auctions.models import Auction
live_auctions = Auction.objects.filter(status='live').count()
print(f'✅ Found {live_auctions} live auctions')
print('✅ Auto-bidding system ready')
" || {
    echo -e "${YELLOW}⚠️ No live auctions found - create one in admin panel${NC}"
}

echo ""
echo -e "${GREEN}🎉 Backend is running!${NC}"
echo "================================"
echo "📱 Django: http://localhost:8000"
echo "📊 Admin: http://localhost:8000/admin"
echo ""
echo "📁 Logs: django.log, celery_worker.log, celery_beat.log"
echo ""
echo -e "${BLUE}🚀 Now start Flutter app:${NC}"
echo "cd fish_auction_app"
echo "flutter pub get"
echo "flutter run -d web-server --web-port 8080"
echo ""
echo -e "${GREEN}🎯 Features Ready:${NC}"
echo "✅ WebSocket support (Daphne ASGI server)"
echo "✅ Real-time bidding updates"
echo "✅ Asynchronous auto-bidding (one by one)"
echo "✅ Server-side processing (works when app closed)"
echo "✅ Celery workers for background tasks"
echo ""
echo -e "${YELLOW}📝 Note: Using InMemoryChannelLayer for testing${NC}"
echo -e "${YELLOW}📝 For production, install Redis and use RedisChannelLayer${NC}"
echo ""
echo "🛑 Press Ctrl+C to stop all services"

# Cleanup function
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    
    [ ! -z "$DJANGO_PID" ] && kill $DJANGO_PID 2>/dev/null
    [ ! -z "$CELERY_WORKER_PID" ] && kill $CELERY_WORKER_PID 2>/dev/null
    [ ! -z "$CELERY_BEAT_PID" ] && kill $CELERY_BEAT_PID 2>/dev/null
    
    pkill -f "runserver 8000" 2>/dev/null || true
    pkill -f "daphne.*8000" 2>/dev/null || true
    pkill -f "celery.*fish_auction" 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
    exit 0
}

trap cleanup INT TERM

# Keep running
while true; do
    sleep 1
done
