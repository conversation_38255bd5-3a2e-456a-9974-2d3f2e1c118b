#!/usr/bin/env python3
"""
Setup real-time auto-bidding test with multiple users
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from decimal import Decimal

def setup_realtime_test():
    print("🔧 Setting up Real-time Auto-bidding Test...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    print(f"🎯 Auction: {live_auction.title} (ID: {live_auction.id})")
    print(f"   Current price: ${live_auction.current_price}")
    
    # Clear existing auto-bids
    print("\n🧹 Clearing existing auto-bids...")
    AutoBid.objects.filter(auction=live_auction).delete()
    
    # Get buyer users
    buyers = User.objects.filter(user_type='buyer')[:4]
    if buyers.count() < 4:
        print("❌ Need at least 4 buyer users!")
        return
    
    # Create auto-bids with different max amounts
    auto_bid_configs = [
        {'user': buyers[0], 'max_amount': 85.00},  # Highest bidder
        {'user': buyers[1], 'max_amount': 82.00},  # Second highest
        {'user': buyers[2], 'max_amount': 80.00},  # Third highest
        {'user': buyers[3], 'max_amount': 79.00},  # Fourth highest
    ]
    
    print("\n🤖 Creating auto-bids:")
    for config in auto_bid_configs:
        auto_bid = AutoBid.objects.create(
            auction=live_auction,
            bidder=config['user'],
            max_amount=Decimal(str(config['max_amount'])),
            current_bid_amount=live_auction.current_price,
            is_active=True
        )
        print(f"   ✅ {config['user'].username}: max ${config['max_amount']}")
    
    # Reset auction to a lower price for testing
    reset_price = Decimal('75.00')
    live_auction.current_price = reset_price
    live_auction.save()
    
    print(f"\n🔄 Reset auction price to: ${reset_price}")
    print(f"   This will allow all auto-bids to compete!")
    
    # Show final setup
    active_auto_bids = AutoBid.objects.filter(
        auction=live_auction,
        is_active=True,
        max_amount__gt=live_auction.current_price
    ).order_by('-max_amount')
    
    print(f"\n📊 Final Setup:")
    print(f"   Auction price: ${live_auction.current_price}")
    print(f"   Active auto-bids: {active_auto_bids.count()}")
    for ab in active_auto_bids:
        print(f"     - {ab.bidder.username}: max ${ab.max_amount}")
    
    print(f"\n🎯 Ready for real-time testing!")
    print(f"   1. Connect your Flutter app to auction {live_auction.id}")
    print(f"   2. Place a manual bid to trigger auto-bid competition")
    print(f"   3. Watch real-time updates in your app!")
    print(f"   4. Auto-bids should compete: {buyers[3].username} → {buyers[2].username} → {buyers[1].username} → {buyers[0].username}")

if __name__ == "__main__":
    setup_realtime_test()
