#!/bin/bash

# Fish Auction App - Quick Start Script with Webhook Support
# This script starts all necessary services for development

echo "🐟 Fish Auction App - Quick Start with Webhooks"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

echo -e "${BLUE}🔍 Checking requirements...${NC}"

# Check Python and set the correct command
PYTHON_CMD=""
if command_exists python3; then
    PYTHON_CMD="python3"
elif command_exists python; then
    PYTHON_CMD="python"
else
    echo -e "${RED}❌ Python is not installed${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Python is available ($PYTHON_CMD)${NC}"

# Check Redis
if ! command_exists redis-cli; then
    echo -e "${YELLOW}⚠️ Redis CLI not found. Installing Redis...${NC}"
    if command_exists brew; then
        brew install redis
    elif command_exists apt-get; then
        sudo apt-get update && sudo apt-get install redis-server
    else
        echo -e "${RED}❌ Please install Redis manually${NC}"
        exit 1
    fi
fi

# Start Redis if not running
if ! redis-cli ping >/dev/null 2>&1; then
    echo -e "${BLUE}🚀 Starting Redis...${NC}"
    if command_exists brew; then
        brew services start redis
    else
        sudo systemctl start redis-server
    fi
    sleep 2
fi
echo -e "${GREEN}✅ Redis is running${NC}"

# Check ngrok
if ! command_exists ngrok; then
    echo -e "${YELLOW}⚠️ ngrok not found. Please install it:${NC}"
    echo "  - Download from: https://ngrok.com/download"
    echo "  - Or use: npm install -g ngrok"
    echo "  - Or use: brew install ngrok (macOS)"
    exit 1
fi
echo -e "${GREEN}✅ ngrok is available${NC}"

# Activate virtual environment if it exists
if [ -d "venv" ] && [ -f "venv/bin/activate" ]; then
    echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
    source venv/bin/activate
    echo -e "${GREEN}✅ Virtual environment activated${NC}"
elif [ -d "venv" ]; then
    echo -e "${YELLOW}⚠️ Virtual environment directory exists but activation script not found${NC}"
    echo -e "${BLUE}💡 Creating new virtual environment...${NC}"
    $PYTHON_CMD -m venv venv
    source venv/bin/activate
    echo -e "${GREEN}✅ Virtual environment created and activated${NC}"
else
    echo -e "${BLUE}💡 Creating virtual environment...${NC}"
    $PYTHON_CMD -m venv venv
    source venv/bin/activate
    echo -e "${GREEN}✅ Virtual environment created and activated${NC}"
fi

# Install Python dependencies
echo -e "${BLUE}📦 Installing Python dependencies...${NC}"
pip install -r requirements.txt >/dev/null 2>&1

# Run Django migrations
echo -e "${BLUE}🗄️ Running database migrations...${NC}"
$PYTHON_CMD manage.py migrate >/dev/null 2>&1
echo -e "${GREEN}✅ Database is ready${NC}"

# Check if port 8000 is in use
if port_in_use 8000; then
    echo -e "${YELLOW}⚠️ Port 8000 is in use. Stopping existing process...${NC}"
    pkill -f "runserver 8000" 2>/dev/null
    sleep 2
fi

# Start Django server with WebSocket support (ASGI)
echo -e "${BLUE}🚀 Starting Django server with WebSocket support...${NC}"
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application > django.log 2>&1 &
DJANGO_PID=$!
sleep 3

# Check if Django server started successfully
if ! port_in_use 8000; then
    echo -e "${RED}❌ Failed to start Django server${NC}"
    echo -e "${YELLOW}📋 Django server log:${NC}"
    cat django.log
    exit 1
fi
echo -e "${GREEN}✅ Django server running on http://localhost:8000${NC}"

# Start Celery worker in background
echo -e "${BLUE}🔄 Starting Celery worker...${NC}"
celery -A fish_auction worker --loglevel=info > celery_worker.log 2>&1 &
CELERY_WORKER_PID=$!

# Start Celery beat in background
echo -e "${BLUE}⏰ Starting Celery beat...${NC}"
celery -A fish_auction beat --loglevel=info > celery_beat.log 2>&1 &
CELERY_BEAT_PID=$!

# Start ngrok tunnel
echo -e "${BLUE}🌐 Starting ngrok tunnel...${NC}"
ngrok http 8000 > ngrok.log 2>&1 &
NGROK_PID=$!
sleep 5

# Get ngrok public URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | $PYTHON_CMD -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data['tunnels']:
        if tunnel['proto'] == 'https':
            print(tunnel['public_url'])
            break
except:
    pass
")

if [ -z "$NGROK_URL" ]; then
    echo -e "${RED}❌ Failed to get ngrok URL${NC}"
    echo "Check ngrok.log for details"
else
    echo -e "${GREEN}✅ ngrok tunnel active: $NGROK_URL${NC}"
    
    # Display webhook setup instructions
    echo ""
    echo -e "${YELLOW}🎯 STRIPE WEBHOOK SETUP INSTRUCTIONS${NC}"
    echo "================================================"
    echo -e "${BLUE}📍 Your webhook URL: $NGROK_URL/api/payments/stripe/webhook/${NC}"
    echo ""
    echo "📋 Steps to complete setup:"
    echo "1. Go to Stripe Dashboard: https://dashboard.stripe.com/"
    echo "2. Navigate to: Developers → Webhooks"
    echo "3. Click 'Add endpoint'"
    echo "4. Enter endpoint URL: $NGROK_URL/api/payments/stripe/webhook/"
    echo "5. Select these events:"
    echo "   - payment_intent.succeeded"
    echo "   - payment_intent.payment_failed"
    echo "   - invoice.payment_succeeded"
    echo "6. Click 'Add endpoint'"
    echo "7. Copy the 'Signing secret' (starts with whsec_)"
    echo "8. Add it to your .env file:"
    echo "   STRIPE_WEBHOOK_SECRET=whsec_your_signing_secret_here"
    echo ""
    echo -e "${GREEN}🧪 Test your webhook:${NC}"
    echo "1. In Stripe Dashboard, go to your webhook"
    echo "2. Click 'Send test webhook'"
    echo "3. Select 'payment_intent.succeeded'"
    echo "4. Check Django logs: tail -f django.log"
fi

echo ""
echo -e "${GREEN}🎉 All services are running!${NC}"
echo "================================================"
echo "📱 Django Backend: http://localhost:8000"
echo "🌐 Public URL: $NGROK_URL"
echo "📊 ngrok Dashboard: http://localhost:4040"
echo ""
echo "📁 Log files:"
echo "  - Django: django.log"
echo "  - Celery Worker: celery_worker.log"
echo "  - Celery Beat: celery_beat.log"
echo "  - ngrok: ngrok.log"
echo ""
echo "🛑 To stop all services, press Ctrl+C"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping all services...${NC}"
    
    # Kill background processes
    [ ! -z "$DJANGO_PID" ] && kill $DJANGO_PID 2>/dev/null
    [ ! -z "$CELERY_WORKER_PID" ] && kill $CELERY_WORKER_PID 2>/dev/null
    [ ! -z "$CELERY_BEAT_PID" ] && kill $CELERY_BEAT_PID 2>/dev/null
    [ ! -z "$NGROK_PID" ] && kill $NGROK_PID 2>/dev/null
    
    # Kill any remaining processes
    pkill -f "runserver 8000" 2>/dev/null
    pkill -f "celery.*fish_auction" 2>/dev/null
    pkill -f "ngrok http 8000" 2>/dev/null
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup INT TERM

# Keep script running
echo "⏳ Services are running. Press Ctrl+C to stop..."
while true; do
    sleep 1
done
