#!/usr/bin/env python3
"""
Simple test of WebSocket connection without async issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json

def test_websocket_backend():
    """Test WebSocket backend functionality"""
    print("🧪 Testing WebSocket Backend")
    print("=" * 30)
    
    # Find auction
    try:
        auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
        if not auction:
            print("❌ No live auction found")
            return False
        
        print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
        
    except Exception as e:
        print(f"❌ Error finding auction: {e}")
        return False
    
    # Test channel layer
    channel_layer = get_channel_layer()
    print(f"✅ Channel layer: {type(channel_layer).__name__}")
    
    # Send test WebSocket message
    try:
        bid_data = {
            'auction_id': auction.id,
            'bid_id': 9999,
            'amount': '50.00',
            'bidder': 'test_user',
            'timestamp': '2025-06-22T21:00:00Z',
            'current_price': '50.00',
            'total_bids': auction.total_bids + 1,
            'bid_type': 'test'
        }
        
        async_to_sync(channel_layer.group_send)(
            f'auction_{auction.id}',
            {
                'type': 'bid_update',
                'bid_data': bid_data
            }
        )
        
        print("✅ WebSocket message sent successfully!")
        print(f"📤 Group: auction_{auction.id}")
        print(f"📋 Data: {json.dumps(bid_data, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error sending WebSocket message: {e}")
        return False

def check_server_status():
    """Check if Daphne server is running properly"""
    print("\n🔍 Checking Server Status")
    print("=" * 25)
    
    import subprocess
    try:
        # Check if Daphne is running
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if 'daphne' in result.stdout and 'fish_auction.asgi' in result.stdout:
            print("✅ Daphne ASGI server is running")
        else:
            print("❌ Daphne server not found")
            return False
        
        # Test HTTP connection
        import requests
        try:
            response = requests.get('http://localhost:8000/admin/', timeout=5)
            print(f"✅ HTTP endpoint responding (status: {response.status_code})")
        except Exception as e:
            print(f"❌ HTTP endpoint failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

if __name__ == "__main__":
    print("🔍 WebSocket Connection Tester")
    print("=" * 35)
    
    # Check server status
    if not check_server_status():
        print("\n❌ Server issues detected")
        sys.exit(1)
    
    # Test WebSocket backend
    if test_websocket_backend():
        print("\n🎉 WebSocket Backend Test Successful!")
        print("\n📱 Flutter App Instructions:")
        print("1. Restart Flutter app:")
        print("   cd fish_auction_app")
        print("   flutter run -d web-server --web-port 8080")
        print()
        print("2. Navigate to Jamin fish auction")
        print("3. Check for green WiFi icon (WebSocket connected)")
        print("4. Look for real-time updates")
        print()
        print("🔍 Debug in Browser:")
        print("- Open Developer Tools (F12)")
        print("- Network tab → Filter 'WS'")
        print("- Should see WebSocket connection to ws://localhost:8000/ws/auction/20/")
        print("- Console should show: '📨 WebSocket message received: bid_update'")
        
    else:
        print("\n❌ WebSocket Backend Test Failed")
        sys.exit(1)
