import 'dart:async';
import 'package:flutter/material.dart';

import '../models/location_model.dart';
import '../services/location_service.dart';
import '../utils/api_response.dart';

class DeliveryProvider extends ChangeNotifier {
  final LocationService _locationService = LocationService();
  
  // State management
  bool _isLoading = false;
  String? _error;
  
  // Delivery tracking data
  final Map<String, DeliveryTracking> _deliveryTrackings = {};
  final Map<String, SellerLocation> _sellerLocations = {};
  
  // Auto-refresh timers
  final Map<String, Timer> _refreshTimers = {};
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  DeliveryTracking? getDeliveryTracking(String auctionId) {
    return _deliveryTrackings[auctionId];
  }
  
  SellerLocation? getSellerLocation(String auctionId) {
    return _sellerLocations[auctionId];
  }
  
  List<DeliveryTracking> get allDeliveries => _deliveryTrackings.values.toList();
  
  List<DeliveryTracking> get activeDeliveries {
    return _deliveryTrackings.values
        .where((delivery) => delivery.isActive)
        .toList();
  }
  
  List<DeliveryTracking> get completedDeliveries {
    return _deliveryTrackings.values
        .where((delivery) => delivery.isCompleted)
        .toList();
  }

  // Load delivery tracking for specific auction
  Future<void> loadDeliveryTracking(String auctionId, {bool refresh = false}) async {
    if (!refresh && _deliveryTrackings.containsKey(auctionId)) {
      return; // Already loaded
    }
    
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _locationService.getDeliveryTracking(auctionId);
      
      if (response.isSuccess && response.data != null) {
        _deliveryTrackings[auctionId] = response.data!;
        
        // Start auto-refresh for active deliveries
        if (response.data!.isActive) {
          _startAutoRefresh(auctionId);
        }
        
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load delivery tracking');
      }
    } catch (e) {
      _setError('Error loading delivery tracking: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load seller location for auction
  Future<void> loadSellerLocation(String auctionId, {bool refresh = false}) async {
    if (!refresh && _sellerLocations.containsKey(auctionId)) {
      return; // Already loaded
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await _locationService.getSellerLocation(auctionId);

      if (response.isSuccess && response.data != null) {
        _sellerLocations[auctionId] = response.data!;

        // Start auto-refresh for active sellers
        if (response.data!.isActive) {
          _startSellerLocationRefresh(auctionId);
        }

        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load seller location');
      }
    } catch (e) {
      _setError('Error loading seller location: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Update delivery status
  Future<bool> updateDeliveryStatus({
    required String auctionId,
    required String status,
    String? description,
    Location? location,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _locationService.updateDeliveryStatus(
        auctionId: auctionId,
        status: status,
        description: description,
        location: location,
      );
      
      if (response.isSuccess && response.data != null) {
        _deliveryTrackings[auctionId] = response.data!;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update delivery status');
        return false;
      }
    } catch (e) {
      _setError('Error updating delivery status: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update seller location
  Future<bool> updateSellerLocation(
    Location location, {
    String? status,
  }) async {
    try {
      final response = await _locationService.updateSellerLocation(
        location,
        status: status,
      );
      
      if (response.isSuccess && response.data != null) {
        _sellerLocations[response.data!.sellerId] = response.data!;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update seller location');
        return false;
      }
    } catch (e) {
      _setError('Error updating seller location: ${e.toString()}');
      return false;
    }
  }

  // Start auto-refresh for delivery tracking
  void _startAutoRefresh(String auctionId) {
    _stopAutoRefresh(auctionId);
    
    _refreshTimers[auctionId] = Timer.periodic(
      const Duration(seconds: 30),
      (timer) async {
        final delivery = _deliveryTrackings[auctionId];
        if (delivery == null || !delivery.isActive) {
          _stopAutoRefresh(auctionId);
          return;
        }
        
        await loadDeliveryTracking(auctionId, refresh: true);
      },
    );
  }

  // Start auto-refresh for seller location
  void _startSellerLocationRefresh(String auctionId) {
    _stopSellerLocationRefresh(auctionId);

    _refreshTimers['seller_$auctionId'] = Timer.periodic(
      const Duration(seconds: 30),
      (timer) async {
        final sellerLocation = _sellerLocations[auctionId];
        if (sellerLocation == null || !sellerLocation.isActive) {
          _stopSellerLocationRefresh(auctionId);
          return;
        }

        await loadSellerLocation(auctionId, refresh: true);
      },
    );
  }

  // Stop auto-refresh for delivery tracking
  void _stopAutoRefresh(String auctionId) {
    _refreshTimers[auctionId]?.cancel();
    _refreshTimers.remove(auctionId);
  }

  // Stop auto-refresh for seller location
  void _stopSellerLocationRefresh(String auctionId) {
    _refreshTimers['seller_$auctionId']?.cancel();
    _refreshTimers.remove('seller_$auctionId');
  }

  // Get delivery progress percentage
  double getDeliveryProgress(String auctionId) {
    final delivery = _deliveryTrackings[auctionId];
    if (delivery == null) return 0.0;
    
    switch (delivery.status) {
      case 'pending':
        return 0.2;
      case 'preparing':
        return 0.4;
      case 'ready':
        return 0.6;
      case 'picked_up':
      case 'in_transit':
        return 0.8;
      case 'out_for_delivery':
        return 0.9;
      case 'delivered':
        return 1.0;
      default:
        return 0.0;
    }
  }

  // Get next expected status
  String? getNextExpectedStatus(String auctionId) {
    final delivery = _deliveryTrackings[auctionId];
    if (delivery == null) return null;
    
    switch (delivery.status) {
      case 'pending':
        return 'preparing';
      case 'preparing':
        return 'ready';
      case 'ready':
        return 'picked_up';
      case 'picked_up':
        return 'in_transit';
      case 'in_transit':
        return 'out_for_delivery';
      case 'out_for_delivery':
        return 'delivered';
      default:
        return null;
    }
  }

  // Get estimated time for next status
  Duration? getEstimatedTimeToNext(String auctionId) {
    final delivery = _deliveryTrackings[auctionId];
    if (delivery == null || delivery.estimatedDelivery == null) return null;
    
    final now = DateTime.now();
    final estimatedDelivery = delivery.estimatedDelivery!;
    
    switch (delivery.status) {
      case 'pending':
        return const Duration(minutes: 30); // 30 minutes to start preparing
      case 'preparing':
        return const Duration(hours: 1); // 1 hour to be ready
      case 'ready':
        return const Duration(minutes: 30); // 30 minutes for pickup
      case 'picked_up':
      case 'in_transit':
        return estimatedDelivery.difference(now);
      case 'out_for_delivery':
        return const Duration(minutes: 30); // 30 minutes for final delivery
      default:
        return null;
    }
  }

  // Clear all data
  void clearAll() {
    _deliveryTrackings.clear();
    _sellerLocations.clear();
    _stopAllRefreshTimers();
    _clearError();
    notifyListeners();
  }

  // Clear data for specific auction
  void clearDeliveryTracking(String auctionId) {
    _deliveryTrackings.remove(auctionId);
    _stopAutoRefresh(auctionId);
    notifyListeners();
  }

  // Clear data for specific auction
  void clearSellerLocation(String auctionId) {
    _sellerLocations.remove(auctionId);
    _stopSellerLocationRefresh(auctionId);
    notifyListeners();
  }

  // Stop all refresh timers
  void _stopAllRefreshTimers() {
    for (final timer in _refreshTimers.values) {
      timer.cancel();
    }
    _refreshTimers.clear();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    _stopAllRefreshTimers();
    super.dispose();
  }
}
