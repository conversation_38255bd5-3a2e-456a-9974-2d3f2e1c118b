import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  
  String _currentLanguage = 'en'; // Default to English
  Locale _currentLocale = const Locale('en');

  String get currentLanguage => _currentLanguage;
  Locale get currentLocale => _currentLocale;

  // Supported languages
  static const List<Locale> supportedLocales = [
    Locale('en'), // English
    Locale('ar'), // Arabic
  ];

  // Initialize language from stored preferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);
      
      if (savedLanguage != null && _isLanguageSupported(savedLanguage)) {
        _currentLanguage = savedLanguage;
        _currentLocale = Locale(savedLanguage);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading language preference: $e');
    }
  }

  // Set new language
  Future<void> setLanguage(String languageCode) async {
    if (!_isLanguageSupported(languageCode)) {
      throw ArgumentError('Unsupported language: $languageCode');
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      
      _currentLanguage = languageCode;
      _currentLocale = Locale(languageCode);
      
      notifyListeners();
    } catch (e) {
      print('Error saving language preference: $e');
      throw Exception('Failed to save language preference');
    }
  }

  // Check if language is supported
  bool _isLanguageSupported(String languageCode) {
    return supportedLocales.any((locale) => locale.languageCode == languageCode);
  }

  // Get language display name
  String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return languageCode.toUpperCase();
    }
  }

  // Get language native name
  String getLanguageNativeName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return languageCode.toUpperCase();
    }
  }

  // Check if current language is RTL
  bool get isRTL => _currentLanguage == 'ar';

  // Get text direction
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;
}
