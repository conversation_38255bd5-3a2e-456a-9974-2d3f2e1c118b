import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationProvider extends ChangeNotifier {
  // Auction notifications
  bool _newAuctions = true;
  bool _auctionEndingSoon = true;
  bool _outbidAlerts = true;
  bool _auctionWon = true;

  // Payment notifications
  bool _paymentReminders = true;
  bool _paymentConfirmations = true;
  bool _walletUpdates = true;

  // Delivery notifications
  bool _deliveryUpdates = true;
  bool _deliveryReminders = true;

  // General notifications
  bool _marketing = false;
  bool _appUpdates = true;

  // Notification methods
  bool _pushNotifications = true;
  bool _emailNotifications = true;
  bool _smsNotifications = false;
  bool _whatsappNotifications = true;

  // Getters
  bool get newAuctions => _newAuctions;
  bool get auctionEndingSoon => _auctionEndingSoon;
  bool get outbidAlerts => _outbidAlerts;
  bool get auctionWon => _auctionWon;
  bool get paymentReminders => _paymentReminders;
  bool get paymentConfirmations => _paymentConfirmations;
  bool get walletUpdates => _walletUpdates;
  bool get deliveryUpdates => _deliveryUpdates;
  bool get deliveryReminders => _deliveryReminders;
  bool get marketing => _marketing;
  bool get appUpdates => _appUpdates;
  bool get pushNotifications => _pushNotifications;
  bool get emailNotifications => _emailNotifications;
  bool get smsNotifications => _smsNotifications;
  bool get whatsappNotifications => _whatsappNotifications;

  // Initialize from stored preferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load auction notifications
      _newAuctions = prefs.getBool('new_auctions') ?? true;
      _auctionEndingSoon = prefs.getBool('auction_ending_soon') ?? true;
      _outbidAlerts = prefs.getBool('outbid_alerts') ?? true;
      _auctionWon = prefs.getBool('auction_won') ?? true;

      // Load payment notifications
      _paymentReminders = prefs.getBool('payment_reminders') ?? true;
      _paymentConfirmations = prefs.getBool('payment_confirmations') ?? true;
      _walletUpdates = prefs.getBool('wallet_updates') ?? true;

      // Load delivery notifications
      _deliveryUpdates = prefs.getBool('delivery_updates') ?? true;
      _deliveryReminders = prefs.getBool('delivery_reminders') ?? true;

      // Load general notifications
      _marketing = prefs.getBool('marketing') ?? false;
      _appUpdates = prefs.getBool('app_updates') ?? true;

      // Load notification methods
      _pushNotifications = prefs.getBool('push_notifications') ?? true;
      _emailNotifications = prefs.getBool('email_notifications') ?? true;
      _smsNotifications = prefs.getBool('sms_notifications') ?? false;
      _whatsappNotifications = prefs.getBool('whatsapp_notifications') ?? true;

      notifyListeners();
    } catch (e) {
      print('Error loading notification preferences: $e');
    }
  }

  // Auction notification setters
  Future<void> setNewAuctions(bool value) async {
    _newAuctions = value;
    await _saveBoolPreference('new_auctions', value);
    notifyListeners();
  }

  Future<void> setAuctionEndingSoon(bool value) async {
    _auctionEndingSoon = value;
    await _saveBoolPreference('auction_ending_soon', value);
    notifyListeners();
  }

  Future<void> setOutbidAlerts(bool value) async {
    _outbidAlerts = value;
    await _saveBoolPreference('outbid_alerts', value);
    notifyListeners();
  }

  Future<void> setAuctionWon(bool value) async {
    _auctionWon = value;
    await _saveBoolPreference('auction_won', value);
    notifyListeners();
  }

  // Payment notification setters
  Future<void> setPaymentReminders(bool value) async {
    _paymentReminders = value;
    await _saveBoolPreference('payment_reminders', value);
    notifyListeners();
  }

  Future<void> setPaymentConfirmations(bool value) async {
    _paymentConfirmations = value;
    await _saveBoolPreference('payment_confirmations', value);
    notifyListeners();
  }

  Future<void> setWalletUpdates(bool value) async {
    _walletUpdates = value;
    await _saveBoolPreference('wallet_updates', value);
    notifyListeners();
  }

  // Delivery notification setters
  Future<void> setDeliveryUpdates(bool value) async {
    _deliveryUpdates = value;
    await _saveBoolPreference('delivery_updates', value);
    notifyListeners();
  }

  Future<void> setDeliveryReminders(bool value) async {
    _deliveryReminders = value;
    await _saveBoolPreference('delivery_reminders', value);
    notifyListeners();
  }

  // General notification setters
  Future<void> setMarketing(bool value) async {
    _marketing = value;
    await _saveBoolPreference('marketing', value);
    notifyListeners();
  }

  Future<void> setAppUpdates(bool value) async {
    _appUpdates = value;
    await _saveBoolPreference('app_updates', value);
    notifyListeners();
  }

  // Notification method setters
  Future<void> setPushNotifications(bool value) async {
    _pushNotifications = value;
    await _saveBoolPreference('push_notifications', value);
    notifyListeners();
  }

  Future<void> setEmailNotifications(bool value) async {
    _emailNotifications = value;
    await _saveBoolPreference('email_notifications', value);
    notifyListeners();
  }

  Future<void> setSmsNotifications(bool value) async {
    _smsNotifications = value;
    await _saveBoolPreference('sms_notifications', value);
    notifyListeners();
  }

  Future<void> setWhatsappNotifications(bool value) async {
    _whatsappNotifications = value;
    await _saveBoolPreference('whatsapp_notifications', value);
    notifyListeners();
  }

  // Helper method to save boolean preferences
  Future<void> _saveBoolPreference(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(key, value);
    } catch (e) {
      print('Error saving notification preference $key: $e');
    }
  }

  // Reset all notifications to default
  Future<void> resetToDefaults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear all notification preferences
      final keys = [
        'new_auctions', 'auction_ending_soon', 'outbid_alerts', 'auction_won',
        'payment_reminders', 'payment_confirmations', 'wallet_updates',
        'delivery_updates', 'delivery_reminders',
        'marketing', 'app_updates',
        'push_notifications', 'email_notifications', 'sms_notifications', 'whatsapp_notifications'
      ];
      
      for (String key in keys) {
        await prefs.remove(key);
      }
      
      // Reinitialize with defaults
      await initialize();
    } catch (e) {
      print('Error resetting notification preferences: $e');
    }
  }

  // Check if user should receive a specific type of notification
  bool shouldReceiveNotification(String notificationType) {
    if (!_pushNotifications) return false; // No push notifications enabled
    
    switch (notificationType) {
      case 'new_auction':
        return _newAuctions;
      case 'auction_ending_soon':
        return _auctionEndingSoon;
      case 'outbid':
        return _outbidAlerts;
      case 'auction_won':
        return _auctionWon;
      case 'payment_reminder':
        return _paymentReminders;
      case 'payment_confirmation':
        return _paymentConfirmations;
      case 'wallet_update':
        return _walletUpdates;
      case 'delivery_update':
        return _deliveryUpdates;
      case 'delivery_reminder':
        return _deliveryReminders;
      case 'marketing':
        return _marketing;
      case 'app_update':
        return _appUpdates;
      default:
        return false;
    }
  }
}
