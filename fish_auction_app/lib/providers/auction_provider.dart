import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/auction_model.dart';
import '../models/bid_model.dart';
import '../services/auction_service.dart';

class AuctionProvider with ChangeNotifier {
  final AuctionService _auctionService = AuctionService();

  // State variables
  List<Auction> _auctions = [];
  List<AuctionCategory> _categories = [];
  List<Auction> _watchlist = [];
  List<Bid> _userBids = [];
  List<Auction> _myAuctions = [];
  List<Auction> _participatedAuctions = [];
  List<Auction> _earnedAuctions = [];
  List<Auction> _directBuyAuctions = [];
  Auction? _selectedAuction;
  List<Bid> _selectedAuctionBids = [];
  List<Bid> _auctionBids = [];
  Map<String, dynamic>? _userAutoBid;

  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;
  bool _isInitialized = false;

  // Pagination
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Filters
  String? _statusFilter;
  int? _categoryFilter;
  String? _searchQuery;
  String? _ordering;

  // Getters
  List<Auction> get auctions => _auctions;
  List<AuctionCategory> get categories => _categories;
  List<Auction> get watchlist => _watchlist;
  List<Bid> get userBids => _userBids;
  List<Auction> get myAuctions => _myAuctions;
  List<Auction> get participatedAuctions => _participatedAuctions;
  List<Auction> get earnedAuctions => _earnedAuctions;
  Auction? get selectedAuction => _selectedAuction;
  List<Bid> get selectedAuctionBids => _selectedAuctionBids;
  List<Bid> get auctionBids => _auctionBids;
  Map<String, dynamic>? get userAutoBid => _userAutoBid;

  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;

  String? get statusFilter => _statusFilter;
  int? get categoryFilter => _categoryFilter;
  String? get searchQuery => _searchQuery;
  String? get ordering => _ordering;

  // Live auctions
  List<Auction> get liveAuctions => _auctions.where((auction) => auction.isLive).toList();

  // Ending soon auctions - only show truly live auctions with time remaining
  List<Auction> get endingSoonAuctions =>
      liveAuctions.where((auction) => auction.timeRemaining.inHours < 24 && auction.hasTimeRemaining).toList()
        ..sort((a, b) => a.timeRemaining.compareTo(b.timeRemaining));

  // Ended auctions
  List<Auction> get endedAuctions => _auctions.where((auction) => auction.isEnded).toList();

  // All auctions (including ended)
  List<Auction> get allAuctions => _auctions;

  // Auctions visible to buyers (excludes draft and ended auctions)
  List<Auction> get buyerVisibleAuctions => _auctions.where((auction) => !auction.isDraft && !auction.isEnded).toList();

  // Scheduled and coming soon auctions for buyers
  List<Auction> get scheduledAuctions => _auctions.where((auction) => auction.isScheduled).toList();

  // Coming soon auctions (scheduled auctions that haven't started yet)
  List<Auction> get comingSoonAuctions => scheduledAuctions.where((auction) => auction.hasTimeRemaining).toList();

  // Direct buy auctions (buy_now type)
  List<Auction> get directBuyAuctions => _directBuyAuctions;

  // Initialize
  Future<void> initialize() async {
    if (_isInitialized) return;

    _isInitialized = true;
    await Future.wait([loadCategories(), loadAuctions(refresh: true)]);
  }

  // Load auctions
  Future<void> loadAuctions({
    bool refresh = false,
    String? status,
    int? category,
    String? search,
    String? ordering,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _auctions.clear();
    }

    if (!_hasMoreData && !refresh) return;

    _setLoading(refresh);
    _setLoadingMore(!refresh);
    _clearError();

    try {
      // Update filters
      _statusFilter = status;
      _categoryFilter = category;
      _searchQuery = search;
      _ordering = ordering;

      final response = await _auctionService.getAuctions(
        status: _statusFilter,
        category: _categoryFilter,
        search: _searchQuery,
        ordering: _ordering,
        page: _currentPage,
      );

      if (response.isSuccess) {
        final newAuctions = response.data ?? [];

        if (refresh) {
          _auctions = newAuctions;
        } else {
          _auctions.addAll(newAuctions);
        }

        _hasMoreData = newAuctions.length >= 20; // Assuming page size is 20
        _currentPage++;

        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load auctions');
      }
    } catch (e) {
      _setError('Error loading auctions: ${e.toString()}');
    } finally {
      _setLoading(false);
      _setLoadingMore(false);
    }
  }

  // Load more auctions (pagination)
  Future<void> loadMoreAuctions() async {
    if (!_isLoadingMore && _hasMoreData) {
      await loadAuctions();
    }
  }

  // Load categories
  Future<void> loadCategories() async {
    try {
      final response = await _auctionService.getCategories();
      if (response.isSuccess) {
        _categories = response.data ?? [];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  // Load auction details
  Future<void> loadAuctionDetails(int auctionId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _auctionService.getAuction(auctionId);
      if (response.isSuccess) {
        _selectedAuction = response.data;
        await loadAuctionBids(auctionId);
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load auction details');
      }
    } catch (e) {
      _setError('Error loading auction details: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load auction bids
  Future<void> loadAuctionBids(int auctionId) async {
    try {
      final response = await _auctionService.getAuctionBids(auctionId);
      if (response.isSuccess) {
        _selectedAuctionBids = response.data ?? [];
        _auctionBids = response.data ?? [];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading auction bids: $e');
    }
  }

  // Place bid
  Future<bool> placeBid(int auctionId, double amount) async {
    _clearError();

    try {
      final response = await _auctionService.placeBid(auctionId: auctionId, amount: amount);

      if (response.isSuccess) {
        // Don't reload bids immediately - let WebSocket updates handle it
        // Just update the auction price in the list
        _updateAuctionInList(auctionId, amount);

        // Reload user bids to update location button visibility
        loadUserBids();

        // Optionally refresh auction details after a short delay to ensure consistency
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (_selectedAuction?.id == auctionId) {
            loadAuctionBids(auctionId);
          }
        });

        return true;
      } else {
        _setError(response.message ?? 'Failed to place bid');
        return false;
      }
    } catch (e) {
      _setError('Error placing bid: ${e.toString()}');
      return false;
    }
  }

  // Set auto bid
  Future<bool> setAutoBid(int auctionId, double maxAmount) async {
    _clearError();

    try {
      final response = await _auctionService.setAutoBid(auctionId: auctionId, maxAmount: maxAmount);

      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.message ?? 'Failed to set auto bid');
        return false;
      }
    } catch (e) {
      _setError('Error setting auto bid: ${e.toString()}');
      return false;
    }
  }

  // Toggle watchlist
  Future<bool> toggleWatchlist(int auctionId) async {
    try {
      final auction = _auctions.firstWhere((a) => a.id == auctionId);

      if (auction.isWatched) {
        final response = await _auctionService.removeFromWatchlist(auctionId);
        if (response.isSuccess) {
          _updateAuctionWatchStatus(auctionId, false);
          return true;
        }
      } else {
        final response = await _auctionService.addToWatchlist(auctionId);
        if (response.isSuccess) {
          _updateAuctionWatchStatus(auctionId, true);
          return true;
        }
      }
      return false;
    } catch (e) {
      _setError('Error updating watchlist: ${e.toString()}');
      return false;
    }
  }

  // Load watchlist
  Future<void> loadWatchlist() async {
    try {
      final response = await _auctionService.getWatchlist();
      if (response.isSuccess) {
        _watchlist = response.data ?? [];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading watchlist: $e');
    }
  }

  // Load user bids
  Future<void> loadUserBids() async {
    try {
      final response = await _auctionService.getUserBids();
      if (response.isSuccess) {
        _userBids = response.data ?? [];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading user bids: $e');
    }
  }

  // Load earned auctions (won by user)
  Future<void> loadEarnedAuctions() async {
    try {
      print('🔄 Loading earned auctions...');
      final response = await _auctionService.getEarnedAuctions();
      if (response.isSuccess) {
        _earnedAuctions = response.data ?? [];
        print('✅ Loaded ${_earnedAuctions.length} earned auctions');
        for (var auction in _earnedAuctions) {
          print('  - Auction ${auction.id}: ${auction.title} (${auction.status})');
        }
        notifyListeners();
      } else {
        print('❌ Failed to load earned auctions: ${response.message}');
      }
    } catch (e) {
      print('❌ Error loading earned auctions: $e');
    }
  }

  // Load my auctions (for sellers)
  Future<void> loadMyAuctions({bool refresh = false}) async {
    if (refresh) {
      _myAuctions.clear();
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await _auctionService.getMyAuctions();
      if (response.isSuccess) {
        _myAuctions = response.data ?? [];
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load my auctions');
      }
    } catch (e) {
      _setError('Error loading my auctions: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create auction
  Future<Auction?> createAuction({
    required String title,
    required String description,
    required int fishCategory,
    required String fishType,
    required double weight,
    required int quantity,
    required DateTime catchDate,
    required String catchLocation,
    required String auctionType,
    required double startingPrice,
    double? reservePrice,
    double? targetPrice,
    double? buyNowPrice,
    required double bidIncrement,
    required DateTime startTime,
    DateTime? endTime, // Optional for buy_now auctions
    required File mainImage,
    List<File>? additionalImages,
    String status = 'draft', // Default to draft
    double? latitude,
    double? longitude,
    String? deliveryAddress,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _auctionService.createAuction(
        title: title,
        description: description,
        fishCategory: fishCategory,
        fishType: fishType,
        weight: weight,
        quantity: quantity,
        catchDate: catchDate,
        catchLocation: catchLocation,
        auctionType: auctionType,
        startingPrice: startingPrice,
        reservePrice: reservePrice,
        targetPrice: targetPrice,
        buyNowPrice: buyNowPrice,
        bidIncrement: bidIncrement,
        startTime: startTime,
        endTime: endTime,
        mainImage: mainImage,
        additionalImages: additionalImages,
        status: status,
        latitude: latitude,
        longitude: longitude,
        deliveryAddress: deliveryAddress,
      );

      if (response.isSuccess) {
        // Add to my auctions list
        _myAuctions.insert(0, response.data!);
        notifyListeners();
        return response.data;
      } else {
        _setError(response.message ?? 'Failed to create auction');
        return null;
      }
    } catch (e) {
      _setError('Error creating auction: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Search auctions
  Future<void> searchAuctions(String query) async {
    await loadAuctions(refresh: true, search: query);
  }

  // Filter auctions
  Future<void> filterAuctions({String? status, int? category, String? ordering}) async {
    await loadAuctions(refresh: true, status: status, category: category, ordering: ordering);
  }

  // Clear filters
  Future<void> clearFilters() async {
    await loadAuctions(refresh: true);
  }

  // Load direct buy auctions (buy_now type)
  Future<void> loadDirectBuyAuctions() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _auctionService.getAuctions(status: 'live', auctionType: 'buy_now');

      if (response.isSuccess) {
        _directBuyAuctions = response.data ?? [];
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load direct buy auctions');
      }
    } catch (e) {
      _setError('Error loading direct buy auctions: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Refresh data
  Future<void> refresh() async {
    await loadAuctions(refresh: true);
    if (_selectedAuction != null) {
      await loadAuctionDetails(_selectedAuction!.id);
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setLoadingMore(bool loading) {
    _isLoadingMore = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _updateAuctionInList(int auctionId, double newPrice) {
    final index = _auctions.indexWhere((a) => a.id == auctionId);
    if (index != -1) {
      _auctions[index] = _auctions[index].copyWith(currentPrice: newPrice, totalBids: _auctions[index].totalBids + 1);
      notifyListeners();
    }
  }

  void _updateAuctionWatchStatus(int auctionId, bool isWatched) {
    final index = _auctions.indexWhere((a) => a.id == auctionId);
    if (index != -1) {
      _auctions[index] = _auctions[index].copyWith(isWatched: isWatched);
    }

    if (_selectedAuction?.id == auctionId) {
      _selectedAuction = _selectedAuction!.copyWith(isWatched: isWatched);
    }

    notifyListeners();
  }

  // Clear error manually
  void clearError() {
    _clearError();
  }

  // Check if user has bid on a specific auction
  bool hasUserBidOnAuction(int auctionId) {
    final hasUserBid = _userBids.any((bid) => bid.auction?.id == auctionId || bid.auctionId == auctionId);
    print('DEBUG: Checking user bid for auction $auctionId: $hasUserBid');
    print('DEBUG: Total user bids: ${_userBids.length}');
    for (var bid in _userBids) {
      print('DEBUG: Bid ID: ${bid.id}, Auction ID: ${bid.auctionId ?? bid.auction?.id}, Amount: ${bid.amount}');
    }
    return hasUserBid;
  }

  // Load participated auctions
  Future<void> loadMyParticipatedAuctions() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _auctionService.getMyParticipatedAuctions();
      if (response.isSuccess) {
        _participatedAuctions = response.data ?? [];
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load participated auctions');
      }
    } catch (e) {
      _setError('Error loading participated auctions: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load user auto bid for specific auction
  Future<void> loadUserAutoBid(int auctionId) async {
    try {
      final response = await _auctionService.getUserAutoBid(auctionId);
      if (response.isSuccess) {
        _userAutoBid = response.data;
        notifyListeners();
      }
    } catch (e) {
      print('Error loading user auto bid: $e');
    }
  }

  // Cancel auto bid
  Future<bool> cancelAutoBid(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.cancelAutoBid(auctionId);
      if (response.isSuccess) {
        _userAutoBid = null;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to cancel auto bid');
        return false;
      }
    } catch (e) {
      _setError('Error cancelling auto bid: ${e.toString()}');
      return false;
    }
  }

  // Clear selected auction
  void clearSelectedAuction() {
    _selectedAuction = null;
    _selectedAuctionBids.clear();
    notifyListeners();
  }

  // Make auction live
  Future<bool> makeAuctionLive(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.makeAuctionLive(auctionId);
      if (response.isSuccess) {
        // Update the auction in the local list
        final index = _myAuctions.indexWhere((a) => a.id == auctionId);
        if (index != -1) {
          _myAuctions[index] = _myAuctions[index].copyWith(status: 'live');
          notifyListeners();
        }
        return true;
      } else {
        _setError(response.message ?? 'Failed to make auction live');
        return false;
      }
    } catch (e) {
      _setError('Error making auction live: ${e.toString()}');
      return false;
    }
  }

  // Schedule auction
  Future<bool> scheduleAuction(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.scheduleAuction(auctionId);
      if (response.isSuccess) {
        _updateAuctionStatusInLists(response.data!);
        return true;
      } else {
        _setError(response.message ?? 'Failed to schedule auction');
        return false;
      }
    } catch (e) {
      _setError('Error scheduling auction: ${e.toString()}');
      return false;
    }
  }

  // Stop auction
  Future<bool> stopAuction(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.stopAuction(auctionId);
      if (response.isSuccess) {
        _updateAuctionStatusInLists(response.data!);
        return true;
      } else {
        _setError(response.message ?? 'Failed to stop auction');
        return false;
      }
    } catch (e) {
      _setError('Error stopping auction: ${e.toString()}');
      return false;
    }
  }

  // Close auction and notify winner
  Future<bool> closeAuction(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.closeAuction(auctionId);
      if (response.isSuccess) {
        _updateAuctionStatusInLists(response.data!);
        return true;
      } else {
        _setError(response.message ?? 'Failed to close auction');
        return false;
      }
    } catch (e) {
      _setError('Error closing auction: ${e.toString()}');
      return false;
    }
  }

  // Cancel auction
  Future<bool> cancelAuction(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.cancelAuction(auctionId);
      if (response.isSuccess) {
        _updateAuctionStatusInLists(response.data!);
        return true;
      } else {
        _setError(response.message ?? 'Failed to cancel auction');
        return false;
      }
    } catch (e) {
      _setError('Error cancelling auction: ${e.toString()}');
      return false;
    }
  }

  // Reactivate auction
  Future<bool> reactivateAuction(int auctionId) async {
    _clearError();

    try {
      final response = await _auctionService.reactivateAuction(auctionId);
      if (response.isSuccess) {
        _updateAuctionStatusInLists(response.data!);
        return true;
      } else {
        _setError(response.message ?? 'Failed to reactivate auction');
        return false;
      }
    } catch (e) {
      _setError('Error reactivating auction: ${e.toString()}');
      return false;
    }
  }

  // Helper method to update auction status in lists
  void _updateAuctionStatusInLists(Auction updatedAuction) {
    // Update in my auctions list
    final myIndex = _myAuctions.indexWhere((a) => a.id == updatedAuction.id);
    if (myIndex != -1) {
      _myAuctions[myIndex] = updatedAuction;
    }

    // Update in main auctions list
    final mainIndex = _auctions.indexWhere((a) => a.id == updatedAuction.id);
    if (mainIndex != -1) {
      _auctions[mainIndex] = updatedAuction;
    }

    // Update selected auction if it's the same
    if (_selectedAuction?.id == updatedAuction.id) {
      _selectedAuction = updatedAuction;
    }

    notifyListeners();
  }

  // Update auction (only drafts)
  Future<Auction?> updateAuction({
    required int auctionId,
    required String title,
    required String description,
    required int fishCategory,
    required String fishType,
    required double weight,
    required int quantity,
    required DateTime catchDate,
    required String catchLocation,
    required String auctionType,
    required double startingPrice,
    double? reservePrice,
    double? buyNowPrice,
    required double bidIncrement,
    required DateTime startTime,
    required DateTime endTime,
    File? mainImage,
    List<File>? additionalImages,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _auctionService.updateAuction(
        auctionId: auctionId,
        title: title,
        description: description,
        fishCategory: fishCategory,
        fishType: fishType,
        weight: weight,
        quantity: quantity,
        catchDate: catchDate,
        catchLocation: catchLocation,
        auctionType: auctionType,
        startingPrice: startingPrice,
        reservePrice: reservePrice,
        buyNowPrice: buyNowPrice,
        bidIncrement: bidIncrement,
        startTime: startTime,
        endTime: endTime,
        mainImage: mainImage,
        additionalImages: additionalImages,
      );

      if (response.isSuccess && response.data != null) {
        final updatedAuction = response.data!;

        // Update the auction in the local list
        final index = _myAuctions.indexWhere((a) => a.id == auctionId);
        if (index != -1) {
          _myAuctions[index] = updatedAuction;
          notifyListeners();
        }

        return updatedAuction;
      } else {
        _setError(response.message ?? 'Failed to update auction');
        return null;
      }
    } catch (e) {
      _setError('Error updating auction: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }
}
