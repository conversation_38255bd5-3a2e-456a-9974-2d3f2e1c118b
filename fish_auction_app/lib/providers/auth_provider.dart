import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  User? _user;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;
  bool get isInitialized => _isInitialized;
  bool get canBid => _authService.canBid;
  bool get canSell => _authService.canSell;
  bool get isVerified => _authService.isVerified;
  String get userType => _authService.userType;
  double get walletBalance => _authService.walletBalance;
  String get kycStatus => _authService.kycStatus;

  // Initialize
  Future<void> initialize() async {
    if (_isInitialized) return;

    _setLoading(true);
    try {
      await _authService.initialize();
      _user = _authService.currentUser;
      _isInitialized = true;
    } catch (e) {
      _setError('Initialization failed: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login({required String username, required String password}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.login(username: username, password: password);

      if (response.isSuccess) {
        _user = response.data;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Login failed');
        return false;
      }
    } catch (e) {
      _setError('Login error: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register
  Future<bool> register({
    required String username,
    required String email,
    required String password,
    required String userType,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.register(
        username: username,
        email: email,
        password: password,
        userType: userType,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
      );

      if (response.isSuccess) {
        _user = response.data;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Registration failed');
        return false;
      }
    } catch (e) {
      _setError('Registration error: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update profile
  Future<bool> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? address,
    String? city,
    String? country,
    String? preferredLanguage,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.updateProfile(
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        address: address,
        city: city,
        country: country,
        preferredLanguage: preferredLanguage,
      );

      if (response.isSuccess) {
        _user = response.data;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Profile update failed');
        return false;
      }
    } catch (e) {
      _setError('Profile update error: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<bool> changePassword({required String oldPassword, required String newPassword}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.changePassword(oldPassword: oldPassword, newPassword: newPassword);

      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.message ?? 'Password change failed');
        return false;
      }
    } catch (e) {
      _setError('Password change error: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Submit verification document
  Future<bool> submitVerificationDocument({required File document, required String documentType}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.submitVerificationDocument(document: document, documentType: documentType);

      if (response.isSuccess) {
        // Refresh user profile to get updated verification status
        await refreshProfile();
        return true;
      } else {
        _setError(response.message ?? 'Document submission failed');
        return false;
      }
    } catch (e) {
      _setError('Document submission error: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Submit multiple verification documents at once
  Future<bool> submitBatchVerificationDocuments({required File governmentId, required File huntingApproval}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.submitBatchVerificationDocuments(
        governmentId: governmentId,
        huntingApproval: huntingApproval,
      );

      if (response.isSuccess) {
        // Refresh user profile to get updated verification status
        await refreshProfile();
        return true;
      } else {
        _setError(response.message ?? 'Batch document submission failed');
        return false;
      }
    } catch (e) {
      _setError('Batch document submission error: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get user's document verification requests
  Future<List<Map<String, dynamic>>> getUserDocumentRequests() async {
    try {
      final response = await _authService.getUserDocumentRequests();

      if (response.isSuccess) {
        return response.data ?? [];
      } else {
        _setError(response.message ?? 'Failed to load document requests');
        return [];
      }
    } catch (e) {
      _setError('Error loading document requests: ${e.toString()}');
      return [];
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    if (!isLoggedIn) return;

    try {
      final response = await _authService.getUserProfile();
      if (response.isSuccess) {
        _user = response.data;
        notifyListeners();
      }
    } catch (e) {
      print('Error refreshing profile: $e');
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _authService.logout();
      _user = null;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Logout error: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Handle token expiration (called by API service)
  Future<void> handleTokenExpiration() async {
    _user = null;
    _setError('Session expired. Please login again.');
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Update wallet balance (called from payment provider)
  void updateWalletBalance(double newBalance) {
    if (_user != null) {
      _user = _user!.copyWith(walletBalance: newBalance);
      notifyListeners();
    }
  }

  // Update KYC status
  void updateKycStatus(String newStatus) {
    if (_user != null) {
      _user = _user!.copyWith(kycStatus: newStatus);
      notifyListeners();
    }
  }

  // Clear error manually
  void clearError() {
    _clearError();
  }
}
