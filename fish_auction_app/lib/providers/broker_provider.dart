import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/broker_service_model.dart';
import '../services/broker_service.dart';

class BrokerProvider with ChangeNotifier {
  final BrokerApiService _brokerService = BrokerApiService();

  // State variables
  bool _isLoading = false;
  String? _error;

  // Data
  List<BrokerService> _brokerServices = [];
  List<ServiceRequest> _myServiceRequests = [];
  List<ServiceRequest> _availableServiceRequests = [];
  List<ServiceExecution> _myServiceExecutions = [];
  List<BrokerQuote> _currentQuotes = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<BrokerService> get brokerServices => _brokerServices;
  List<ServiceRequest> get myServiceRequests => _myServiceRequests;
  List<ServiceRequest> get availableServiceRequests => _availableServiceRequests;
  List<ServiceExecution> get myServiceExecutions => _myServiceExecutions;
  List<BrokerQuote> get currentQuotes => _currentQuotes;

  // Load broker services
  Future<void> loadBrokerServices() async {
    _setLoading(true);
    try {
      final response = await _brokerService.getBrokerServices();
      if (response.isSuccess) {
        _brokerServices = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message ?? 'Failed to load broker services');
      }
    } catch (e) {
      _setError('Error loading broker services: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create service request
  Future<bool> createServiceRequest(ServiceRequest request) async {
    _setLoading(true);
    try {
      final response = await _brokerService.createServiceRequest(request);
      if (response.isSuccess) {
        // Add to my requests list
        _myServiceRequests.insert(0, response.data!);
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to create service request');
        return false;
      }
    } catch (e) {
      _setError('Error creating service request: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load my service requests
  Future<void> loadMyServiceRequests() async {
    _setLoading(true);
    try {
      final response = await _brokerService.getMyServiceRequests();
      if (response.isSuccess) {
        _myServiceRequests = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message ?? 'Failed to load service requests');
      }
    } catch (e) {
      _setError('Error loading service requests: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load available service requests (for brokers)
  Future<void> loadAvailableServiceRequests() async {
    _setLoading(true);
    try {
      final response = await _brokerService.getAvailableServiceRequests();
      if (response.isSuccess) {
        _availableServiceRequests = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message ?? 'Failed to load available service requests');
      }
    } catch (e) {
      _setError('Error loading available service requests: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load quotes for a service request
  Future<void> loadServiceRequestQuotes(String serviceRequestId) async {
    _setLoading(true);
    try {
      final response = await _brokerService.getServiceRequestQuotes(serviceRequestId);
      if (response.isSuccess) {
        _currentQuotes = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message ?? 'Failed to load quotes');
      }
    } catch (e) {
      _setError('Error loading quotes: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create broker quote
  Future<bool> createBrokerQuote(BrokerQuote quote) async {
    _setLoading(true);
    try {
      final response = await _brokerService.createBrokerQuote(quote);
      if (response.isSuccess) {
        // Remove the request from available list since broker has quoted
        _availableServiceRequests.removeWhere((req) => req.id == quote.serviceRequestId);
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to create quote');
        return false;
      }
    } catch (e) {
      _setError('Error creating quote: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Select broker
  Future<bool> selectBroker(String serviceRequestId, String quoteId) async {
    _setLoading(true);
    try {
      final response = await _brokerService.selectBroker(serviceRequestId, quoteId);
      if (response.isSuccess) {
        // Update the service request in my list
        final index = _myServiceRequests.indexWhere((req) => req.id == serviceRequestId);
        if (index != -1) {
          _myServiceRequests[index] = response.data!;
        }
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to select broker');
        return false;
      }
    } catch (e) {
      _setError('Error selecting broker: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load service executions
  Future<void> loadServiceExecutions({bool isBroker = false}) async {
    _setLoading(true);
    try {
      final response = isBroker
          ? await _brokerService.getBrokerServiceExecutions()
          : await _brokerService.getClientServiceExecutions();

      if (response.isSuccess) {
        _myServiceExecutions = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message ?? 'Failed to load service executions');
      }
    } catch (e) {
      _setError('Error loading service executions: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Update service execution
  Future<bool> updateServiceExecution(String executionId, Map<String, dynamic> updates) async {
    _setLoading(true);
    try {
      final response = await _brokerService.updateServiceExecution(executionId, updates);
      if (response.isSuccess) {
        // Update the execution in the list
        final index = _myServiceExecutions.indexWhere((exec) => exec.id == executionId);
        if (index != -1) {
          _myServiceExecutions[index] = response.data!;
        }
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update service execution');
        return false;
      }
    } catch (e) {
      _setError('Error updating service execution: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update service execution with image
  Future<bool> updateServiceExecutionWithImage(String executionId, Map<String, dynamic> updates, File image) async {
    _setLoading(true);
    try {
      final response = await _brokerService.updateServiceExecutionWithImage(executionId, updates, image);
      if (response.isSuccess) {
        // Update the execution in the list
        final index = _myServiceExecutions.indexWhere((exec) => exec.id == executionId);
        if (index != -1) {
          _myServiceExecutions[index] = response.data!;
        }
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update service execution with image');
        return false;
      }
    } catch (e) {
      _setError('Error updating service execution with image: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Submit client feedback
  Future<bool> submitClientFeedback(String executionId, String feedback, int rating) async {
    _setLoading(true);
    try {
      final response = await _brokerService.submitClientFeedback(executionId, feedback, rating);
      if (response.isSuccess) {
        // Update the execution in the list
        final index = _myServiceExecutions.indexWhere((exec) => exec.id == executionId);
        if (index != -1) {
          _myServiceExecutions[index] = response.data!;
        }
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to submit feedback');
        return false;
      }
    } catch (e) {
      _setError('Error submitting feedback: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Release payment
  Future<bool> releasePayment(String executionId) async {
    _setLoading(true);
    try {
      final response = await _brokerService.releasePayment(executionId);
      if (response.isSuccess) {
        // Update the execution status
        final index = _myServiceExecutions.indexWhere((exec) => exec.id == executionId);
        if (index != -1) {
          // Reload the execution to get updated status
          await loadServiceExecutions();
        }
        _clearError();
        return true;
      } else {
        _setError(response.message ?? 'Failed to release payment');
        return false;
      }
    } catch (e) {
      _setError('Error releasing payment: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Get service request by ID
  ServiceRequest? getServiceRequestById(String id) {
    try {
      return _myServiceRequests.firstWhere((req) => req.id == id);
    } catch (e) {
      return null;
    }
  }

  // Check if auction can request services (12+ hours before end)
  bool canRequestServices(DateTime auctionEndTime) {
    final now = DateTime.now();
    final timeUntilEnd = auctionEndTime.difference(now);
    return timeUntilEnd.inHours >= 12;
  }
}
