import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Ocean/Fish Theme
  static const Color primary = Color(0xFF1E88E5); // Ocean Blue
  static const Color primaryDark = Color(0xFF1565C0);
  static const Color primaryLight = Color(0xFF64B5F6);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF26A69A); // Teal
  static const Color secondaryDark = Color(0xFF00695C);
  static const Color secondaryLight = Color(0xFF80CBC4);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF7043); // Coral
  static const Color accentDark = Color(0xFFD84315);
  static const Color accentLight = Color(0xFFFFAB91);
  
  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF121212);
  static const Color backgroundDark = Color(0xFF1E1E1E);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textLight = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Auction Status Colors
  static const Color liveAuction = Color(0xFF4CAF50);
  static const Color scheduledAuction = Color(0xFFFF9800);
  static const Color endedAuction = Color(0xFF757575);
  static const Color cancelledAuction = Color(0xFFF44336);
  
  // Bid Colors
  static const Color winningBid = Color(0xFF4CAF50);
  static const Color outbidBid = Color(0xFFFF9800);
  static const Color losingBid = Color(0xFFF44336);
  
  // Delivery Status Colors
  static const Color pendingDelivery = Color(0xFFFF9800);
  static const Color inTransitDelivery = Color(0xFF2196F3);
  static const Color deliveredDelivery = Color(0xFF4CAF50);
  
  // Payment Status Colors
  static const Color pendingPayment = Color(0xFFFF9800);
  static const Color completedPayment = Color(0xFF4CAF50);
  static const Color failedPayment = Color(0xFFF44336);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF424242);
  static const Color divider = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1F000000);
  static const Color shadowDark = Color(0x3F000000);
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  
  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF1E88E5),
    Color(0xFF1565C0),
  ];
  
  static const List<Color> secondaryGradient = [
    Color(0xFF26A69A),
    Color(0xFF00695C),
  ];
  
  static const List<Color> accentGradient = [
    Color(0xFFFF7043),
    Color(0xFFD84315),
  ];
  
  // Fish Category Colors
  static const Color freshSeaFish = Color(0xFF1E88E5);
  static const Color shellfish = Color(0xFFFF7043);
  static const Color freshwaterFish = Color(0xFF26A69A);
  static const Color processedFish = Color(0xFF9C27B0);
  
  // User Type Colors
  static const Color buyer = Color(0xFF4CAF50);
  static const Color seller = Color(0xFF2196F3);
  static const Color broker = Color(0xFFFF9800);
  static const Color serviceProvider = Color(0xFF9C27B0);
  static const Color admin = Color(0xFFF44336);
  
  // Rating Colors
  static const Color ratingExcellent = Color(0xFF4CAF50);
  static const Color ratingGood = Color(0xFF8BC34A);
  static const Color ratingAverage = Color(0xFFFF9800);
  static const Color ratingPoor = Color(0xFFFF5722);
  static const Color ratingVeryPoor = Color(0xFFF44336);
  
  // Notification Colors
  static const Color notificationInfo = Color(0xFF2196F3);
  static const Color notificationSuccess = Color(0xFF4CAF50);
  static const Color notificationWarning = Color(0xFFFF9800);
  static const Color notificationError = Color(0xFFF44336);
  
  // Chart Colors
  static const List<Color> chartColors = [
    Color(0xFF1E88E5),
    Color(0xFF26A69A),
    Color(0xFFFF7043),
    Color(0xFF9C27B0),
    Color(0xFF4CAF50),
    Color(0xFFFF9800),
    Color(0xFFF44336),
    Color(0xFF795548),
  ];
  
  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
  
  // Map Colors
  static const Color mapMarker = Color(0xFF1E88E5);
  static const Color mapRoute = Color(0xFF26A69A);
  static const Color mapArea = Color(0x401E88E5);
  
  // Social Colors
  static const Color whatsapp = Color(0xFF25D366);
  static const Color email = Color(0xFF1976D2);
  static const Color phone = Color(0xFF4CAF50);
  
  // Helper Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'live':
        return liveAuction;
      case 'scheduled':
        return scheduledAuction;
      case 'ended':
        return endedAuction;
      case 'cancelled':
        return cancelledAuction;
      case 'pending':
        return warning;
      case 'completed':
        return success;
      case 'failed':
        return error;
      default:
        return textSecondary;
    }
  }
  
  static Color getUserTypeColor(String userType) {
    switch (userType.toLowerCase()) {
      case 'buyer':
        return buyer;
      case 'seller':
        return seller;
      case 'broker':
        return broker;
      case 'service_provider':
        return serviceProvider;
      case 'admin':
        return admin;
      default:
        return textSecondary;
    }
  }
  
  static Color getRatingColor(double rating) {
    if (rating >= 4.5) return ratingExcellent;
    if (rating >= 3.5) return ratingGood;
    if (rating >= 2.5) return ratingAverage;
    if (rating >= 1.5) return ratingPoor;
    return ratingVeryPoor;
  }
}
