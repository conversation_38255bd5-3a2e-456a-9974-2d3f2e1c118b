/// Generic API response wrapper class
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final String? message;
  final int? statusCode;

  const ApiResponse._({
    required this.success,
    this.data,
    this.error,
    this.message,
    this.statusCode,
  });

  /// Create a successful response
  factory ApiResponse.success({
    required T data,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse._(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create an error response
  factory ApiResponse.error({
    required String message,
    String? error,
    int? statusCode,
  }) {
    return ApiResponse._(
      success: false,
      error: error ?? message,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create a response from JSON
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    try {
      if (json['success'] == true || json['status'] == 'success') {
        return ApiResponse.success(
          data: fromJsonT(json['data']),
          message: json['message']?.toString(),
          statusCode: json['statusCode'] as int?,
        );
      } else {
        return ApiResponse.error(
          message: json['error']?.toString() ?? json['message']?.toString() ?? 'Unknown error',
          statusCode: json['statusCode'] as int?,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to parse response: $e',
        statusCode: json['statusCode'] as int?,
      );
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data,
      'error': error,
      'message': message,
      'statusCode': statusCode,
    };
  }

  /// Check if the response is successful
  bool get isSuccess => success;

  /// Check if the response is an error
  bool get isError => !success;

  /// Get the error message or a default message
  String get errorMessage => error ?? message ?? 'Unknown error occurred';

  /// Get the success message or a default message
  String get successMessage => message ?? 'Operation completed successfully';

  @override
  String toString() {
    if (success) {
      return 'ApiResponse.success(data: $data, message: $message)';
    } else {
      return 'ApiResponse.error(error: $error, message: $message)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ApiResponse<T> &&
        other.success == success &&
        other.data == data &&
        other.error == error &&
        other.message == message &&
        other.statusCode == statusCode;
  }

  @override
  int get hashCode {
    return Object.hash(success, data, error, message, statusCode);
  }
}

/// Extension methods for ApiResponse
extension ApiResponseExtensions<T> on ApiResponse<T> {
  /// Map the data to a different type
  ApiResponse<R> map<R>(R Function(T) mapper) {
    if (isSuccess && data != null) {
      try {
        return ApiResponse.success(
          data: mapper(data as T),
          message: message,
          statusCode: statusCode,
        );
      } catch (e) {
        return ApiResponse.error(
          message: 'Failed to map data: $e',
          statusCode: statusCode,
        );
      }
    } else {
      return ApiResponse.error(
        message: error ?? 'No data to map',
        statusCode: statusCode,
      );
    }
  }

  /// Execute a function if the response is successful
  ApiResponse<T> onSuccess(void Function(T) callback) {
    if (isSuccess && data != null) {
      callback(data as T);
    }
    return this;
  }

  /// Execute a function if the response is an error
  ApiResponse<T> onError(void Function(String) callback) {
    if (isError) {
      callback(errorMessage);
    }
    return this;
  }

  /// Get the data or throw an exception if error
  T get dataOrThrow {
    if (isSuccess && data != null) {
      return data as T;
    } else {
      throw Exception(errorMessage);
    }
  }

  /// Get the data or return a default value
  T dataOr(T defaultValue) {
    return isSuccess && data != null ? data as T : defaultValue;
  }
}

/// Utility class for creating common API responses
class ApiResponseUtils {
  /// Create a successful response with no data
  static ApiResponse<void> successVoid({String? message, int? statusCode}) {
    return ApiResponse.success(
      data: null,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create a successful response with a boolean result
  static ApiResponse<bool> successBool(bool result, {String? message, int? statusCode}) {
    return ApiResponse.success(
      data: result,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create a successful response with a string result
  static ApiResponse<String> successString(String result, {String? message, int? statusCode}) {
    return ApiResponse.success(
      data: result,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create a successful response with a map result
  static ApiResponse<Map<String, dynamic>> successMap(
    Map<String, dynamic> result, {
    String? message,
    int? statusCode,
  }) {
    return ApiResponse.success(
      data: result,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create a successful response with a list result
  static ApiResponse<List<T>> successList<T>(
    List<T> result, {
    String? message,
    int? statusCode,
  }) {
    return ApiResponse.success(
      data: result,
      message: message,
      statusCode: statusCode,
    );
  }

  /// Create an error response for network issues
  static ApiResponse<T> networkError<T>([String? customMessage]) {
    return ApiResponse.error(
      message: customMessage ?? 'Network connection failed. Please check your internet connection.',
      statusCode: 0,
    );
  }

  /// Create an error response for timeout issues
  static ApiResponse<T> timeoutError<T>([String? customMessage]) {
    return ApiResponse.error(
      message: customMessage ?? 'Request timed out. Please try again.',
      statusCode: 408,
    );
  }

  /// Create an error response for server issues
  static ApiResponse<T> serverError<T>([String? customMessage]) {
    return ApiResponse.error(
      message: customMessage ?? 'Server error occurred. Please try again later.',
      statusCode: 500,
    );
  }

  /// Create an error response for unauthorized access
  static ApiResponse<T> unauthorizedError<T>([String? customMessage]) {
    return ApiResponse.error(
      message: customMessage ?? 'Unauthorized access. Please login again.',
      statusCode: 401,
    );
  }

  /// Create an error response for forbidden access
  static ApiResponse<T> forbiddenError<T>([String? customMessage]) {
    return ApiResponse.error(
      message: customMessage ?? 'Access forbidden. You do not have permission to perform this action.',
      statusCode: 403,
    );
  }

  /// Create an error response for not found
  static ApiResponse<T> notFoundError<T>([String? customMessage]) {
    return ApiResponse.error(
      message: customMessage ?? 'Resource not found.',
      statusCode: 404,
    );
  }

  /// Create an error response for validation issues
  static ApiResponse<T> validationError<T>(String message) {
    return ApiResponse.error(
      message: message,
      statusCode: 422,
    );
  }
}
