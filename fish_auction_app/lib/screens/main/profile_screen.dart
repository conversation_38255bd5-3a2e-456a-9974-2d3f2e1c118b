import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import '../profile/edit_profile_screen.dart';
import '../profile/settings_screen.dart';
import '../profile/change_password_screen.dart';
import '../profile/payment_methods_screen.dart';
import '../profile/language_settings_screen.dart';
import '../support/ai_support_screen.dart';
import '../legal/privacy_policy_screen.dart';
import '../legal/terms_of_service_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with WidgetsBindingObserver {
  // Document selection state
  File? _selectedGovernmentId;
  File? _selectedHuntingApproval;
  bool _isSubmittingDocuments = false;

  // Document verification status
  final List<Map<String, dynamic>> _documentRequests = [];
  bool _isLoadingDocumentStatus = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDocumentStatus();
      _refreshUserProfile();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app comes back to foreground
      _refreshData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.profile),
        actions: [IconButton(onPressed: _showSettings, icon: const Icon(Icons.settings))],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.user;
          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          return RefreshIndicator(
            onRefresh: _refreshData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  // Profile Header
                  _buildProfileHeader(user, localizations),

                  const SizedBox(height: AppConstants.largePadding),

                  // KYC Status
                  _buildKycStatus(authProvider, localizations),

                  const SizedBox(height: AppConstants.largePadding),

                  // Profile Options
                  _buildProfileOptions(localizations),

                  const SizedBox(height: AppConstants.largePadding),

                  // Account Actions
                  _buildAccountActions(authProvider, localizations),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(user, AppLocalizations localizations) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: AppColors.primaryGradient,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        children: [
          // Avatar
          CircleAvatar(
            radius: 40,
            backgroundColor: AppColors.textLight,
            child: user.profileImage != null
                ? ClipOval(child: Image.network(user.profileImage!, width: 80, height: 80, fit: BoxFit.cover))
                : Icon(Icons.person, size: 40, color: AppColors.primary),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Name
          Text(
            user.displayName,
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // User Type
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.smallPadding,
            ),
            decoration: BoxDecoration(
              color: AppColors.textLight.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Text(
              _getUserTypeDisplayName(user.userType),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.w500),
            ),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // Email
          Text(
            user.email,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
          ),
        ],
      ),
    );
  }

  Widget _buildKycStatus(AuthProvider authProvider, AppLocalizations localizations) {
    // Only sellers need KYC verification - buyers and brokers don't need it
    if (authProvider.userType == 'buyer' || authProvider.userType == 'broker') {
      return const SizedBox.shrink(); // Hide KYC section for buyers and brokers
    }

    final kycStatus = authProvider.kycStatus;
    Color statusColor;
    IconData statusIcon;
    String statusText;
    bool showSubmitButton = false;

    // Check document verification status
    final hasPendingDocs = _documentRequests.any((doc) => doc['status'] == 'pending');
    final hasApprovedDocs = _documentRequests.any((doc) => doc['status'] == 'approved');
    final hasRejectedDocs = _documentRequests.any((doc) => doc['status'] == 'rejected');
    final hasAnyDocs = _documentRequests.isNotEmpty;

    // Debug: Log the current state
    debugPrint(
      'KYC Status Check: hasAnyDocs=$hasAnyDocs, hasPendingDocs=$hasPendingDocs, hasApprovedDocs=$hasApprovedDocs, hasRejectedDocs=$hasRejectedDocs',
    );
    debugPrint('Document requests count: ${_documentRequests.length}');

    // Check if user is already verified (from backend)
    final isUserVerified = authProvider.isVerified;

    if (isUserVerified) {
      // User is verified by admin
      statusColor = AppColors.success;
      statusIcon = Icons.verified;
      statusText = 'Account verified - You can create auctions';
      showSubmitButton = false;
    } else if (hasPendingDocs) {
      // Documents are pending review
      statusColor = AppColors.warning;
      statusIcon = Icons.schedule;
      statusText = 'Documents submitted - Waiting for admin review';
      showSubmitButton = false;
    } else if (hasApprovedDocs && !hasRejectedDocs) {
      // All documents approved (but user verification might be pending)
      statusColor = AppColors.success;
      statusIcon = Icons.verified;
      statusText = 'Documents approved - Account verified';
      showSubmitButton = false;
    } else if (hasRejectedDocs) {
      // Some documents rejected
      statusColor = AppColors.error;
      statusIcon = Icons.cancel;
      statusText = 'Documents rejected - Please resubmit';
      showSubmitButton = true;
    } else if (!hasAnyDocs) {
      // No documents submitted yet
      switch (kycStatus) {
        case 'approved':
          statusColor = AppColors.success;
          statusIcon = Icons.verified;
          statusText = localizations.kycApproved;
          showSubmitButton = false;
          break;
        case 'pending':
          statusColor = AppColors.warning;
          statusIcon = Icons.schedule;
          statusText = localizations.kycPending;
          showSubmitButton = false;
          break;
        case 'rejected':
          statusColor = AppColors.error;
          statusIcon = Icons.cancel;
          statusText = localizations.kycRejected;
          showSubmitButton = true;
          break;
        default:
          statusColor = AppColors.textSecondary;
          statusIcon = Icons.info_outline;
          statusText = localizations.kycNotSubmitted;
          showSubmitButton = true;
      }
    } else {
      // Fallback to original KYC status
      switch (kycStatus) {
        case 'approved':
          statusColor = AppColors.success;
          statusIcon = Icons.verified;
          statusText = localizations.kycApproved;
          showSubmitButton = false;
          break;
        case 'pending':
          statusColor = AppColors.warning;
          statusIcon = Icons.schedule;
          statusText = localizations.kycPending;
          showSubmitButton = false;
          break;
        case 'rejected':
          statusColor = AppColors.error;
          statusIcon = Icons.cancel;
          statusText = localizations.kycRejected;
          showSubmitButton = true;
          break;
        default:
          statusColor = AppColors.textSecondary;
          statusIcon = Icons.info_outline;
          statusText = localizations.kycNotSubmitted;
          showSubmitButton = true;
      }
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 24),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localizations.kycVerification,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
                Text(
                  statusText,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: statusColor, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          if (showSubmitButton) OutlinedButton(onPressed: _showKycSubmission, child: Text(localizations.submitKyc)),
        ],
      ),
    );
  }

  Widget _buildProfileOptions(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Options',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        _buildOptionTile(
          icon: Icons.edit,
          title: 'Edit Profile',
          subtitle: 'Update your personal information',
          onTap: _showEditProfile,
        ),
        _buildOptionTile(
          icon: Icons.lock,
          title: localizations.changePassword,
          subtitle: 'Update your account password',
          onTap: _showChangePassword,
        ),
        _buildOptionTile(
          icon: Icons.notifications,
          title: localizations.notificationSettings,
          subtitle: 'Manage your notification preferences',
          onTap: _showNotificationSettings,
        ),
        _buildOptionTile(
          icon: Icons.language,
          title: localizations.language,
          subtitle: localizations.changeAppLanguage,
          onTap: _showLanguageSettings,
        ),
        _buildOptionTile(
          icon: Icons.payment,
          title: 'Payment Methods',
          subtitle: 'Manage your withdrawal methods',
          onTap: _showPaymentMethods,
        ),
      ],
    );
  }

  Widget _buildAccountActions(AuthProvider authProvider, AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Account', style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: AppConstants.defaultPadding),
        _buildOptionTile(
          icon: Icons.help,
          title: localizations.support,
          subtitle: 'Get help and contact support',
          onTap: _showSupport,
        ),
        _buildOptionTile(
          icon: Icons.privacy_tip,
          title: localizations.privacyPolicy,
          subtitle: 'Read our privacy policy',
          onTap: _showPrivacyPolicy,
        ),
        _buildOptionTile(
          icon: Icons.description,
          title: localizations.termsOfService,
          subtitle: 'Read terms and conditions',
          onTap: _showTermsOfService,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        DangerButton(
          text: localizations.logout,
          onPressed: () => _showLogoutConfirmation(authProvider, localizations),
          icon: Icons.logout,
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(color: AppColors.primary.withOpacity(0.1), borderRadius: BorderRadius.circular(8)),
        child: Icon(icon, color: AppColors.primary, size: 20),
      ),
      title: Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary)),
      trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
      onTap: onTap,
    );
  }

  String _getUserTypeDisplayName(String userType) {
    final localizations = AppLocalizations.of(context);
    switch (userType) {
      case 'buyer':
        return localizations.buyerType;
      case 'seller':
        return localizations.sellerType;
      case 'broker':
        return localizations.brokerType;
      case 'service_provider':
        return localizations.serviceProviderType;
      case 'admin':
        return localizations.administrator;
      default:
        return localizations.user;
    }
  }

  void _showSettings() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));
  }

  void _showKycSubmission() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppConstants.borderRadius)),
      ),
      builder: (context) =>
          StatefulBuilder(builder: (context, setModalState) => _buildKycSubmissionSheet(setModalState)),
    );
  }

  Widget _buildKycSubmissionSheet([StateSetter? setModalState]) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(2)),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Title
          Text(
            'KYC Verification - Seller Documents',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Warning for sellers
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(color: AppColors.warning.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.warning_amber_outlined, color: AppColors.warning, size: 20),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'As a fish seller, you must upload BOTH documents below. Both are required for approval.',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary, fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Required Documents Header
          Text(
            'Required Documents (Both Required)',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.error),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Document types
          _buildDocumentSelectionCard(
            '1. Government ID (Required)',
            'Passport, Driver\'s License, or National ID',
            Icons.badge,
            _selectedGovernmentId,
            () => _selectDocument('government_id'),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildDocumentSelectionCard(
            '2. Hunting Approval (Required)',
            'Hunting license, fishing permit, or authorization to catch and sell fish',
            Icons.business_outlined,
            _selectedHuntingApproval,
            () => _selectDocument('hunting_approval'),
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Submit button
          if (_selectedGovernmentId != null && _selectedHuntingApproval != null)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmittingDocuments ? null : _submitDocuments,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppConstants.borderRadius)),
                ),
                child: _isSubmittingDocuments
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('Submitting Documents...'),
                        ],
                      )
                    : const Text(
                        'Submit Documents for Verification',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
              ),
            ),
          const SizedBox(height: AppConstants.largePadding),

          // Info note
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(color: AppColors.info.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info, size: 20),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'Both documents are required for seller verification. Admin will review within 24-48 hours and notify you of approval status.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.info),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Close button - only show if no documents selected, otherwise submit button is shown above
          if (_selectedGovernmentId == null && _selectedHuntingApproval == null)
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(onPressed: () => Navigator.pop(context), child: const Text('Close')),
            ),

          // If only one document is selected, show a message to select the other
          if ((_selectedGovernmentId != null && _selectedHuntingApproval == null) ||
              (_selectedGovernmentId == null && _selectedHuntingApproval != null))
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    border: Border.all(color: AppColors.warning.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: AppColors.warning, size: 20),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Please select the ${_selectedGovernmentId == null ? 'Government ID' : 'Hunting Approval'} document to continue.',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.warning),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(onPressed: () => Navigator.pop(context), child: const Text('Close')),
                ),
              ],
            ),

          // Safe area padding
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildDocumentSelectionCard(
    String title,
    String description,
    IconData icon,
    File? selectedFile,
    VoidCallback onTap,
  ) {
    final bool isSelected = selectedFile != null;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          border: Border.all(color: isSelected ? AppColors.success : AppColors.border, width: isSelected ? 2 : 1),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          color: isSelected ? AppColors.success.withOpacity(0.05) : null,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.success.withOpacity(0.1) : AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isSelected ? Icons.check_circle : icon,
                color: isSelected ? AppColors.success : AppColors.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppColors.success : null,
                    ),
                  ),
                  Text(
                    isSelected ? 'Document selected: ${selectedFile.path.split('/').last}' : description,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: isSelected ? AppColors.success : AppColors.textSecondary),
                  ),
                ],
              ),
            ),
            Icon(
              isSelected ? Icons.edit : Icons.camera_alt,
              color: isSelected ? AppColors.success : AppColors.primary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _selectDocument(String documentType) {
    // Don't close the verification sheet - show document selection options
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select ${documentType == 'government_id' ? 'Government ID' : 'Hunting Approval'}',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.largePadding),

            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context); // Close document source selection
                _takePhoto(documentType);
              },
            ),

            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context); // Close document source selection
                _pickFromGallery(documentType);
              },
            ),

            const SizedBox(height: AppConstants.defaultPadding),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
            ),

            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  Future<void> _takePhoto(String documentType) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        final File file = File(image.path);

        // Verify file exists and has content
        if (await file.exists()) {
          final fileSize = await file.length();
          print('📸 Photo taken: ${file.path}, Size: $fileSize bytes');
          await _storeSelectedDocument(file, documentType);
        } else {
          _showErrorDialog('Failed to save photo. Please try again.');
        }
      }
    } catch (e) {
      _showErrorDialog('Failed to take photo: ${e.toString()}');
    }
  }

  Future<void> _pickFromGallery(String documentType) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        final File file = File(image.path);

        // Verify file exists and has content
        if (await file.exists()) {
          final fileSize = await file.length();
          print('🖼️ Image selected: ${file.path}, Size: $fileSize bytes');
          await _storeSelectedDocument(file, documentType);
        } else {
          _showErrorDialog('Failed to access selected image. Please try again.');
        }
      }
    } catch (e) {
      _showErrorDialog('Failed to pick image: ${e.toString()}');
    }
  }

  Future<void> _storeSelectedDocument(File file, String documentType) async {
    try {
      // Copy file to app documents directory to ensure it persists
      final directory = await getApplicationDocumentsDirectory();

      // Determine file extension based on original file or default to jpg
      String originalExtension = 'jpg';
      if (file.path.contains('.')) {
        originalExtension = file.path.split('.').last.toLowerCase();
        // Ensure it's a valid extension
        if (!['jpg', 'jpeg', 'png', 'pdf'].contains(originalExtension)) {
          originalExtension = 'jpg';
        }
      }

      final fileName = '${documentType}_${DateTime.now().millisecondsSinceEpoch}.$originalExtension';
      final newPath = '${directory.path}/$fileName';

      final newFile = await file.copy(newPath);

      setState(() {
        if (documentType == 'government_id') {
          _selectedGovernmentId = newFile;
        } else if (documentType == 'hunting_approval') {
          _selectedHuntingApproval = newFile;
        }
      });

      // Force rebuild of the modal sheet to show green container immediately
      if (mounted) {
        // Trigger a rebuild of the parent widget tree
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {});
          }
        });
      }

      print('📁 Document stored: $newPath');

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${documentType == 'government_id' ? 'Government ID' : 'Hunting Approval'} selected successfully!',
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // If this was called from the verification sheet, refresh it to show the updated state
      // The verification sheet will automatically show the green feedback for the selected document
    } catch (e) {
      print('❌ Error storing document: $e');
      _showErrorDialog('Failed to store selected document: ${e.toString()}');
    }
  }

  Future<void> _submitDocuments() async {
    if (!mounted || _selectedGovernmentId == null || _selectedHuntingApproval == null) return;

    setState(() {
      _isSubmittingDocuments = true;
    });

    try {
      // Verify files exist before submission
      final govIdExists = await _selectedGovernmentId!.exists();
      final huntingExists = await _selectedHuntingApproval!.exists();

      print('📋 Submitting documents:');
      print('   Government ID: ${_selectedGovernmentId!.path} (exists: $govIdExists)');
      print('   Hunting Approval: ${_selectedHuntingApproval!.path} (exists: $huntingExists)');

      if (!govIdExists || !huntingExists) {
        throw Exception('One or more selected files no longer exist. Please select the documents again.');
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Submit both documents at once using batch submission
      final success = await authProvider.submitBatchVerificationDocuments(
        governmentId: _selectedGovernmentId!,
        huntingApproval: _selectedHuntingApproval!,
      );

      if (success) {
        // Both documents submitted successfully
        if (mounted) {
          _showBothDocumentsSuccessDialog();

          // Clear selected documents and refresh document status
          setState(() {
            _selectedGovernmentId = null;
            _selectedHuntingApproval = null;
          });

          // Refresh document status to show pending state
          await _loadDocumentStatus();
        }
      } else {
        // Get the actual error message from the provider
        String errorMessage = authProvider.error ?? 'Unknown error occurred';

        // Clean up duplicate error messages
        if (errorMessage.contains('Only PDF, JPEG, PNG files are allowed')) {
          errorMessage = 'Only PDF, JPEG, PNG files are allowed. Please select valid image files.';
        }

        throw Exception(errorMessage);
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = e.toString().replaceFirst('Exception: ', '');
        _showErrorDialog(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingDocuments = false;
        });
      }
    }
  }

  void _showErrorDialog(String message) {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [ElevatedButton(onPressed: () => Navigator.pop(context), child: const Text('OK'))],
      ),
    );
  }

  void _showSuccessDialog(String documentType) {
    if (!mounted) return;

    String message;
    if (documentType == 'government_id') {
      message =
          'Your Government ID has been uploaded successfully! Please also upload your Hunting Approval to complete verification.';
    } else if (documentType == 'hunting_approval') {
      message =
          'Your Hunting Approval has been uploaded successfully! Make sure you have also uploaded your Government ID for complete verification.';
    } else {
      message = 'Your $documentType has been uploaded successfully! It will be reviewed within 24-48 hours.';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Document Uploaded'),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close bottom sheet
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showEditProfile() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const EditProfileScreen()));
  }

  void _showChangePassword() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const ChangePasswordScreen()));
  }

  void _showBothDocumentsSuccessDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success, size: 28),
            const SizedBox(width: 12),
            const Text('Documents Submitted!'),
          ],
        ),
        content: const Text(
          'Both your Government ID and Hunting Approval documents have been submitted successfully! '
          'They will be reviewed within 24-48 hours. You will receive a notification once the review is complete.',
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
      ),
    );
  }

  void _showNotificationSettings() {
    // TODO: Navigate to notification settings screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Notification settings screen coming soon')));
  }

  void _showLanguageSettings() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const LanguageSettingsScreen()));
  }

  void _showPaymentMethods() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const PaymentMethodsScreen()));
  }

  void _showSupport() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const AISupportScreen()));
  }

  void _showPrivacyPolicy() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()));
  }

  void _showTermsOfService() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const TermsOfServiceScreen()));
  }

  void _showLogoutConfirmation(AuthProvider authProvider, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.logout),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await authProvider.logout();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: Text(localizations.logout),
          ),
        ],
      ),
    );
  }

  // Load document verification status
  Future<void> _loadDocumentStatus() async {
    if (!mounted) return;

    setState(() {
      _isLoadingDocumentStatus = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final requests = await authProvider.getUserDocumentRequests();

      // Debug: Log document requests
      debugPrint('Loaded ${requests.length} document requests');
      for (var request in requests) {
        debugPrint('Document ${request['document_type']}: ${request['status']}');
      }

      if (mounted) {
        setState(() {
          _documentRequests.clear();
          _documentRequests.addAll(requests);
          _isLoadingDocumentStatus = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading document status: $e');
      if (mounted) {
        setState(() {
          _isLoadingDocumentStatus = false;
        });
      }
    }
  }

  // Check if user has pending documents
  bool get _hasPendingDocuments {
    return _documentRequests.any((doc) => doc['status'] == 'pending');
  }

  // Check if user has approved documents
  bool get _hasApprovedDocuments {
    return _documentRequests.any((doc) => doc['status'] == 'approved');
  }

  // Get document status for specific type
  String? _getDocumentStatus(String documentType) {
    final doc = _documentRequests.firstWhere((doc) => doc['document_type'] == documentType, orElse: () => {});
    return doc['status'];
  }

  // Refresh user profile data
  Future<void> _refreshUserProfile() async {
    if (!mounted) return;

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.refreshProfile();
    } catch (e) {
      print('Error refreshing user profile: $e');
    }
  }

  // Refresh all data (profile + document status)
  Future<void> _refreshData() async {
    if (!mounted) return;

    await Future.wait([_refreshUserProfile(), _loadDocumentStatus()]);
  }
}
