import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/auction_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/auction/auction_card.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/categories/fish_category_card.dart';
import '../auction/auction_detail_screen.dart';
import '../notifications/notifications_screen.dart';
import '../support/ai_support_screen.dart';
import '../broker/my_service_requests_screen.dart';
import '../broker/client_service_tracking_screen.dart';
import '../auction/earned_auctions_screen.dart';
import '../payment/earned_auction_screen.dart';
import '../auction/category_auctions_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Use addPostFrameCallback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshData();
    });
  }

  Future<void> _refreshData() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    await auctionProvider.loadAuctions(refresh: true);

    // Load earned auctions for buyers
    if (authProvider.userType == 'buyer') {
      await auctionProvider.loadEarnedAuctions();
    }
  }

  Future<void> _toggleWatchlist(int auctionId) async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final success = await auctionProvider.toggleWatchlist(auctionId);

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(auctionProvider.error ?? 'Failed to update watchlist'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          slivers: [
            // App Bar
            _buildSliverAppBar(localizations, authProvider),

            // Body Content
            SliverToBoxAdapter(
              child: Column(
                children: [
                  // Welcome Section
                  _buildWelcomeSection(localizations, authProvider),

                  // Quick Stats
                  _buildQuickStats(localizations, authProvider),

                  // Quick Actions for buyers
                  if (authProvider.userType == 'buyer') _buildQuickActionsSection(authProvider),

                  // Live Auctions Section
                  _buildLiveAuctionsSection(localizations),

                  // Ending Soon Section
                  _buildEndingSoonSection(localizations),

                  // Coming Soon Section (for buyers)
                  if (authProvider.userType == 'buyer') _buildComingSoonSection(localizations),

                  // Ended Auctions Section (only for sellers/admins)
                  if (authProvider.userType != 'buyer') _buildEndedAuctionsSection(localizations),

                  // Categories Section
                  _buildCategoriesSection(localizations),

                  // Earned Auctions Section (only for buyers)
                  if (authProvider.userType == 'buyer') _buildEarnedAuctionsSection(localizations),

                  // Recent Activity (if user has bids)
                  if (authProvider.canBid) _buildRecentActivitySection(localizations),

                  const SizedBox(height: 100), // Bottom padding for navigation
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(AppLocalizations localizations, AuthProvider authProvider) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: AppColors.primaryGradient,
            ),
          ),
        ),
      ),
      title: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(color: AppColors.textLight, borderRadius: BorderRadius.circular(8)),
            child: const Icon(Icons.waves, color: AppColors.primary, size: 20),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            localizations.appName,
            style: const TextStyle(color: AppColors.textLight, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      actions: [
        // Notifications
        IconButton(
          onPressed: () {
            Navigator.of(context).push(MaterialPageRoute(builder: (context) => const NotificationsScreen()));
          },
          icon: Stack(
            children: [
              const Icon(Icons.notifications_outlined, color: AppColors.textLight),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(color: AppColors.accent, shape: BoxShape.circle),
                ),
              ),
            ],
          ),
        ),

        // Support Chat
        IconButton(
          onPressed: () {
            Navigator.of(context).push(MaterialPageRoute(builder: (context) => const AISupportScreen()));
          },
          icon: const Icon(Icons.support_agent_outlined, color: AppColors.textLight),
        ),
      ],
    );
  }

  Widget _buildWelcomeSection(AppLocalizations localizations, AuthProvider authProvider) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: AppColors.secondaryGradient,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${localizations.welcome}, ${authProvider.user?.displayName ?? 'User'}!',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _getWelcomeMessage(localizations, authProvider.userType),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          // Only show KYC verification for sellers - buyers and brokers don't need it
          if (!authProvider.isVerified && authProvider.userType == 'seller')
            OutlinedCustomButton(
              text: 'Complete KYC Verification',
              onPressed: () {
                // Navigate to profile screen where KYC is handled
                Navigator.of(context).pushNamed('/profile');
              },
              borderColor: AppColors.textLight,
              textColor: AppColors.textLight,
            ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(AppLocalizations localizations, AuthProvider authProvider) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.gavel,
                  title: localizations.liveAuctions,
                  value: auctionProvider.liveAuctions.length.toString(),
                  color: AppColors.liveAuction,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  icon: authProvider.userType == 'buyer' ? Icons.upcoming : Icons.schedule,
                  title: authProvider.userType == 'buyer' ? 'Coming Soon' : localizations.endingSoon,
                  value: authProvider.userType == 'buyer'
                      ? auctionProvider.comingSoonAuctions.length.toString()
                      : auctionProvider.endingSoonAuctions.length.toString(),
                  color: AppColors.warning,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.account_balance_wallet,
                  title: 'Balance',
                  value: '\$${authProvider.walletBalance.toStringAsFixed(0)}',
                  color: AppColors.success,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard({required IconData icon, required String title, required String value, required Color color}) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 1))],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: color, fontWeight: FontWeight.bold),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(AuthProvider authProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإجراءات السريعة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'طلبات الخدمات',
                  'عرض طلباتي',
                  Icons.assignment_outlined,
                  AppColors.primary,
                  () => _navigateToMyServiceRequests(),
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildActionCard(
                  'تتبع الخدمات',
                  'متابعة التقدم',
                  Icons.track_changes_outlined,
                  AppColors.info,
                  () => _navigateToServiceTracking(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: color.withOpacity(0.2)),
          boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 1))],
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveAuctionsSection(AppLocalizations localizations) {
    return Consumer2<AuctionProvider, AuthProvider>(
      builder: (context, auctionProvider, authProvider, child) {
        // For buyers, show only non-ended auctions; for sellers/admins, show all
        final auctions = authProvider.userType == 'buyer'
            ? auctionProvider.buyerVisibleAuctions.take(5).toList()
            : auctionProvider.allAuctions.take(5).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.recentAuctions,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pushNamed('/auctions');
                    },
                    child: Text('${localizations.view} ${localizations.auctions}'),
                  ),
                ],
              ),
            ),

            if (auctionProvider.isLoading)
              const SizedBox(height: 200, child: Center(child: LoadingIndicator()))
            else if (auctions.isEmpty)
              Container(
                height: 150,
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.border),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.gavel_outlined, size: 48, color: AppColors.textHint),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        localizations.noAuctions,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textHint),
                      ),
                    ],
                  ),
                ),
              )
            else
              SizedBox(
                height: 367,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                  itemCount: auctions.length,
                  itemBuilder: (context, index) {
                    final auction = auctions[index];
                    return Container(
                      width: 250,
                      margin: const EdgeInsets.only(right: AppConstants.defaultPadding),
                      child: AuctionCard(
                        auction: auction,
                        onTap: () {
                          Navigator.of(
                            context,
                          ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
                        },
                        onWatchToggle: () => _toggleWatchlist(auction.id),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildEndingSoonSection(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final endingSoon = auctionProvider.endingSoonAuctions.take(3).toList();

        if (endingSoon.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Text(
                localizations.endingSoon,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              itemCount: endingSoon.length,
              itemBuilder: (context, index) {
                final auction = endingSoon[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                  child: AuctionCard(
                    auction: auction,
                    isCompact: true,
                    onTap: () {
                      Navigator.of(
                        context,
                      ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
                    },
                    onWatchToggle: () => _toggleWatchlist(auction.id),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildComingSoonSection(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final comingSoon = auctionProvider.comingSoonAuctions.take(3).toList();

        if (comingSoon.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Coming Soon',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pushNamed('/auctions', arguments: {'status': 'scheduled'});
                    },
                    child: const Text('View All'),
                  ),
                ],
              ),
            ),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              itemCount: comingSoon.length,
              itemBuilder: (context, index) {
                final auction = comingSoon[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                  child: AuctionCard(
                    auction: auction,
                    isCompact: true,
                    onTap: () {
                      Navigator.of(
                        context,
                      ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
                    },
                    onWatchToggle: () => _toggleWatchlist(auction.id),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildEndedAuctionsSection(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final endedAuctions = auctionProvider.endedAuctions.take(3).toList();

        if (endedAuctions.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Ended Auctions',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate to ended auctions list
                      Navigator.of(context).pushNamed('/ended-auctions');
                    },
                    child: const Text('View All'),
                  ),
                ],
              ),
            ),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              itemCount: endedAuctions.length,
              itemBuilder: (context, index) {
                final auction = endedAuctions[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                  child: AuctionCard(
                    auction: auction,
                    isCompact: true,
                    onTap: () {
                      Navigator.of(
                        context,
                      ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
                    },
                    onWatchToggle: () => _toggleWatchlist(auction.id),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoriesSection(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final categories = auctionProvider.categories.take(6).toList();

        if (categories.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.categories,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pushNamed('/auctions');
                    },
                    child: Text('View All'),
                  ),
                ],
              ),
            ),

            // Horizontal scrolling categories
            SizedBox(
              height: 185,
              child: ListView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  return Container(
                    padding: EdgeInsets.symmetric(vertical: 3),
                    width: 140,
                    margin: const EdgeInsets.only(right: AppConstants.defaultPadding),
                    child: FishCategoryCard(
                      category: category,
                      onTap: () {
                        Navigator.of(
                          context,
                        ).push(MaterialPageRoute(builder: (context) => CategoryAuctionsScreen(category: category)));
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEarnedAuctionsSection(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        // Get recent earned auctions for the current user
        final earnedAuctions = auctionProvider.earnedAuctions.take(3).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        'Earned Auctions',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      // Red dot indicator for new wins
                      if (earnedAuctions.isNotEmpty)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(color: AppColors.accent, shape: BoxShape.circle),
                        ),
                    ],
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate directly to earned auctions screen
                      Navigator.of(context).push(MaterialPageRoute(builder: (context) => const EarnedAuctionsScreen()));
                    },
                    child: const Text('View All'),
                  ),
                ],
              ),
            ),

            if (earnedAuctions.isEmpty)
              Container(
                height: 120,
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.border),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.emoji_events_outlined, size: 48, color: AppColors.textHint),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'No won auctions yet',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textHint),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                itemCount: earnedAuctions.length,
                itemBuilder: (context, index) {
                  final auction = earnedAuctions[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                    child: AuctionCard(
                      auction: auction,
                      isCompact: true,
                      onTap: () {
                        // Navigate to earned auction screen for payment/delivery tracking
                        Navigator.of(
                          context,
                        ).push(MaterialPageRoute(builder: (context) => EarnedAuctionScreen(auction: auction)));
                      },
                      onWatchToggle: () => _toggleWatchlist(auction.id),
                    ),
                  );
                },
              ),
          ],
        );
      },
    );
  }

  Widget _buildRecentActivitySection(AppLocalizations localizations) {
    return Consumer2<AuctionProvider, AuthProvider>(
      builder: (context, auctionProvider, authProvider, child) {
        // Generate recent activities based on user's auctions and bids
        final recentActivities = _generateRecentActivities(auctionProvider, authProvider);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Activity',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate to notifications screen for full activity history
                      Navigator.of(context).push(MaterialPageRoute(builder: (context) => const NotificationsScreen()));
                    },
                    child: const Text('View All'),
                  ),
                ],
              ),
            ),

            if (recentActivities.isEmpty)
              Container(
                height: 120,
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.border),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.history, size: 48, color: AppColors.textHint),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'No recent activity',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textHint),
                      ),
                    ],
                  ),
                ),
              )
            else
              Container(
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.border),
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  itemCount: recentActivities.length,
                  separatorBuilder: (context, index) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final activity = recentActivities[index];
                    return _buildActivityItem(activity);
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  String _getWelcomeMessage(AppLocalizations localizations, String userType) {
    switch (userType) {
      case 'seller':
        return localizations.readyToSellMessage;
      case 'broker':
        return localizations.facilitateTradingMessage;
      case 'service_provider':
        return localizations.fishingServicesMessage;
      case 'admin':
        return localizations.adminManageMessage;
      default:
        return localizations.discoverAuctionsMessage;
    }
  }

  void _navigateToMyServiceRequests() {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const MyServiceRequestsScreen()));
  }

  void _navigateToServiceTracking() {
    Navigator.push(context, MaterialPageRoute(builder: (context) => const ClientServiceTrackingScreen()));
  }

  List<Map<String, dynamic>> _generateRecentActivities(AuctionProvider auctionProvider, AuthProvider authProvider) {
    final activities = <Map<String, dynamic>>[];
    final now = DateTime.now();

    // Add recent auctions user has bid on
    for (final auction in auctionProvider.allAuctions.take(10)) {
      if (auction.totalBids > 0) {
        activities.add({
          'type': 'bid_activity',
          'title': 'Bidding activity',
          'subtitle': 'Auction: ${auction.title}',
          'time': auction.updatedAt,
          'icon': Icons.gavel,
          'color': AppColors.primary,
          'auction': auction,
        });
      }
    }

    // Add recent earned auctions
    for (final auction in auctionProvider.earnedAuctions.take(5)) {
      activities.add({
        'type': 'auction_won',
        'title': 'Auction won!',
        'subtitle': auction.title,
        'time': auction.endTime ?? auction.updatedAt,
        'icon': Icons.emoji_events,
        'color': AppColors.success,
        'auction': auction,
      });
    }

    // Add recent watchlist additions (simulated)
    for (final auction in auctionProvider.watchlist.take(3)) {
      activities.add({
        'type': 'watchlist_added',
        'title': 'Added to watchlist',
        'subtitle': auction.title,
        'time': auction.createdAt,
        'icon': Icons.favorite,
        'color': AppColors.accent,
        'auction': auction,
      });
    }

    // Sort by time (most recent first) and take only the latest 5
    activities.sort((a, b) => (b['time'] as DateTime).compareTo(a['time'] as DateTime));
    return activities.take(5).toList();
  }

  Widget _buildActivityItem(Map<String, dynamic> activity) {
    final timeAgo = _getTimeAgo(activity['time'] as DateTime);

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 4),
      leading: CircleAvatar(
        backgroundColor: (activity['color'] as Color).withOpacity(0.1),
        child: Icon(activity['icon'] as IconData, color: activity['color'] as Color, size: 20),
      ),
      title: Text(
        activity['title'] as String,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        activity['subtitle'] as String,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Text(
        timeAgo,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textHint, fontSize: 11),
      ),
      onTap: () {
        // Navigate to auction detail if auction is available
        if (activity['auction'] != null) {
          Navigator.of(
            context,
          ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: activity['auction'])));
        }
      },
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
