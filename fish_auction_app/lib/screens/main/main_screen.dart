import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../providers/auction_provider.dart';
import '../../models/auction_model.dart';
import '../../l10n/generated/app_localizations.dart';
import '../auction/auction_detail_screen.dart';
import '../auction/create_auction_screen.dart';
import 'home_screen.dart';
import 'auctions_screen.dart';
import 'watchlist_screen.dart';
import 'wallet_screen.dart';
import 'profile_screen.dart';
import '../delivery/seller_delivery_screen.dart';
import '../broker/broker_dashboard_screen.dart';
import '../broker/available_requests_screen.dart';
import '../broker/my_service_requests_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    // Use addPostFrameCallback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.initialize();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(index, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    // Define screens based on user type
    final screens = _getScreensForUserType(authProvider.userType);
    final bottomNavItems = _getBottomNavItemsForUserType(localizations, authProvider.userType);

    return Scaffold(
      body: PageView(controller: _pageController, onPageChanged: _onPageChanged, children: screens),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 10, offset: const Offset(0, -2))],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppColors.surface,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: AppColors.textSecondary,
          selectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
          unselectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
          items: bottomNavItems,
        ),
      ),
    );
  }

  List<Widget> _getScreensForUserType(String userType) {
    switch (userType) {
      case 'seller':
        return [
          const HomeScreen(),
          const AuctionsScreen(),
          const MyAuctionsScreen(), // Seller's own auctions
          const SellerDeliveryScreen(), // Delivery management
          const WalletScreen(),
          const ProfileScreen(),
        ];
      case 'broker':
        return [
          const BrokerDashboardScreen(),
          const AvailableRequestsScreen(),
          const MyServiceRequestsScreen(),
          const WalletScreen(),
          const ProfileScreen(),
        ];
      case 'service_provider':
        return [
          const HomeScreen(),
          const MarketplaceScreen(), // Services marketplace
          const MyServicesScreen(), // Provider's services
          const WalletScreen(),
          const ProfileScreen(),
        ];
      case 'admin':
        return [
          const HomeScreen(),
          const AuctionsScreen(),
          const AdminPanelScreen(), // Admin management
          const WalletScreen(),
          const ProfileScreen(),
        ];
      default: // buyer
        return [
          const HomeScreen(),
          const AuctionsScreen(),
          const WatchlistScreen(),
          const WalletScreen(),
          const ProfileScreen(),
        ];
    }
  }

  List<BottomNavigationBarItem> _getBottomNavItemsForUserType(AppLocalizations localizations, String userType) {
    switch (userType) {
      case 'seller':
        return [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home_outlined),
            activeIcon: const Icon(Icons.home),
            label: localizations.home,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.gavel_outlined),
            activeIcon: const Icon(Icons.gavel),
            label: localizations.auctions,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.store_outlined),
            activeIcon: const Icon(Icons.store),
            label: localizations.myAuctions,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.local_shipping_outlined),
            activeIcon: const Icon(Icons.local_shipping),
            label: localizations.deliveries,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.account_balance_wallet_outlined),
            activeIcon: const Icon(Icons.account_balance_wallet),
            label: localizations.wallet,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_outline),
            activeIcon: const Icon(Icons.person),
            label: localizations.profile,
          ),
        ];
      case 'broker':
        return [
          BottomNavigationBarItem(
            icon: const Icon(Icons.dashboard_outlined),
            activeIcon: const Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.assignment_outlined),
            activeIcon: const Icon(Icons.assignment),
            label: 'Available',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.work_outline),
            activeIcon: const Icon(Icons.work),
            label: 'My Services',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.account_balance_wallet_outlined),
            activeIcon: const Icon(Icons.account_balance_wallet),
            label: localizations.wallet,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_outline),
            activeIcon: const Icon(Icons.person),
            label: localizations.profile,
          ),
        ];
      case 'service_provider':
        return [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home_outlined),
            activeIcon: const Icon(Icons.home),
            label: localizations.home,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.store_mall_directory_outlined),
            activeIcon: const Icon(Icons.store_mall_directory),
            label: localizations.marketplace,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.build_outlined),
            activeIcon: const Icon(Icons.build),
            label: localizations.myServices,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.account_balance_wallet_outlined),
            activeIcon: const Icon(Icons.account_balance_wallet),
            label: localizations.wallet,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_outline),
            activeIcon: const Icon(Icons.person),
            label: localizations.profile,
          ),
        ];
      case 'admin':
        return [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home_outlined),
            activeIcon: const Icon(Icons.home),
            label: localizations.home,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.gavel_outlined),
            activeIcon: const Icon(Icons.gavel),
            label: localizations.auctions,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.admin_panel_settings_outlined),
            activeIcon: const Icon(Icons.admin_panel_settings),
            label: 'Admin',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.account_balance_wallet_outlined),
            activeIcon: const Icon(Icons.account_balance_wallet),
            label: localizations.wallet,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_outline),
            activeIcon: const Icon(Icons.person),
            label: localizations.profile,
          ),
        ];
      default: // buyer
        return [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home_outlined),
            activeIcon: const Icon(Icons.home),
            label: localizations.home,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.gavel_outlined),
            activeIcon: const Icon(Icons.gavel),
            label: localizations.auctions,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.bookmark_outline),
            activeIcon: const Icon(Icons.bookmark),
            label: localizations.watchlist,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.account_balance_wallet_outlined),
            activeIcon: const Icon(Icons.account_balance_wallet),
            label: localizations.wallet,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_outline),
            activeIcon: const Icon(Icons.person),
            label: localizations.profile,
          ),
        ];
    }
  }
}

// My Auctions Screen for sellers
class MyAuctionsScreen extends StatefulWidget {
  const MyAuctionsScreen({super.key});

  @override
  State<MyAuctionsScreen> createState() => _MyAuctionsScreenState();
}

class _MyAuctionsScreenState extends State<MyAuctionsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMyAuctions();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMyAuctions() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadMyAuctions(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Auctions'),
        actions: [IconButton(onPressed: _createNewAuction, icon: const Icon(Icons.add), tooltip: 'Create Auction')],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Draft'),
            Tab(text: 'Live'),
            Tab(text: 'Ended'),
          ],
        ),
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildMyAuctionsList(auctionProvider, null),
              _buildMyAuctionsList(auctionProvider, 'draft'),
              _buildMyAuctionsList(auctionProvider, 'live'),
              _buildMyAuctionsList(auctionProvider, 'ended'),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewAuction,
        tooltip: 'Create New Auction',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildMyAuctionsList(AuctionProvider auctionProvider, String? status) {
    final auctions = auctionProvider.myAuctions;
    final filteredAuctions = status == null ? auctions : auctions.where((a) => a.status == status).toList();

    if (auctionProvider.isLoading && filteredAuctions.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (filteredAuctions.isEmpty) {
      return _buildEmptyState(status);
    }

    return RefreshIndicator(
      onRefresh: _loadMyAuctions,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredAuctions.length,
        itemBuilder: (context, index) {
          final auction = filteredAuctions[index];
          return Container(margin: const EdgeInsets.only(bottom: 16), child: _buildMyAuctionCard(auction));
        },
      ),
    );
  }

  Widget _buildMyAuctionCard(Auction auction) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(auction.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                ),
                _buildStatusChip(auction.status),
              ],
            ),
            const SizedBox(height: 8),
            Text('${auction.fishType} • ${auction.formattedQuantity}', style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  'Current: \$${auction.currentPrice.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green),
                ),
                const Spacer(),
                Text('${auction.totalBids} bids', style: TextStyle(color: Colors.grey[600])),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(onPressed: () => _viewAuction(auction), child: const Text('View')),
                ),
                const SizedBox(width: 8),
                if (auction.status == 'draft') ...[
                  Expanded(
                    child: OutlinedButton(onPressed: () => _editAuction(auction), child: const Text('Edit')),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(onPressed: () => _makeAuctionLive(auction), child: const Text('Make Live')),
                  ),
                ],
                if (auction.status == 'live')
                  Expanded(
                    child: ElevatedButton(onPressed: () => _manageAuction(auction), child: const Text('Manage')),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status) {
      case 'draft':
        color = Colors.grey;
        label = 'Draft';
        break;
      case 'scheduled':
        color = Colors.orange;
        label = 'Scheduled';
        break;
      case 'live':
        color = Colors.green;
        label = 'Live';
        break;
      case 'ended':
        color = Colors.red;
        label = 'Ended';
        break;
      default:
        color = Colors.grey;
        label = status;
    }

    return Chip(
      label: Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      backgroundColor: color,
    );
  }

  Widget _buildEmptyState(String? status) {
    String message;
    String actionText;

    switch (status) {
      case 'draft':
        message = 'No draft auctions';
        actionText = 'Create your first auction';
        break;
      case 'live':
        message = 'No live auctions';
        actionText = 'Start selling your fish';
        break;
      case 'ended':
        message = 'No ended auctions';
        actionText = 'Your auction history will appear here';
        break;
      default:
        message = 'No auctions yet';
        actionText = 'Create your first auction to start selling';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.gavel, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(message, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(
            actionText,
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          if (status != 'ended')
            ElevatedButton.icon(
              onPressed: _createNewAuction,
              icon: const Icon(Icons.add),
              label: const Text('Create Auction'),
            ),
        ],
      ),
    );
  }

  void _createNewAuction() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const CreateAuctionScreen())).then((result) {
      if (result != null) {
        // Refresh my auctions list to show the newly created auction
        _loadMyAuctions();
      }
    });
  }

  void _viewAuction(Auction auction) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
  }

  void _editAuction(Auction auction) {
    // For now, show a dialog with basic edit options
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Auction'),
        content: const Text(
          'Auction editing is available. You can update the title, description, and timing for draft auctions.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Navigate to proper edit screen
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Full edit functionality will be available soon!'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _makeAuctionLive(Auction auction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Make Auction Live'),
        content: const Text(
          'Are you sure you want to make this auction live? Once live, you cannot edit the auction details.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
              final success = await auctionProvider.makeAuctionLive(auction.id);

              if (mounted) {
                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Auction is now live!'), backgroundColor: AppColors.success),
                  );
                  // Refresh the auctions list
                  _loadMyAuctions();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(auctionProvider.error ?? 'Failed to make auction live'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            child: const Text('Make Live'),
          ),
        ],
      ),
    );
  }

  void _manageAuction(Auction auction) {
    // TODO: Show auction management options
    showModalBottomSheet(context: context, builder: (context) => _buildManageAuctionSheet(auction));
  }

  Widget _buildManageAuctionSheet(Auction auction) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Manage Auction', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.visibility),
            title: const Text('View Details'),
            onTap: () {
              Navigator.pop(context);
              _viewAuction(auction);
            },
          ),
          ListTile(
            leading: const Icon(Icons.history),
            title: const Text('Bidding History'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Show bidding history
              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Bidding history coming soon!')));
            },
          ),
          ListTile(
            leading: const Icon(Icons.message),
            title: const Text('Contact Bidders'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Contact bidders
              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Contact feature coming soon!')));
            },
          ),
          if (auction.status == 'live')
            ListTile(
              leading: const Icon(Icons.stop, color: Colors.red),
              title: const Text('End Auction Early'),
              onTap: () {
                Navigator.pop(context);
                _showEndAuctionDialog(auction);
              },
            ),
        ],
      ),
    );
  }

  void _showEndAuctionDialog(Auction auction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('End Auction Early'),
        content: const Text('Are you sure you want to end this auction early? This action cannot be undone.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement end auction early
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('End auction feature coming soon!')));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('End Auction'),
          ),
        ],
      ),
    );
  }
}

class MarketplaceScreen extends StatelessWidget {
  const MarketplaceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('Marketplace Screen')));
  }
}

class MyServicesScreen extends StatelessWidget {
  const MyServicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('My Services Screen')));
  }
}

class AdminPanelScreen extends StatelessWidget {
  const AdminPanelScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text('Admin Panel Screen')));
  }
}
