import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auction_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/auction/auction_card.dart';
import '../../widgets/common/loading_overlay.dart';
import '../auction/auction_detail_screen.dart';
import '../payment/earned_auction_screen.dart';

class AuctionsScreen extends StatefulWidget {
  const AuctionsScreen({super.key});

  @override
  State<AuctionsScreen> createState() => _AuctionsScreenState();
}

class _AuctionsScreenState extends State<AuctionsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      auctionProvider.loadAuctions(refresh: true);
    });
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      auctionProvider.loadMoreAuctions();
    }
  }

  Future<void> _onRefresh() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadAuctions(refresh: true);
  }

  void _onTabChanged() {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);

    switch (_tabController.index) {
      case 0: // All
        auctionProvider.loadAuctions(refresh: true);
        break;
      case 1: // Live
        auctionProvider.loadAuctions(refresh: true, status: 'live');
        break;
      case 2: // Scheduled
        auctionProvider.loadAuctions(refresh: true, status: 'scheduled');
        break;
      case 3: // Direct Buy
        auctionProvider.loadDirectBuyAuctions();
        break;
      case 4: // Ended
        auctionProvider.loadAuctions(refresh: true, status: 'ended');
        break;
      case 5: // Earned
        auctionProvider.loadEarnedAuctions();
        break;
    }
  }

  Future<void> _toggleWatchlist(int auctionId) async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final success = await auctionProvider.toggleWatchlist(auctionId);

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(auctionProvider.error ?? 'Failed to update watchlist'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.auctions),
        actions: [
          IconButton(onPressed: _showFilterDialog, icon: const Icon(Icons.filter_list)),
          IconButton(onPressed: _showSearchDialog, icon: const Icon(Icons.search)),
        ],
        bottom: TabBar(
          controller: _tabController,
          onTap: (_) => _onTabChanged(),
          tabs: [
            Tab(text: localizations.all),
            Tab(text: localizations.liveAuctions),
            Tab(text: localizations.scheduled),
            Tab(text: 'Direct Buy'),
            Tab(text: localizations.ended),
            Tab(text: localizations.earned),
          ],
        ),
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildAuctionsList(auctionProvider, localizations),
              _buildAuctionsList(auctionProvider, localizations),
              _buildAuctionsList(auctionProvider, localizations),
              _buildDirectBuyList(auctionProvider, localizations),
              _buildAuctionsList(auctionProvider, localizations),
              _buildEarnedAuctionsList(auctionProvider, localizations),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAuctionsList(AuctionProvider auctionProvider, AppLocalizations localizations) {
    if (auctionProvider.isLoading && auctionProvider.auctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (auctionProvider.auctions.isEmpty) {
      return _buildEmptyState(localizations);
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: auctionProvider.auctions.length + (auctionProvider.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= auctionProvider.auctions.length) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final auction = auctionProvider.auctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: AuctionCard(
              auction: auction,
              onTap: () {
                Navigator.of(
                  context,
                ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
              },
              onWatchToggle: () => _toggleWatchlist(auction.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDirectBuyList(AuctionProvider auctionProvider, AppLocalizations localizations) {
    // Use direct buy auctions (buy_now type)
    final directBuyAuctions = auctionProvider.directBuyAuctions;

    if (auctionProvider.isLoading && directBuyAuctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (directBuyAuctions.isEmpty) {
      return _buildDirectBuyEmptyState(localizations);
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: directBuyAuctions.length,
        itemBuilder: (context, index) {
          final auction = directBuyAuctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: AuctionCard(
              auction: auction,
              onTap: () {
                Navigator.of(
                  context,
                ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
              },
              onWatchToggle: () => _toggleWatchlist(auction.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEarnedAuctionsList(AuctionProvider auctionProvider, AppLocalizations localizations) {
    // Use earned auctions from the dedicated API call
    final earnedAuctions = auctionProvider.earnedAuctions;

    if (auctionProvider.isLoading && earnedAuctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (earnedAuctions.isEmpty) {
      return _buildEarnedEmptyState(localizations);
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: earnedAuctions.length,
        itemBuilder: (context, index) {
          final auction = earnedAuctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: AuctionCard(
              auction: auction,
              onTap: () {
                Navigator.of(
                  context,
                ).push(MaterialPageRoute(builder: (context) => EarnedAuctionScreen(auction: auction)));
              },
              onWatchToggle: () => _toggleWatchlist(auction.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDirectBuyEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.shopping_cart_outlined, size: 64, color: AppColors.textHint),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Direct Buy Items',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppColors.textHint),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Items with fixed prices will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEarnedEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.emoji_events_outlined, size: 64, color: AppColors.textHint),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Earned Auctions',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppColors.textHint),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Auctions you win will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.gavel_outlined, size: 64, color: AppColors.textHint),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            localizations.noAuctions,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppColors.textHint),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Check back later for new auctions',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: _onRefresh,
            icon: const Icon(Icons.refresh),
            label: Text(localizations.refresh),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppConstants.borderRadius)),
      ),
      builder: (context) => _buildFilterSheet(),
    );
  }

  Widget _buildFilterSheet() {
    final localizations = AppLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.all(AppConstants.largePadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                localizations.filter,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              IconButton(onPressed: () => Navigator.of(context).pop(), icon: const Icon(Icons.close)),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Categories Filter
          Text(
            localizations.categories,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          Consumer<AuctionProvider>(
            builder: (context, auctionProvider, child) {
              return Wrap(
                spacing: AppConstants.smallPadding,
                children: auctionProvider.categories.map((category) {
                  return FilterChip(
                    label: Text(category.name),
                    selected: auctionProvider.categoryFilter == category.id,
                    onSelected: (selected) {
                      final categoryId = selected ? category.id : null;
                      auctionProvider.filterAuctions(category: categoryId);
                      Navigator.of(context).pop();
                    },
                  );
                }).toList(),
              );
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Sort Options
          Text(
            localizations.sort,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          Column(
            children: [
              _buildSortOption('Price: Low to High', 'current_price'),
              _buildSortOption('Price: High to Low', '-current_price'),
              _buildSortOption('Ending Soon', 'end_time'),
              _buildSortOption('Most Recent', '-created_at'),
              _buildSortOption('Most Bids', '-total_bids'),
            ],
          ),

          const SizedBox(height: AppConstants.largePadding),

          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
                    auctionProvider.clearFilters();
                    Navigator.of(context).pop();
                  },
                  child: Text(localizations.clear),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: ElevatedButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.apply)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortOption(String title, String value) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final isSelected = auctionProvider.ordering == value;

        return ListTile(
          title: Text(title),
          trailing: isSelected ? const Icon(Icons.check, color: AppColors.primary) : null,
          onTap: () {
            auctionProvider.filterAuctions(ordering: value);
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  void _showSearchDialog() {
    showDialog(context: context, builder: (context) => _buildSearchDialog());
  }

  Widget _buildSearchDialog() {
    final localizations = AppLocalizations.of(context);
    final searchController = TextEditingController();

    return AlertDialog(
      title: Text(localizations.search),
      content: TextField(
        controller: searchController,
        decoration: InputDecoration(hintText: 'Search auctions...', prefixIcon: const Icon(Icons.search)),
        autofocus: true,
        onSubmitted: (query) {
          if (query.trim().isNotEmpty) {
            final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
            auctionProvider.searchAuctions(query.trim());
          }
          Navigator.of(context).pop();
        },
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
        ElevatedButton(
          onPressed: () {
            final query = searchController.text.trim();
            if (query.isNotEmpty) {
              final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
              auctionProvider.searchAuctions(query);
            }
            Navigator.of(context).pop();
          },
          child: Text(localizations.search),
        ),
      ],
    );
  }
}
