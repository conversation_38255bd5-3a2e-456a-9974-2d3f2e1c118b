import 'package:fish_auction_app/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auction_provider.dart';
import '../../widgets/auction/auction_card.dart';
import '../../widgets/common/loading_overlay.dart';
import '../auction/auction_detail_screen.dart';

class WatchlistScreen extends StatefulWidget {
  const WatchlistScreen({super.key});

  @override
  State<WatchlistScreen> createState() => _WatchlistScreenState();
}

class _WatchlistScreenState extends State<WatchlistScreen> {
  @override
  void initState() {
    super.initState();
    _loadWatchlist();
  }

  Future<void> _loadWatchlist() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadWatchlist();
  }

  Future<void> _onRefresh() async {
    await _loadWatchlist();
  }

  Future<void> _toggleWatchlist(int auctionId) async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final success = await auctionProvider.toggleWatchlist(auctionId);

    if (success) {
      // Refresh the watchlist to remove the item
      await _loadWatchlist();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(auctionProvider.error ?? 'Failed to update watchlist'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.watchlist),
        actions: [IconButton(onPressed: _onRefresh, icon: const Icon(Icons.refresh))],
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          if (auctionProvider.isLoading) {
            return const Center(child: LoadingIndicator());
          }

          if (auctionProvider.watchlist.isEmpty) {
            return _buildEmptyState(localizations);
          }

          return RefreshIndicator(
            onRefresh: _onRefresh,
            child: ListView.builder(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              itemCount: auctionProvider.watchlist.length,
              itemBuilder: (context, index) {
                final auction = auctionProvider.watchlist[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                  child: AuctionCard(
                    auction: auction,
                    onTap: () {
                      Navigator.of(
                        context,
                      ).push(MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
                    },
                    onWatchToggle: () => _toggleWatchlist(auction.id),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bookmark_outline, size: 64, color: AppColors.textHint),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            localizations.noWatchlist,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppColors.textHint),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Add auctions to your watchlist to keep track of them',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to auctions screen
              DefaultTabController.of(context).animateTo(1);
            },
            icon: const Icon(Icons.gavel),
            label: const Text('Browse Auctions'),
          ),
        ],
      ),
    );
  }
}
