import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../providers/language_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_overlay.dart';
import 'register_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.login(
      username: _usernameController.text.trim(),
      password: _passwordController.text,
    );

    if (mounted) {
      if (success) {
        // Navigation is handled by AppNavigator
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).loginSuccess), backgroundColor: AppColors.success),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error ?? AppLocalizations.of(context).loginFailed),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _navigateToRegister() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const RegisterScreen()));
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return LoadingOverlay(
            isLoading: authProvider.isLoading,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: AppColors.primaryGradient,
                ),
              ),
              child: SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  child: Column(
                    children: [
                      const SizedBox(height: 60),

                      // Logo and Title
                      _buildHeader(localizations),

                      const SizedBox(height: 60),

                      // Login Form
                      _buildLoginForm(localizations),

                      const SizedBox(height: AppConstants.largePadding),

                      // Login Button
                      CustomButton(
                        text: localizations.login,
                        onPressed: _handleLogin,
                        isLoading: authProvider.isLoading,
                        backgroundColor: AppColors.textLight,
                        textColor: AppColors.primary,
                      ),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Register Link
                      _buildRegisterLink(localizations),

                      const SizedBox(height: 40),

                      // Language Toggle
                      _buildLanguageToggle(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(AppLocalizations localizations) {
    return Column(
      children: [
        // Logo
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.textLight,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 15, offset: const Offset(0, 8))],
          ),
          child: const Icon(Icons.waves, size: 50, color: AppColors.primary),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // App Name
        Text(
          localizations.appName,
          style: Theme.of(
            context,
          ).textTheme.displaySmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.smallPadding),

        // Welcome Text
        Text(
          localizations.welcome,
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(color: AppColors.textLight.withOpacity(0.9), fontWeight: FontWeight.w400),
        ),
      ],
    );
  }

  Widget _buildLoginForm(AppLocalizations localizations) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Username Field
          CustomTextField(
            controller: _usernameController,
            labelText: localizations.username,
            hintText: localizations.enterUsernameOrEmail,
            prefixIcon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return localizations.fieldRequired;
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Password Field
          CustomTextField(
            controller: _passwordController,
            labelText: localizations.password,
            hintText: 'Enter your password',
            prefixIcon: Icons.lock_outline,
            obscureText: _obscurePassword,
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility : Icons.visibility_off,
                color: AppColors.textLight.withOpacity(0.7),
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return localizations.fieldRequired;
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // Remember Me Checkbox
          Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
                fillColor: WidgetStateProperty.all(AppColors.textLight),
                checkColor: AppColors.primary,
              ),
              Text(
                localizations.rememberMe,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRegisterLink(AppLocalizations localizations) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          localizations.dontHaveAccount,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.8)),
        ),
        GestureDetector(
          onTap: _navigateToRegister,
          child: Text(
            localizations.register,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textLight,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageToggle() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: AppColors.textLight.withOpacity(0.2),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () async {
                  await languageProvider.setLanguage('en');
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                    vertical: AppConstants.smallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: languageProvider.currentLanguage == 'en' ? AppColors.primary : Colors.transparent,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: const Text(
                    'EN',
                    style: TextStyle(color: AppColors.textLight, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              GestureDetector(
                onTap: () async {
                  await languageProvider.setLanguage('ar');
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                    vertical: AppConstants.smallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: languageProvider.currentLanguage == 'ar' ? AppColors.primary : Colors.transparent,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: const Text(
                    'العربية',
                    style: TextStyle(color: AppColors.textLight, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
