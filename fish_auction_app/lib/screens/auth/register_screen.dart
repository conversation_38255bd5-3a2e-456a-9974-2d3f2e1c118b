import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_overlay.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();

  String _selectedUserType = AppConstants.buyerType;
  bool _acceptTerms = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_acceptTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please accept the terms and conditions'), backgroundColor: AppColors.error),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.register(
      username: _usernameController.text.trim(),
      email: _emailController.text.trim(),
      password: _passwordController.text,
      userType: _selectedUserType,
      firstName: _firstNameController.text.trim().isNotEmpty ? _firstNameController.text.trim() : null,
      lastName: _lastNameController.text.trim().isNotEmpty ? _lastNameController.text.trim() : null,
      phoneNumber: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).registrationSuccess), backgroundColor: AppColors.success),
        );
        Navigator.of(context).pop(); // Go back to login or main screen
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error ?? AppLocalizations.of(context).registrationFailed),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).pleaseConfirmPassword;
    }
    if (value != _passwordController.text) {
      return AppLocalizations.of(context).passwordsDoNotMatch;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return LoadingOverlay(
            isLoading: authProvider.isLoading,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: AppColors.primaryGradient,
                ),
              ),
              child: SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    children: [
                      // Header
                      _buildHeader(localizations),

                      const SizedBox(height: 40),

                      // Registration Form
                      _buildRegistrationForm(localizations),

                      const SizedBox(height: AppConstants.largePadding),

                      // Register Button
                      CustomButton(
                        text: localizations.register,
                        onPressed: _handleRegister,
                        isLoading: authProvider.isLoading,
                        backgroundColor: AppColors.textLight,
                        textColor: AppColors.primary,
                      ),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Login Link
                      _buildLoginLink(localizations),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(AppLocalizations localizations) {
    return Column(
      children: [
        // Back Button
        Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back, color: AppColors.textLight),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Title
        Text(
          localizations.register,
          style: Theme.of(
            context,
          ).textTheme.displaySmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: AppConstants.smallPadding),

        Text(
          localizations.createYourAccount,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
        ),
      ],
    );
  }

  Widget _buildRegistrationForm(AppLocalizations localizations) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // User Type Selection
          _buildUserTypeSelection(localizations),

          const SizedBox(height: AppConstants.defaultPadding),

          // Username Field
          CustomTextField(
            controller: _usernameController,
            labelText: localizations.username,
            hintText: 'Choose a username',
            prefixIcon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return localizations.fieldRequired;
              }
              if (value.trim().length < AppConstants.minUsernameLength) {
                return 'Username must be at least ${AppConstants.minUsernameLength} characters';
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Email Field
          EmailTextField(controller: _emailController, labelText: localizations.email),

          const SizedBox(height: AppConstants.defaultPadding),

          // Name Fields Row
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _firstNameController,
                  labelText: localizations.firstName,
                  hintText: 'First name',
                  prefixIcon: Icons.badge_outlined,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: CustomTextField(
                  controller: _lastNameController,
                  labelText: localizations.lastName,
                  hintText: 'Last name',
                  prefixIcon: Icons.badge_outlined,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Phone Field
          PhoneTextField(controller: _phoneController, labelText: localizations.phoneNumber),

          const SizedBox(height: AppConstants.defaultPadding),

          // Password Field
          PasswordTextField(controller: _passwordController, labelText: localizations.password),

          const SizedBox(height: AppConstants.defaultPadding),

          // Confirm Password Field
          PasswordTextField(
            controller: _confirmPasswordController,
            labelText: localizations.confirmPassword,
            validator: _validateConfirmPassword,
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Terms and Conditions
          _buildTermsCheckbox(localizations),
        ],
      ),
    );
  }

  Widget _buildUserTypeSelection(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Type',
          style: Theme.of(
            context,
          ).textTheme.labelMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9), fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Container(
          decoration: BoxDecoration(
            color: AppColors.textLight.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.textLight.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              _buildUserTypeOption(
                AppConstants.buyerType,
                localizations.buyerType,
                localizations.buyFishFromAuctions,
                Icons.shopping_cart_outlined,
              ),
              _buildUserTypeOption(
                AppConstants.sellerType,
                localizations.sellerType,
                localizations.sellFishThroughAuctions,
                Icons.store_outlined,
              ),
              _buildUserTypeOption(
                AppConstants.brokerType,
                localizations.brokerType,
                localizations.facilitateFishTrading,
                Icons.handshake_outlined,
              ),
              _buildUserTypeOption(
                AppConstants.serviceProviderType,
                localizations.serviceProviderType,
                localizations.provideFishingServices,
                Icons.build_outlined,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUserTypeOption(String value, String title, String description, IconData icon) {
    final isSelected = _selectedUserType == value;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedUserType = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.textLight.withOpacity(0.2) : Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        child: Row(
          children: [
            Icon(icon, color: isSelected ? AppColors.textLight : AppColors.textLight.withOpacity(0.7)),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: isSelected ? AppColors.textLight : AppColors.textLight.withOpacity(0.9),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textLight.withOpacity(0.7)),
                  ),
                ],
              ),
            ),
            Radio<String>(
              value: value,
              groupValue: _selectedUserType,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedUserType = newValue;
                  });
                }
              },
              fillColor: WidgetStateProperty.all(AppColors.textLight),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsCheckbox(AppLocalizations localizations) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
          fillColor: WidgetStateProperty.all(AppColors.textLight),
          checkColor: AppColors.primary,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _acceptTerms = !_acceptTerms;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 12),
              child: RichText(
                text: TextSpan(
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
                  children: [
                    const TextSpan(text: 'I agree to the '),
                    TextSpan(
                      text: 'Terms of Service',
                      style: TextStyle(
                        color: AppColors.textLight,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    const TextSpan(text: ' and '),
                    TextSpan(
                      text: 'Privacy Policy',
                      style: TextStyle(
                        color: AppColors.textLight,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginLink(AppLocalizations localizations) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          localizations.alreadyHaveAccount,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.8)),
        ),
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Text(
            localizations.login,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textLight,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }
}
