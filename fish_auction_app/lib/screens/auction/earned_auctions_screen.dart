import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auction_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/auth_service.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/auction/auction_card.dart';
import '../../widgets/common/loading_overlay.dart';
import 'auction_win_details_screen.dart';
import '../payment/payment_screen.dart';
import '../payment/earned_auction_screen.dart';

class EarnedAuctionsScreen extends StatefulWidget {
  const EarnedAuctionsScreen({super.key});

  @override
  State<EarnedAuctionsScreen> createState() => _EarnedAuctionsScreenState();
}

class _EarnedAuctionsScreenState extends State<EarnedAuctionsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Timer? _paymentTimer;
  final Map<int, Duration> _paymentTimeRemaining = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadEarnedAuctions();
    _startPaymentTimer();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _paymentTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadEarnedAuctions() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final authService = AuthService();
    final currentUser = authService.currentUser;

    print('🔍 EARNED AUCTIONS SCREEN: Current user: ${currentUser?.username} (ID: ${currentUser?.id})');
    print('🔍 EARNED AUCTIONS SCREEN: Loading earned auctions...');

    await auctionProvider.loadEarnedAuctions();
    _updatePaymentTimers();

    print('🔍 EARNED AUCTIONS SCREEN: Loaded ${auctionProvider.earnedAuctions.length} earned auctions');
    for (var auction in auctionProvider.earnedAuctions) {
      print(
        '🔍 EARNED AUCTIONS SCREEN: - Auction ${auction.id}: ${auction.title} (Status: ${auction.status}, Payment received: ${auction.payment_received})',
      );
    }
  }

  void _startPaymentTimer() {
    _paymentTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        _updatePaymentTimers();
      } else {
        timer.cancel();
      }
    });
  }

  void _updatePaymentTimers() {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final pendingPayments = auctionProvider.earnedAuctions
        .where((auction) => auction.status == 'ended' && !auction.payment_received && auction.payment_deadline != null)
        .toList();

    setState(() {
      _paymentTimeRemaining.clear();
      for (final auction in pendingPayments) {
        final deadline = auction.payment_deadline!;
        final now = DateTime.now();
        final remaining = deadline.difference(now);

        if (remaining.isNegative) {
          _paymentTimeRemaining[auction.id] = Duration.zero;
        } else {
          _paymentTimeRemaining[auction.id] = remaining;
        }
      }
    });
  }

  String _formatTimeRemaining(Duration duration) {
    if (duration.isNegative || duration == Duration.zero) {
      return 'Expired';
    }

    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.emoji_events, color: AppColors.accent),
            const SizedBox(width: AppConstants.smallPadding),
            const Text('Earned Auctions'),
            const SizedBox(width: AppConstants.smallPadding),
            // Red dot indicator
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(color: AppColors.accent, shape: BoxShape.circle),
            ),
          ],
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.accent,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Won'),
            Tab(text: 'Pending Payment'),
            Tab(text: 'Completed'),
          ],
        ),
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildWonAuctionsList(auctionProvider),
              _buildPendingPaymentList(auctionProvider),
              _buildCompletedAuctionsList(auctionProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildWonAuctionsList(AuctionProvider auctionProvider) {
    final wonAuctions = auctionProvider.earnedAuctions.where((auction) => !auction.payment_received).toList();

    if (auctionProvider.isLoading && wonAuctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (wonAuctions.isEmpty) {
      return _buildEmptyState(
        icon: Icons.emoji_events_outlined,
        title: 'No Won Auctions',
        subtitle: 'Auctions you win will appear here',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadEarnedAuctions,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: wonAuctions.length,
        itemBuilder: (context, index) {
          final auction = wonAuctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: _buildEarnedAuctionCard(auction),
          );
        },
      ),
    );
  }

  Widget _buildPendingPaymentList(AuctionProvider auctionProvider) {
    final pendingPayments = auctionProvider.earnedAuctions
        .where((auction) => auction.status == 'ended' && !auction.payment_received)
        .toList();

    if (auctionProvider.isLoading && pendingPayments.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (pendingPayments.isEmpty) {
      return _buildEmptyState(
        icon: Icons.payment_outlined,
        title: 'No Pending Payments',
        subtitle: 'Auctions requiring payment will appear here',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadEarnedAuctions,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: pendingPayments.length,
        itemBuilder: (context, index) {
          final auction = pendingPayments[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: _buildPendingPaymentCard(auction),
          );
        },
      ),
    );
  }

  Widget _buildCompletedAuctionsList(AuctionProvider auctionProvider) {
    final completedAuctions = auctionProvider.earnedAuctions.where((auction) => auction.payment_received).toList();

    if (auctionProvider.isLoading && completedAuctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (completedAuctions.isEmpty) {
      return _buildEmptyState(
        icon: Icons.check_circle_outline,
        title: 'No Completed Purchases',
        subtitle: 'Completed purchases will appear here',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadEarnedAuctions,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: completedAuctions.length,
        itemBuilder: (context, index) {
          final auction = completedAuctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: _buildCompletedAuctionCard(auction),
          );
        },
      ),
    );
  }

  Widget _buildEarnedAuctionCard(auction) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(auction.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding, vertical: 4),
                  decoration: BoxDecoration(color: AppColors.success, borderRadius: BorderRadius.circular(12)),
                  child: const Text(
                    'WON',
                    style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text('${auction.fishType} • ${auction.formattedQuantity}', style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Text(
                  'Won for: \$${auction.currentPrice.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.success),
                ),
                const Spacer(),
                ElevatedButton(onPressed: () => _viewWinDetails(auction), child: const Text('View Details')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingPaymentCard(auction) {
    final timeRemaining = _paymentTimeRemaining[auction.id];
    final isExpired = timeRemaining == null || timeRemaining == Duration.zero;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(auction.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding, vertical: 4),
                  decoration: BoxDecoration(
                    color: isExpired ? AppColors.error : AppColors.warning,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isExpired ? 'EXPIRED' : 'PAYMENT DUE',
                    style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),

            // Payment deadline countdown
            if (auction.payment_deadline != null)
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: isExpired ? AppColors.error.withOpacity(0.1) : AppColors.warning.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.timer, size: 20, color: isExpired ? AppColors.error : AppColors.warning),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      isExpired ? 'Payment deadline expired' : 'Time remaining: ${_formatTimeRemaining(timeRemaining)}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isExpired ? AppColors.error : AppColors.warning,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Amount due: \$${auction.currentPrice.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.warning),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(onPressed: () => _viewWinDetails(auction), child: const Text('View Details')),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: ElevatedButton(
                    onPressed: isExpired ? null : () => _proceedToPayment(auction),
                    style: ElevatedButton.styleFrom(backgroundColor: isExpired ? Colors.grey : AppColors.warning),
                    child: Text(isExpired ? 'Expired' : 'Pay Now'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompletedAuctionCard(auction) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(auction.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding, vertical: 4),
                  decoration: BoxDecoration(color: AppColors.success, borderRadius: BorderRadius.circular(12)),
                  child: const Text(
                    'COMPLETED',
                    style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Paid: \$${auction.currentPrice.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.success),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(onPressed: () => _viewWinDetails(auction), child: const Text('View Details')),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: ElevatedButton(onPressed: () => _trackDelivery(auction), child: const Text('Track Delivery')),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState({required IconData icon, required String title, required String subtitle}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _viewWinDetails(auction) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => AuctionWinDetailsScreen(auction: auction)));
  }

  void _proceedToPayment(auction) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => PaymentScreen(auction: auction))).then((_) {
      // Refresh the auctions list when returning from payment screen
      _loadEarnedAuctions();
    });
  }

  void _trackDelivery(auction) {
    // Navigate to the unified earned auction screen for delivery tracking
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => EarnedAuctionScreen(auction: auction)));
  }
}
