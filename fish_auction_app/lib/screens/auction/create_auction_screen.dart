import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:io';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auction_provider.dart';
import '../../models/location_model.dart';
import '../../services/location_service.dart';
import '../../l10n/generated/app_localizations.dart';

class CreateAuctionScreen extends StatefulWidget {
  const CreateAuctionScreen({super.key});

  @override
  State<CreateAuctionScreen> createState() => _CreateAuctionScreenState();
}

class _CreateAuctionScreenState extends State<CreateAuctionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _fishTypeController = TextEditingController();
  final _weightController = TextEditingController();
  final _quantityController = TextEditingController();
  final _catchLocationController = TextEditingController();
  final _deliveryAddressController = TextEditingController();
  final _startingPriceController = TextEditingController();
  final _reservePriceController = TextEditingController();
  final _targetPriceController = TextEditingController();
  final _buyNowPriceController = TextEditingController();
  final _bidIncrementController = TextEditingController();

  int? _selectedCategoryId;
  String _auctionType = 'live';
  DateTime? _catchDate;
  DateTime? _startTime;
  DateTime? _endTime;
  File? _mainImage;
  final List<File> _additionalImages = [];

  // Location related variables
  final LocationService _locationService = LocationService();
  Location? _currentLocation;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    // Set default values
    _bidIncrementController.text = '1.00';
    _quantityController.text = '1';

    // Set default start time to now + 1 hour
    _startTime = DateTime.now().add(const Duration(hours: 1));
    // Set default end time to start time + 24 hours
    _endTime = _startTime!.add(const Duration(hours: 24));
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fishTypeController.dispose();
    _weightController.dispose();
    _quantityController.dispose();
    _catchLocationController.dispose();
    _deliveryAddressController.dispose();
    _startingPriceController.dispose();
    _reservePriceController.dispose();
    _targetPriceController.dispose();
    _buyNowPriceController.dispose();
    _bidIncrementController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Auction'),
        actions: [
          Consumer<AuctionProvider>(
            builder: (context, auctionProvider, child) {
              return auctionProvider.isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(16),
                      child: SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2)),
                    )
                  : TextButton(onPressed: _submitAuction, child: const Text('Create'));
            },
          ),
        ],
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          if (auctionProvider.error != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(auctionProvider.error!), backgroundColor: AppColors.error));
              auctionProvider.clearError();
            });
          }

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Information Section
                  _buildSectionTitle('Basic Information'),
                  _buildBasicInfoSection(),

                  const SizedBox(height: AppConstants.largePadding),

                  // Fish Details Section
                  _buildSectionTitle('Fish Details'),
                  _buildFishDetailsSection(auctionProvider),

                  const SizedBox(height: AppConstants.largePadding),

                  // Pricing Section
                  _buildSectionTitle('Pricing'),
                  _buildPricingSection(),

                  const SizedBox(height: AppConstants.largePadding),

                  // Timing Section
                  _buildSectionTitle('Auction Timing'),
                  _buildTimingSection(),

                  const SizedBox(height: AppConstants.largePadding),

                  // Images Section
                  _buildSectionTitle('Images'),
                  _buildImagesSection(),

                  const SizedBox(height: AppConstants.largePadding * 2),

                  // Create Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: auctionProvider.isLoading ? null : () => _submitAuction(isDraft: true),
                          style: OutlinedButton.styleFrom(padding: const EdgeInsets.all(AppConstants.defaultPadding)),
                          child: const Text(
                            'Save as Draft',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppConstants.defaultPadding),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: auctionProvider.isLoading ? null : () => _submitAuction(isDraft: false),
                          style: ElevatedButton.styleFrom(padding: const EdgeInsets.all(AppConstants.defaultPadding)),
                          child: auctionProvider.isLoading
                              ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                              : const Text('Publish Live', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Text(title, style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        // Title
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(labelText: 'Auction Title *', hintText: 'e.g., Fresh Atlantic Salmon'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Title is required';
            }
            if (value.trim().length < 5) {
              return 'Title must be at least 5 characters';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Description
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(labelText: 'Description *', hintText: 'Describe your fish in detail...'),
          maxLines: 4,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Description is required';
            }
            if (value.trim().length < 20) {
              return 'Description must be at least 20 characters';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Auction Type
        DropdownButtonFormField<String>(
          value: _auctionType,
          decoration: const InputDecoration(labelText: 'Auction Type'),
          items: const [
            DropdownMenuItem(value: 'live', child: Text('Live Auction')),
            DropdownMenuItem(value: 'timed', child: Text('Timed Auction')),
            DropdownMenuItem(value: 'buy_now', child: Text('Buy Now')),
          ],
          onChanged: (value) {
            setState(() {
              _auctionType = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildFishDetailsSection(AuctionProvider auctionProvider) {
    return Column(
      children: [
        // Fish Category
        DropdownButtonFormField<int>(
          value: _selectedCategoryId,
          decoration: const InputDecoration(labelText: 'Fish Category *'),
          items: auctionProvider.categories.map((category) {
            return DropdownMenuItem<int>(value: category.id, child: Text(category.name));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'Please select a fish category';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Fish Type
        TextFormField(
          controller: _fishTypeController,
          decoration: const InputDecoration(labelText: 'Fish Type *', hintText: 'e.g., Atlantic Salmon, Tuna, etc.'),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Fish type is required';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Weight and Quantity Row
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _weightController,
                decoration: const InputDecoration(labelText: 'Weight (kg) *', hintText: '0.0'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Weight is required';
                  }
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0) {
                    return 'Enter valid weight';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: TextFormField(
                controller: _quantityController,
                decoration: const InputDecoration(labelText: 'Quantity *', hintText: '1'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Quantity is required';
                  }
                  final quantity = int.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'Enter valid quantity';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Catch Date
        InkWell(
          onTap: _selectCatchDate,
          child: InputDecorator(
            decoration: const InputDecoration(labelText: 'Catch Date *'),
            child: Text(
              _catchDate != null ? '${_catchDate!.day}/${_catchDate!.month}/${_catchDate!.year}' : 'Select catch date',
              style: TextStyle(color: _catchDate != null ? null : Colors.grey[600]),
            ),
          ),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Catch Location with GPS
        _buildLocationSection(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Delivery Address for Brokers
        _buildDeliveryAddressField(),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location Label
        Text('Catch Location *', style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500)),
        const SizedBox(height: AppConstants.smallPadding),

        // Permission Info (if no location)
        if (_currentLocation == null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              border: Border.all(color: Colors.blue[200]!),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'Tap "Get Location" to capture your current GPS coordinates. This helps buyers verify your fishing location.',
                    style: TextStyle(color: Colors.blue[700], fontSize: 13),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
        ],

        // Location Display Container
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            border: Border.all(color: _currentLocation == null ? AppColors.error : AppColors.success, width: 2),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            color: _currentLocation != null
                ? AppColors.success.withValues(alpha: 0.1)
                : AppColors.error.withValues(alpha: 0.1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _currentLocation != null ? Icons.location_on : Icons.location_off,
                    color: _currentLocation != null ? AppColors.primary : AppColors.error,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Text(
                      _currentLocation != null ? 'Location captured' : 'Location required',
                      style: TextStyle(
                        color: _currentLocation != null ? AppColors.textPrimary : AppColors.error,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  // Get Location Button
                  ElevatedButton.icon(
                    onPressed: _isGettingLocation ? null : _getCurrentLocation,
                    icon: _isGettingLocation
                        ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                        : const Icon(Icons.my_location, size: 18),
                    label: Text(_isGettingLocation ? 'Getting...' : 'Get Location'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.defaultPadding,
                        vertical: AppConstants.smallPadding,
                      ),
                    ),
                  ),
                ],
              ),

              if (_currentLocation != null) ...[
                const SizedBox(height: AppConstants.smallPadding),
                Container(
                  padding: const EdgeInsets.all(AppConstants.smallPadding),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Coordinates:',
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w600, color: AppColors.primary),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Lat: ${_currentLocation!.latitude.toStringAsFixed(6)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        'Lng: ${_currentLocation!.longitude.toStringAsFixed(6)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      if (_currentLocation!.accuracy != null)
                        Text(
                          'Accuracy: ±${_currentLocation!.accuracy!.toStringAsFixed(0)}m',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                        ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),

        if (_currentLocation == null)
          Padding(
            padding: const EdgeInsets.only(top: AppConstants.smallPadding),
            child: Text(
              'Please capture your current location to proceed',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.error),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryAddressField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Address (for Broker Services)',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        TextFormField(
          controller: _deliveryAddressController,
          decoration: const InputDecoration(
            labelText: 'Delivery/Pickup Address',
            hintText: 'Enter the address where fish will be available for pickup/delivery',
            helperText: 'This helps brokers locate and provide services for your auction',
            prefixIcon: Icon(Icons.location_on_outlined),
          ),
          maxLines: 2,
          validator: (value) {
            // Optional field, no validation required
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPricingSection() {
    return Column(
      children: [
        // Starting Price (for live/timed) or Fixed Price (for buy_now)
        TextFormField(
          controller: _startingPriceController,
          decoration: InputDecoration(
            labelText: _auctionType == 'buy_now' ? 'Fixed Price (\$) *' : 'Starting Price (\$) *',
            hintText: '0.00',
            prefixText: '\$ ',
            helperText: _auctionType == 'buy_now' ? 'Buyers will pay this exact amount' : 'Initial bidding price',
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return _auctionType == 'buy_now' ? 'Fixed price is required' : 'Starting price is required';
            }
            final price = double.tryParse(value);
            if (price == null || price <= 0) {
              return _auctionType == 'buy_now' ? 'Enter valid fixed price' : 'Enter valid starting price';
            }
            return null;
          },
        ),

        // Only show bidding-related fields for live/timed auctions
        if (_auctionType != 'buy_now') ...[
          const SizedBox(height: AppConstants.defaultPadding),

          // Reserve Price (Optional)
          TextFormField(
            controller: _reservePriceController,
            decoration: const InputDecoration(
              labelText: 'Reserve Price (\$)',
              hintText: '0.00 (Optional)',
              prefixText: '\$ ',
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final price = double.tryParse(value);
                if (price == null || price <= 0) {
                  return 'Enter valid reserve price';
                }
                final startingPrice = double.tryParse(_startingPriceController.text);
                if (startingPrice != null && price < startingPrice) {
                  return 'Reserve price must be >= starting price';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Target Price (Optional)
          TextFormField(
            controller: _targetPriceController,
            decoration: const InputDecoration(
              labelText: 'Target Price (\$)',
              hintText: '0.00 (Optional - enables early close)',
              prefixText: '\$ ',
              helperText: 'When reached, you can close auction early',
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final price = double.tryParse(value);
                if (price == null || price <= 0) {
                  return 'Enter valid target price';
                }
                final startingPrice = double.tryParse(_startingPriceController.text);
                if (startingPrice != null && price < startingPrice) {
                  return 'Target price must be >= starting price';
                }
              }
              return null;
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Bid Increment
          TextFormField(
            controller: _bidIncrementController,
            decoration: const InputDecoration(labelText: 'Bid Increment (\$) *', hintText: '1.00', prefixText: '\$ '),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Bid increment is required';
              }
              final increment = double.tryParse(value);
              if (increment == null || increment <= 0) {
                return 'Enter valid bid increment';
              }
              return null;
            },
          ),
        ],
      ],
    );
  }

  Widget _buildTimingSection() {
    return Column(
      children: [
        // Show different timing fields based on auction type
        if (_auctionType == 'buy_now') ...[
          // Buy Now - No timing needed
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(color: AppColors.info.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.info, color: AppColors.info),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'Buy Now items are available immediately and don\'t require timing settings.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.info),
                  ),
                ),
              ],
            ),
          ),
        ] else if (_auctionType == 'live') ...[
          // Live Auction - Only end time needed (starts immediately)
          Container(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Text(
              'Live auctions start immediately when created.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.success),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          InkWell(
            onTap: _selectEndTime,
            child: InputDecorator(
              decoration: const InputDecoration(labelText: 'End Time *'),
              child: Text(
                _endTime != null
                    ? '${_endTime!.day}/${_endTime!.month}/${_endTime!.year} ${_endTime!.hour}:${_endTime!.minute.toString().padLeft(2, '0')}'
                    : 'Select end time',
                style: TextStyle(color: _endTime != null ? null : Colors.grey[600]),
              ),
            ),
          ),
        ] else if (_auctionType == 'timed') ...[
          // Timed Auction - Both start and end time
          InkWell(
            onTap: _selectStartTime,
            child: InputDecorator(
              decoration: const InputDecoration(labelText: 'Start Time *'),
              child: Text(
                _startTime != null
                    ? '${_startTime!.day}/${_startTime!.month}/${_startTime!.year} ${_startTime!.hour}:${_startTime!.minute.toString().padLeft(2, '0')}'
                    : 'Select start time',
                style: TextStyle(color: _startTime != null ? null : Colors.grey[600]),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          InkWell(
            onTap: _selectEndTime,
            child: InputDecorator(
              decoration: const InputDecoration(labelText: 'End Time *'),
              child: Text(
                _endTime != null
                    ? '${_endTime!.day}/${_endTime!.month}/${_endTime!.year} ${_endTime!.hour}:${_endTime!.minute.toString().padLeft(2, '0')}'
                    : 'Select end time',
                style: TextStyle(color: _endTime != null ? null : Colors.grey[600]),
              ),
            ),
          ),
        ],

        // Show duration for timed auctions
        if (_auctionType == 'timed' && _startTime != null && _endTime != null)
          Padding(
            padding: const EdgeInsets.only(top: AppConstants.smallPadding),
            child: Text(
              'Duration: ${_endTime!.difference(_startTime!).inHours} hours',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
          ),
      ],
    );
  }

  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main Image
        Text('Main Image *', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
        const SizedBox(height: AppConstants.smallPadding),

        InkWell(
          onTap: _selectMainImage,
          child: Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.border),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: _mainImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    child: Image.file(_mainImage!, fit: BoxFit.cover),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_photo_alternate, size: 48, color: Colors.grey[400]),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text('Tap to add main image', style: TextStyle(color: Colors.grey[600])),
                    ],
                  ),
          ),
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Additional Images
        Text(
          'Additional Images (Optional)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: AppConstants.smallPadding),

        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _additionalImages.length + 1,
            itemBuilder: (context, index) {
              if (index == _additionalImages.length) {
                // Add button
                return InkWell(
                  onTap: _addAdditionalImage,
                  child: Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: AppConstants.smallPadding),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.border),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.add, color: Colors.grey[400]),
                        Text('Add', style: TextStyle(color: Colors.grey[600], fontSize: 12)),
                      ],
                    ),
                  ),
                );
              }

              // Image item
              return Container(
                width: 100,
                margin: const EdgeInsets.only(right: AppConstants.smallPadding),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      child: Image.file(_additionalImages[index], width: 100, height: 100, fit: BoxFit.cover),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: InkWell(
                        onTap: () => _removeAdditionalImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                          child: const Icon(Icons.close, color: Colors.white, size: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // Location methods
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      print('📍 Starting location request from UI...');

      // Use the location service which handles all permission logic
      final location = await _locationService.getCurrentLocation();
      if (location != null) {
        setState(() {
          _currentLocation = location;
          // Update the text field with a descriptive location
          _catchLocationController.text =
              'GPS: ${location.latitude.toStringAsFixed(6)}, ${location.longitude.toStringAsFixed(6)}';
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Location captured successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('📍 Location error in UI: $e');
      if (mounted) {
        String errorMessage = e.toString();
        // Remove "Exception: " prefix if present
        if (errorMessage.startsWith('Exception: ')) {
          errorMessage = errorMessage.substring(11);
        }

        final isPermissionError = errorMessage.toLowerCase().contains('permission');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 8),
            action: isPermissionError
                ? SnackBarAction(
                    label: 'Settings',
                    textColor: Colors.white,
                    onPressed: () async {
                      // Open iOS settings
                      await Geolocator.openAppSettings();
                    },
                  )
                : null,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGettingLocation = false;
        });
      }
    }
  }

  // Date and time selection methods
  Future<void> _selectCatchDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _catchDate ?? DateTime.now().subtract(const Duration(days: 1)),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _catchDate = date;
      });
    }
  }

  Future<void> _selectStartTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startTime ?? DateTime.now().add(const Duration(hours: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_startTime ?? DateTime.now()),
      );
      if (time != null) {
        setState(() {
          _startTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
          // Update end time to be 24 hours later if not set
          if (_endTime == null || _endTime!.isBefore(_startTime!)) {
            _endTime = _startTime!.add(const Duration(hours: 24));
          }
        });
      }
    }
  }

  Future<void> _selectEndTime() async {
    final minDate = _startTime ?? DateTime.now().add(const Duration(hours: 1));
    final date = await showDatePicker(
      context: context,
      initialDate: _endTime ?? minDate.add(const Duration(hours: 24)),
      firstDate: minDate,
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_endTime ?? minDate.add(const Duration(hours: 24))),
      );
      if (time != null) {
        final newEndTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        if (newEndTime.isAfter(minDate)) {
          setState(() {
            _endTime = newEndTime;
          });
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('End time must be after start time')));
        }
      }
    }
  }

  // Image selection methods
  Future<void> _selectMainImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        setState(() {
          _mainImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to select image: ${e.toString()}'), backgroundColor: Colors.red));
      }
    }
  }

  Future<void> _addAdditionalImage() async {
    if (_additionalImages.length >= 5) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Maximum 5 additional images allowed')));
      return;
    }

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        setState(() {
          _additionalImages.add(File(image.path));
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to select image: ${e.toString()}'), backgroundColor: Colors.red));
      }
    }
  }

  void _removeAdditionalImage(int index) {
    setState(() {
      _additionalImages.removeAt(index);
    });
  }

  // Submit auction
  Future<void> _submitAuction({bool isDraft = false}) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCategoryId == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select a fish category')));
      return;
    }

    if (_catchDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select catch date')));
      return;
    }

    if (_currentLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please capture your current location')));
      return;
    }

    if (_startTime == null || _endTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select start and end times')));
      return;
    }

    if (_mainImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select a main image')));
      return;
    }

    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);

    // Determine the correct status and timing based on auction type
    String auctionStatus;
    DateTime? startTime;
    DateTime? endTime;

    if (isDraft) {
      auctionStatus = 'draft';
      startTime = _startTime;
      endTime = _endTime;
    } else {
      final now = DateTime.now();

      switch (_auctionType) {
        case 'live':
          // Live auctions start immediately
          auctionStatus = 'live';
          startTime = now;
          endTime = _endTime;
          break;
        case 'timed':
          // Timed auctions are scheduled if start time is in future
          if (_startTime!.isAfter(now)) {
            auctionStatus = 'scheduled';
          } else {
            auctionStatus = 'live';
          }
          startTime = _startTime;
          endTime = _endTime;
          break;
        case 'buy_now':
          // Buy now items are immediately available
          auctionStatus = 'live';
          startTime = now;
          endTime = null; // No end time for buy now
          break;
        default:
          auctionStatus = 'draft';
          startTime = _startTime;
          endTime = _endTime;
      }
    }

    final auction = await auctionProvider.createAuction(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      fishCategory: _selectedCategoryId!,
      fishType: _fishTypeController.text.trim(),
      weight: double.parse(_weightController.text),
      quantity: int.parse(_quantityController.text),
      catchDate: _catchDate!,
      catchLocation: _catchLocationController.text.trim(),
      auctionType: _auctionType,
      startingPrice: double.parse(_startingPriceController.text),
      reservePrice: _auctionType != 'buy_now' && _reservePriceController.text.isNotEmpty
          ? double.parse(_reservePriceController.text)
          : null,
      targetPrice: _auctionType != 'buy_now' && _targetPriceController.text.isNotEmpty
          ? double.parse(_targetPriceController.text)
          : null,
      buyNowPrice: _buyNowPriceController.text.isNotEmpty ? double.parse(_buyNowPriceController.text) : null,
      bidIncrement: _auctionType != 'buy_now' ? double.parse(_bidIncrementController.text) : 1.0, // Default for buy_now
      startTime: startTime!,
      endTime: endTime,
      mainImage: _mainImage!,
      additionalImages: _additionalImages.isNotEmpty ? _additionalImages : null,
      status: auctionStatus,
      latitude: _currentLocation?.latitude,
      longitude: _currentLocation?.longitude,
      deliveryAddress: _deliveryAddressController.text.trim().isNotEmpty
          ? _deliveryAddressController.text.trim()
          : null,
    );

    if (auction != null && mounted) {
      String successMessage;
      if (isDraft) {
        successMessage = 'Auction saved as draft successfully!';
      } else if (auctionStatus == 'scheduled') {
        successMessage =
            'Auction scheduled successfully! It will start at ${_startTime!.day}/${_startTime!.month}/${_startTime!.year} ${_startTime!.hour}:${_startTime!.minute.toString().padLeft(2, '0')}';
      } else {
        successMessage = 'Auction published live successfully!';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(successMessage),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 4),
        ),
      );
      Navigator.of(context).pop(auction);
    }
  }
}
