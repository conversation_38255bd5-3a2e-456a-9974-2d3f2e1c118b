import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auction_provider.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/auction/auction_card.dart';
import '../../widgets/common/loading_overlay.dart';
import 'auction_detail_screen.dart';

class CategoryAuctionsScreen extends StatefulWidget {
  final AuctionCategory category;

  const CategoryAuctionsScreen({
    super.key,
    required this.category,
  });

  @override
  State<CategoryAuctionsScreen> createState() => _CategoryAuctionsScreenState();
}

class _CategoryAuctionsScreenState extends State<CategoryAuctionsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _liveScrollController = ScrollController();
  final ScrollController _scheduledScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // Load category auctions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCategoryAuctions();
    });

    // Setup scroll listeners for pagination
    _liveScrollController.addListener(_onLiveScroll);
    _scheduledScrollController.addListener(_onScheduledScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _liveScrollController.dispose();
    _scheduledScrollController.dispose();
    super.dispose();
  }

  Future<void> _loadCategoryAuctions() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadAuctions(
      refresh: true,
      category: widget.category.id,
    );
  }

  Future<void> _onRefresh() async {
    await _loadCategoryAuctions();
  }

  void _onLiveScroll() {
    if (_liveScrollController.position.pixels >=
        _liveScrollController.position.maxScrollExtent - 200) {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      if (!auctionProvider.isLoadingMore) {
        auctionProvider.loadMoreAuctions();
      }
    }
  }

  void _onScheduledScroll() {
    if (_scheduledScrollController.position.pixels >=
        _scheduledScrollController.position.maxScrollExtent - 200) {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      if (!auctionProvider.isLoadingMore) {
        auctionProvider.loadMoreAuctions();
      }
    }
  }

  Future<void> _toggleWatchlist(int auctionId) async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final success = await auctionProvider.toggleWatchlist(auctionId);

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(auctionProvider.error ?? 'Failed to update watchlist'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.category.getLocalizedName(
            Localizations.localeOf(context).languageCode,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.accent,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text: localizations.liveAuctions,
              icon: const Icon(Icons.live_tv, size: 20),
            ),
            Tab(
              text: 'Scheduled',
              icon: const Icon(Icons.schedule, size: 20),
            ),
          ],
        ),
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildLiveAuctionsList(auctionProvider, localizations),
              _buildScheduledAuctionsList(auctionProvider, localizations),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLiveAuctionsList(AuctionProvider auctionProvider, AppLocalizations localizations) {
    // Filter live auctions for this category
    final liveAuctions = auctionProvider.auctions
        .where((auction) => 
            auction.category.id == widget.category.id && 
            (auction.isLive || auction.status == 'live'))
        .toList();

    if (auctionProvider.isLoading && liveAuctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (liveAuctions.isEmpty) {
      return _buildEmptyState(
        icon: Icons.live_tv_outlined,
        title: 'No Live Auctions',
        subtitle: 'No live auctions in this category at the moment',
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _liveScrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: liveAuctions.length + (auctionProvider.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= liveAuctions.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(AppConstants.defaultPadding),
                child: LoadingIndicator(),
              ),
            );
          }

          final auction = liveAuctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: AuctionCard(
              auction: auction,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AuctionDetailScreen(auction: auction),
                  ),
                );
              },
              onWatchToggle: () => _toggleWatchlist(auction.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildScheduledAuctionsList(AuctionProvider auctionProvider, AppLocalizations localizations) {
    // Filter scheduled auctions for this category
    final scheduledAuctions = auctionProvider.auctions
        .where((auction) => 
            auction.category.id == widget.category.id && 
            (auction.isScheduled || auction.status == 'scheduled'))
        .toList();

    if (auctionProvider.isLoading && scheduledAuctions.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (scheduledAuctions.isEmpty) {
      return _buildEmptyState(
        icon: Icons.schedule_outlined,
        title: 'No Scheduled Auctions',
        subtitle: 'No scheduled auctions in this category',
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scheduledScrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: scheduledAuctions.length + (auctionProvider.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= scheduledAuctions.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(AppConstants.defaultPadding),
                child: LoadingIndicator(),
              ),
            );
          }

          final auction = scheduledAuctions[index];
          return Container(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: AuctionCard(
              auction: auction,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AuctionDetailScreen(auction: auction),
                  ),
                );
              },
              onWatchToggle: () => _toggleWatchlist(auction.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.textHint),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textHint,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
