import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auction_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/loading_overlay.dart';
import 'auction_detail_screen.dart';
import '../../l10n/generated/app_localizations.dart';

class MyAuctionsScreen extends StatefulWidget {
  const MyAuctionsScreen({super.key});

  @override
  State<MyAuctionsScreen> createState() => _MyAuctionsScreenState();
}

class _MyAuctionsScreenState extends State<MyAuctionsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadMyAuctions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMyAuctions() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadMyParticipatedAuctions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Auctions'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All Participated'),
            Tab(text: 'Auto Bidding'),
            Tab(text: 'Manual Bids'),
            Tab(text: 'Won Auctions'),
          ],
        ),
      ),
      body: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, child) {
          return LoadingOverlay(
            isLoading: auctionProvider.isLoading,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllParticipatedTab(auctionProvider),
                _buildAutoBiddingTab(auctionProvider),
                _buildManualBidsTab(auctionProvider),
                _buildWonAuctionsTab(auctionProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAllParticipatedTab(AuctionProvider auctionProvider) {
    final auctions = auctionProvider.participatedAuctions;

    if (auctions.isEmpty) {
      return _buildEmptyState(
        'No Participated Auctions',
        'You haven\'t participated in any auctions yet',
        Icons.gavel_outlined,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: auctions.length,
      itemBuilder: (context, index) {
        final auction = auctions[index];
        return _buildAuctionCard(auction, showBidStatus: true);
      },
    );
  }

  Widget _buildAutoBiddingTab(AuctionProvider auctionProvider) {
    final auctions = auctionProvider.participatedAuctions.where((auction) => auction.hasAutoBid).toList();

    if (auctions.isEmpty) {
      return _buildEmptyState('No Auto Bidding', 'You don\'t have any active auto bids', Icons.auto_mode);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: auctions.length,
      itemBuilder: (context, index) {
        final auction = auctions[index];
        return _buildAuctionCard(auction, showAutoBidDetails: true);
      },
    );
  }

  Widget _buildManualBidsTab(AuctionProvider auctionProvider) {
    final auctions = auctionProvider.participatedAuctions
        .where((auction) => auction.hasManualBids && !auction.hasAutoBid)
        .toList();

    if (auctions.isEmpty) {
      return _buildEmptyState('No Manual Bids', 'You don\'t have any manual bids', Icons.touch_app);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: auctions.length,
      itemBuilder: (context, index) {
        final auction = auctions[index];
        return _buildAuctionCard(auction, showBidHistory: true);
      },
    );
  }

  Widget _buildWonAuctionsTab(AuctionProvider auctionProvider) {
    final auctions = auctionProvider.participatedAuctions.where((auction) => auction.isWon).toList();

    if (auctions.isEmpty) {
      return _buildEmptyState('No Won Auctions', 'You haven\'t won any auctions yet', Icons.emoji_events);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: auctions.length,
      itemBuilder: (context, index) {
        final auction = auctions[index];
        return _buildAuctionCard(auction, showWinDetails: true);
      },
    );
  }

  Widget _buildAuctionCard(
    Auction auction, {
    bool showBidStatus = false,
    bool showAutoBidDetails = false,
    bool showBidHistory = false,
    bool showWinDetails = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _navigateToAuctionDetail(auction),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      auction.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                  _buildStatusBadge(auction),
                ],
              ),

              const SizedBox(height: 8),

              // Auction details
              Row(
                children: [
                  Text(
                    'Current: \$${auction.currentPrice.toStringAsFixed(2)}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '${auction.totalBids} bids',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
                  ),
                ],
              ),

              if (showBidStatus) ...[const SizedBox(height: 8), _buildBidStatusRow(auction)],

              if (showAutoBidDetails) ...[const SizedBox(height: 8), _buildAutoBidDetails(auction)],

              if (showBidHistory) ...[const SizedBox(height: 8), _buildBidHistoryPreview(auction)],

              if (showWinDetails) ...[const SizedBox(height: 8), _buildWinDetails(auction)],

              const SizedBox(height: 8),

              // Time remaining
              if (auction.isLive && auction.hasTimeRemaining)
                Text(
                  'Ends in ${_formatTimeRemaining(auction.timeRemaining)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: auction.timeRemaining.inHours < 1 ? AppColors.error : AppColors.warning,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(Auction auction) {
    Color color;
    String text;

    if (auction.isWon) {
      color = AppColors.success;
      text = 'WON';
    } else if (auction.isLive) {
      color = AppColors.liveAuction;
      text = 'LIVE';
    } else if (auction.isEnded) {
      color = AppColors.endedAuction;
      text = 'ENDED';
    } else {
      color = AppColors.textSecondary;
      text = auction.status.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(12)),
      child: Text(
        text,
        style: const TextStyle(color: AppColors.textLight, fontSize: 10, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildBidStatusRow(Auction auction) {
    return Row(
      children: [
        if (auction.hasAutoBid) ...[
          Icon(Icons.auto_mode, size: 16, color: AppColors.warning),
          const SizedBox(width: 4),
          Text(
            'Auto Bid Active',
            style: TextStyle(color: AppColors.warning, fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ],
        if (auction.hasManualBids) ...[
          if (auction.hasAutoBid) const SizedBox(width: 12),
          Icon(Icons.touch_app, size: 16, color: AppColors.info),
          const SizedBox(width: 4),
          Text('${auction.myBidsCount} manual bids', style: TextStyle(color: AppColors.info, fontSize: 12)),
        ],
      ],
    );
  }

  Widget _buildAutoBidDetails(Auction auction) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.warning.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.auto_mode, color: AppColors.warning, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Max: \$${auction.autoBidMaxAmount?.toStringAsFixed(2) ?? '0.00'}',
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                ),
                Text(
                  'Remaining: \$${auction.autoBidRemainingAmount?.toStringAsFixed(2) ?? '0.00'}',
                  style: TextStyle(color: AppColors.textSecondary, fontSize: 11),
                ),
              ],
            ),
          ),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: auction.isAutoBidActive ? AppColors.success : AppColors.error,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBidHistoryPreview(Auction auction) {
    return Text(
      'Your highest bid: \$${auction.myHighestBid?.toStringAsFixed(2) ?? '0.00'}',
      style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
    );
  }

  Widget _buildWinDetails(Auction auction) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.success.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.emoji_events, color: AppColors.success, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Won for \$${auction.currentPrice.toStringAsFixed(2)}',
              style: TextStyle(color: AppColors.success, fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.textHint),
          const SizedBox(height: 16),
          Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary)),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textHint),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatTimeRemaining(Duration duration) {
    // Handle expired auctions
    if (duration.inSeconds <= 0) {
      return 'Expired';
    }

    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  void _navigateToAuctionDetail(Auction auction) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
  }
}
