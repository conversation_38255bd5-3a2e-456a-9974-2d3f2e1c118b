import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/auction/payment_timeout_widget.dart';

class AuctionWinDetailsScreen extends StatefulWidget {
  final Auction auction;

  const AuctionWinDetailsScreen({super.key, required this.auction});

  @override
  State<AuctionWinDetailsScreen> createState() => _AuctionWinDetailsScreenState();
}

class _AuctionWinDetailsScreenState extends State<AuctionWinDetailsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Auction Won'),
        backgroundColor: AppColors.success,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              // Share auction win
              _shareAuctionWin();
            },
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Congratulations Header
              _buildCongratulationsHeader(),

              const SizedBox(height: AppConstants.largePadding),

              // Auction Details Card
              _buildAuctionDetailsCard(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Payment Timeout Widget (if payment not received)
              if (!widget.auction.payment_received)
                PaymentTimeoutWidget(
                  auction: widget.auction,
                  onPayNow: _proceedToPayment,
                  onTimeExpired: () {
                    setState(() {
                      // Refresh the screen when payment expires
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Payment deadline has expired. The auction may be reassigned.'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  },
                ),

              // Payment Information Card
              _buildPaymentInformationCard(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Seller Information Card
              _buildSellerInformationCard(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Delivery Information Card
              if (widget.auction.payment_received) _buildDeliveryInformationCard(),

              const SizedBox(height: AppConstants.largePadding),

              // Action Buttons
              _buildActionButtons(),

              const SizedBox(height: AppConstants.largePadding),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCongratulationsHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.success, Color(0xFF4CAF50)],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.success.withOpacity(0.3), blurRadius: 8, offset: const Offset(0, 4))],
      ),
      child: Column(
        children: [
          const Icon(Icons.emoji_events, size: 64, color: Colors.white),
          const SizedBox(height: AppConstants.defaultPadding),
          const Text(
            '🎉 Congratulations!',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You won the auction for "${widget.auction.title}"',
            style: const TextStyle(fontSize: 16, color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAuctionDetailsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.gavel, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Auction Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Auction Image
            if (widget.auction.images.isNotEmpty)
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  image: DecorationImage(image: NetworkImage(widget.auction.primaryImage), fit: BoxFit.cover),
                ),
              ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Auction Info
            _buildInfoRow('Item', widget.auction.title),
            _buildInfoRow('Fish Type', widget.auction.fishType),
            _buildInfoRow('Quantity', widget.auction.formattedQuantity),
            _buildInfoRow('Quality Grade', widget.auction.qualityGrade),
            if (widget.auction.location != null) _buildInfoRow('Catch Location', widget.auction.location!),

            const Divider(),

            // Winning Bid Info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Your Winning Bid:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Text(
                  '\$${widget.auction.currentPrice.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppColors.success),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInformationCard() {
    final timeRemaining = _getPaymentTimeRemaining();
    final isPaymentOverdue = timeRemaining.isNegative;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  widget.auction.payment_received ? Icons.check_circle : Icons.payment,
                  color: widget.auction.payment_received ? AppColors.success : AppColors.warning,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  widget.auction.payment_received ? 'Payment Completed' : 'Payment Required',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            if (!widget.auction.payment_received) ...[
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: isPaymentOverdue ? AppColors.error.withOpacity(0.1) : AppColors.warning.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: isPaymentOverdue ? AppColors.error : AppColors.warning),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          isPaymentOverdue ? Icons.warning : Icons.schedule,
                          color: isPaymentOverdue ? AppColors.error : AppColors.warning,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Text(
                          isPaymentOverdue ? 'Payment Overdue!' : 'Payment Deadline',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isPaymentOverdue ? AppColors.error : AppColors.warning,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      isPaymentOverdue
                          ? 'Your payment deadline has passed. The auction may be reassigned to the next bidder.'
                          : 'You have ${_formatDuration(timeRemaining)} remaining to complete payment.',
                      style: TextStyle(color: isPaymentOverdue ? AppColors.error : AppColors.warning),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.defaultPadding),
            ],

            _buildInfoRow('Amount Due', '\$${widget.auction.currentPrice.toStringAsFixed(2)}'),
            if (widget.auction.payment_received)
              _buildInfoRow('Payment Status', 'Completed ✓')
            else
              _buildInfoRow('Payment Status', 'Pending'),
          ],
        ),
      ),
    );
  }

  Widget _buildSellerInformationCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Seller Information', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    widget.auction.seller.displayName.substring(0, 1).toUpperCase(),
                    style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.auction.seller.displayName,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Row(
                        children: [
                          Icon(
                            widget.auction.seller.isVerified ? Icons.verified : Icons.info,
                            size: 16,
                            color: widget.auction.seller.isVerified ? AppColors.success : AppColors.warning,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            widget.auction.seller.isVerified ? 'Verified Seller' : 'Unverified',
                            style: TextStyle(
                              fontSize: 12,
                              color: widget.auction.seller.isVerified ? AppColors.success : AppColors.warning,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _contactSeller(),
                  icon: const Icon(Icons.message),
                  tooltip: 'Contact Seller',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryInformationCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.local_shipping, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Delivery Information', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            _buildInfoRow('Delivery Status', 'Preparing for shipment'),
            _buildInfoRow('Estimated Delivery', '2-3 business days'),

            const SizedBox(height: AppConstants.defaultPadding),

            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _trackDelivery(),
                    icon: const Icon(Icons.track_changes),
                    label: const Text('Track Delivery'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _viewSellerLocation(),
                    icon: const Icon(Icons.location_on),
                    label: const Text('Seller Location'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    if (widget.auction.payment_received) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _trackDelivery(),
              icon: const Icon(Icons.track_changes),
              label: const Text('Track Your Delivery'),
              style: ElevatedButton.styleFrom(padding: const EdgeInsets.all(AppConstants.defaultPadding)),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _viewSellerLocation(),
              icon: const Icon(Icons.location_on),
              label: const Text('View Seller Location'),
              style: OutlinedButton.styleFrom(padding: const EdgeInsets.all(AppConstants.defaultPadding)),
            ),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _getPaymentTimeRemaining().isNegative ? null : () => _proceedToPayment(),
              icon: const Icon(Icons.payment),
              label: Text(_getPaymentTimeRemaining().isNegative ? 'Payment Deadline Passed' : 'Pay Now'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getPaymentTimeRemaining().isNegative ? Colors.grey : AppColors.success,
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _contactSeller(),
              icon: const Icon(Icons.message),
              label: const Text('Contact Seller'),
              style: OutlinedButton.styleFrom(padding: const EdgeInsets.all(AppConstants.defaultPadding)),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500, color: AppColors.textSecondary),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
          ),
        ],
      ),
    );
  }

  Duration _getPaymentTimeRemaining() {
    if (widget.auction.payment_deadline == null) {
      return Duration.zero;
    }
    return widget.auction.payment_deadline!.difference(DateTime.now());
  }

  String _formatDuration(Duration duration) {
    if (duration.isNegative) return 'Expired';

    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  void _proceedToPayment() {
    Navigator.of(context).pushNamed('/payment', arguments: widget.auction);
  }

  void _trackDelivery() {
    // Navigate to the unified earned auction screen for delivery tracking
    Navigator.of(context).pushReplacementNamed('/main');
  }

  void _viewSellerLocation() {
    Navigator.of(context).pushNamed('/seller-location', arguments: widget.auction);
  }

  void _contactSeller() {
    // TODO: Implement contact seller functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact seller functionality will be implemented soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _shareAuctionWin() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality will be implemented soon!'), backgroundColor: AppColors.info),
    );
  }
}
