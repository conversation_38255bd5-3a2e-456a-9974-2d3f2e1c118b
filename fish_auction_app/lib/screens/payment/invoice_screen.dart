import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/payment_service.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../l10n/generated/app_localizations.dart';

class InvoiceScreen extends StatefulWidget {
  final Auction auction;
  final String paymentId;

  const InvoiceScreen({super.key, required this.auction, required this.paymentId});

  @override
  State<InvoiceScreen> createState() => _InvoiceScreenState();
}

class _InvoiceScreenState extends State<InvoiceScreen> {
  final PaymentService _paymentService = PaymentService();
  bool _isLoading = false;
  Map<String, dynamic>? _invoiceData;
  String? _error;
  String _selectedLanguage = 'en'; // Default to English

  @override
  void initState() {
    super.initState();
    _loadInvoiceData();
  }

  Future<void> _loadInvoiceData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _paymentService.getInvoice(widget.paymentId, language: _selectedLanguage);

      if (response.isSuccess && response.data != null) {
        setState(() {
          _invoiceData = response.data;
        });
      } else {
        setState(() {
          _error = response.message ?? 'Failed to load invoice';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading invoice: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadPDF() async {
    setState(() => _isLoading = true);

    try {
      final response = await _paymentService.downloadInvoicePDF(widget.paymentId, language: _selectedLanguage);

      if (response.isSuccess && response.data != null) {
        final pdfUrl = response.data!;

        // Launch PDF URL
        final uri = Uri.parse(pdfUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          _showErrorSnackBar('Could not open PDF');
        }
      } else {
        _showErrorSnackBar(response.message ?? 'Failed to download PDF');
      }
    } catch (e) {
      _showErrorSnackBar('Error downloading PDF: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.error));
    }
  }

  String _getLocalizedLabel(String key) {
    if (_invoiceData != null && _invoiceData!['localized_labels'] != null) {
      final labels = _invoiceData!['localized_labels'] as Map<String, dynamic>;
      return labels[key]?.toString() ?? key;
    }
    return key;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(_getLocalizedLabel('invoice')),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // Language selector
          PopupMenuButton<String>(
            icon: const Icon(Icons.language),
            onSelected: (String language) {
              setState(() {
                _selectedLanguage = language;
              });
              _loadInvoiceData(); // Reload with new language
            },
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                value: 'en',
                child: Row(
                  children: [
                    Icon(Icons.check, color: _selectedLanguage == 'en' ? AppColors.primary : Colors.transparent),
                    const SizedBox(width: 8),
                    const Text('English'),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'ar',
                child: Row(
                  children: [
                    Icon(Icons.check, color: _selectedLanguage == 'ar' ? AppColors.primary : Colors.transparent),
                    const SizedBox(width: 8),
                    const Text('العربية'),
                  ],
                ),
              ),
            ],
          ),
          if (_invoiceData != null)
            IconButton(onPressed: _downloadPDF, icon: const Icon(Icons.download), tooltip: 'Download PDF'),
        ],
      ),
      body: LoadingOverlay(isLoading: _isLoading, child: _buildBody(localizations)),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    if (_error != null) {
      return _buildErrorState();
    }

    if (_invoiceData == null) {
      return const Center(child: LoadingIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInvoiceHeader(),
          const SizedBox(height: AppConstants.largePadding),
          _buildInvoiceDetails(),
          const SizedBox(height: AppConstants.largePadding),
          _buildBillingInfo(),
          const SizedBox(height: AppConstants.largePadding),
          _buildAuctionDetails(),
          const SizedBox(height: AppConstants.largePadding),
          _buildPaymentSummary(),
          const SizedBox(height: AppConstants.largePadding),
          _buildDownloadButton(),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to Load Invoice',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.error, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _error ?? 'Unknown error occurred',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: _loadInvoiceData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary, foregroundColor: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.receipt_long, size: 32, color: AppColors.primary),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'INVOICE',
                  style: Theme.of(
                    context,
                  ).textTheme.headlineSmall?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
                ),
                Text(
                  'Invoice #${_invoiceData!['invoice_number']?.toString() ?? 'N/A'}',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.success,
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Text(
              (_invoiceData!['localized_status']?.toString() ?? _invoiceData!['status']?.toString() ?? 'UNKNOWN')
                  .toUpperCase(),
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceDetails() {
    final issueDateStr = _invoiceData!['issue_date']?.toString();
    final dueDateStr = _invoiceData!['due_date']?.toString();
    final paidDateStr = _invoiceData!['paid_date']?.toString();

    return _buildSection(
      title: _getLocalizedLabel('invoice_details'),
      child: Column(
        children: [
          if (issueDateStr != null)
            _buildDetailRow(_getLocalizedLabel('issue_date'), _formatDate(DateTime.parse(issueDateStr))),
          if (dueDateStr != null)
            _buildDetailRow(_getLocalizedLabel('due_date'), _formatDate(DateTime.parse(dueDateStr))),
          if (paidDateStr != null)
            _buildDetailRow(_getLocalizedLabel('paid_date'), _formatDate(DateTime.parse(paidDateStr))),
        ],
      ),
    );
  }

  Widget _buildBillingInfo() {
    return _buildSection(
      title: _getLocalizedLabel('billing_information'),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getLocalizedLabel('bill_to'),
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
                ),
                const SizedBox(height: 8),
                Text(_invoiceData!['buyer_name']?.toString() ?? 'N/A'),
                Text(_invoiceData!['buyer_email']?.toString() ?? 'N/A'),
                if (_invoiceData!['buyer_address']?.toString().isNotEmpty == true)
                  Text(_invoiceData!['buyer_address'].toString()),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getLocalizedLabel('bill_from'),
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
                ),
                const SizedBox(height: 8),
                Text(_invoiceData!['seller_name']?.toString() ?? 'N/A'),
                Text(_invoiceData!['seller_email']?.toString() ?? 'N/A'),
                if (_invoiceData!['seller_address']?.toString().isNotEmpty == true)
                  Text(_invoiceData!['seller_address'].toString()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuctionDetails() {
    return _buildSection(
      title: _getLocalizedLabel('auction_details'),
      child: Column(
        children: [
          _buildDetailRow(_getLocalizedLabel('auction_title'), widget.auction.title),
          _buildDetailRow(_getLocalizedLabel('fish_type'), widget.auction.fishType),
          _buildDetailRow(_getLocalizedLabel('final_price'), '\$${widget.auction.currentPrice.toStringAsFixed(2)}'),
        ],
      ),
    );
  }

  Widget _buildPaymentSummary() {
    final subtotal = double.tryParse(_invoiceData!['subtotal']?.toString() ?? '0') ?? 0.0;
    final taxAmount = double.tryParse(_invoiceData!['tax_amount']?.toString() ?? '0') ?? 0.0;
    final totalAmount = double.tryParse(_invoiceData!['total_amount']?.toString() ?? '0') ?? 0.0;

    return _buildSection(
      title: _getLocalizedLabel('payment_summary'),
      child: Column(
        children: [
          _buildDetailRow(_getLocalizedLabel('subtotal'), '\$${subtotal.toStringAsFixed(2)}'),
          if (taxAmount > 0) _buildDetailRow(_getLocalizedLabel('tax'), '\$${taxAmount.toStringAsFixed(2)}'),
          const Divider(),
          _buildDetailRow(_getLocalizedLabel('total_amount'), '\$${totalAmount.toStringAsFixed(2)}', isTotal: true),
        ],
      ),
    );
  }

  Widget _buildDownloadButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _downloadPDF,
        icon: const Icon(Icons.download),
        label: const Text('Download PDF Receipt'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          child,
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
              color: isTotal ? AppColors.primary : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
