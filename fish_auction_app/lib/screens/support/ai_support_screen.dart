import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';

class AISupportScreen extends StatefulWidget {
  const AISupportScreen({super.key});

  @override
  State<AISupportScreen> createState() => _AISupportScreenState();
}

class _AISupportScreenState extends State<AISupportScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;

  @override
  void initState() {
    super.initState();
    _addWelcomeMessage();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _addWelcomeMessage() {
    _messages.add(
      ChatMessage(
        text: 'Hello! I\'m your AI assistant for Fish Auction. How can I help you today?',
        isUser: false,
        timestamp: DateTime.now(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(color: AppColors.primary, shape: BoxShape.circle),
              child: const Icon(Icons.smart_toy, color: Colors.white, size: 20),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('AI Support', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Text(
                  'Online',
                  style: TextStyle(fontSize: 12, color: AppColors.success, fontWeight: FontWeight.normal),
                ),
              ],
            ),
          ],
        ),
        actions: [IconButton(onPressed: _showQuickActions, icon: const Icon(Icons.more_vert))],
      ),
      body: Column(
        children: [
          // Quick Help Section
          _buildQuickHelp(),

          // Messages
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              itemCount: _messages.length + (_isTyping ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _messages.length && _isTyping) {
                  return _buildTypingIndicator();
                }
                return _buildMessageBubble(_messages[index]);
              },
            ),
          ),

          // Input Area
          _buildInputArea(),
        ],
      ),
    );
  }

  Widget _buildQuickHelp() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background,
        border: Border(bottom: BorderSide(color: AppColors.border)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Quick Help', style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
          const SizedBox(height: AppConstants.smallPadding),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickHelpChip('How to bid?', Icons.gavel),
              _buildQuickHelpChip('Payment methods', Icons.payment),
              _buildQuickHelpChip('KYC verification', Icons.verified_user),
              _buildQuickHelpChip('Delivery info', Icons.local_shipping),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickHelpChip(String text, IconData icon) {
    return InkWell(
      onTap: () => _sendQuickMessage(text),
      child: Chip(
        avatar: Icon(icon, size: 16),
        label: Text(text, style: const TextStyle(fontSize: 12)),
        backgroundColor: AppColors.primary.withOpacity(0.1),
        side: BorderSide(color: AppColors.primary.withOpacity(0.3)),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Row(
        mainAxisAlignment: message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(color: AppColors.primary, shape: BoxShape.circle),
              child: const Icon(Icons.smart_toy, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message.isUser ? AppColors.primary : AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                border: message.isUser ? null : Border.all(color: AppColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(message.text, style: TextStyle(color: message.isUser ? Colors.white : AppColors.textPrimary)),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(fontSize: 10, color: message.isUser ? Colors.white70 : AppColors.textSecondary),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return CircleAvatar(
                  radius: 16,
                  backgroundColor: AppColors.primary.withOpacity(0.1),
                  child: authProvider.user?.profileImage != null
                      ? ClipOval(
                          child: Image.network(
                            authProvider.user!.profileImage!,
                            width: 32,
                            height: 32,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(Icons.person, size: 16, color: AppColors.primary),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: const BoxDecoration(color: AppColors.primary, shape: BoxShape.circle),
            child: const Icon(Icons.smart_toy, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: AppColors.border),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypingDot(0),
                const SizedBox(width: 4),
                _buildTypingDot(1),
                const SizedBox(width: 4),
                _buildTypingDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 600 + (index * 200)),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.5 + (value * 0.5),
          child: Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(color: AppColors.textSecondary.withOpacity(value), shape: BoxShape.circle),
          ),
        );
      },
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(top: BorderSide(color: AppColors.border)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type your message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(color: AppColors.border),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          FloatingActionButton.small(onPressed: _sendMessage, child: const Icon(Icons.send)),
        ],
      ),
    );
  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(text: text, isUser: true, timestamp: DateTime.now()));
      _messageController.clear();
      _isTyping = true;
    });

    _scrollToBottom();
    _simulateAIResponse(text);
  }

  void _sendQuickMessage(String text) {
    _messageController.text = text;
    _sendMessage();
  }

  void _simulateAIResponse(String userMessage) {
    // Simulate AI thinking time
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isTyping = false;
        _messages.add(ChatMessage(text: _generateAIResponse(userMessage), isUser: false, timestamp: DateTime.now()));
      });
      _scrollToBottom();
    });
  }

  String _generateAIResponse(String userMessage) {
    final message = userMessage.toLowerCase();

    if (message.contains('bid') || message.contains('how to bid')) {
      return 'To place a bid:\n1. Find an auction you\'re interested in\n2. Tap on the auction to view details\n3. Click "Place Bid" button\n4. Enter your bid amount\n5. Confirm your bid\n\nRemember: Your bid must be higher than the current highest bid!';
    } else if (message.contains('payment')) {
      return 'We accept multiple payment methods:\n• Credit/Debit Cards\n• Bank Transfer\n• Digital Wallets\n• Cryptocurrency\n\nAll payments are secure and processed instantly. You can add funds to your wallet from the Profile section.';
    } else if (message.contains('kyc') || message.contains('verification')) {
      return 'KYC verification is required to participate in auctions:\n1. Go to Profile → KYC Verification\n2. Upload a government-issued ID\n3. Upload proof of address\n4. Wait for approval (24-48 hours)\n\nThis ensures a safe trading environment for everyone!';
    } else if (message.contains('delivery')) {
      return 'Delivery information:\n• Free delivery for orders over \$50\n• Standard delivery: 2-3 business days\n• Express delivery: Next day\n• Live tracking available\n• Temperature-controlled transport for fresh fish\n\nYou\'ll receive tracking details once your order ships!';
    } else {
      return 'Thank you for your question! I\'m here to help with:\n• Bidding process\n• Payment methods\n• Account verification\n• Delivery information\n• General platform questions\n\nCould you please be more specific about what you need help with?';
    }
  }

  String _formatTime(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('Clear Chat'),
              onTap: () {
                Navigator.pop(context);
                _clearChat();
              },
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('FAQ'),
              onTap: () {
                Navigator.pop(context);
                _showFAQ();
              },
            ),
            ListTile(
              leading: const Icon(Icons.contact_support),
              title: const Text('Contact Human Support'),
              onTap: () {
                Navigator.pop(context);
                _contactHumanSupport();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _clearChat() {
    setState(() {
      _messages.clear();
      _addWelcomeMessage();
    });
  }

  void _showFAQ() {
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('FAQ screen coming soon!')));
  }

  void _contactHumanSupport() {
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Human support contact coming soon!')));
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({required this.text, required this.isUser, required this.timestamp});
}
