import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/delivery_model.dart';
import '../../services/delivery_service.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../l10n/generated/app_localizations.dart';
import 'delivery_detail_screen.dart';

class SellerDeliveryScreen extends StatefulWidget {
  const SellerDeliveryScreen({super.key});

  @override
  State<SellerDeliveryScreen> createState() => _SellerDeliveryScreenState();
}

class _SellerDeliveryScreenState extends State<SellerDeliveryScreen> with SingleTickerProviderStateMixin {
  final DeliveryService _deliveryService = DeliveryService();
  late TabController _tabController;

  List<Delivery> _allDeliveries = [];
  List<Delivery> _pendingDeliveries = [];
  List<Delivery> _inProgressDeliveries = [];
  List<Delivery> _completedDeliveries = [];

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDeliveries();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDeliveries() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _deliveryService.getSellerDeliveries();

      if (response.isSuccess && response.data != null) {
        setState(() {
          _allDeliveries = response.data!;
          _categorizeDeliveries();
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? 'Failed to load deliveries';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading deliveries: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _categorizeDeliveries() {
    _pendingDeliveries = _allDeliveries.where((d) => d.isPending).toList();
    _inProgressDeliveries = _allDeliveries.where((d) => d.isPickedUp || d.isInTransit || d.isOutForDelivery).toList();
    _completedDeliveries = _allDeliveries.where((d) => d.isDelivered || d.isFailed || d.isCancelled).toList();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Management'),
        actions: [IconButton(onPressed: _loadDeliveries, icon: const Icon(Icons.refresh), tooltip: 'Refresh')],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(text: 'All (${_allDeliveries.length})'),
            Tab(text: 'Pending (${_pendingDeliveries.length})'),
            Tab(text: 'In Progress (${_inProgressDeliveries.length})'),
            Tab(text: 'Completed (${_completedDeliveries.length})'),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: _errorMessage != null
            ? _buildErrorState()
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildDeliveryList(_allDeliveries),
                  _buildDeliveryList(_pendingDeliveries),
                  _buildDeliveryList(_inProgressDeliveries),
                  _buildDeliveryList(_completedDeliveries),
                ],
              ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _errorMessage!,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.error),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(onPressed: _loadDeliveries, child: const Text('Retry')),
        ],
      ),
    );
  }

  Widget _buildDeliveryList(List<Delivery> deliveries) {
    if (deliveries.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadDeliveries,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: deliveries.length,
        itemBuilder: (context, index) {
          final delivery = deliveries[index];
          return _buildDeliveryCard(delivery);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.local_shipping_outlined, size: 64, color: AppColors.textHint),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No deliveries found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textHint),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Deliveries will appear here when buyers complete payment',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryCard(Delivery delivery) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _navigateToDeliveryDetail(delivery),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with auction title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      delivery.auction.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                  _buildStatusBadge(delivery),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // Buyer info
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    'Buyer: ${delivery.buyer.username}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // Tracking number
              Row(
                children: [
                  Icon(Icons.confirmation_number, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    'Tracking: ${delivery.trackingNumber}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // Delivery address
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.location_on, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      delivery.deliveryAddress,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Step-by-step delivery status buttons
                  if (delivery.isPending) ...[
                    TextButton.icon(
                      onPressed: () => _updateDeliveryStatus(delivery, 'picked_up'),
                      icon: const Icon(Icons.local_shipping, size: 16),
                      label: const Text('Mark Picked Up'),
                    ),
                  ] else if (delivery.isPickedUp) ...[
                    TextButton.icon(
                      onPressed: () => _updateDeliveryStatus(delivery, 'in_transit'),
                      icon: const Icon(Icons.directions_car, size: 16),
                      label: const Text('In Transit'),
                    ),
                  ] else if (delivery.isInTransit) ...[
                    TextButton.icon(
                      onPressed: () => _updateDeliveryStatus(delivery, 'out_for_delivery'),
                      icon: const Icon(Icons.delivery_dining, size: 16),
                      label: const Text('Out for Delivery'),
                    ),
                  ] else if (delivery.isOutForDelivery) ...[
                    TextButton.icon(
                      onPressed: () => _updateDeliveryStatus(delivery, 'delivered'),
                      icon: const Icon(Icons.check_circle, size: 16),
                      label: const Text('Mark Delivered'),
                    ),
                  ],

                  // Direct completion button (only show if not already delivered)
                  if (!delivery.isDelivered && !delivery.isFailed && !delivery.isCancelled) ...[
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () => _showCompleteDeliveryDialog(delivery),
                      icon: const Icon(Icons.done_all, size: 16),
                      label: const Text('Complete Delivery'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],

                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _navigateToDeliveryDetail(delivery),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('View Details'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(Delivery delivery) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: delivery.statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: delivery.statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(delivery.statusIcon, size: 12, color: delivery.statusColor),
          const SizedBox(width: 4),
          Text(
            delivery.statusDisplayName,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: delivery.statusColor, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Future<void> _updateDeliveryStatus(Delivery delivery, String newStatus) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Delivery Status'),
        content: Text('Mark this delivery as ${_getStatusDisplayName(newStatus)}?'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
          ElevatedButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('Confirm')),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      final response = await _deliveryService.updateSellerDeliveryStatus(
        deliveryId: delivery.id,
        status: newStatus,
        message: 'Status updated by seller',
      );

      if (mounted) {
        if (response.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Delivery status updated to ${_getStatusDisplayName(newStatus)}'),
              backgroundColor: AppColors.success,
            ),
          );
          _loadDeliveries(); // Refresh the list
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? 'Failed to update delivery status'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating delivery status: ${e.toString()}'), backgroundColor: AppColors.error),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'picked_up':
        return 'Picked Up';
      case 'in_transit':
        return 'In Transit';
      case 'out_for_delivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      default:
        return status;
    }
  }

  void _showCompleteDeliveryDialog(Delivery delivery) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Delivery'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 48),
            const SizedBox(height: 16),
            const Text('Are you sure you want to mark this delivery as completed?', textAlign: TextAlign.center),
            const SizedBox(height: 8),
            const Text(
              'This will skip all intermediate delivery steps and directly mark the delivery as delivered.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _updateDeliveryStatus(delivery, 'delivered');
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
            child: const Text('Complete Delivery'),
          ),
        ],
      ),
    );
  }

  void _navigateToDeliveryDetail(Delivery delivery) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => DeliveryDetailScreen(delivery: delivery)));
  }
}
