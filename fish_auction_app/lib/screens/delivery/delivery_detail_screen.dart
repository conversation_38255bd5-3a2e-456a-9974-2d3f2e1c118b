import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/delivery_model.dart';
import '../../services/delivery_service.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../l10n/generated/app_localizations.dart';

class DeliveryDetailScreen extends StatefulWidget {
  final Delivery delivery;

  const DeliveryDetailScreen({super.key, required this.delivery});

  @override
  State<DeliveryDetailScreen> createState() => _DeliveryDetailScreenState();
}

class _DeliveryDetailScreenState extends State<DeliveryDetailScreen> {
  final DeliveryService _deliveryService = DeliveryService();
  late Delivery _delivery;
  List<DeliveryUpdate> _updates = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _delivery = widget.delivery;
    _loadDeliveryUpdates();
  }

  Future<void> _loadDeliveryUpdates() async {
    setState(() => _isLoading = true);

    try {
      final response = await _deliveryService.getDeliveryUpdates(_delivery.id);

      if (response.isSuccess && response.data != null) {
        setState(() {
          _updates = response.data!;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading delivery updates: ${e.toString()}'), backgroundColor: AppColors.error),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Details'),
        actions: [IconButton(onPressed: _loadDeliveryUpdates, icon: const Icon(Icons.refresh), tooltip: 'Refresh')],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDeliveryHeader(),
              const SizedBox(height: AppConstants.largePadding),
              _buildAuctionInfo(),
              const SizedBox(height: AppConstants.largePadding),
              _buildDeliveryInfo(),
              const SizedBox(height: AppConstants.largePadding),
              _buildDeliveryUpdates(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeliveryHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_delivery.statusIcon, color: _delivery.statusColor, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _delivery.statusDisplayName,
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(color: _delivery.statusColor, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Tracking Number: ${_delivery.trackingNumber}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuctionInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Auction Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('Title', _delivery.auction.title),
            _buildInfoRow('Fish Type', _delivery.auction.fishType),
            _buildInfoRow('Quantity', '${_delivery.auction.quantity} ${_delivery.auction.unit}'),
            _buildInfoRow('Final Price', '\$${_delivery.auction.currentPrice.toStringAsFixed(2)}'),
            _buildInfoRow('Buyer', _delivery.buyer.username),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Delivery Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('Pickup Address', _delivery.pickupAddress),
            _buildInfoRow('Delivery Address', _delivery.deliveryAddress),
            if (_delivery.estimatedPickupTime != null)
              _buildInfoRow('Estimated Pickup', _formatDateTime(_delivery.estimatedPickupTime!)),
            if (_delivery.estimatedDeliveryTime != null)
              _buildInfoRow('Estimated Delivery', _formatDateTime(_delivery.estimatedDeliveryTime!)),
            if (_delivery.pickedUpAt != null) _buildInfoRow('Picked Up At', _formatDateTime(_delivery.pickedUpAt!)),
            if (_delivery.deliveredAt != null) _buildInfoRow('Delivered At', _formatDateTime(_delivery.deliveredAt!)),
            if (_delivery.deliveryCost > 0)
              _buildInfoRow('Delivery Cost', '\$${_delivery.deliveryCost.toStringAsFixed(2)}'),
            if (_delivery.specialInstructions?.isNotEmpty == true)
              _buildInfoRow('Special Instructions', _delivery.specialInstructions!),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryUpdates() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Delivery Updates',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (_updates.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  child: Column(
                    children: [
                      Icon(Icons.update_outlined, size: 48, color: AppColors.textHint),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'No updates yet',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textHint),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _updates.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final update = _updates[index];
                  return _buildUpdateItem(update);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600, color: AppColors.textSecondary),
            ),
          ),
          Expanded(child: Text(value, style: Theme.of(context).textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Widget _buildUpdateItem(DeliveryUpdate update) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 12,
            height: 12,
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(color: AppColors.primary, shape: BoxShape.circle),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  update.message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDateTime(update.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                ),
                Text(
                  'By: ${update.createdBy.username}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
