import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../l10n/generated/app_localizations.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Terms of Service')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fish Auction Terms of Service',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
            const SizedBox(height: AppConstants.largePadding),

            _buildSection(
              context,
              'Acceptance of Terms',
              'By accessing and using Fish Auction platform, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
            ),

            _buildSection(
              context,
              'Platform Description',
              'Fish Auction is an online marketplace that connects fish sellers with buyers through live auctions. We provide the platform and tools for conducting auctions but are not directly involved in the actual transactions between users.',
            ),

            _buildSection(
              context,
              'User Accounts',
              'To participate in auctions, you must:\n\n• Create an account with accurate information\n• Complete KYC verification process\n• Be at least 18 years old\n• Maintain the security of your account\n• Notify us of any unauthorized use',
            ),

            _buildSection(
              context,
              'Auction Rules',
              'All auctions are subject to the following rules:\n\n• Bids are binding commitments to purchase\n• Winning bidders must complete payment within 24 hours\n• Sellers must deliver products as described\n• All sales are final unless otherwise specified\n• Reserve prices may apply to certain auctions',
            ),

            _buildSection(
              context,
              'Payment Terms',
              'Payment processing:\n\n• All payments are processed securely through our platform\n• Accepted payment methods include credit cards, bank transfers, and digital wallets\n• Transaction fees may apply\n• Refunds are subject to our refund policy\n• Sellers receive payment after successful delivery confirmation',
            ),

            _buildSection(
              context,
              'Quality and Delivery',
              'Sellers are responsible for:\n\n• Accurate product descriptions and images\n• Proper packaging and preservation\n• Timely delivery within specified timeframes\n• Product quality as advertised\n• Compliance with food safety regulations',
            ),

            _buildSection(
              context,
              'Prohibited Activities',
              'Users may not:\n\n• Bid without intent to purchase\n• Manipulate auction prices through shill bidding\n• List illegal or unsafe products\n• Violate any applicable laws or regulations\n• Interfere with platform operations\n• Create multiple accounts to circumvent restrictions',
            ),

            _buildSection(
              context,
              'Dispute Resolution',
              'In case of disputes:\n\n• Contact our support team first\n• We provide mediation services\n• Binding arbitration may be required\n• Legal action should be a last resort\n• All disputes are subject to local jurisdiction',
            ),

            _buildSection(
              context,
              'Limitation of Liability',
              'Fish Auction is not liable for:\n\n• Product quality or safety issues\n• Delivery delays or failures\n• User disputes or conflicts\n• Financial losses from auction participation\n• Technical issues or platform downtime',
            ),

            _buildSection(
              context,
              'Termination',
              'We reserve the right to terminate accounts for:\n\n• Violation of these terms\n• Fraudulent or suspicious activity\n• Non-payment of fees\n• Abuse of platform features\n• Legal or regulatory requirements',
            ),

            _buildSection(
              context,
              'Changes to Terms',
              'We may modify these terms at any time. Users will be notified of significant changes. Continued use of the platform constitutes acceptance of modified terms.',
            ),

            _buildSection(
              context,
              'Contact Information',
              'For questions about these terms:\n\n• Email: <EMAIL>\n• Phone: +****************\n• Address: 123 Harbor Street, Coastal City, CC 12345',
            ),

            const SizedBox(height: AppConstants.largePadding),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppColors.warning.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning_amber_outlined, color: AppColors.warning, size: 20),
                      const SizedBox(width: AppConstants.smallPadding),
                      Text(
                        'Important Notice',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.warning),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    'By using Fish Auction, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. Please review them carefully.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: AppConstants.smallPadding),
        Text(content, style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.6)),
        const SizedBox(height: AppConstants.largePadding),
      ],
    );
  }
}
