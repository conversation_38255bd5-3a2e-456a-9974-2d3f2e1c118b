import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../l10n/generated/app_localizations.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  PackageInfo? _packageInfo;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _packageInfo = packageInfo;
      });
    } catch (e) {
      print('Error loading package info: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: const Text('About'), elevation: 0),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // App Logo and Info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.largePadding),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: AppColors.primaryGradient,
                ),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                children: [
                  // App Icon
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppColors.textLight,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 10, offset: const Offset(0, 4))],
                    ),
                    child: Icon(Icons.waves, size: 60, color: AppColors.primary),
                  ),

                  const SizedBox(height: AppConstants.defaultPadding),

                  // App Name
                  Text(
                    'Fish Auction',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineMedium?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
                  ),

                  const SizedBox(height: AppConstants.smallPadding),

                  // Version
                  Text(
                    'Version ${_packageInfo?.version ?? '1.0.0'} (${_packageInfo?.buildNumber ?? '1'})',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
                  ),

                  const SizedBox(height: AppConstants.smallPadding),

                  // Tagline
                  Text(
                    'Your trusted fish trading platform',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textLight.withOpacity(0.8),
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.largePadding),

            // App Description
            _buildInfoSection(
              'About Fish Auction',
              'Fish Auction is a comprehensive platform designed to connect fish sellers with buyers through live auctions. Our app provides a secure, transparent, and efficient marketplace for the fish trading community.',
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Features
            _buildInfoSection(
              'Key Features',
              '• Live fish auctions with real-time bidding\n'
                  '• Secure payment processing with Stripe\n'
                  '• GPS-enabled delivery tracking\n'
                  '• Multi-language support (English & Arabic)\n'
                  '• KYC verification for sellers\n'
                  '• AI-powered customer support\n'
                  '• WhatsApp notifications\n'
                  '• Watchlist and auto-bidding',
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Contact Info
            _buildInfoSection(
              'Contact Information',
              'For support, feedback, or business inquiries:\n\n'
                  'Email: <EMAIL>\n'
                  'Phone: +****************\n'
                  'Website: www.fishauction.com',
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Legal Links
            _buildLegalSection(),

            const SizedBox(height: AppConstants.defaultPadding),

            // Credits
            _buildInfoSection(
              'Credits',
              'Developed with ❤️ by the Fish Auction Team\n\n'
                  'Special thanks to:\n'
                  '• Flutter team for the amazing framework\n'
                  '• Our beta testers and early adopters\n'
                  '• The fish trading community for their feedback',
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Copyright
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppColors.border),
              ),
              child: Text(
                '© ${DateTime.now().year} Fish Auction. All rights reserved.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(String title, String content) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary, height: 1.5),
          ),
        ],
      ),
    );
  }

  Widget _buildLegalSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Legal',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(Icons.privacy_tip, color: AppColors.primary),
            title: const Text('Privacy Policy'),
            trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
            onTap: () {
              // TODO: Navigate to privacy policy
              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Privacy Policy coming soon')));
            },
          ),

          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(Icons.description, color: AppColors.primary),
            title: const Text('Terms of Service'),
            trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
            onTap: () {
              // TODO: Navigate to terms of service
              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Terms of Service coming soon')));
            },
          ),

          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Icon(Icons.gavel, color: AppColors.primary),
            title: const Text('Auction Rules'),
            trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
            onTap: () {
              // TODO: Navigate to auction rules
              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Auction Rules coming soon')));
            },
          ),
        ],
      ),
    );
  }
}
