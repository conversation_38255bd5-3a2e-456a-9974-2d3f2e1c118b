import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../services/notification_service.dart';
import '../../models/notification_model.dart' as NotificationModel;
import '../auction/auction_detail_screen.dart';
import '../../providers/auction_provider.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final NotificationService _notificationService = NotificationService();
  List<NotificationModel.Notification> _notifications = [];
  bool _isLoading = false;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadNotifications();
      }
    });
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      final response = await _notificationService.getNotifications();
      if (response.isSuccess && response.data != null) {
        setState(() {
          _notifications = response.data!;
        });
      }
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to load notifications: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final unreadCount = _notifications.where((n) => !n.isRead).length;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.notifications),
        actions: [if (unreadCount > 0) TextButton(onPressed: _markAllAsRead, child: const Text('Mark All Read'))],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _notifications.isEmpty
          ? _buildEmptyState(localizations)
          : RefreshIndicator(
              onRefresh: _loadNotifications,
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                itemCount: _notifications.length,
                itemBuilder: (context, index) {
                  final notification = _notifications[index];
                  return _buildNotificationCard(notification);
                },
              ),
            ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.notifications_none, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Notifications',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ll see auction updates, bids, and important messages here.',
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel.Notification notification) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _handleNotificationTap(notification),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(_getNotificationIcon(notification), color: _getNotificationColor(notification), size: 20),
              ),

              const SizedBox(width: AppConstants.defaultPadding),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(color: AppColors.primary, shape: BoxShape.circle),
                          ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.smallPadding),

                    Text(
                      notification.message,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
                    ),

                    const SizedBox(height: AppConstants.smallPadding),

                    Text(
                      notification.formattedTime,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationModel.Notification notification) {
    // Determine icon based on notification content
    if (notification.title.toLowerCase().contains('won') || notification.title.toLowerCase().contains('auction won')) {
      return Icons.emoji_events;
    } else if (notification.title.toLowerCase().contains('outbid')) {
      return Icons.trending_up;
    } else if (notification.title.toLowerCase().contains('ending') ||
        notification.title.toLowerCase().contains('reminder')) {
      return Icons.schedule;
    } else if (notification.title.toLowerCase().contains('payment')) {
      return Icons.payment;
    } else if (notification.title.toLowerCase().contains('kyc') ||
        notification.title.toLowerCase().contains('verification')) {
      return Icons.verified_user;
    } else if (notification.auctionId != null) {
      return Icons.gavel;
    } else {
      return Icons.info;
    }
  }

  Color _getNotificationColor(NotificationModel.Notification notification) {
    // Determine color based on notification content
    if (notification.title.toLowerCase().contains('won') || notification.title.toLowerCase().contains('auction won')) {
      return AppColors.success;
    } else if (notification.title.toLowerCase().contains('outbid')) {
      return AppColors.warning;
    } else if (notification.title.toLowerCase().contains('ending') ||
        notification.title.toLowerCase().contains('reminder')) {
      return AppColors.accent;
    } else if (notification.title.toLowerCase().contains('payment')) {
      return AppColors.primary;
    } else if (notification.title.toLowerCase().contains('kyc') ||
        notification.title.toLowerCase().contains('verification')) {
      return AppColors.info;
    } else {
      return AppColors.textSecondary;
    }
  }

  void _handleNotificationTap(NotificationModel.Notification notification) async {
    // Mark as read
    if (!notification.isRead) {
      await _notificationService.markAsRead(notification.id);
      _loadNotifications(); // Refresh to update read status
    }

    // Navigate based on notification type
    if (notification.auctionId != null) {
      // Navigate to auction details - need to fetch auction first
      _navigateToAuction(notification.auctionId!);
    }
  }

  void _navigateToAuction(int auctionId) async {
    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Fetch auction details
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      await auctionProvider.loadAuctionDetails(auctionId);

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Get the auction
      final auction = auctionProvider.auctions.firstWhere(
        (a) => a.id == auctionId,
        orElse: () => throw Exception('Auction not found'),
      );

      // Navigate to auction details
      if (mounted) {
        Navigator.push(context, MaterialPageRoute(builder: (context) => AuctionDetailScreen(auction: auction)));
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.pop(context);

      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to load auction: $e')));
      }
    }
  }

  void _markAllAsRead() async {
    try {
      // Mark all notifications as read via API
      await _notificationService.markAllAsRead();
      // Refresh notifications to update UI
      _loadNotifications();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to mark all as read: $e')));
      }
    }
  }
}

class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  bool isRead;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
  });
}

enum NotificationType { auctionWon, outbid, auctionEnding, payment, kyc, general }
