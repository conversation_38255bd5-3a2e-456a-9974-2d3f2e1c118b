import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../services/payment_service.dart';
import '../../services/api_service.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../l10n/generated/app_localizations.dart';

class PaymentMethodsScreen extends StatefulWidget {
  const PaymentMethodsScreen({super.key});

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  final PaymentService _paymentService = PaymentService();
  bool _isLoading = false;
  List<Map<String, dynamic>> _payoutMethods = [];

  @override
  void initState() {
    super.initState();
    _initializeAndLoadMethods();
  }

  Future<void> _initializeAndLoadMethods() async {
    // Ensure auth tokens are loaded before making API calls
    await ApiService().loadAuthTokens();
    _loadPayoutMethods();
  }

  Future<void> _loadPayoutMethods() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _paymentService.getPayoutMethods();
      print('🔍 Loading payout methods...');
      print('📡 API Response success: ${response.isSuccess}');
      print('📊 API Response data: ${response.data}');
      print('📊 API Response data type: ${response.data.runtimeType}');

      if (response.isSuccess && response.data != null) {
        setState(() {
          _payoutMethods = List<Map<String, dynamic>>.from(response.data!);
          print('✅ Loaded ${_payoutMethods.length} payment methods');
        });
      } else {
        print('❌ Failed to load payment methods: ${response.message}');
      }
    } catch (e) {
      print('❌ Exception loading payout methods: $e');
      debugPrint('Error loading payout methods: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Debug method to manually test API call
  void _debugLoadMethods() async {
    print('🐛 DEBUG: Manually triggering _loadPayoutMethods()');
    await ApiService().loadAuthTokens();
    print('🐛 DEBUG: Auth tokens reloaded');
    print('🐛 DEBUG: Auth status: ${ApiService().authStatus}');
    _loadPayoutMethods();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Methods'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: _debugLoadMethods, icon: const Icon(Icons.bug_report)),
          IconButton(onPressed: _showAddPaymentMethodDialog, icon: const Icon(Icons.add)),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: _payoutMethods.isEmpty ? _buildEmptyState() : _buildPaymentMethodsList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.payment, size: 80, color: Colors.grey[400]),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Payment Methods',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600], fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Add a payment method to withdraw your earnings',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton.icon(
              onPressed: _showAddPaymentMethodDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Payment Method'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.largePadding,
                  vertical: AppConstants.defaultPadding,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _payoutMethods.length,
      itemBuilder: (context, index) {
        final method = _payoutMethods[index];
        return _buildPaymentMethodCard(method);
      },
    );
  }

  Widget _buildPaymentMethodCard(Map<String, dynamic> method) {
    final String type = method['payout_type'] ?? '';
    final bool isDefault = method['is_default'] ?? false;
    final bool isVerified = method['is_verified'] ?? false;

    IconData icon;
    String title;
    String subtitle;

    switch (type) {
      case 'paypal':
        icon = Icons.payment;
        title = 'PayPal';
        subtitle = method['paypal_email'] ?? '';
        break;
      case 'stripe':
        icon = Icons.credit_card;
        title = 'Stripe';
        subtitle = 'Connected Account';
        break;
      case 'bank_transfer':
        icon = Icons.account_balance;
        title = 'Bank Transfer';
        subtitle = method['bank_name'] ?? '';
        break;
      case 'payoneer':
        icon = Icons.account_balance_wallet;
        title = 'Payoneer';
        subtitle = method['payoneer_email'] ?? '';
        break;
      default:
        icon = Icons.payment;
        title = type;
        subtitle = '';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withOpacity(0.1),
          child: Icon(icon, color: AppColors.primary),
        ),
        title: Row(
          children: [
            Text(title),
            if (isDefault) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(color: AppColors.success, borderRadius: BorderRadius.circular(10)),
                child: const Text(
                  'Default',
                  style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (subtitle.isNotEmpty) Text(subtitle),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  isVerified ? Icons.verified : Icons.pending,
                  size: 16,
                  color: isVerified ? AppColors.success : AppColors.warning,
                ),
                const SizedBox(width: 4),
                Text(
                  isVerified ? 'Verified' : 'Pending Verification',
                  style: TextStyle(color: isVerified ? AppColors.success : AppColors.warning, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, method),
          itemBuilder: (context) => [
            if (!isDefault) const PopupMenuItem(value: 'set_default', child: Text('Set as Default')),
            if (!isVerified && type != 'bank_transfer') const PopupMenuItem(value: 'test', child: Text('Test Method')),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action, Map<String, dynamic> method) {
    switch (action) {
      case 'set_default':
        _setDefaultPaymentMethod(method['id']);
        break;
      case 'test':
        _testPaymentMethod(method['id']);
        break;
      case 'delete':
        _deletePaymentMethod(method['id']);
        break;
    }
  }

  void _setDefaultPaymentMethod(String methodId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _paymentService.setDefaultPayoutMethod(methodId);
      if (response.isSuccess) {
        _loadPayoutMethods();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Default payment method updated'), backgroundColor: AppColors.success),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message ?? 'Failed to update default method'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testPaymentMethod(String methodId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _paymentService.testPayoutMethod(methodId);
      if (response.isSuccess) {
        _loadPayoutMethods();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Payment method verified successfully!'), backgroundColor: AppColors.success),
        );
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(response.message ?? 'Test failed'), backgroundColor: AppColors.error));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _deletePaymentMethod(String methodId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment Method'),
        content: const Text('Are you sure you want to delete this payment method?'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final response = await _paymentService.deletePayoutMethod(methodId);
        if (response.isSuccess) {
          _loadPayoutMethods();
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Payment method deleted'), backgroundColor: AppColors.success));
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response.message ?? 'Failed to delete method'), backgroundColor: AppColors.error),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showAddPaymentMethodDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) => AddPaymentMethodSheet(
        onMethodAdded: () {
          _loadPayoutMethods();
        },
      ),
    );
  }
}

class AddPaymentMethodSheet extends StatefulWidget {
  final VoidCallback onMethodAdded;

  const AddPaymentMethodSheet({super.key, required this.onMethodAdded});

  @override
  State<AddPaymentMethodSheet> createState() => _AddPaymentMethodSheetState();
}

class _AddPaymentMethodSheetState extends State<AddPaymentMethodSheet> {
  final PaymentService _paymentService = PaymentService();
  String _selectedType = 'paypal';
  bool _isLoading = false;

  // Controllers for different payment methods
  final _paypalEmailController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _accountHolderController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _routingNumberController = TextEditingController();
  final _payoneerEmailController = TextEditingController();

  // Telr controllers
  final _telrMerchantIdController = TextEditingController();
  final _telrAccountEmailController = TextEditingController();
  final _telrAccountHolderController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: AppConstants.defaultPadding,
        right: AppConstants.defaultPadding,
        top: AppConstants.defaultPadding,
        bottom: MediaQuery.of(context).viewInsets.bottom + AppConstants.defaultPadding,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Add Payment Method',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Payment method type selection
          DropdownButtonFormField<String>(
            value: _selectedType,
            decoration: const InputDecoration(labelText: 'Payment Method Type', border: OutlineInputBorder()),
            items: const [
              DropdownMenuItem(value: 'paypal', child: Text('PayPal')),
              DropdownMenuItem(value: 'stripe', child: Text('Stripe')),
              DropdownMenuItem(value: 'bank_transfer', child: Text('Bank Transfer')),
              DropdownMenuItem(value: 'payoneer', child: Text('Payoneer')),
              DropdownMenuItem(value: 'telr', child: Text('Telr')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Dynamic form based on selected type
          _buildFormFields(),

          const SizedBox(height: AppConstants.largePadding),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _addPaymentMethod,
                  style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary, foregroundColor: Colors.white),
                  child: _isLoading
                      ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(strokeWidth: 2))
                      : const Text('Add Method'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    switch (_selectedType) {
      case 'paypal':
        return TextField(
          controller: _paypalEmailController,
          decoration: const InputDecoration(
            labelText: 'PayPal Email',
            border: OutlineInputBorder(),
            hintText: '<EMAIL>',
          ),
          keyboardType: TextInputType.emailAddress,
        );

      case 'bank_transfer':
        return Column(
          children: [
            TextField(
              controller: _bankNameController,
              decoration: const InputDecoration(labelText: 'Bank Name', border: OutlineInputBorder()),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextField(
              controller: _accountHolderController,
              decoration: const InputDecoration(labelText: 'Account Holder Name', border: OutlineInputBorder()),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextField(
              controller: _accountNumberController,
              decoration: const InputDecoration(labelText: 'Account Number', border: OutlineInputBorder()),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextField(
              controller: _routingNumberController,
              decoration: const InputDecoration(labelText: 'Routing Number', border: OutlineInputBorder()),
            ),
          ],
        );

      case 'payoneer':
        return TextField(
          controller: _payoneerEmailController,
          decoration: const InputDecoration(
            labelText: 'Payoneer Email',
            border: OutlineInputBorder(),
            hintText: '<EMAIL>',
          ),
          keyboardType: TextInputType.emailAddress,
        );

      case 'stripe':
        return Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.info.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.info.withOpacity(0.3)),
          ),
          child: const Text(
            'Stripe integration requires additional setup. Please contact support for assistance.',
            style: TextStyle(color: AppColors.info),
          ),
        );

      case 'telr':
        return Column(
          children: [
            TextField(
              controller: _telrMerchantIdController,
              decoration: const InputDecoration(
                labelText: 'Telr Merchant ID',
                border: OutlineInputBorder(),
                hintText: 'Enter your Telr merchant ID',
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextField(
              controller: _telrAccountEmailController,
              decoration: const InputDecoration(
                labelText: 'Account Email',
                border: OutlineInputBorder(),
                hintText: '<EMAIL>',
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextField(
              controller: _telrAccountHolderController,
              decoration: const InputDecoration(
                labelText: 'Account Holder Name',
                border: OutlineInputBorder(),
                hintText: 'Full name as registered with Telr',
              ),
            ),
          ],
        );

      default:
        return const SizedBox.shrink();
    }
  }

  void _addPaymentMethod() async {
    if (!_validateForm()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Map<String, dynamic> data = {'payout_type': _selectedType};

      switch (_selectedType) {
        case 'paypal':
          data['paypal_email'] = _paypalEmailController.text.trim();
          break;
        case 'bank_transfer':
          data['bank_name'] = _bankNameController.text.trim();
          data['account_holder_name'] = _accountHolderController.text.trim();
          data['account_number'] = _accountNumberController.text.trim();
          data['routing_number'] = _routingNumberController.text.trim();
          break;
        case 'payoneer':
          data['payoneer_email'] = _payoneerEmailController.text.trim();
          break;
        case 'telr':
          data['telr_merchant_id'] = _telrMerchantIdController.text.trim();
          data['telr_account_email'] = _telrAccountEmailController.text.trim();
          data['telr_account_holder_name'] = _telrAccountHolderController.text.trim();
          break;
        case 'stripe':
          // Stripe requires special handling
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Stripe integration coming soon'), backgroundColor: AppColors.info),
          );
          return;
      }

      final response = await _paymentService.addPayoutMethod(data);
      if (response.isSuccess) {
        widget.onMethodAdded();
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Payment method added successfully!'), backgroundColor: AppColors.success),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(response.message ?? 'Failed to add payment method'), backgroundColor: AppColors.error),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _validateForm() {
    switch (_selectedType) {
      case 'paypal':
        if (_paypalEmailController.text.trim().isEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Please enter PayPal email'), backgroundColor: AppColors.error));
          return false;
        }
        break;
      case 'bank_transfer':
        if (_bankNameController.text.trim().isEmpty ||
            _accountHolderController.text.trim().isEmpty ||
            _accountNumberController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please fill all required bank details'), backgroundColor: AppColors.error),
          );
          return false;
        }
        break;
      case 'payoneer':
        if (_payoneerEmailController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please enter Payoneer email'), backgroundColor: AppColors.error),
          );
          return false;
        }
        break;
    }
    return true;
  }

  @override
  void dispose() {
    _paypalEmailController.dispose();
    _bankNameController.dispose();
    _accountHolderController.dispose();
    _accountNumberController.dispose();
    _routingNumberController.dispose();
    _payoneerEmailController.dispose();
    super.dispose();
  }
}
