import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import 'language_settings_screen.dart';
import 'notification_settings_screen.dart';
import 'account_settings_screen.dart';
import '../legal/privacy_policy_screen.dart';
import '../legal/terms_of_service_screen.dart';
import '../legal/about_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations.settings), elevation: 0),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // App Preferences Section
            _buildSectionHeader('App Preferences'),
            _buildSettingsGroup([
              _buildSettingsTile(
                icon: Icons.language,
                title: localizations.language,
                subtitle: 'English', // TODO: Get current language
                onTap: () => _navigateToLanguageSettings(),
              ),
              _buildSettingsTile(
                icon: Icons.notifications,
                title: localizations.notificationSettings,
                subtitle: 'Manage your notifications',
                onTap: () => _navigateToNotificationSettings(),
              ),
              _buildSettingsTile(
                icon: Icons.dark_mode,
                title: 'Theme',
                subtitle: 'Light mode', // TODO: Get current theme
                onTap: () => _showThemeDialog(),
              ),
            ]),

            const SizedBox(height: AppConstants.largePadding),

            // Account Section
            _buildSectionHeader('Account'),
            _buildSettingsGroup([
              _buildSettingsTile(
                icon: Icons.account_circle,
                title: 'Account Settings',
                subtitle: 'Manage your account preferences',
                onTap: () => _navigateToAccountSettings(),
              ),
              _buildSettingsTile(
                icon: Icons.security,
                title: 'Privacy & Security',
                subtitle: 'Control your privacy settings',
                onTap: () => _navigateToPrivacySettings(),
              ),
              _buildSettingsTile(
                icon: Icons.payment,
                title: 'Payment Settings',
                subtitle: 'Manage payment methods',
                onTap: () => _navigateToPaymentSettings(),
              ),
            ]),

            const SizedBox(height: AppConstants.largePadding),

            // Support Section
            _buildSectionHeader('Support & Legal'),
            _buildSettingsGroup([
              _buildSettingsTile(
                icon: Icons.help,
                title: 'Help & Support',
                subtitle: 'Get help and contact support',
                onTap: () => _navigateToSupport(),
              ),
              _buildSettingsTile(
                icon: Icons.privacy_tip,
                title: localizations.privacyPolicy,
                subtitle: 'Read our privacy policy',
                onTap: () => _navigateToPrivacyPolicy(),
              ),
              _buildSettingsTile(
                icon: Icons.description,
                title: localizations.termsOfService,
                subtitle: 'Terms and conditions',
                onTap: () => _navigateToTermsOfService(),
              ),
              _buildSettingsTile(
                icon: Icons.info,
                title: 'About',
                subtitle: 'App version and information',
                onTap: () => _navigateToAbout(),
              ),
            ]),

            const SizedBox(height: AppConstants.largePadding),

            // App Version
            _buildAppVersion(),

            const SizedBox(height: AppConstants.largePadding),

            // Logout Button
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return DangerButton(
                  text: localizations.logout,
                  onPressed: () => _showLogoutConfirmation(authProvider, localizations),
                  icon: Icons.logout,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
      ),
    );
  }

  Widget _buildSettingsGroup(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(color: AppColors.primary.withOpacity(0.1), borderRadius: BorderRadius.circular(8)),
        child: Icon(icon, color: AppColors.primary, size: 20),
      ),
      title: Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary)),
      trailing: trailing ?? const Icon(Icons.chevron_right, color: AppColors.textSecondary),
      onTap: onTap,
    );
  }

  Widget _buildAppVersion() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        children: [
          Icon(Icons.waves, size: 48, color: AppColors.primary),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Fish Auction',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          Text(
            'Version 1.0.0',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Built with ❤️ for fish traders',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary, fontStyle: FontStyle.italic),
          ),
        ],
      ),
    );
  }

  void _navigateToLanguageSettings() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const LanguageSettingsScreen()));
  }

  void _navigateToNotificationSettings() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const NotificationSettingsScreen()));
  }

  void _navigateToAccountSettings() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const AccountSettingsScreen()));
  }

  void _navigateToPrivacySettings() {
    // TODO: Implement privacy settings screen
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Privacy settings coming soon')));
  }

  void _navigateToPaymentSettings() {
    // TODO: Implement payment settings screen
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Payment settings coming soon')));
  }

  void _navigateToSupport() {
    // TODO: Navigate to support screen
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Support screen coming soon')));
  }

  void _navigateToPrivacyPolicy() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()));
  }

  void _navigateToTermsOfService() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const TermsOfServiceScreen()));
  }

  void _navigateToAbout() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const AboutScreen()));
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.light_mode),
              title: const Text('Light'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement theme switching
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Theme switching coming soon')));
              },
            ),
            ListTile(
              leading: const Icon(Icons.dark_mode),
              title: const Text('Dark'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement theme switching
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Theme switching coming soon')));
              },
            ),
            ListTile(
              leading: const Icon(Icons.auto_mode),
              title: const Text('System'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement theme switching
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Theme switching coming soon')));
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutConfirmation(AuthProvider authProvider, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.logout),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await authProvider.logout();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: Text(localizations.logout),
          ),
        ],
      ),
    );
  }
}
