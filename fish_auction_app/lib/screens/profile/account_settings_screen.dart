import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../location/location_settings_screen.dart';
import '../location/location_history_screen.dart';
import '../../widgets/common/custom_button.dart';
import 'edit_profile_screen.dart';
import 'change_password_screen.dart';

class AccountSettingsScreen extends StatefulWidget {
  const AccountSettingsScreen({super.key});

  @override
  State<AccountSettingsScreen> createState() => _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends State<AccountSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Account Settings'), elevation: 0),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.user;
          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Account Info Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: AppColors.primaryGradient,
                    ),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: AppColors.textLight,
                        child: user.profileImage != null
                            ? ClipOval(
                                child: Image.network(user.profileImage!, width: 80, height: 80, fit: BoxFit.cover),
                              )
                            : Icon(Icons.person, size: 40, color: AppColors.primary),
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        user.displayName,
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        user.email,
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.largePadding),

                // Profile Management
                _buildSectionHeader('Profile Management'),
                _buildSettingsGroup([
                  _buildSettingsTile(
                    icon: Icons.edit,
                    title: 'Edit Profile',
                    subtitle: 'Update your personal information',
                    onTap: () => _navigateToEditProfile(),
                  ),
                  _buildSettingsTile(
                    icon: Icons.lock,
                    title: localizations.changePassword,
                    subtitle: 'Update your account password',
                    onTap: () => _navigateToChangePassword(),
                  ),
                  // Only show KYC verification for sellers - buyers and brokers don't need it
                  if (authProvider.userType == 'seller')
                    _buildSettingsTile(
                      icon: Icons.verified_user,
                      title: 'KYC Verification',
                      subtitle: _getKycStatusText(authProvider.kycStatus),
                      onTap: () => _showKycInfo(authProvider.kycStatus),
                      trailing: _getKycStatusIcon(authProvider.kycStatus),
                    ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Location Management (for sellers only)
                if (user.userType == 'seller') ...[
                  _buildSectionHeader('Location Management'),
                  _buildSettingsGroup([
                    _buildSettingsTile(
                      icon: Icons.location_on,
                      title: 'Live Location Sharing',
                      subtitle: 'Manage location sharing for auctions',
                      onTap: () => _navigateToLocationSettings(),
                    ),
                    _buildSettingsTile(
                      icon: Icons.my_location,
                      title: 'Auto-Location Setup',
                      subtitle: 'Configure automatic location detection',
                      onTap: () => _showAutoLocationSetup(),
                    ),
                    _buildSettingsTile(
                      icon: Icons.map,
                      title: 'Location History',
                      subtitle: 'View your location sharing history',
                      onTap: () => _navigateToLocationHistory(),
                    ),
                  ]),
                  const SizedBox(height: AppConstants.largePadding),
                ],

                // Account Security
                _buildSectionHeader('Security'),
                _buildSettingsGroup([
                  _buildSettingsTile(
                    icon: Icons.security,
                    title: 'Two-Factor Authentication',
                    subtitle: 'Add extra security to your account',
                    onTap: () => _show2FASettings(),
                  ),
                  _buildSettingsTile(
                    icon: Icons.devices,
                    title: 'Active Sessions',
                    subtitle: 'Manage your logged-in devices',
                    onTap: () => _showActiveSessions(),
                  ),
                  _buildSettingsTile(
                    icon: Icons.history,
                    title: 'Login History',
                    subtitle: 'View your recent login activity',
                    onTap: () => _showLoginHistory(),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Data & Privacy
                _buildSectionHeader('Data & Privacy'),
                _buildSettingsGroup([
                  _buildSettingsTile(
                    icon: Icons.download,
                    title: 'Download My Data',
                    subtitle: 'Get a copy of your data',
                    onTap: () => _requestDataDownload(),
                  ),
                  _buildSettingsTile(
                    icon: Icons.visibility_off,
                    title: 'Privacy Settings',
                    subtitle: 'Control who can see your information',
                    onTap: () => _showPrivacySettings(),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Danger Zone
                _buildSectionHeader('Danger Zone'),
                _buildSettingsGroup([
                  _buildSettingsTile(
                    icon: Icons.pause_circle,
                    title: 'Deactivate Account',
                    subtitle: 'Temporarily disable your account',
                    onTap: () => _showDeactivateConfirmation(),
                    titleColor: AppColors.warning,
                  ),
                  _buildSettingsTile(
                    icon: Icons.delete_forever,
                    title: 'Delete Account',
                    subtitle: 'Permanently delete your account',
                    onTap: () => _showDeleteConfirmation(),
                    titleColor: AppColors.error,
                  ),
                ]),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
      ),
    );
  }

  Widget _buildSettingsGroup(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
    Color? titleColor,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: (titleColor ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: titleColor ?? AppColors.primary, size: 20),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: titleColor),
      ),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary)),
      trailing: trailing ?? const Icon(Icons.chevron_right, color: AppColors.textSecondary),
      onTap: onTap,
    );
  }

  String _getKycStatusText(String status) {
    switch (status) {
      case 'approved':
        return 'Verified';
      case 'pending':
        return 'Under review';
      case 'rejected':
        return 'Rejected - resubmit required';
      default:
        return 'Not submitted';
    }
  }

  Widget _getKycStatusIcon(String status) {
    switch (status) {
      case 'approved':
        return Icon(Icons.verified, color: AppColors.success, size: 20);
      case 'pending':
        return Icon(Icons.schedule, color: AppColors.warning, size: 20);
      case 'rejected':
        return Icon(Icons.error, color: AppColors.error, size: 20);
      default:
        return Icon(Icons.info_outline, color: AppColors.textSecondary, size: 20);
    }
  }

  void _navigateToEditProfile() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const EditProfileScreen()));
  }

  void _navigateToChangePassword() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const ChangePasswordScreen()));
  }

  void _showKycInfo(String status) {
    // TODO: Show KYC information dialog
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('KYC information coming soon')));
  }

  void _show2FASettings() {
    // TODO: Implement 2FA settings
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('2FA settings coming soon')));
  }

  void _showActiveSessions() {
    // TODO: Show active sessions
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Active sessions coming soon')));
  }

  void _showLoginHistory() {
    // TODO: Show login history
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Login history coming soon')));
  }

  void _requestDataDownload() {
    // TODO: Implement data download
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Data download coming soon')));
  }

  void _showPrivacySettings() {
    // TODO: Show privacy settings
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Privacy settings coming soon')));
  }

  void _showDeactivateConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deactivate Account'),
        content: const Text(
          'Are you sure you want to deactivate your account? You can reactivate it later by logging in.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement account deactivation
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('Account deactivation coming soon')));
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.warning),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Are you sure you want to permanently delete your account? This action cannot be undone and all your data will be lost.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement account deletion
              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Account deletion coming soon')));
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _navigateToLocationSettings() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const LocationSettingsScreen()));
  }

  void _showAutoLocationSetup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auto-Location Setup'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configure automatic location detection for your auctions. This will help buyers verify your fishing location.',
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.gps_fixed),
              title: const Text('Enable Auto-Location'),
              subtitle: const Text('Automatically detect and share your location'),
              trailing: Switch(
                value: false, // TODO: Get from user preferences
                onChanged: (value) {
                  Navigator.of(context).pop();
                  _toggleAutoLocation(value);
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.location_searching),
              title: const Text('Test Location'),
              subtitle: const Text('Test your current location detection'),
              onTap: () {
                Navigator.of(context).pop();
                _testCurrentLocation();
              },
            ),
          ],
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Close'))],
      ),
    );
  }

  void _navigateToLocationHistory() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const LocationHistoryScreen()));
  }

  void _toggleAutoLocation(bool enabled) {
    // TODO: Implement auto-location toggle
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(enabled ? 'Auto-location enabled successfully' : 'Auto-location disabled'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _testCurrentLocation() async {
    // TODO: Implement location testing
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Testing current location...')));

    // Simulate location detection
    await Future.delayed(const Duration(seconds: 2));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Location detected: 25.2048° N, 55.2708° E'), backgroundColor: AppColors.success),
    );
  }
}
