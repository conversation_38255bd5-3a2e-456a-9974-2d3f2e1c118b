import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/language_provider.dart';
import '../../l10n/generated/app_localizations.dart';

class LanguageSettingsScreen extends StatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  State<LanguageSettingsScreen> createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState extends State<LanguageSettingsScreen> {
  final List<LanguageOption> _languages = [
    LanguageOption(code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸'),
    LanguageOption(code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦'),
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations.language), elevation: 0),
      body: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: AppColors.primaryGradient,
                    ),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: Column(
                    children: [
                      Icon(Icons.language, size: 48, color: AppColors.textLight),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        'Choose Your Language',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'Select your preferred language for the app',
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.largePadding),

                // Language Options
                Text(
                  'Available Languages',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 2))],
                  ),
                  child: Column(
                    children: _languages.map((language) {
                      final isSelected = languageProvider.currentLanguage == language.code;

                      return _buildLanguageTile(
                        language: language,
                        isSelected: isSelected,
                        onTap: () => _selectLanguage(language.code, languageProvider),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: AppConstants.largePadding),

                // Language Info
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: AppColors.info.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    border: Border.all(color: AppColors.info.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: AppColors.info, size: 20),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: Text(
                          'The app will restart to apply the new language settings.',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.info),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLanguageTile({required LanguageOption language, required bool isSelected, required VoidCallback onTap}) {
    return ListTile(
      leading: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.background,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: isSelected ? AppColors.primary : AppColors.border, width: isSelected ? 2 : 1),
        ),
        child: Center(child: Text(language.flag, style: const TextStyle(fontSize: 24))),
      ),
      title: Text(
        language.name,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
          color: isSelected ? AppColors.primary : null,
        ),
      ),
      subtitle: Text(
        language.nativeName,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: isSelected ? AppColors.primary.withOpacity(0.8) : AppColors.textSecondary,
        ),
      ),
      trailing: isSelected
          ? Icon(Icons.check_circle, color: AppColors.primary, size: 24)
          : Icon(Icons.radio_button_unchecked, color: AppColors.textSecondary, size: 24),
      onTap: onTap,
    );
  }

  void _selectLanguage(String languageCode, LanguageProvider languageProvider) {
    if (languageProvider.currentLanguage == languageCode) {
      return; // Already selected
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Language'),
        content: const Text('Are you sure you want to change the language? The app will restart to apply the changes.'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Show loading
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Row(
                    children: [CircularProgressIndicator(), SizedBox(width: 16), Text('Changing language...')],
                  ),
                ),
              );

              // Change language
              await languageProvider.setLanguage(languageCode);

              // Close loading dialog
              if (mounted) {
                Navigator.of(context).pop();

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Language changed successfully!'), backgroundColor: AppColors.success),
                );

                // Go back to previous screen
                Navigator.of(context).pop();
              }
            },
            child: const Text('Change'),
          ),
        ],
      ),
    );
  }
}

class LanguageOption {
  final String code;
  final String name;
  final String nativeName;
  final String flag;

  LanguageOption({required this.code, required this.name, required this.nativeName, required this.flag});
}
