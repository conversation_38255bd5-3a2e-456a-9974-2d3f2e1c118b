import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../services/payment_service.dart';
import '../../widgets/common/loading_overlay.dart';

class WithdrawalRequestsScreen extends StatefulWidget {
  const WithdrawalRequestsScreen({super.key});

  @override
  State<WithdrawalRequestsScreen> createState() => _WithdrawalRequestsScreenState();
}

class _WithdrawalRequestsScreenState extends State<WithdrawalRequestsScreen> {
  final PaymentService _paymentService = PaymentService();
  bool _isLoading = false;
  List<Map<String, dynamic>> _withdrawalRequests = [];

  @override
  void initState() {
    super.initState();
    _loadWithdrawalRequests();
  }

  Future<void> _loadWithdrawalRequests() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _paymentService.getUserWithdrawalRequests();
      if (response.isSuccess && response.data != null) {
        setState(() {
          _withdrawalRequests = List<Map<String, dynamic>>.from(response.data!['withdrawals']);
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? 'Failed to load withdrawal requests'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Withdrawal Requests'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadWithdrawalRequests, icon: const Icon(Icons.refresh))],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: _withdrawalRequests.isEmpty ? _buildEmptyState() : _buildWithdrawalsList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_balance_wallet, size: 80, color: Colors.grey[400]),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Withdrawal Requests',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600], fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'You haven\'t made any withdrawal requests yet.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalsList() {
    return RefreshIndicator(
      onRefresh: _loadWithdrawalRequests,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: _withdrawalRequests.length,
        itemBuilder: (context, index) {
          final withdrawal = _withdrawalRequests[index];
          return _buildWithdrawalCard(withdrawal);
        },
      ),
    );
  }

  Widget _buildWithdrawalCard(Map<String, dynamic> withdrawal) {
    final status = withdrawal['status'] as String;
    final payoutStatus = withdrawal['payout_status'] as String;
    final amount = withdrawal['amount'] as double;
    final payoutAmount = withdrawal['payout_amount'] as double;
    final platformFee = withdrawal['platform_fee'] as double;
    final createdAt = DateTime.parse(withdrawal['created_at']);
    final payoutMethod = withdrawal['payout_method'] as Map<String, dynamic>;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with amount and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '\$${amount.toStringAsFixed(2)}',
                  style: Theme.of(
                    context,
                  ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
                ),
                _buildStatusBadge(status, payoutStatus),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),

            // Payment method
            Row(
              children: [
                Icon(_getPaymentMethodIcon(payoutMethod['type']), color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${_getPaymentMethodTitle(payoutMethod['type'])} - ${payoutMethod['details']}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),

            // Amount breakdown
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(color: Colors.grey[100], borderRadius: BorderRadius.circular(8)),
              child: Column(
                children: [
                  _buildAmountRow('Withdrawal Amount:', '\$${amount.toStringAsFixed(2)}'),
                  _buildAmountRow('Platform Fee:', '-\$${platformFee.toStringAsFixed(2)}'),
                  const Divider(),
                  _buildAmountRow('You Receive:', '\$${payoutAmount.toStringAsFixed(2)}', isTotal: true),
                ],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),

            // Date and additional info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Requested: ${_formatDate(createdAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                if (withdrawal['external_transaction_id'] != null)
                  Text(
                    'TX: ${withdrawal['external_transaction_id']}',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600], fontFamily: 'monospace'),
                  ),
              ],
            ),

            // Status-specific information
            if (status == 'rejected' && withdrawal['rejected_at'] != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.error.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: AppColors.error, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Rejected on ${_formatDate(DateTime.parse(withdrawal['rejected_at']))}. Amount returned to your wallet.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.error),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            if (status == 'completed' && withdrawal['completed_at'] != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.success.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle_outline, color: AppColors.success, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Completed on ${_formatDate(DateTime.parse(withdrawal['completed_at']))}. Funds sent to your account.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.success),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAmountRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: isTotal ? FontWeight.bold : FontWeight.normal),
          ),
          Text(
            amount,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(String status, String payoutStatus) {
    Color backgroundColor;
    Color textColor;
    String displayText;

    switch (status) {
      case 'pending':
        backgroundColor = AppColors.warning;
        textColor = Colors.white;
        displayText = 'Pending Approval';
        break;
      case 'approved':
        backgroundColor = AppColors.info;
        textColor = Colors.white;
        displayText = 'Approved';
        break;
      case 'completed':
        backgroundColor = AppColors.success;
        textColor = Colors.white;
        displayText = 'Completed';
        break;
      case 'rejected':
        backgroundColor = AppColors.error;
        textColor = Colors.white;
        displayText = 'Rejected';
        break;
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        displayText = status.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(color: backgroundColor, borderRadius: BorderRadius.circular(16)),
      child: Text(
        displayText,
        style: TextStyle(color: textColor, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  IconData _getPaymentMethodIcon(String type) {
    switch (type) {
      case 'paypal':
        return Icons.payment;
      case 'stripe':
        return Icons.credit_card;
      case 'bank_transfer':
        return Icons.account_balance;
      case 'payoneer':
        return Icons.account_balance_wallet;
      case 'telr':
        return Icons.credit_card_outlined;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodTitle(String type) {
    switch (type) {
      case 'paypal':
        return 'PayPal';
      case 'stripe':
        return 'Stripe';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'payoneer':
        return 'Payoneer';
      case 'telr':
        return 'Telr';
      default:
        return type.toUpperCase();
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
