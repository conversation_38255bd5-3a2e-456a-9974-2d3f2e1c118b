import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/notification_provider.dart';
import '../../l10n/generated/app_localizations.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations.notificationSettings), elevation: 0),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: AppColors.primaryGradient,
                    ),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: Column(
                    children: [
                      Icon(Icons.notifications, size: 48, color: AppColors.textLight),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        'Notification Preferences',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'Customize how you receive notifications',
                        style: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.largePadding),

                // Auction Notifications
                _buildSectionHeader('Auction Notifications'),
                _buildNotificationGroup([
                  _buildNotificationTile(
                    title: 'New Auctions',
                    subtitle: 'Get notified when new auctions are posted',
                    value: notificationProvider.newAuctions,
                    onChanged: (value) => notificationProvider.setNewAuctions(value),
                  ),
                  _buildNotificationTile(
                    title: 'Auction Ending Soon',
                    subtitle: 'Alerts when watched auctions are ending',
                    value: notificationProvider.auctionEndingSoon,
                    onChanged: (value) => notificationProvider.setAuctionEndingSoon(value),
                  ),
                  _buildNotificationTile(
                    title: 'Outbid Alerts',
                    subtitle: 'When someone outbids you',
                    value: notificationProvider.outbidAlerts,
                    onChanged: (value) => notificationProvider.setOutbidAlerts(value),
                  ),
                  _buildNotificationTile(
                    title: 'Auction Won',
                    subtitle: 'When you win an auction',
                    value: notificationProvider.auctionWon,
                    onChanged: (value) => notificationProvider.setAuctionWon(value),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Payment Notifications
                _buildSectionHeader('Payment Notifications'),
                _buildNotificationGroup([
                  _buildNotificationTile(
                    title: 'Payment Reminders',
                    subtitle: 'Reminders for pending payments',
                    value: notificationProvider.paymentReminders,
                    onChanged: (value) => notificationProvider.setPaymentReminders(value),
                  ),
                  _buildNotificationTile(
                    title: 'Payment Confirmations',
                    subtitle: 'When payments are processed',
                    value: notificationProvider.paymentConfirmations,
                    onChanged: (value) => notificationProvider.setPaymentConfirmations(value),
                  ),
                  _buildNotificationTile(
                    title: 'Wallet Updates',
                    subtitle: 'Balance changes and transactions',
                    value: notificationProvider.walletUpdates,
                    onChanged: (value) => notificationProvider.setWalletUpdates(value),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Delivery Notifications
                _buildSectionHeader('Delivery Notifications'),
                _buildNotificationGroup([
                  _buildNotificationTile(
                    title: 'Delivery Updates',
                    subtitle: 'Status updates for your deliveries',
                    value: notificationProvider.deliveryUpdates,
                    onChanged: (value) => notificationProvider.setDeliveryUpdates(value),
                  ),
                  _buildNotificationTile(
                    title: 'Delivery Reminders',
                    subtitle: 'Pickup and delivery reminders',
                    value: notificationProvider.deliveryReminders,
                    onChanged: (value) => notificationProvider.setDeliveryReminders(value),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // General Notifications
                _buildSectionHeader('General'),
                _buildNotificationGroup([
                  _buildNotificationTile(
                    title: 'Marketing & Promotions',
                    subtitle: 'Special offers and promotions',
                    value: notificationProvider.marketing,
                    onChanged: (value) => notificationProvider.setMarketing(value),
                  ),
                  _buildNotificationTile(
                    title: 'App Updates',
                    subtitle: 'New features and updates',
                    value: notificationProvider.appUpdates,
                    onChanged: (value) => notificationProvider.setAppUpdates(value),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Notification Methods
                _buildSectionHeader('Notification Methods'),
                _buildNotificationGroup([
                  _buildNotificationTile(
                    title: 'Push Notifications',
                    subtitle: 'In-app notifications',
                    value: notificationProvider.pushNotifications,
                    onChanged: (value) => notificationProvider.setPushNotifications(value),
                  ),
                  _buildNotificationTile(
                    title: 'Email Notifications',
                    subtitle: 'Receive notifications via email',
                    value: notificationProvider.emailNotifications,
                    onChanged: (value) => notificationProvider.setEmailNotifications(value),
                  ),
                  _buildNotificationTile(
                    title: 'SMS Notifications',
                    subtitle: 'Receive notifications via SMS',
                    value: notificationProvider.smsNotifications,
                    onChanged: (value) => notificationProvider.setSmsNotifications(value),
                  ),
                  _buildNotificationTile(
                    title: 'WhatsApp Notifications',
                    subtitle: 'Receive notifications via WhatsApp',
                    value: notificationProvider.whatsappNotifications,
                    onChanged: (value) => notificationProvider.setWhatsappNotifications(value),
                  ),
                ]),

                const SizedBox(height: AppConstants.largePadding),

                // Test Notification Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _sendTestNotification(notificationProvider),
                    icon: const Icon(Icons.send),
                    label: const Text('Send Test Notification'),
                    style: ElevatedButton.styleFrom(padding: const EdgeInsets.all(AppConstants.defaultPadding)),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
      ),
    );
  }

  Widget _buildNotificationGroup(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildNotificationTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary)),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primary,
    );
  }

  void _sendTestNotification(NotificationProvider notificationProvider) {
    // TODO: Implement test notification
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: const Text('Test notification sent!'), backgroundColor: AppColors.success));
  }
}
