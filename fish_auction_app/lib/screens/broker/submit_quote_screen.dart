import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/broker_service_model.dart';
import '../../providers/broker_provider.dart';
import '../../widgets/common/loading_overlay.dart';

class SubmitQuoteScreen extends StatefulWidget {
  final ServiceRequest serviceRequest;

  const SubmitQuoteScreen({super.key, required this.serviceRequest});

  @override
  State<SubmitQuoteScreen> createState() => _SubmitQuoteScreenState();
}

class _SubmitQuoteScreenState extends State<SubmitQuoteScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _durationController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;

  @override
  void dispose() {
    _amountController.dispose();
    _durationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تقديم عرض'), backgroundColor: AppColors.primary, foregroundColor: Colors.white),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: _isLoading || brokerProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Service Request Info
                    _buildServiceRequestInfo(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Quote Form
                    _buildQuoteForm(),

                    const SizedBox(height: AppConstants.defaultPadding * 2),

                    // Submit Button
                    _buildSubmitButton(brokerProvider),

                    if (brokerProvider.error != null) ...[
                      const SizedBox(height: AppConstants.defaultPadding),
                      _buildErrorMessage(brokerProvider.error!),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildServiceRequestInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الطلب',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            widget.serviceRequest.service?.nameAr ?? 'خدمة غير محددة',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Text(
            'المزاد: ${widget.serviceRequest.auctionTitle ?? 'غير محدد'}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  widget.serviceRequest.locationDescription,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                ),
              ),
            ],
          ),
          if (widget.serviceRequest.specialInstructions.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'تعليمات: ${widget.serviceRequest.specialInstructions}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuoteForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('تفاصيل العرض', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: AppConstants.defaultPadding),

        // Amount
        TextFormField(
          controller: _amountController,
          decoration: const InputDecoration(
            labelText: 'المبلغ المطلوب (ريال) *',
            hintText: 'أدخل المبلغ بالريال السعودي',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.attach_money),
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال المبلغ';
            }
            final amount = double.tryParse(value);
            if (amount == null || amount <= 0) {
              return 'يرجى إدخال مبلغ صحيح';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Duration
        TextFormField(
          controller: _durationController,
          decoration: const InputDecoration(
            labelText: 'المدة المتوقعة *',
            hintText: 'مثال: ساعتان، يوم واحد، 3 ساعات',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.schedule),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال المدة المتوقعة';
            }
            return null;
          },
        ),

        const SizedBox(height: AppConstants.defaultPadding),

        // Notes
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات إضافية',
            hintText: 'أي معلومات إضافية تريد إضافتها...',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),

        const SizedBox(height: AppConstants.smallPadding),

        // Tips
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.lightbulb, color: AppColors.info, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'نصائح لعرض أفضل',
                    style: Theme.of(
                      context,
                    ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold, color: AppColors.info),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                '• حدد سعراً تنافسياً ومعقولاً\n• اذكر خبرتك في هذا النوع من الخدمات\n• كن واضحاً في تقدير المدة المطلوبة\n• أضف أي ضمانات أو خدمات إضافية',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.info),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BrokerProvider brokerProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _submitQuote(brokerProvider),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('تقديم العرض', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Text(error, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.error)),
    );
  }

  Future<void> _submitQuote(BrokerProvider brokerProvider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Safely parse the amount with error handling
    final amountText = _amountController.text.trim();
    final amount = double.tryParse(amountText);

    if (amount == null || amount <= 0) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال مبلغ صحيح'), backgroundColor: AppColors.error));
      return;
    }

    final quote = BrokerQuote(
      id: '',
      serviceRequestId: widget.serviceRequest.id,
      brokerId: '',
      amount: amount,
      estimatedDuration: _durationController.text.trim(),
      notes: _notesController.text.trim(),
      status: 'pending',
      createdAt: DateTime.now(),
      brokerRating: 0.0,
    );

    final success = await brokerProvider.createBrokerQuote(quote);

    setState(() {
      _isLoading = false;
    });

    if (success) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم تقديم العرض بنجاح'), backgroundColor: AppColors.success));
        Navigator.of(context).pop();
      }
    }
  }
}
