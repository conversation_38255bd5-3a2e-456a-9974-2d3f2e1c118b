import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/broker_service_model.dart';
import '../../providers/broker_provider.dart';
import '../../widgets/common/loading_overlay.dart';

class ClientServiceTrackingScreen extends StatefulWidget {
  const ClientServiceTrackingScreen({super.key});

  @override
  State<ClientServiceTrackingScreen> createState() => _ClientServiceTrackingScreenState();
}

class _ClientServiceTrackingScreenState extends State<ClientServiceTrackingScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadServiceExecutions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadServiceExecutions() async {
    final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
    await brokerProvider.loadServiceExecutions(isBroker: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تتبع الخدمات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadServiceExecutions, icon: const Icon(Icons.refresh))],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'النشطة'),
            Tab(text: 'المكتملة'),
            Tab(text: 'الكل'),
          ],
        ),
      ),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: brokerProvider.isLoading,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildExecutionsList(brokerProvider, 'active'),
                _buildExecutionsList(brokerProvider, 'completed'),
                _buildExecutionsList(brokerProvider, 'all'),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildExecutionsList(BrokerProvider brokerProvider, String filter) {
    if (brokerProvider.error != null) {
      return _buildErrorState(brokerProvider.error!);
    }

    List<ServiceExecution> executions;
    switch (filter) {
      case 'active':
        executions = brokerProvider.myServiceExecutions
            .where((exec) => exec.status == 'started' || exec.status == 'in_progress')
            .toList();
        break;
      case 'completed':
        executions = brokerProvider.myServiceExecutions
            .where((exec) => exec.status == 'completed' || exec.status == 'payment_released')
            .toList();
        break;
      default:
        executions = brokerProvider.myServiceExecutions;
    }

    if (executions.isEmpty) {
      return _buildEmptyState(filter);
    }

    return RefreshIndicator(
      onRefresh: _loadServiceExecutions,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: executions.length,
        itemBuilder: (context, index) {
          final execution = executions[index];
          return _buildExecutionCard(execution, brokerProvider);
        },
      ),
    );
  }

  Widget _buildExecutionCard(ServiceExecution execution, BrokerProvider brokerProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    execution.statusDisplayAr,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                _buildStatusChip(execution.status),
              ],
            ),

            const SizedBox(height: AppConstants.smallPadding),

            // Service info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.smallPadding),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.05),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'البروكر: ${execution.brokerName}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    'الخدمة: ${execution.serviceName}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                  ),
                  if (execution.startedAt != null)
                    Text(
                      'بدأ في: ${_formatDateTime(execution.startedAt!)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                    ),
                  if (execution.completedAt != null)
                    Text(
                      'اكتمل في: ${_formatDateTime(execution.completedAt!)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                    ),
                ],
              ),
            ),

            // Progress indicator
            const SizedBox(height: AppConstants.smallPadding),
            _buildProgressIndicator(execution),

            // Report
            if (execution.reportText.isNotEmpty || execution.reportImages.isNotEmpty) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.info.withOpacity(0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تقرير البروكر:',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500, color: AppColors.info),
                    ),
                    const SizedBox(height: 4),
                    if (execution.reportText.isNotEmpty) ...[
                      Text(execution.reportText, style: Theme.of(context).textTheme.bodySmall),
                      if (execution.reportImages.isNotEmpty) const SizedBox(height: AppConstants.smallPadding),
                    ],
                    // Report Images
                    if (execution.reportImages.isNotEmpty) ...[
                      Text(
                        'صور التقرير:',
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500, color: AppColors.info),
                      ),
                      const SizedBox(height: 4),
                      SizedBox(
                        height: 120,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: execution.reportImages.length,
                          itemBuilder: (context, index) {
                            final imageUrl = _getFullImageUrl(execution.reportImages[index]);
                            return Container(
                              margin: const EdgeInsets.only(right: AppConstants.smallPadding),
                              child: GestureDetector(
                                onTap: () => _showImageDialog(context, imageUrl),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                                  child: Image.network(
                                    imageUrl,
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 120,
                                        height: 120,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade300,
                                          borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                                        ),
                                        child: const Icon(Icons.broken_image, color: Colors.grey),
                                      );
                                    },
                                    loadingBuilder: (context, child, loadingProgress) {
                                      if (loadingProgress == null) return child;
                                      return Container(
                                        width: 120,
                                        height: 120,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade200,
                                          borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                                        ),
                                        child: const Center(child: CircularProgressIndicator()),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],

            // Actions
            if (execution.status == 'completed') ...[
              const SizedBox(height: AppConstants.defaultPadding),
              if (!execution.clientApproved) ...[
                // Show rating button if not approved yet
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _showFeedbackDialog(execution, brokerProvider),
                    icon: const Icon(Icons.star),
                    label: const Text('تقييم الخدمة والموافقة'),
                    style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary, foregroundColor: Colors.white),
                  ),
                ),
              ] else if (!execution.paymentReleased) ...[
                // Show payment button if approved but not paid
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _releasePayment(execution, brokerProvider),
                    icon: const Icon(Icons.payment),
                    label: const Text('تحويل المبلغ للبروكر'),
                    style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
                  ),
                ),
              ] else ...[
                // Show completed status
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    border: Border.all(color: AppColors.success.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: AppColors.success),
                      const SizedBox(width: AppConstants.smallPadding),
                      const Expanded(
                        child: Text(
                          'تم تحويل المبلغ بنجاح',
                          style: TextStyle(color: AppColors.success, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(ServiceExecution execution) {
    List<String> steps = ['started', 'in_progress', 'completed', 'payment_released'];
    List<String> stepLabels = ['بدأ التنفيذ', 'قيد التنفيذ', 'مكتمل', 'تم التحويل'];

    int currentStep = steps.indexOf(execution.status);
    if (currentStep == -1) currentStep = 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('حالة التقدم:', style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500)),
        const SizedBox(height: AppConstants.smallPadding),
        Row(
          children: List.generate(steps.length, (index) {
            bool isCompleted = index <= currentStep;
            bool isCurrent = index == currentStep;

            return Expanded(
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isCompleted ? AppColors.success : Colors.grey.withOpacity(0.3),
                      shape: BoxShape.circle,
                      border: isCurrent ? Border.all(color: AppColors.primary, width: 2) : null,
                    ),
                    child: isCompleted
                        ? const Icon(Icons.check, color: Colors.white, size: 16)
                        : Text(
                            '${index + 1}',
                            style: const TextStyle(color: Colors.white, fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                  ),
                  if (index < steps.length - 1)
                    Expanded(
                      child: Container(
                        height: 2,
                        color: isCompleted ? AppColors.success : Colors.grey.withOpacity(0.3),
                      ),
                    ),
                ],
              ),
            );
          }),
        ),
        const SizedBox(height: 4),
        Row(
          children: List.generate(stepLabels.length, (index) {
            return Expanded(
              child: Text(
                stepLabels[index],
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  color: index <= currentStep ? AppColors.success : AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;

    switch (status) {
      case 'started':
        color = AppColors.info;
        text = 'بدأ التنفيذ';
        break;
      case 'in_progress':
        color = AppColors.warning;
        text = 'قيد التنفيذ';
        break;
      case 'completed':
        color = AppColors.success;
        text = 'مكتمل';
        break;
      case 'client_approved':
        color = AppColors.success;
        text = 'موافقة العميل';
        break;
      case 'payment_released':
        color = AppColors.primary;
        text = 'تم التحويل';
        break;
      default:
        color = AppColors.textSecondary;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildEmptyState(String filter) {
    String title, subtitle;
    IconData icon;

    switch (filter) {
      case 'active':
        icon = Icons.work_outline;
        title = 'لا توجد خدمات نشطة';
        subtitle = 'الخدمات النشطة ستظهر هنا';
        break;
      case 'completed':
        icon = Icons.check_circle_outline;
        title = 'لا توجد خدمات مكتملة';
        subtitle = 'الخدمات المكتملة ستظهر هنا';
        break;
      default:
        icon = Icons.assignment_outlined;
        title = 'لا توجد خدمات';
        subtitle = 'خدماتك ستظهر هنا';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppConstants.defaultPadding),
          Text('حدث خطأ', style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.error)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(onPressed: _loadServiceExecutions, child: const Text('إعادة المحاولة')),
        ],
      ),
    );
  }

  void _showFeedbackDialog(ServiceExecution execution, BrokerProvider brokerProvider) {
    final feedbackController = TextEditingController();
    int rating = 5;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('تقييم الخدمة والموافقة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.info, color: AppColors.info, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تقييمك يعني موافقتك على الخدمة وسيتيح لك تحويل المبلغ',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.info),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text('كيف تقيم جودة الخدمة المقدمة؟', style: Theme.of(context).textTheme.bodyMedium),
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () => setState(() => rating = index + 1),
                    icon: Icon(index < rating ? Icons.star : Icons.star_border, color: AppColors.warning, size: 32),
                  );
                }),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              TextField(
                controller: feedbackController,
                decoration: const InputDecoration(
                  labelText: 'تعليقك (اختياري)',
                  hintText: 'اكتب تعليقك على الخدمة...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await brokerProvider.submitClientFeedback(execution.id, feedbackController.text, rating);
              },
              child: const Text('تقييم وموافقة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _releasePayment(ServiceExecution execution, BrokerProvider brokerProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تحويل المبلغ'),
        content: const Text(
          'هل تريد تحويل المبلغ للبروكر؟\n\nسيتم تحويل المبلغ فوراً ولا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
            child: const Text('تحويل المبلغ'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await brokerProvider.releasePayment(execution.id);

      if (success && mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم تحويل المبلغ بنجاح'), backgroundColor: AppColors.success));
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showImageDialog(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
                      child: const Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.broken_image, size: 64, color: Colors.grey),
                          SizedBox(height: 8),
                          Text('فشل في تحميل الصورة'),
                        ],
                      ),
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8)),
                      child: const Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [CircularProgressIndicator(), SizedBox(height: 8), Text('جاري تحميل الصورة...')],
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white, size: 30),
                style: IconButton.styleFrom(backgroundColor: Colors.black54, shape: const CircleBorder()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper function to construct full image URL from relative path
  String _getFullImageUrl(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      // Already a full URL
      return imageUrl;
    } else if (imageUrl.startsWith('/')) {
      // Relative path starting with /
      return '${AppConstants.baseUrl.replaceAll('/api', '')}$imageUrl';
    } else {
      // Relative path without leading /
      return '${AppConstants.baseUrl.replaceAll('/api', '')}/$imageUrl';
    }
  }
}
