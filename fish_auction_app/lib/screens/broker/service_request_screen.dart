import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../models/broker_service_model.dart';
import '../../providers/broker_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../l10n/generated/app_localizations.dart';

class ServiceRequestScreen extends StatefulWidget {
  final Auction auction;

  const ServiceRequestScreen({super.key, required this.auction});

  @override
  State<ServiceRequestScreen> createState() => _ServiceRequestScreenState();
}

class _ServiceRequestScreenState extends State<ServiceRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _locationController = TextEditingController();
  final _instructionsController = TextEditingController();

  BrokerService? _selectedService;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadBrokerServices();
  }

  @override
  void dispose() {
    _locationController.dispose();
    _instructionsController.dispose();
    super.dispose();
  }

  Future<void> _loadBrokerServices() async {
    final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
    await brokerProvider.loadBrokerServices();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب خدمة بروكر'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: _isLoading || brokerProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Auction Info
                    _buildAuctionInfo(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Service Selection
                    _buildServiceSelection(brokerProvider),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Location Input
                    _buildLocationInput(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Special Instructions
                    _buildInstructionsInput(),

                    const SizedBox(height: AppConstants.defaultPadding * 2),

                    // Submit Button
                    _buildSubmitButton(brokerProvider),

                    if (brokerProvider.error != null) ...[
                      const SizedBox(height: AppConstants.defaultPadding),
                      _buildErrorMessage(brokerProvider.error!),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAuctionInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل المزاد',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            widget.auction.title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Text(
            'السعر الحالي: \$${widget.auction.currentPrice.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            'يبدأ في: ${_formatDateTime(widget.auction.startTime)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceSelection(BrokerProvider brokerProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الخدمة المطلوبة *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        if (brokerProvider.brokerServices.isEmpty)
          const Center(child: CircularProgressIndicator())
        else
          ...brokerProvider.brokerServices.map((service) => _buildServiceOption(service)),
      ],
    );
  }

  Widget _buildServiceOption(BrokerService service) {
    final isSelected = _selectedService?.id == service.id;

    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedService = service;
          });
        },
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.grey.withOpacity(0.05),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(
              color: isSelected ? AppColors.primary : Colors.grey.withOpacity(0.3),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? AppColors.primary : Colors.grey,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service.nameAr,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.primary : null,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      service.descriptionAr,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('وصف الموقع *', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: AppConstants.smallPadding),
        TextFormField(
          controller: _locationController,
          decoration: const InputDecoration(hintText: 'اكتب وصف مفصل للموقع...', border: OutlineInputBorder()),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال وصف الموقع';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildInstructionsInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('تعليمات خاصة', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: AppConstants.smallPadding),
        TextFormField(
          controller: _instructionsController,
          decoration: const InputDecoration(hintText: 'أي تعليمات إضافية للبروكر...', border: OutlineInputBorder()),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BrokerProvider brokerProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _selectedService != null ? () => _submitRequest(brokerProvider) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('إرسال طلب الخدمة', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Text(error, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.error)),
    );
  }

  Future<void> _submitRequest(BrokerProvider brokerProvider) async {
    if (!_formKey.currentState!.validate() || _selectedService == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final request = ServiceRequest(
      id: '',
      clientId: '',
      auctionId: widget.auction.id.toString(),
      serviceId: _selectedService!.id,
      locationDescription: _locationController.text.trim(),
      latitude: 0.0, // Default coordinates - can be updated later
      longitude: 0.0, // Default coordinates - can be updated later
      specialInstructions: _instructionsController.text.trim(),
      status: 'pending',
      paymentHeld: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final success = await brokerProvider.createServiceRequest(request);

    setState(() {
      _isLoading = false;
    });

    if (success) {
      // Refresh the requests list to update the UI
      await brokerProvider.loadMyServiceRequests();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم إرسال طلب الخدمة بنجاح'), backgroundColor: AppColors.success));
        Navigator.of(context).pop();
      }
    } else {
      // Show error message if request failed
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(brokerProvider.error ?? 'فشل في إرسال طلب الخدمة'), backgroundColor: AppColors.error),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
