import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../providers/broker_provider.dart';
import '../../widgets/common/loading_overlay.dart';

class BrokerProfileSetupScreen extends StatefulWidget {
  const BrokerProfileSetupScreen({super.key});

  @override
  State<BrokerProfileSetupScreen> createState() => _BrokerProfileSetupScreenState();
}

class _BrokerProfileSetupScreenState extends State<BrokerProfileSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serviceAreasController = TextEditingController();
  final _maxServicesController = TextEditingController(text: '5');

  bool _isAvailable = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _serviceAreasController.dispose();
    _maxServicesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعداد ملف الوسيط'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: _isLoading || brokerProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    _buildHeader(),

                    const SizedBox(height: AppConstants.defaultPadding * 2),

                    // Service Areas
                    _buildServiceAreasField(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Max Concurrent Services
                    _buildMaxServicesField(),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Availability Toggle
                    _buildAvailabilityToggle(),

                    const SizedBox(height: AppConstants.defaultPadding * 2),

                    // Save Button
                    _buildSaveButton(brokerProvider),

                    if (brokerProvider.error != null) ...[
                      const SizedBox(height: AppConstants.defaultPadding),
                      _buildErrorMessage(brokerProvider.error!),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(Icons.person_pin_circle, size: 48, color: AppColors.primary),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'إعداد ملف الوسيط',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'قم بإعداد ملفك الشخصي كوسيط لتتمكن من تقديم العروض',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceAreasField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المناطق التي تخدمها *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        TextFormField(
          controller: _serviceAreasController,
          decoration: const InputDecoration(hintText: 'مثال: الرياض، جدة، الدمام', prefixIcon: Icon(Icons.location_on)),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال المناطق التي تخدمها';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildMaxServicesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الحد الأقصى للخدمات المتزامنة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        TextFormField(
          controller: _maxServicesController,
          decoration: const InputDecoration(hintText: '5', prefixIcon: Icon(Icons.work)),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال الحد الأقصى للخدمات';
            }
            final number = int.tryParse(value.trim());
            if (number == null || number < 1 || number > 20) {
              return 'يرجى إدخال رقم بين 1 و 20';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildAvailabilityToggle() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(Icons.access_time, color: AppColors.primary),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'متاح لتلقي طلبات جديدة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  'يمكنك تغيير هذا الإعداد لاحقاً',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
          Switch(
            value: _isAvailable,
            onChanged: (value) {
              setState(() {
                _isAvailable = value;
              });
            },
            activeColor: AppColors.success,
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton(BrokerProvider brokerProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _saveBrokerProfile(brokerProvider),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: AppConstants.defaultPadding),
        ),
        child: const Text('حفظ الملف الشخصي', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppColors.error),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(error, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.error)),
          ),
        ],
      ),
    );
  }

  Future<void> _saveBrokerProfile(BrokerProvider brokerProvider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // TODO: Implement broker profile save API call
    // For now, just simulate success
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('تم حفظ الملف الشخصي بنجاح'), backgroundColor: AppColors.success));
      Navigator.of(context).pop();
    }
  }
}
