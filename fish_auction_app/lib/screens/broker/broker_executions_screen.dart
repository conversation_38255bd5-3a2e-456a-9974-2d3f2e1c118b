import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/broker_service_model.dart';
import '../../providers/broker_provider.dart';
import '../../widgets/common/loading_overlay.dart';

class BrokerExecutionsScreen extends StatefulWidget {
  const BrokerExecutionsScreen({super.key});

  @override
  State<BrokerExecutionsScreen> createState() => _BrokerExecutionsScreenState();
}

class _BrokerExecutionsScreenState extends State<BrokerExecutionsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadExecutions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadExecutions() async {
    final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
    await brokerProvider.loadServiceExecutions(isBroker: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('خدماتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadExecutions, icon: const Icon(Icons.refresh))],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'النشطة'),
            Tab(text: 'المكتملة'),
            Tab(text: 'الكل'),
          ],
        ),
      ),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: brokerProvider.isLoading,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildExecutionsList(brokerProvider, 'active'),
                _buildExecutionsList(brokerProvider, 'completed'),
                _buildExecutionsList(brokerProvider, 'all'),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildExecutionsList(BrokerProvider brokerProvider, String filter) {
    if (brokerProvider.error != null) {
      return _buildErrorState(brokerProvider.error!);
    }

    List<ServiceExecution> executions;
    switch (filter) {
      case 'active':
        executions = brokerProvider.myServiceExecutions
            .where((exec) => exec.status == 'started' || exec.status == 'in_progress')
            .toList();
        break;
      case 'completed':
        executions = brokerProvider.myServiceExecutions
            .where((exec) => exec.status == 'completed' || exec.status == 'payment_released')
            .toList();
        break;
      default:
        executions = brokerProvider.myServiceExecutions;
    }

    if (executions.isEmpty) {
      return _buildEmptyState(filter);
    }

    return RefreshIndicator(
      onRefresh: _loadExecutions,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: executions.length,
        itemBuilder: (context, index) {
          final execution = executions[index];
          return _buildExecutionCard(execution, brokerProvider);
        },
      ),
    );
  }

  Widget _buildExecutionCard(ServiceExecution execution, BrokerProvider brokerProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    execution.statusDisplayAr,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                _buildStatusChip(execution.status),
              ],
            ),

            const SizedBox(height: AppConstants.smallPadding),

            // Service info
            Text(
              'الخدمة: ${execution.serviceName}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),

            Text(
              'المزاد: ${execution.auctionTitle}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),

            const SizedBox(height: 4),

            // Dates
            if (execution.startedAt != null)
              Text(
                'بدأ في: ${_formatDateTime(execution.startedAt!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              ),

            if (execution.completedAt != null)
              Text(
                'اكتمل في: ${_formatDateTime(execution.completedAt!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              ),

            // Report
            if (execution.reportText.isNotEmpty) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'التقرير:',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
                    ),
                    Text(execution.reportText, style: Theme.of(context).textTheme.bodySmall),
                  ],
                ),
              ),
            ],

            // Actions
            if (execution.status == 'started' || execution.status == 'in_progress') ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _updateStatus(execution, brokerProvider),
                      child: const Text('تحديث الحالة'),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _submitReport(execution, brokerProvider),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إرسال تقرير'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;

    switch (status) {
      case 'started':
        color = AppColors.info;
        text = 'بدأ التنفيذ';
        break;
      case 'in_progress':
        color = AppColors.warning;
        text = 'قيد التنفيذ';
        break;
      case 'completed':
        color = AppColors.success;
        text = 'مكتمل';
        break;
      case 'client_approved':
        color = AppColors.success;
        text = 'موافقة العميل';
        break;
      case 'payment_released':
        color = AppColors.primary;
        text = 'تم تحويل المبلغ';
        break;
      default:
        color = AppColors.textSecondary;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildEmptyState(String filter) {
    String title, subtitle;
    IconData icon;

    switch (filter) {
      case 'active':
        icon = Icons.work_outline;
        title = 'لا توجد خدمات نشطة';
        subtitle = 'الخدمات النشطة ستظهر هنا';
        break;
      case 'completed':
        icon = Icons.check_circle_outline;
        title = 'لا توجد خدمات مكتملة';
        subtitle = 'الخدمات المكتملة ستظهر هنا';
        break;
      default:
        icon = Icons.assignment_outlined;
        title = 'لا توجد خدمات';
        subtitle = 'خدماتك ستظهر هنا';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppConstants.defaultPadding),
          Text('حدث خطأ', style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.error)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(onPressed: _loadExecutions, child: const Text('إعادة المحاولة')),
        ],
      ),
    );
  }

  void _updateStatus(ServiceExecution execution, BrokerProvider brokerProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الخدمة'),
        content: const Text('هل تريد تحديث حالة الخدمة إلى "قيد التنفيذ"؟'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // Get the current execution from provider to ensure we have the latest data
              final currentExecution = brokerProvider.myServiceExecutions.firstWhere(
                (exec) => exec.id == execution.id,
                orElse: () => execution, // Fallback to original if not found
              );
              await brokerProvider.updateServiceExecution(currentExecution.id, {'status': 'in_progress'});
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  void _submitReport(ServiceExecution execution, BrokerProvider brokerProvider) {
    // Get the current execution from provider to ensure we have the latest data
    final currentExecution = brokerProvider.myServiceExecutions.firstWhere(
      (exec) => exec.id == execution.id,
      orElse: () => execution, // Fallback to original if not found
    );

    final reportController = TextEditingController(text: currentExecution.reportText);
    File? selectedImage;

    // Debug: Print execution ID
    print('🔍 Submitting report for execution ID: "${currentExecution.id}"');

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إرسال تقرير الخدمة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: reportController,
                  decoration: const InputDecoration(
                    labelText: 'تقرير الخدمة',
                    hintText: 'اكتب تقرير مفصل عن الخدمة المنجزة...',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 4,
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Image upload section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Text('صورة تأكيد إنجاز العمل', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: AppConstants.smallPadding),
                      if (selectedImage != null) ...[
                        Container(
                          height: 150,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(image: FileImage(selectedImage!), fit: BoxFit.cover),
                          ),
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            TextButton.icon(
                              onPressed: () => _pickImage(ImageSource.camera, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              }),
                              icon: const Icon(Icons.camera_alt),
                              label: const Text('تغيير الصورة'),
                            ),
                            TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  selectedImage = null;
                                });
                              },
                              icon: const Icon(Icons.delete, color: Colors.red),
                              label: const Text('حذف الصورة', style: TextStyle(color: Colors.red)),
                            ),
                          ],
                        ),
                      ] else ...[
                        const Icon(Icons.image, size: 50, color: Colors.grey),
                        const SizedBox(height: AppConstants.smallPadding),
                        const Text('أضف صورة لتأكيد إنجاز العمل', style: TextStyle(color: Colors.grey)),
                        const SizedBox(height: AppConstants.smallPadding),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ElevatedButton.icon(
                              onPressed: () => _pickImage(ImageSource.camera, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              }),
                              icon: const Icon(Icons.camera_alt),
                              label: const Text('كاميرا'),
                            ),
                            ElevatedButton.icon(
                              onPressed: () => _pickImage(ImageSource.gallery, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              }),
                              icon: const Icon(Icons.photo_library),
                              label: const Text('المعرض'),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                // Debug: Check execution ID before API call
                if (currentExecution.id.isEmpty) {
                  print('❌ ERROR: Execution ID is empty!');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('خطأ: معرف التنفيذ مفقود'), backgroundColor: AppColors.error),
                  );
                  return;
                }

                // Prepare update data
                final updateData = {'status': 'completed', 'report_text': reportController.text};

                // If image is selected, upload it first and add to report_images
                if (selectedImage != null) {
                  await brokerProvider.updateServiceExecutionWithImage(currentExecution.id, updateData, selectedImage!);
                } else {
                  await brokerProvider.updateServiceExecution(currentExecution.id, updateData);
                }
              },
              child: const Text('إرسال'),
            ),
          ],
        ),
      ),
    );
  }

  // Image picker method
  Future<void> _pickImage(ImageSource source, Function(File?) onImagePicked) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: source, maxWidth: 1024, maxHeight: 1024, imageQuality: 80);

      if (image != null) {
        onImagePicked(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في اختيار الصورة: ${e.toString()}'), backgroundColor: AppColors.error),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
