import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/broker_service_model.dart';
import '../../providers/broker_provider.dart';
import '../../widgets/common/loading_overlay.dart';
import 'service_quotes_screen.dart';

class MyServiceRequestsScreen extends StatefulWidget {
  final String? filterAuctionId;
  final String? auctionTitle;

  const MyServiceRequestsScreen({super.key, this.filterAuctionId, this.auctionTitle});

  @override
  State<MyServiceRequestsScreen> createState() => _MyServiceRequestsScreenState();
}

class _MyServiceRequestsScreenState extends State<MyServiceRequestsScreen> {
  @override
  void initState() {
    super.initState();
    _loadServiceRequests();
  }

  Future<void> _loadServiceRequests() async {
    final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
    await brokerProvider.loadMyServiceRequests();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.filterAuctionId != null ? 'طلبات الخدمات - ${widget.auctionTitle ?? 'المزاد'}' : 'طلبات الخدمات',
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadServiceRequests, icon: const Icon(Icons.refresh))],
      ),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: brokerProvider.isLoading,
            child: RefreshIndicator(onRefresh: _loadServiceRequests, child: _buildContent(brokerProvider)),
          );
        },
      ),
    );
  }

  Widget _buildContent(BrokerProvider brokerProvider) {
    if (brokerProvider.error != null) {
      return _buildErrorState(brokerProvider.error!);
    }

    // Filter requests by auction ID if provided
    final requests = widget.filterAuctionId != null
        ? brokerProvider.myServiceRequests
              .where((req) => req.auctionId.toString() == widget.filterAuctionId.toString())
              .toList()
        : brokerProvider.myServiceRequests;

    if (requests.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: requests.length,
      itemBuilder: (context, index) {
        final request = requests[index];
        return _buildServiceRequestCard(request);
      },
    );
  }

  Widget _buildServiceRequestCard(ServiceRequest request) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _viewServiceRequestDetails(request),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Text(
                      request.service?.nameAr ?? 'خدمة غير محددة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                  _buildStatusChip(request.status),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // Auction info
              Text(
                'المزاد: ${request.auctionTitle ?? 'غير محدد'}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),

              const SizedBox(height: 4),

              // Location
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      request.locationDescription,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // Footer
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    _formatDateTime(request.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                  ),
                  const Spacer(),
                  if (request.quotes.isNotEmpty)
                    Text(
                      '${request.quotes.length} عرض',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: AppColors.primary, fontWeight: FontWeight.w500),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;

    switch (status) {
      case 'pending':
        color = AppColors.warning;
        text = 'في انتظار العروض';
        break;
      case 'quotes_received':
        color = AppColors.info;
        text = 'تم استلام العروض';
        break;
      case 'broker_selected':
        color = AppColors.success;
        text = 'تم اختيار البروكر';
        break;
      case 'payment_held':
        color = AppColors.primary;
        text = 'تم حجز المبلغ';
        break;
      case 'in_progress':
        color = AppColors.info;
        text = 'قيد التنفيذ';
        break;
      case 'completed':
        color = AppColors.success;
        text = 'مكتملة';
        break;
      case 'cancelled':
        color = AppColors.error;
        text = 'ملغية';
        break;
      default:
        color = AppColors.textSecondary;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.support_agent_outlined, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا توجد طلبات خدمات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'طلبات الخدمات التي تقوم بإنشائها ستظهر هنا',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppConstants.defaultPadding),
          Text('حدث خطأ', style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.error)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(onPressed: _loadServiceRequests, child: const Text('إعادة المحاولة')),
        ],
      ),
    );
  }

  void _viewServiceRequestDetails(ServiceRequest request) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => ServiceQuotesScreen(serviceRequest: request)));
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
