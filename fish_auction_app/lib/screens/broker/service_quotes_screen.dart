import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/broker_service_model.dart';
import '../../providers/broker_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/payment_service.dart';
import '../../widgets/common/loading_overlay.dart';

class ServiceQuotesScreen extends StatefulWidget {
  final ServiceRequest serviceRequest;

  const ServiceQuotesScreen({super.key, required this.serviceRequest});

  @override
  State<ServiceQuotesScreen> createState() => _ServiceQuotesScreenState();
}

class _ServiceQuotesScreenState extends State<ServiceQuotesScreen> {
  final PaymentService _paymentService = PaymentService();

  @override
  void initState() {
    super.initState();
    _loadQuotes();
  }

  Future<void> _loadQuotes() async {
    final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
    await brokerProvider.loadServiceRequestQuotes(widget.serviceRequest.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('عروض البروكرز'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadQuotes, icon: const Icon(Icons.refresh))],
      ),
      body: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          return LoadingOverlay(
            isLoading: brokerProvider.isLoading,
            child: Column(
              children: [
                // Service Request Info
                _buildServiceRequestInfo(),

                // Quotes List
                Expanded(child: _buildQuotesList(brokerProvider)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildServiceRequestInfo() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.serviceRequest.service?.nameAr ?? 'خدمة غير محددة',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'المزاد: ${widget.serviceRequest.auctionTitle ?? 'غير محدد'}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  widget.serviceRequest.locationDescription,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                ),
              ),
            ],
          ),
          if (widget.serviceRequest.specialInstructions.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'تعليمات: ${widget.serviceRequest.specialInstructions}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuotesList(BrokerProvider brokerProvider) {
    if (brokerProvider.error != null) {
      return _buildErrorState(brokerProvider.error!);
    }

    if (brokerProvider.currentQuotes.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      itemCount: brokerProvider.currentQuotes.length,
      itemBuilder: (context, index) {
        final quote = brokerProvider.currentQuotes[index];
        return _buildQuoteCard(quote, brokerProvider);
      },
    );
  }

  Widget _buildQuoteCard(BrokerQuote quote, BrokerProvider brokerProvider) {
    final isSelected = widget.serviceRequest.selectedBrokerId == quote.brokerId;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: isSelected ? Border.all(color: AppColors.success, width: 2) : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Broker Info
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    child: Text(
                      quote.brokerName[0].toUpperCase(),
                      style: const TextStyle(color: AppColors.primary, fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          quote.brokerName,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Row(
                          children: [
                            ...List.generate(5, (index) {
                              return Icon(
                                index < quote.brokerRating.floor() ? Icons.star : Icons.star_border,
                                size: 16,
                                color: AppColors.warning,
                              );
                            }),
                            const SizedBox(width: 4),
                            Text(
                              '(${quote.brokerRating.toStringAsFixed(1)})',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.success.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.success.withOpacity(0.3)),
                      ),
                      child: Text(
                        'مختار',
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(color: AppColors.success, fontWeight: FontWeight.w500),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Quote Details
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'السعر',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                              ),
                              Text(
                                '${quote.amount.toStringAsFixed(0)} ريال',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'المدة المتوقعة',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                              ),
                              Text(
                                quote.estimatedDuration,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    if (quote.notes.isNotEmpty) ...[
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        'ملاحظات:',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                      ),
                      Text(quote.notes, style: Theme.of(context).textTheme.bodyMedium),
                    ],
                  ],
                ),
              ),

              // Action Button - Show if not selected and quotes are available
              if (!isSelected && brokerProvider.currentQuotes.isNotEmpty) ...[
                const SizedBox(height: AppConstants.defaultPadding),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _selectBroker(quote, brokerProvider),
                    style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary, foregroundColor: Colors.white),
                    child: const Text('اختيار هذا البروكر'),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.hourglass_empty, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا توجد عروض بعد',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'سيتم إشعارك عند وصول عروض من البروكرز',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: AppConstants.defaultPadding),
          Text('حدث خطأ', style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.error)),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(onPressed: _loadQuotes, child: const Text('إعادة المحاولة')),
        ],
      ),
    );
  }

  Future<void> _selectBroker(BrokerQuote quote, BrokerProvider brokerProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاختيار'),
        content: Text(
          'هل تريد اختيار ${quote.brokerName} لتنفيذ الخدمة بمبلغ ${quote.amount.toStringAsFixed(0)} ريال؟\n\nسيتم حجز المبلغ من محفظتك.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('إلغاء')),
          ElevatedButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('تأكيد')),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await brokerProvider.selectBroker(widget.serviceRequest.id, quote.id);

      if (success && mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم اختيار البروكر بنجاح'), backgroundColor: AppColors.success));
        Navigator.of(context).pop();
      } else if (mounted && brokerProvider.error != null) {
        // Check if it's insufficient balance error
        if (brokerProvider.error!.contains('Insufficient wallet balance') ||
            brokerProvider.error!.contains('رصيد المحفظة غير كافي')) {
          _showInsufficientBalanceDialog(quote);
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(brokerProvider.error!), backgroundColor: AppColors.error));
        }
      }
    }
  }

  void _showInsufficientBalanceDialog(BrokerQuote quote) async {
    // Get current wallet balance
    double walletBalance = 0.0;
    try {
      final response = await _paymentService.getWalletBalance();
      if (response.isSuccess) {
        walletBalance = response.data!;
      }
    } catch (e) {
      // Handle error getting balance
    }

    if (!mounted) return;

    final requiredAmount = quote.amount - walletBalance;
    final suggestedAmount = (requiredAmount + 10).ceilToDouble();
    final amountController = TextEditingController(text: suggestedAmount.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.account_balance_wallet, color: AppColors.error),
            SizedBox(width: 8),
            Text('رصيد غير كافي'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'رصيدك الحالي: ${walletBalance.toStringAsFixed(2)} ريال\n'
              'المبلغ المطلوب: ${quote.amount.toStringAsFixed(2)} ريال\n'
              'تحتاج إلى ${requiredAmount.toStringAsFixed(2)} ريال إضافي',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'المبلغ المراد إضافته',
                suffixText: 'ريال',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processAddFunds(amountController.text, quote);
            },
            child: const Text('إضافة رصيد'),
          ),
        ],
      ),
    );
  }

  Future<void> _processAddFunds(String amount, BrokerQuote quote) async {
    final double? amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال مبلغ صحيح'), backgroundColor: AppColors.error));
      return;
    }

    try {
      final response = await _paymentService.topUpWalletWithStripe(amount: amountValue, context: context);

      if (response.isSuccess) {
        if (!mounted) return;

        // Refresh auth provider balance
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        await authProvider.refreshProfile();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الرصيد بنجاح! يمكنك الآن اختيار البروكر.'),
            backgroundColor: AppColors.success,
          ),
        );

        // Try selecting broker again
        final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
        final success = await brokerProvider.selectBroker(widget.serviceRequest.id, quote.id);

        if (success && mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم اختيار البروكر بنجاح'), backgroundColor: AppColors.success));
          Navigator.of(context).pop();
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(response.message ?? 'فشل في إضافة الرصيد'), backgroundColor: AppColors.error),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إضافة الرصيد: ${e.toString()}'), backgroundColor: AppColors.error),
        );
      }
    }
  }
}
