import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../widgets/common/loading_overlay.dart';

class LocationHistoryScreen extends StatefulWidget {
  const LocationHistoryScreen({super.key});

  @override
  State<LocationHistoryScreen> createState() => _LocationHistoryScreenState();
}

class _LocationHistoryScreenState extends State<LocationHistoryScreen> {
  bool _isLoading = false;
  List<LocationHistoryItem> _locationHistory = [];

  @override
  void initState() {
    super.initState();
    _loadLocationHistory();
  }

  Future<void> _loadLocationHistory() async {
    setState(() => _isLoading = true);
    
    try {
      // TODO: Load location history from API
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for demonstration
      setState(() {
        _locationHistory = [
          LocationHistoryItem(
            id: '1',
            auctionTitle: 'Fresh Red Snapper - Premium Quality',
            location: 'Dubai Marina, UAE',
            latitude: 25.0772,
            longitude: 55.1395,
            timestamp: DateTime.now().subtract(const Duration(hours: 2)),
            duration: const Duration(hours: 3),
            status: 'Completed',
          ),
          LocationHistoryItem(
            id: '2',
            auctionTitle: 'Wild Caught Hammour',
            location: 'Jumeirah Beach, Dubai',
            latitude: 25.2048,
            longitude: 55.2708,
            timestamp: DateTime.now().subtract(const Duration(days: 1)),
            duration: const Duration(hours: 2, minutes: 30),
            status: 'Completed',
          ),
          LocationHistoryItem(
            id: '3',
            auctionTitle: 'Fresh Kingfish Catch',
            location: 'Al Mamzar Beach, Dubai',
            latitude: 25.2867,
            longitude: 55.3458,
            timestamp: DateTime.now().subtract(const Duration(days: 3)),
            duration: const Duration(hours: 4),
            status: 'Completed',
          ),
        ];
      });
    } catch (e) {
      _showErrorSnackBar('Failed to load location history');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Location History'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadLocationHistory,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: _locationHistory.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.location_history,
                      size: 64,
                      color: AppColors.textHint,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Text(
                      'No Location History',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textHint,
                      ),
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Text(
                      'Your location sharing history will appear here when you enable location sharing for auctions.',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              )
            : RefreshIndicator(
                onRefresh: _loadLocationHistory,
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  itemCount: _locationHistory.length,
                  itemBuilder: (context, index) {
                    final item = _locationHistory[index];
                    return _buildLocationHistoryCard(item);
                  },
                ),
              ),
      ),
    );
  }

  Widget _buildLocationHistoryCard(LocationHistoryItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with auction title and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    item.auctionTitle,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.smallPadding,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(item.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getStatusColor(item.status).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    item.status,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(item.status),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // Location info
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: AppColors.primary),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    item.location,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // Coordinates
            Row(
              children: [
                const Icon(Icons.gps_fixed, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  'Lat: ${item.latitude.toStringAsFixed(4)}, Lng: ${item.longitude.toStringAsFixed(4)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // Timestamp and duration
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  _formatDateTime(item.timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                const Icon(Icons.timer, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  _formatDuration(item.duration),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewOnMap(item),
                    icon: const Icon(Icons.map, size: 16),
                    label: const Text('View on Map'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _shareLocation(item),
                    icon: const Icon(Icons.share, size: 16),
                    label: const Text('Share'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppColors.success;
      case 'active':
        return AppColors.primary;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      return '${hours}h ${minutes}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  void _viewOnMap(LocationHistoryItem item) {
    // TODO: Open map with location
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${item.location} on map...'),
      ),
    );
  }

  void _shareLocation(LocationHistoryItem item) {
    // TODO: Share location
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing location for ${item.auctionTitle}...'),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}

class LocationHistoryItem {
  final String id;
  final String auctionTitle;
  final String location;
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final Duration duration;
  final String status;

  LocationHistoryItem({
    required this.id,
    required this.auctionTitle,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    required this.duration,
    required this.status,
  });
}
