import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../services/location_service.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/common/custom_button.dart';

class LocationSettingsScreen extends StatefulWidget {
  const LocationSettingsScreen({super.key});

  @override
  State<LocationSettingsScreen> createState() => _LocationSettingsScreenState();
}

class _LocationSettingsScreenState extends State<LocationSettingsScreen> {
  final LocationService _locationService = LocationService();
  bool _isLoading = false;
  bool _isLocationSharingEnabled = false;
  bool _autoLocationEnabled = false;
  String? _currentLocation;

  @override
  void initState() {
    super.initState();
    _loadLocationSettings();
  }

  Future<void> _loadLocationSettings() async {
    setState(() => _isLoading = true);
    
    try {
      // TODO: Load user's location settings from API
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _isLocationSharingEnabled = false; // TODO: Get from API
        _autoLocationEnabled = false; // TODO: Get from API
      });
    } catch (e) {
      _showErrorSnackBar('Failed to load location settings');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Settings'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Location Sharing Section
              _buildSectionHeader('Location Sharing'),
              _buildLocationSharingCard(),
              
              const SizedBox(height: AppConstants.largePadding),
              
              // Auto-Location Section
              _buildSectionHeader('Auto-Location Detection'),
              _buildAutoLocationCard(),
              
              const SizedBox(height: AppConstants.largePadding),
              
              // Current Location Section
              _buildSectionHeader('Current Location'),
              _buildCurrentLocationCard(),
              
              const SizedBox(height: AppConstants.largePadding),
              
              // Location Permissions Section
              _buildSectionHeader('Permissions'),
              _buildPermissionsCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLocationSharingCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLocationSharingEnabled ? Icons.location_on : Icons.location_off,
                  color: _isLocationSharingEnabled ? AppColors.success : AppColors.warning,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'Live Location Sharing',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Switch(
                  value: _isLocationSharingEnabled,
                  onChanged: _toggleLocationSharing,
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              _isLocationSharingEnabled
                  ? 'Your location will be shared with buyers during live auctions to verify fishing locations.'
                  : 'Enable location sharing to allow buyers to see your real-time location during auctions.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _autoLocationEnabled ? Icons.gps_fixed : Icons.gps_off,
                  color: _autoLocationEnabled ? AppColors.success : AppColors.textSecondary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'Auto-Location Detection',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Switch(
                  value: _autoLocationEnabled,
                  onChanged: _toggleAutoLocation,
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              _autoLocationEnabled
                  ? 'Your location will be automatically detected and updated when you create new auctions.'
                  : 'Enable auto-detection to automatically set your location when creating auctions.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            if (_autoLocationEnabled) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              PrimaryButton(
                text: 'Test Current Location',
                onPressed: _testCurrentLocation,
                icon: Icons.location_searching,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.my_location, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Current Location',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (_currentLocation != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                ),
                child: Text(
                  _currentLocation!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
            ],
            Row(
              children: [
                Expanded(
                  child: SecondaryButton(
                    text: 'Get Current Location',
                    onPressed: _getCurrentLocation,
                    icon: Icons.location_searching,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: SecondaryButton(
                    text: 'Clear Location',
                    onPressed: _clearCurrentLocation,
                    icon: Icons.location_disabled,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Location Permissions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Location permissions are required for location sharing and auto-detection features.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            PrimaryButton(
              text: 'Check Permissions',
              onPressed: _checkLocationPermissions,
              icon: Icons.check_circle,
            ),
          ],
        ),
      ),
    );
  }

  void _toggleLocationSharing(bool value) {
    setState(() => _isLocationSharingEnabled = value);
    // TODO: Save to API
    _showSuccessSnackBar(value 
        ? 'Location sharing enabled' 
        : 'Location sharing disabled');
  }

  void _toggleAutoLocation(bool value) {
    setState(() => _autoLocationEnabled = value);
    // TODO: Save to API
    _showSuccessSnackBar(value 
        ? 'Auto-location enabled' 
        : 'Auto-location disabled');
  }

  Future<void> _testCurrentLocation() async {
    setState(() => _isLoading = true);
    
    try {
      final location = await _locationService.getCurrentLocation();
      if (location != null) {
        setState(() {
          _currentLocation = 'Lat: ${location.latitude.toStringAsFixed(6)}, '
                           'Lng: ${location.longitude.toStringAsFixed(6)}';
        });
        _showSuccessSnackBar('Location detected successfully');
      } else {
        _showErrorSnackBar('Failed to get current location');
      }
    } catch (e) {
      _showErrorSnackBar('Error getting location: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentLocation() async {
    await _testCurrentLocation();
  }

  void _clearCurrentLocation() {
    setState(() => _currentLocation = null);
    _showSuccessSnackBar('Location cleared');
  }

  Future<void> _checkLocationPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      final hasPermission = await _locationService.checkLocationPermission();
      if (hasPermission) {
        _showSuccessSnackBar('Location permissions granted');
      } else {
        final granted = await _locationService.requestLocationPermission();
        if (granted) {
          _showSuccessSnackBar('Location permissions granted');
        } else {
          _showErrorSnackBar('Location permissions denied');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error checking permissions: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
