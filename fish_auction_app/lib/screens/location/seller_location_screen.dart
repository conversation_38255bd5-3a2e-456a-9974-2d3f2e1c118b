import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../models/location_model.dart';
import '../../services/location_service.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/loading_overlay.dart';

class SellerLocationScreen extends StatefulWidget {
  final Auction auction;

  const SellerLocationScreen({super.key, required this.auction});

  @override
  State<SellerLocationScreen> createState() => _SellerLocationScreenState();
}

class _SellerLocationScreenState extends State<SellerLocationScreen> {
  final LocationService _locationService = LocationService();
  final MapController _mapController = MapController();

  bool _isLoading = true;
  SellerLocation? _sellerLocation;
  Timer? _locationUpdateTimer;

  // Map markers
  final List<Marker> _markers = [];

  // Default map center (will be updated with seller location)
  LatLng _mapCenter = const LatLng(37.7749, -122.4194); // San Francisco default
  double _mapZoom = 14.0;

  @override
  void initState() {
    super.initState();
    _loadSellerLocation();
    // Don't start automatic updates for hunt location (static location)
  }

  @override
  void dispose() {
    _locationUpdateTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadSellerLocation() async {
    setState(() => _isLoading = true);

    try {
      final response = await _locationService.getSellerLocation(widget.auction.id.toString());

      if (response.isSuccess && response.data != null) {
        setState(() {
          _sellerLocation = response.data!;
          _updateMapWithSellerLocation();
        });
      } else {
        // Handle specific error messages from backend
        String errorMessage = 'Failed to load hunt location';
        if (response.message?.contains('must place a bid') == true) {
          errorMessage = 'You must place a bid to view hunt location';
        } else if (response.message?.contains('not available') == true) {
          errorMessage = 'Hunt location not available';
        }
        _showErrorSnackBar(errorMessage);
      }
    } catch (e) {
      _showErrorSnackBar('Error loading hunt location');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _updateMapWithSellerLocation() {
    if (_sellerLocation == null) return;

    final location = _sellerLocation!.location;
    final position = LatLng(location.latitude, location.longitude);

    // Update map center and zoom
    setState(() {
      _mapCenter = position;
      _mapZoom = 16.0;

      // Update markers
      _markers.clear();
      _markers.add(
        Marker(
          point: position,
          width: 40,
          height: 40,
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: const Icon(Icons.person_pin_circle, color: Colors.white, size: 24),
          ),
        ),
      );
    });

    // Move camera to seller location
    _moveCameraToPosition(position);
  }

  void _moveCameraToPosition(LatLng position) {
    _mapController.move(position, 16.0);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Hunt Location'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: _loadSellerLocation, icon: const Icon(Icons.refresh), tooltip: 'Refresh Location'),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Column(
          children: [
            // Seller Info Card
            _buildSellerInfoCard(),

            // Map
            Expanded(child: _buildMap()),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSellerInfoCard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      widget.auction.seller.displayName.substring(0, 1).toUpperCase(),
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.auction.seller.displayName,
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 12,
                              color: _sellerLocation != null ? AppColors.primary : Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _sellerLocation != null ? 'Hunt Location Available' : 'Loading location...',
                              style: TextStyle(
                                fontSize: 12,
                                color: _sellerLocation != null ? AppColors.primary : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (_sellerLocation != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding, vertical: 4),
                      decoration: BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(12)),
                      child: const Text(
                        'Hunt Place',
                        style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                    ),
                ],
              ),

              if (_sellerLocation != null) ...[
                const SizedBox(height: AppConstants.defaultPadding),
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.location_on, color: AppColors.primary),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('Current Location', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text(
                              '${_sellerLocation!.location.latitude.toStringAsFixed(6)}, ${_sellerLocation!.location.longitude.toStringAsFixed(6)}',
                              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                      if (_sellerLocation!.location.accuracy != null)
                        Text(
                          '±${_sellerLocation!.location.accuracy!.round()}m',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMap() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            initialCenter: _mapCenter,
            initialZoom: _mapZoom,
            minZoom: 5.0,
            maxZoom: 18.0,
            interactionOptions: const InteractionOptions(flags: InteractiveFlag.all),
          ),
          children: [
            // OpenStreetMap tile layer
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'com.fishauction.app',
              maxZoom: 18,
            ),
            // Markers layer
            MarkerLayer(markers: _markers),
            // Attribution layer (required for OpenStreetMap)
            RichAttributionWidget(
              attributions: [
                TextSourceAttribution(
                  'OpenStreetMap contributors',
                  onTap: () {
                    // Could open OSM website
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _contactSeller(),
              icon: const Icon(Icons.message),
              label: const Text('Contact Seller'),
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _openInMaps(),
              icon: const Icon(Icons.directions),
              label: const Text('Get Directions'),
            ),
          ),
        ],
      ),
    );
  }

  void _contactSeller() {
    // TODO: Implement contact seller functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact seller functionality will be implemented soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _openInMaps() {
    if (_sellerLocation == null) {
      _showErrorSnackBar('Seller location not available');
      return;
    }

    // TODO: Implement open in external maps app
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening in maps app will be implemented soon!'), backgroundColor: AppColors.info),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.error));
  }
}
