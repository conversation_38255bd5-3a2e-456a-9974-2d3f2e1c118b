import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
import '../utils/api_response.dart';
import 'api_service.dart';
import '../constants/app_constants.dart';

class StripeService {
  static final StripeService _instance = StripeService._internal();
  factory StripeService() => _instance;
  StripeService._internal();

  final ApiService _apiService = ApiService();
  bool _isInitialized = false;

  // Getter for initialization status
  bool get isInitialized => _isInitialized;

  // Initialize Stripe directly with publishable key
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Use Stripe test publishable key directly (from .env file)
      const publishableKey =
          'pk_test_51OLNPZHIyomRWz3uKNPoOk1439K6zjFyLcBEGoCuzL6JVGfI76Q70kzrxQC4w1y3VvZRirOnv5MntcjNI2cdCYX90016aQVKcs';

      // Initialize Stripe with real SDK
      Stripe.publishableKey = publishableKey;
      await Stripe.instance.applySettings();
      _isInitialized = true;
      debugPrint('Stripe initialized directly with test key');
    } catch (e) {
      throw Exception('Failed to initialize Stripe: ${e.toString()}');
    }
  }

  // Create payment intent for wallet top-up
  Future<ApiResponse<Map<String, dynamic>>> createWalletTopUpIntent({required double amount}) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/wallet/topup/',
        data: {'amount': amount, 'payment_method': 'stripe'},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to create payment intent',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error creating payment intent: ${e.toString()}', statusCode: 500);
    }
  }

  // Process wallet top-up payment with STRIPE'S NATIVE PAYMENT SHEET
  Future<ApiResponse<Map<String, dynamic>>> processWalletTopUp({
    required double amount,
    required BuildContext context,
  }) async {
    try {
      // Step 1: Initialize Stripe if not already done
      if (!_isInitialized) {
        await initialize();
      }

      // Step 2: Validate amount with backend (no Stripe stuff)
      final validateResponse = await _createPaymentIntentFromBackend(amount: amount);
      if (!validateResponse.isSuccess) {
        return ApiResponse.error(
          message: validateResponse.message ?? 'Failed to validate amount',
          statusCode: validateResponse.statusCode ?? 500,
        );
      }

      // Step 3: Create REAL payment intent using Stripe API directly
      final realPaymentIntent = await _createRealStripePaymentIntent(amount);

      // Step 4: Initialize and present Stripe's NATIVE payment sheet with REAL client secret
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: realPaymentIntent['client_secret'],
          merchantDisplayName: 'Fish Auction App',
          style: ThemeMode.system,
        ),
      );

      // Step 5: Present the payment sheet
      await Stripe.instance.presentPaymentSheet();

      // Step 6: If we reach here, payment was successful
      debugPrint('✅ Payment completed successfully with amount: \$${amount.toStringAsFixed(2)}');

      // Step 7: Add amount to user's wallet via backend
      final paymentIntentId = 'payment_${DateTime.now().millisecondsSinceEpoch}';

      debugPrint('🔄 Adding amount to wallet: amount=\$${amount.toStringAsFixed(2)}');
      final confirmResponse = await _confirmPaymentWithBackend(amount: amount, paymentIntentId: paymentIntentId);

      if (confirmResponse.isSuccess) {
        debugPrint('✅ Wallet balance updated successfully');
        return ApiResponse.success(
          data: {
            'message': 'Wallet top-up successful! \$${amount.toStringAsFixed(2)} added to your balance.',
            'amount': amount,
            'status': 'completed',
            'new_balance': confirmResponse.data?['new_balance'],
          },
          statusCode: 200,
        );
      } else {
        debugPrint('❌ Failed to update wallet balance: ${confirmResponse.message}');
        return ApiResponse.error(
          message: 'Payment successful but failed to update wallet balance. Please contact support.',
          statusCode: 500,
        );
      }
    } on StripeException catch (e) {
      if (e.error.code == FailureCode.Canceled) {
        return ApiResponse.error(message: 'Payment cancelled by user', statusCode: 400);
      }
      return ApiResponse.error(
        message: 'Payment failed: ${e.error.localizedMessage ?? e.error.message}',
        statusCode: 400,
      );
    } catch (e) {
      return ApiResponse.error(message: 'Error processing payment: ${e.toString()}', statusCode: 500);
    }
  }

  // Validate amount with backend (no Stripe stuff)
  Future<ApiResponse<Map<String, dynamic>>> _createPaymentIntentFromBackend({required double amount}) async {
    try {
      debugPrint('Validating amount for \$${amount.toStringAsFixed(2)}');

      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/wallet/topup/',
        data: {'amount': amount},
      );

      if (response.isSuccess) {
        debugPrint('✅ Amount validated successfully');
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode ?? 200);
      } else {
        debugPrint('❌ Failed to validate amount: ${response.message}');
        return ApiResponse.error(
          message: response.message ?? 'Failed to validate amount',
          statusCode: response.statusCode ?? 500,
        );
      }
    } catch (e) {
      debugPrint('❌ Error validating amount: $e');
      return ApiResponse.error(message: 'Failed to validate amount: ${e.toString()}', statusCode: 500);
    }
  }

  // Confirm payment with backend to add amount to user's wallet
  Future<ApiResponse<Map<String, dynamic>>> _confirmPaymentWithBackend({
    required double amount,
    required String paymentIntentId,
  }) async {
    try {
      debugPrint('🔄 Confirming payment with backend for \$${amount.toStringAsFixed(2)}');
      debugPrint('🔄 Payment Intent ID: $paymentIntentId');
      debugPrint('🔄 Calling: ${AppConstants.paymentsEndpoint}/wallet/confirm/');

      final requestData = {'amount': amount, 'payment_intent_id': paymentIntentId, 'status': 'completed'};
      debugPrint('🔄 Request data: $requestData');

      final response = await _apiService.post('${AppConstants.paymentsEndpoint}/wallet/confirm/', data: requestData);

      debugPrint('🔄 Response status: ${response.statusCode}');
      debugPrint('🔄 Response success: ${response.isSuccess}');
      debugPrint('🔄 Response message: ${response.message}');
      debugPrint('🔄 Response data: ${response.data}');

      if (response.isSuccess) {
        debugPrint('✅ Payment confirmed and wallet updated');
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode ?? 200);
      } else {
        debugPrint('❌ Failed to confirm payment: ${response.message}');
        return ApiResponse.error(
          message: response.message ?? 'Failed to confirm payment',
          statusCode: response.statusCode ?? 500,
        );
      }
    } catch (e) {
      debugPrint('❌ Error confirming payment: $e');
      return ApiResponse.error(message: 'Failed to confirm payment: ${e.toString()}', statusCode: 500);
    }
  }

  // Create payout for seller - temporarily disabled
  Future<ApiResponse<Map<String, dynamic>>> createPayout({required double amount, required String accountId}) async {
    return ApiResponse.error(message: 'Stripe payout temporarily disabled', statusCode: 501);
  }

  // Create connected account for seller - temporarily disabled
  Future<ApiResponse<Map<String, dynamic>>> createConnectedAccount({required Map<String, dynamic> accountData}) async {
    return ApiResponse.error(message: 'Stripe account creation temporarily disabled', statusCode: 501);
  }

  // Get connected account status - temporarily disabled
  Future<ApiResponse<Map<String, dynamic>>> getConnectedAccountStatus() async {
    return ApiResponse.error(message: 'Stripe account status temporarily disabled', statusCode: 501);
  }

  // Get account onboarding link - temporarily disabled
  Future<ApiResponse<String>> getAccountOnboardingLink() async {
    return ApiResponse.error(message: 'Stripe onboarding temporarily disabled', statusCode: 501);
  }

  // Create REAL Stripe payment intent using your secret key
  Future<Map<String, dynamic>> _createRealStripePaymentIntent(double amount) async {
    try {
      debugPrint('🔥 Creating REAL Stripe payment intent for \$${amount.toStringAsFixed(2)}');

      const stripeSecretKey =
          'sk_test_51OLNPZHIyomRWz3uVKGR91iEwmdYt7N4AOuZtA0Cmsf46weg58JNuvWKcFvlV6nO2QSUACiFqk65fPWkyTuS3KEj00usUtxF04';

      final response = await http.post(
        Uri.parse('https://api.stripe.com/v1/payment_intents'),
        headers: {'Authorization': 'Bearer $stripeSecretKey', 'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'amount': (amount * 100).round().toString(), // Convert to cents
          'currency': 'usd',
          'automatic_payment_methods[enabled]': 'true',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('✅ REAL Stripe payment intent created: ${data['id']}');
        return data;
      } else {
        debugPrint('❌ Failed to create Stripe payment intent: ${response.body}');
        throw Exception('Failed to create payment intent: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Error creating Stripe payment intent: $e');
      rethrow;
    }
  }
}
