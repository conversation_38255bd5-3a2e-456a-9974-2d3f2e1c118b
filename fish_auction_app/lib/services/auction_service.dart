import 'dart:io';
import '../constants/app_constants.dart';
import '../models/auction_model.dart';
import '../models/bid_model.dart';
import '../utils/api_response.dart';
import 'api_service.dart';
import 'auth_service.dart';

class AuctionService {
  static final AuctionService _instance = AuctionService._internal();
  factory AuctionService() => _instance;
  AuctionService._internal();

  final ApiService _apiService = ApiService();

  // Get auctions with filters
  Future<ApiResponse<List<Auction>>> getAuctions({
    String? status,
    int? category,
    String? search,
    String? ordering,
    String? auctionType,
    int page = 1,
    int pageSize = AppConstants.defaultPageSize,
  }) async {
    try {
      print('🔍 DEBUG: Fetching auctions with status: $status');

      final queryParams = <String, dynamic>{'page': page, 'page_size': pageSize};

      if (status != null) queryParams['status'] = status;
      if (category != null) queryParams['category'] = category;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (ordering != null) queryParams['ordering'] = ordering;
      if (auctionType != null) queryParams['auction_type'] = auctionType;

      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/', queryParameters: queryParams);

      print('🔍 DEBUG: Auctions API response: ${response.statusCode}');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final auctions = results.map((json) => Auction.fromJson(json)).toList();

        print('🔍 DEBUG: Found ${auctions.length} auctions from API');
        for (final auction in auctions.take(3)) {
          print('🔍 DEBUG: - ${auction.title} (Status: ${auction.status}, Seller: ${auction.seller.username})');
        }

        return ApiResponse.success(data: auctions, statusCode: 200);
      } else {
        print('🔍 DEBUG: Failed to load auctions: ${response.message}');
        return ApiResponse.error(
          message: response.message ?? 'Failed to load auctions',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('🔍 DEBUG: Exception in getAuctions: $e');
      return ApiResponse.error(message: 'Error loading auctions: ${e.toString()}', statusCode: 500);
    }
  }

  // Get auction by ID
  Future<ApiResponse<Auction>> getAuction(int id) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.auctionsEndpoint}/$id/',
        fromJson: (data) => Auction.fromJson(data as Map<String, dynamic>),
      );

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(data: response.data!, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load auction',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading auction: ${e.toString()}', statusCode: 500);
    }
  }

  // Get auction categories
  Future<ApiResponse<List<AuctionCategory>>> getCategories() async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/categories/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final categories = results.map((json) => AuctionCategory.fromJson(json)).toList();

        return ApiResponse.success(data: categories, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load categories',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading categories: ${e.toString()}', statusCode: 500);
    }
  }

  // Place bid
  Future<ApiResponse<Bid>> placeBid({required int auctionId, required double amount}) async {
    try {
      print('🔍 DEBUG: Placing bid of \$$amount on auction $auctionId');

      final response = await _apiService.post(
        '${AppConstants.auctionsEndpoint}/$auctionId/bid/',
        data: {'amount': amount},
      );

      print('🔍 DEBUG: Place bid response: ${response.statusCode}');
      print('🔍 DEBUG: Place bid data: ${response.data}');

      if (response.isSuccess && response.data != null) {
        final bidData = response.data as Map<String, dynamic>;
        final bid = Bid.fromJson(bidData);

        print('🔍 DEBUG: Bid placed successfully: ${bid.amount}');

        return ApiResponse.success(data: bid, statusCode: 201);
      } else {
        return ApiResponse.error(message: response.message ?? 'Failed to place bid', statusCode: response.statusCode);
      }
    } catch (e) {
      print('🔍 DEBUG: Exception placing bid: $e');
      return ApiResponse.error(message: 'Error placing bid: ${e.toString()}', statusCode: 500);
    }
  }

  // Set auto bid
  Future<ApiResponse<AutoBid>> setAutoBid({required int auctionId, required double maxAmount}) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.auctionsEndpoint}/$auctionId/auto-bid/',
        data: {'max_amount': maxAmount},
        fromJson: (data) => AutoBid.fromJson(data as Map<String, dynamic>),
      );

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(data: response.data!, statusCode: 201);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to set auto bid',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error setting auto bid: ${e.toString()}', statusCode: 500);
    }
  }

  // Get auction bids
  Future<ApiResponse<List<Bid>>> getAuctionBids(int auctionId) async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/$auctionId/bids/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final bids = results.map((json) => Bid.fromJson(json)).toList();

        return ApiResponse.success(data: bids, statusCode: 200);
      } else {
        return ApiResponse.error(message: response.message ?? 'Failed to load bids', statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading bids: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user bids
  Future<ApiResponse<List<Bid>>> getUserBids() async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/my-bids/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final bids = results.map((json) => Bid.fromJson(json)).toList();

        return ApiResponse.success(data: bids, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load your bids',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading your bids: ${e.toString()}', statusCode: 500);
    }
  }

  // Get earned auctions (won by user)
  Future<ApiResponse<List<Auction>>> getEarnedAuctions() async {
    try {
      // Check current user first
      final authService = AuthService();
      final currentUser = authService.currentUser;
      print('👤 Current user: ${currentUser?.username} (ID: ${currentUser?.id})');

      print('🔄 API: Fetching earned auctions from ${AppConstants.auctionsEndpoint}/earned/');
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/earned/');

      if (response.isSuccess) {
        print('✅ API: Earned auctions response received');
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        print('📊 API: Found ${results.length} earned auctions in response');

        // Log the raw response for debugging
        print('🔍 Raw response data: $data');

        final auctions = results.map((json) => Auction.fromJson(json)).toList();
        print('✅ API: Successfully parsed ${auctions.length} earned auctions');

        return ApiResponse.success(data: auctions, statusCode: 200);
      } else {
        print('❌ API: Failed to load earned auctions - ${response.message}');
        return ApiResponse.error(
          message: response.message ?? 'Failed to load earned auctions',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('❌ API: Error loading earned auctions - $e');
      return ApiResponse.error(message: 'Error loading earned auctions: ${e.toString()}', statusCode: 500);
    }
  }

  // Add to watchlist
  Future<ApiResponse<void>> addToWatchlist(int auctionId) async {
    try {
      final response = await _apiService.post('${AppConstants.auctionsEndpoint}/$auctionId/watch/');

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 201);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to add to watchlist',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error adding to watchlist: ${e.toString()}', statusCode: 500);
    }
  }

  // Remove from watchlist
  Future<ApiResponse<void>> removeFromWatchlist(int auctionId) async {
    try {
      final response = await _apiService.delete('${AppConstants.auctionsEndpoint}/$auctionId/watch/');

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 204);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to remove from watchlist',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error removing from watchlist: ${e.toString()}', statusCode: 500);
    }
  }

  // Get watchlist
  Future<ApiResponse<List<Auction>>> getWatchlist() async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/watchlist/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final auctions = results.map((json) => Auction.fromJson(json)).toList();

        return ApiResponse.success(data: auctions, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load watchlist',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading watchlist: ${e.toString()}', statusCode: 500);
    }
  }

  // Get live auctions
  Future<ApiResponse<List<Auction>>> getLiveAuctions() async {
    return getAuctions(status: 'live', ordering: '-end_time');
  }

  // Get ending soon auctions
  Future<ApiResponse<List<Auction>>> getEndingSoonAuctions() async {
    return getAuctions(status: 'live', ordering: 'end_time');
  }

  // Search auctions
  Future<ApiResponse<List<Auction>>> searchAuctions(String query) async {
    return getAuctions(search: query);
  }

  // Get participated auctions
  Future<ApiResponse<List<Auction>>> getMyParticipatedAuctions() async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/participated/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final auctions = results.map((json) => Auction.fromJson(json)).toList();

        return ApiResponse.success(data: auctions, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load participated auctions',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading participated auctions: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user auto bid for specific auction
  Future<ApiResponse<Map<String, dynamic>?>> getUserAutoBid(int auctionId) async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/$auctionId/auto-bid/');

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: 200);
      } else if (response.statusCode == 404) {
        // No auto-bid exists, this is normal
        return ApiResponse.success(data: null, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load auto bid',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading auto bid: ${e.toString()}', statusCode: 500);
    }
  }

  // Cancel auto bid
  Future<ApiResponse<void>> cancelAutoBid(int auctionId) async {
    try {
      final response = await _apiService.delete('${AppConstants.auctionsEndpoint}/$auctionId/auto-bid/');

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 204);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to cancel auto bid',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error cancelling auto bid: ${e.toString()}', statusCode: 500);
    }
  }

  // Get my auctions (for sellers)
  Future<ApiResponse<List<Auction>>> getMyAuctions() async {
    try {
      print('🔍 DEBUG: Fetching my auctions...');

      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/my-auctions/');

      print('🔍 DEBUG: My auctions API response: ${response.statusCode}');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List<dynamic>? ?? [];

        print('🔍 DEBUG: Found ${results.length} auctions from API');

        final auctions = results.map((item) => Auction.fromJson(item as Map<String, dynamic>)).toList();

        auctions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        print('🔍 DEBUG: Returning ${auctions.length} auctions');

        return ApiResponse.success(data: auctions, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load my auctions',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('🔍 DEBUG: Exception in getMyAuctions: $e');
      return ApiResponse.error(message: 'Error loading my auctions: ${e.toString()}', statusCode: 500);
    }
  }

  // Create auction
  Future<ApiResponse<Auction>> createAuction({
    required String title,
    required String description,
    required int fishCategory,
    required String fishType,
    required double weight,
    required int quantity,
    required DateTime catchDate,
    required String catchLocation,
    required String auctionType,
    required double startingPrice,
    double? reservePrice,
    double? targetPrice,
    double? buyNowPrice,
    required double bidIncrement,
    required DateTime startTime,
    DateTime? endTime, // Optional for buy_now auctions
    required File mainImage,
    List<File>? additionalImages,
    String status = 'draft', // Default to draft
    double? latitude,
    double? longitude,
    String? deliveryAddress,
  }) async {
    try {
      print('🔍 DEBUG: Creating auction with title: $title');

      // Prepare form data with proper null handling
      final formData = <String, String>{
        'title': title,
        'description': description,
        'fish_category': fishCategory.toString(),
        'fish_type': fishType,
        'weight': weight.toString(),
        'quantity': quantity.toString(),
        'catch_date': catchDate.toIso8601String().split('T')[0],
        'catch_location': catchLocation,
        'auction_type': auctionType,
        'starting_price': startingPrice.toString(),
        'bid_increment': bidIncrement.toString(),
        'start_time': startTime.toUtc().toIso8601String(),
        'status': status,
      };

      // Only add optional fields if they have values
      if (endTime != null) {
        formData['end_time'] = endTime.toUtc().toIso8601String();
      }

      if (reservePrice != null && reservePrice > 0) {
        formData['reserve_price'] = reservePrice.toString();
      }

      if (targetPrice != null && targetPrice > 0) {
        formData['target_price'] = targetPrice.toString();
      }

      if (buyNowPrice != null && buyNowPrice > 0) {
        formData['buy_now_price'] = buyNowPrice.toString();
      }

      // Add location coordinates if provided
      if (latitude != null && longitude != null) {
        formData['latitude'] = latitude.toString();
        formData['longitude'] = longitude.toString();
      }

      // Add delivery address if provided
      if (deliveryAddress != null && deliveryAddress.isNotEmpty) {
        formData['delivery_address'] = deliveryAddress;
      }

      print('🔍 DEBUG: Calling API to create auction...');
      print('🔍 DEBUG: Form data: $formData');

      // Call the real API to create the auction with file upload
      final response = await _apiService.uploadFile<Map<String, dynamic>>(
        '${AppConstants.auctionsEndpoint}/create/',
        mainImage,
        fieldName: 'main_image',
        additionalData: formData,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      print('🔍 DEBUG: Create auction API response: ${response.statusCode}');
      print('🔍 DEBUG: Response success: ${response.isSuccess}');

      if (response.isSuccess) {
        print('🔍 DEBUG: Raw API response: ${response.data}');
        print('🔍 DEBUG: Response type: ${response.data.runtimeType}');

        // Handle different response formats
        Map<String, dynamic> auctionData;
        if (response.data is Map<String, dynamic>) {
          auctionData = response.data as Map<String, dynamic>;
        } else if (response.data is int) {
          // If API returns just the ID, we need to fetch the full auction
          final auctionId = response.data as int;
          print('🔍 DEBUG: API returned auction ID: $auctionId, fetching full auction...');

          final fetchResponse = await _apiService.get('${AppConstants.auctionsEndpoint}/$auctionId/');

          if (fetchResponse.isSuccess) {
            auctionData = fetchResponse.data as Map<String, dynamic>;
          } else {
            return ApiResponse.error(
              message: 'Auction created but failed to fetch details',
              statusCode: fetchResponse.statusCode,
            );
          }
        } else {
          return ApiResponse.error(
            message: 'Unexpected response format: ${response.data.runtimeType}',
            statusCode: 500,
          );
        }

        try {
          final createdAuction = Auction.fromJson(auctionData);
          print('🔍 DEBUG: Auction created successfully with ID: ${createdAuction.id}');
          print('🔍 DEBUG: Created auction status: ${createdAuction.status}');

          // Force refresh the auction from API to get the latest status
          final refreshResponse = await _apiService.get('${AppConstants.auctionsEndpoint}/${createdAuction.id}/');

          if (refreshResponse.isSuccess) {
            final refreshedData = refreshResponse.data as Map<String, dynamic>;
            final refreshedAuction = Auction.fromJson(refreshedData);
            print('🔍 DEBUG: Refreshed auction status: ${refreshedAuction.status}');
            return ApiResponse.success(data: refreshedAuction, statusCode: 201);
          } else {
            // Return original auction if refresh fails
            return ApiResponse.success(data: createdAuction, statusCode: 201);
          }
        } catch (parseError) {
          print('🔍 DEBUG: Error parsing auction data: $parseError');
          print('🔍 DEBUG: Auction data that failed to parse: $auctionData');
          return ApiResponse.error(
            message: 'Failed to parse auction response: ${parseError.toString()}',
            statusCode: 500,
          );
        }
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to create auction',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('🔍 DEBUG: Exception in createAuction: $e');
      return ApiResponse.error(message: 'Error creating auction: ${e.toString()}', statusCode: 500);
    }
  }

  // Update auction status to live
  Future<ApiResponse<Auction>> updateAuctionStatus(int auctionId, String status) async {
    try {
      print('🔍 DEBUG: Updating auction $auctionId status to: $status');

      final response = await _apiService.put('${AppConstants.auctionsEndpoint}/$auctionId/', data: {'status': status});

      print('🔍 DEBUG: Update status API response: ${response.statusCode}');

      if (response.isSuccess) {
        final auctionData = response.data as Map<String, dynamic>;
        final updatedAuction = Auction.fromJson(auctionData);
        print('🔍 DEBUG: Auction status updated to: ${updatedAuction.status}');

        return ApiResponse.success(data: updatedAuction, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update auction status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('🔍 DEBUG: Exception in updateAuctionStatus: $e');
      return ApiResponse.error(message: 'Error updating auction status: ${e.toString()}', statusCode: 500);
    }
  }

  // Make auction live
  Future<ApiResponse<void>> makeAuctionLive(int auctionId) async {
    try {
      final response = await _apiService.post('${AppConstants.auctionsEndpoint}/$auctionId/make-live/');

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to make auction live',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error making auction live: ${e.toString()}', statusCode: 500);
    }
  }

  // Schedule auction
  Future<ApiResponse<Auction>> scheduleAuction(int auctionId) async {
    return updateAuctionStatus(auctionId, 'scheduled');
  }

  // Stop auction
  Future<ApiResponse<Auction>> stopAuction(int auctionId) async {
    return updateAuctionStatus(auctionId, 'ended');
  }

  // Close auction and notify winner
  Future<ApiResponse<Auction>> closeAuction(int auctionId) async {
    try {
      final response = await _apiService.post('${AppConstants.auctionsEndpoint}/$auctionId/close-early/');

      if (response.isSuccess) {
        final auction = Auction.fromJson(response.data['auction']);
        return ApiResponse.success(data: auction, statusCode: 200, message: response.data['message']);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to close auction',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error closing auction: $e', statusCode: 500);
    }
  }

  // Cancel auction
  Future<ApiResponse<Auction>> cancelAuction(int auctionId) async {
    try {
      final response = await _apiService.post('${AppConstants.auctionsEndpoint}/$auctionId/cancel/');

      if (response.isSuccess) {
        final auction = Auction.fromJson(response.data['auction']);
        return ApiResponse.success(data: auction, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to cancel auction',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error cancelling auction: $e', statusCode: 500);
    }
  }

  // Reactivate auction (from cancelled to draft)
  Future<ApiResponse<Auction>> reactivateAuction(int auctionId) async {
    return updateAuctionStatus(auctionId, 'draft');
  }

  // Update auction (only drafts)
  Future<ApiResponse<Auction>> updateAuction({
    required int auctionId,
    required String title,
    required String description,
    required int fishCategory,
    required String fishType,
    required double weight,
    required int quantity,
    required DateTime catchDate,
    required String catchLocation,
    required String auctionType,
    required double startingPrice,
    double? reservePrice,
    double? buyNowPrice,
    required double bidIncrement,
    required DateTime startTime,
    required DateTime endTime,
    File? mainImage,
    List<File>? additionalImages,
  }) async {
    try {
      final formData = <String, String>{
        'title': title,
        'description': description,
        'fish_category': fishCategory.toString(),
        'fish_type': fishType,
        'weight': weight.toString(),
        'quantity': quantity.toString(),
        'catch_date': catchDate.toIso8601String().split('T')[0],
        'catch_location': catchLocation,
        'auction_type': auctionType,
        'starting_price': startingPrice.toString(),
        'bid_increment': bidIncrement.toString(),
        'start_time': startTime.toUtc().toIso8601String(),
        'end_time': endTime.toUtc().toIso8601String(),
      };

      if (reservePrice != null && reservePrice > 0) {
        formData['reserve_price'] = reservePrice.toString();
      }

      if (buyNowPrice != null && buyNowPrice > 0) {
        formData['buy_now_price'] = buyNowPrice.toString();
      }

      ApiResponse<Map<String, dynamic>> response;

      if (mainImage != null) {
        // For updates with image, we'll use POST to a special update endpoint
        response = await _apiService.uploadFile<Map<String, dynamic>>(
          '${AppConstants.auctionsEndpoint}/$auctionId/update/',
          mainImage,
          fieldName: 'main_image',
          additionalData: formData,
          fromJson: (data) => data as Map<String, dynamic>,
        );
      } else {
        // Update without image using PUT
        response = await _apiService.put('${AppConstants.auctionsEndpoint}/$auctionId/update/', data: formData);
      }

      if (response.isSuccess) {
        final auctionData = response.data as Map<String, dynamic>;
        final updatedAuction = Auction.fromJson(auctionData);
        return ApiResponse.success(data: updatedAuction, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update auction',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error updating auction: ${e.toString()}', statusCode: 500);
    }
  }
}
