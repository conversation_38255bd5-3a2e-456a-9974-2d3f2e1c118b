import '../constants/app_constants.dart';
import '../models/location_model.dart';
import '../models/delivery_model.dart';
import '../utils/api_response.dart';
import 'api_service.dart';

class DeliveryService {
  static final DeliveryService _instance = DeliveryService._internal();
  factory DeliveryService() => _instance;
  DeliveryService._internal();

  final ApiService _apiService = ApiService();

  // Get delivery tracking for auction
  Future<ApiResponse<DeliveryTracking?>> getDeliveryTracking(int auctionId) async {
    try {
      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;

        // Find delivery for this auction
        final deliveryData = results.firstWhere(
          (delivery) => delivery['auction']['id'] == auctionId,
          orElse: () => null,
        );

        if (deliveryData != null) {
          final delivery = DeliveryTracking.fromJson(deliveryData);
          return ApiResponse.success(data: delivery, statusCode: 200);
        } else {
          return ApiResponse.success(data: null, statusCode: 200);
        }
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load delivery tracking',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading delivery tracking: ${e.toString()}', statusCode: 500);
    }
  }

  // Get all deliveries for user
  Future<ApiResponse<List<DeliveryTracking>>> getMyDeliveries() async {
    try {
      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final deliveries = results.map((json) => DeliveryTracking.fromJson(json)).toList();

        return ApiResponse.success(data: deliveries, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load deliveries',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading deliveries: ${e.toString()}', statusCode: 500);
    }
  }

  // Get delivery details by ID
  Future<ApiResponse<DeliveryTracking>> getDeliveryDetails(String deliveryId) async {
    try {
      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/$deliveryId/');

      if (response.isSuccess) {
        final delivery = DeliveryTracking.fromJson(response.data as Map<String, dynamic>);
        return ApiResponse.success(data: delivery, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load delivery details',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading delivery details: ${e.toString()}', statusCode: 500);
    }
  }

  // Update delivery status (for delivery providers)
  Future<ApiResponse<void>> updateDeliveryStatus({
    required String deliveryId,
    required String status,
    required String message,
    Location? location,
  }) async {
    try {
      final data = {'delivery': deliveryId, 'status': status, 'message': message};

      if (location != null) {
        data['latitude'] = location.latitude.toString();
        data['longitude'] = location.longitude.toString();
      }

      final response = await _apiService.post('${AppConstants.deliveryEndpoint}/updates/', data: data);

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 201);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update delivery status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error updating delivery status: ${e.toString()}', statusCode: 500);
    }
  }

  // Rate delivery (for buyers)
  Future<ApiResponse<void>> rateDelivery({
    required String deliveryId,
    required int overallRating,
    required int timelinessRating,
    required int conditionRating,
    required int communicationRating,
    String? reviewText,
  }) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.deliveryEndpoint}/ratings/',
        data: {
          'delivery': deliveryId,
          'overall_rating': overallRating,
          'timeliness_rating': timelinessRating,
          'condition_rating': conditionRating,
          'communication_rating': communicationRating,
          'review_text': reviewText ?? '',
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 201);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to rate delivery',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error rating delivery: ${e.toString()}', statusCode: 500);
    }
  }

  // Get delivery providers
  Future<ApiResponse<List<Map<String, dynamic>>>> getDeliveryProviders() async {
    try {
      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/providers/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List;
        final providers = results.cast<Map<String, dynamic>>();

        return ApiResponse.success(data: providers, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to load delivery providers',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error loading delivery providers: ${e.toString()}', statusCode: 500);
    }
  }

  // Get deliveries for seller (ended auctions with successful payments)
  Future<ApiResponse<List<Delivery>>> getSellerDeliveries({String? status, int? limit, int? offset}) async {
    try {
      final queryParams = <String, String>{};

      if (status != null) queryParams['status'] = status;
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/seller/', queryParameters: queryParams);

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List<dynamic>;

        final deliveries = results.map((delivery) => Delivery.fromJson(delivery)).toList();

        return ApiResponse.success(data: deliveries, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch deliveries',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching deliveries: ${e.toString()}', statusCode: 500);
    }
  }

  // Update delivery status for sellers
  Future<ApiResponse<Delivery>> updateSellerDeliveryStatus({
    required String deliveryId,
    required String status,
    String? message,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final data = <String, dynamic>{'status': status};

      if (message != null) data['message'] = message;
      if (latitude != null) data['latitude'] = latitude;
      if (longitude != null) data['longitude'] = longitude;

      final response = await _apiService.post(
        '${AppConstants.deliveryEndpoint}/$deliveryId/update-status/',
        data: data,
      );

      if (response.isSuccess) {
        final delivery = Delivery.fromJson(response.data as Map<String, dynamic>);

        return ApiResponse.success(data: delivery, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update delivery status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error updating delivery status: ${e.toString()}', statusCode: 500);
    }
  }

  // Get delivery updates/history
  Future<ApiResponse<List<DeliveryUpdate>>> getDeliveryUpdates(String deliveryId) async {
    try {
      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/$deliveryId/updates/');

      if (response.isSuccess) {
        final data = response.data as List<dynamic>;
        final updates = data.map((update) => DeliveryUpdate.fromJson(update)).toList();

        return ApiResponse.success(data: updates, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch delivery updates',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching delivery updates: ${e.toString()}', statusCode: 500);
    }
  }
}
