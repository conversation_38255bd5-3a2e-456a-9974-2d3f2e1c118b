import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/broker_service_model.dart';
import '../utils/api_response.dart';
import '../constants/app_constants.dart';
import 'api_service.dart';

class BrokerApiService {
  // Use AppConstants.baseUrl instead of hardcoded URL
  static String get baseUrl => '${AppConstants.baseUrl.replaceAll('/api', '')}/api/broker';
  final ApiService _apiService = ApiService();

  // Get all available broker services
  Future<ApiResponse<List<BrokerService>>> getBrokerServices() async {
    try {
      final response = await _apiService.get('$baseUrl/services/');

      if (response.isSuccess && response.data != null) {
        // Handle paginated response format
        final responseData = response.data;
        List<dynamic> servicesJson;

        if (responseData is Map<String, dynamic> && responseData.containsKey('results')) {
          // Paginated response
          servicesJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List<dynamic>) {
          // Direct list response
          servicesJson = responseData;
        } else {
          return ApiResponse.error(message: 'Invalid response format');
        }

        final services = servicesJson.map((json) => BrokerService.fromJson(json)).toList();
        return ApiResponse.success(data: services);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load broker services');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading broker services: ${e.toString()}');
    }
  }

  // Create a service request
  Future<ApiResponse<ServiceRequest>> createServiceRequest(ServiceRequest request) async {
    try {
      final response = await _apiService.post('$baseUrl/requests/create/', data: request.toJson());

      if (response.isSuccess && response.data != null) {
        final serviceRequest = ServiceRequest.fromJson(response.data);
        return ApiResponse.success(data: serviceRequest);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to create service request');
    } catch (e) {
      return ApiResponse.error(message: 'Error creating service request: ${e.toString()}');
    }
  }

  // Get my service requests
  Future<ApiResponse<List<ServiceRequest>>> getMyServiceRequests() async {
    try {
      final response = await _apiService.get('$baseUrl/requests/my/');

      if (response.isSuccess && response.data != null) {
        // Handle paginated response format
        final responseData = response.data;
        List<dynamic> requestsJson;

        if (responseData is Map<String, dynamic> && responseData.containsKey('results')) {
          // Paginated response
          requestsJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List<dynamic>) {
          // Direct list response
          requestsJson = responseData;
        } else {
          return ApiResponse.error(message: 'Invalid response format');
        }

        final requests = requestsJson.map((json) => ServiceRequest.fromJson(json)).toList();
        return ApiResponse.success(data: requests);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load service requests');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading service requests: ${e.toString()}');
    }
  }

  // Get available service requests (for brokers)
  Future<ApiResponse<List<ServiceRequest>>> getAvailableServiceRequests() async {
    try {
      final response = await _apiService.get('$baseUrl/requests/available/');

      if (response.isSuccess && response.data != null) {
        // Handle paginated response format
        final responseData = response.data;
        List<dynamic> requestsJson;

        if (responseData is Map<String, dynamic> && responseData.containsKey('results')) {
          // Paginated response
          requestsJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List<dynamic>) {
          // Direct list response
          requestsJson = responseData;
        } else {
          return ApiResponse.error(message: 'Invalid response format');
        }

        final requests = requestsJson.map((json) => ServiceRequest.fromJson(json)).toList();
        return ApiResponse.success(data: requests);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load available service requests');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading available service requests: ${e.toString()}');
    }
  }

  // Get quotes for a service request
  Future<ApiResponse<List<BrokerQuote>>> getServiceRequestQuotes(String serviceRequestId) async {
    try {
      final response = await _apiService.get('$baseUrl/requests/$serviceRequestId/quotes/');

      if (response.isSuccess && response.data != null) {
        // Handle paginated response format
        final responseData = response.data;
        List<dynamic> quotesJson;

        if (responseData is Map<String, dynamic> && responseData.containsKey('results')) {
          // Paginated response
          quotesJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List<dynamic>) {
          // Direct list response
          quotesJson = responseData;
        } else {
          return ApiResponse.error(message: 'Invalid response format');
        }

        final quotes = quotesJson.map((json) => BrokerQuote.fromJson(json)).toList();
        return ApiResponse.success(data: quotes);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load quotes');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading quotes: ${e.toString()}');
    }
  }

  // Create a broker quote
  Future<ApiResponse<BrokerQuote>> createBrokerQuote(BrokerQuote quote) async {
    try {
      final response = await _apiService.post('$baseUrl/quotes/create/', data: quote.toJson());

      if (response.isSuccess && response.data != null) {
        final brokerQuote = BrokerQuote.fromJson(response.data);
        return ApiResponse.success(data: brokerQuote);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to create quote');
    } catch (e) {
      return ApiResponse.error(message: 'Error creating quote: ${e.toString()}');
    }
  }

  // Select a broker for a service request
  Future<ApiResponse<ServiceRequest>> selectBroker(String serviceRequestId, String quoteId) async {
    try {
      final response = await _apiService.post('$baseUrl/requests/$serviceRequestId/select-broker/$quoteId/', data: {});

      if (response.isSuccess && response.data != null) {
        final serviceRequest = ServiceRequest.fromJson(response.data['service_request']);
        return ApiResponse.success(data: serviceRequest);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to select broker');
    } catch (e) {
      return ApiResponse.error(message: 'Error selecting broker: ${e.toString()}');
    }
  }

  // Get broker service executions
  Future<ApiResponse<List<ServiceExecution>>> getBrokerServiceExecutions() async {
    try {
      final response = await _apiService.get('$baseUrl/executions/broker/');

      if (response.isSuccess && response.data != null) {
        // Handle paginated response format
        final responseData = response.data;
        List<dynamic> executionsJson;

        if (responseData is Map<String, dynamic> && responseData.containsKey('results')) {
          // Paginated response
          executionsJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List<dynamic>) {
          // Direct list response
          executionsJson = responseData;
        } else {
          return ApiResponse.error(message: 'Invalid response format');
        }

        final executions = executionsJson.map((json) => ServiceExecution.fromJson(json)).toList();
        return ApiResponse.success(data: executions);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load service executions');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading service executions: ${e.toString()}');
    }
  }

  // Get client service executions
  Future<ApiResponse<List<ServiceExecution>>> getClientServiceExecutions() async {
    try {
      final response = await _apiService.get('$baseUrl/executions/client/');

      if (response.isSuccess && response.data != null) {
        // Handle paginated response format
        final responseData = response.data;
        List<dynamic> executionsJson;

        if (responseData is Map<String, dynamic> && responseData.containsKey('results')) {
          // Paginated response
          executionsJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List<dynamic>) {
          // Direct list response
          executionsJson = responseData;
        } else {
          return ApiResponse.error(message: 'Invalid response format');
        }

        final executions = executionsJson.map((json) => ServiceExecution.fromJson(json)).toList();
        return ApiResponse.success(data: executions);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load service executions');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading service executions: ${e.toString()}');
    }
  }

  // Update service execution
  Future<ApiResponse<ServiceExecution>> updateServiceExecution(String executionId, Map<String, dynamic> updates) async {
    try {
      final response = await _apiService.patch('$baseUrl/executions/$executionId/update/', data: updates);

      if (response.isSuccess && response.data != null) {
        final execution = ServiceExecution.fromJson(response.data);
        return ApiResponse.success(data: execution);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to update service execution');
    } catch (e) {
      return ApiResponse.error(message: 'Error updating service execution: ${e.toString()}');
    }
  }

  // Update service execution with image
  Future<ApiResponse<ServiceExecution>> updateServiceExecutionWithImage(
    String executionId,
    Map<String, dynamic> updates,
    File image,
  ) async {
    try {
      // Upload image first and get the URL
      final imageResponse = await _apiService.uploadFile(
        '$baseUrl/executions/$executionId/upload-image/',
        image,
        fieldName: 'report_image',
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (imageResponse.isSuccess && imageResponse.data != null) {
        // Add the image URL to the updates
        final imageUrl = imageResponse.data!['image_url'] as String;
        final reportImages = updates['report_images'] as List<String>? ?? [];
        reportImages.add(imageUrl);
        updates['report_images'] = reportImages;
      }

      // Now update the service execution with the image URL included
      final response = await _apiService.patch('$baseUrl/executions/$executionId/update/', data: updates);

      if (response.isSuccess && response.data != null) {
        final execution = ServiceExecution.fromJson(response.data);
        return ApiResponse.success(data: execution);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to update service execution with image');
    } catch (e) {
      return ApiResponse.error(message: 'Error updating service execution with image: ${e.toString()}');
    }
  }

  // Submit client feedback
  Future<ApiResponse<ServiceExecution>> submitClientFeedback(String executionId, String feedback, int rating) async {
    try {
      final response = await _apiService.patch(
        '$baseUrl/executions/$executionId/feedback/',
        data: {
          'client_feedback': feedback,
          'client_rating': rating,
          'client_approved': true, // Explicitly approve when rating
        },
      );

      if (response.isSuccess && response.data != null) {
        final execution = ServiceExecution.fromJson(response.data);
        return ApiResponse.success(data: execution);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to submit feedback');
    } catch (e) {
      return ApiResponse.error(message: 'Error submitting feedback: ${e.toString()}');
    }
  }

  // Release payment to broker
  Future<ApiResponse<Map<String, dynamic>>> releasePayment(String executionId) async {
    try {
      final response = await _apiService.post('$baseUrl/executions/$executionId/release-payment/', data: {});

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(data: response.data);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to release payment');
    } catch (e) {
      return ApiResponse.error(message: 'Error releasing payment: ${e.toString()}');
    }
  }

  // Get broker profile
  Future<ApiResponse<Map<String, dynamic>>> getBrokerProfile() async {
    try {
      final response = await _apiService.get('$baseUrl/profile/');

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(data: response.data);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to load broker profile');
    } catch (e) {
      return ApiResponse.error(message: 'Error loading broker profile: ${e.toString()}');
    }
  }

  // Update broker profile
  Future<ApiResponse<Map<String, dynamic>>> updateBrokerProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _apiService.patch('$baseUrl/profile/', data: profileData);

      if (response.isSuccess && response.data != null) {
        return ApiResponse.success(data: response.data);
      }

      return ApiResponse.error(message: response.message ?? 'Failed to update broker profile');
    } catch (e) {
      return ApiResponse.error(message: 'Error updating broker profile: ${e.toString()}');
    }
  }
}
