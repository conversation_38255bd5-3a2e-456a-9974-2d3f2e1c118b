import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

import '../models/location_model.dart';
import '../utils/api_response.dart';
import 'api_service.dart';
import '../constants/app_constants.dart';

class LocationService {
  final ApiService _apiService = ApiService();
  StreamSubscription<Position>? _positionStreamSubscription;
  Timer? _locationUpdateTimer;

  // Check location permissions
  Future<bool> checkLocationPermission() async {
    final permission = await Permission.location.status;
    return permission.isGranted;
  }

  // Request location permissions
  Future<bool> requestLocationPermission() async {
    final permission = await Permission.location.request();
    return permission.isGranted;
  }

  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Get current location with detailed error handling
  Future<Location?> getCurrentLocation() async {
    try {
      print('🔍 Starting location request...');

      // Check if location services are enabled on device
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      print('🔍 Location services enabled: $serviceEnabled');

      if (!serviceEnabled) {
        throw Exception('Location services are disabled. Please enable location services in your device settings.');
      }

      // Check current permission status using Geolocator directly
      LocationPermission permission = await Geolocator.checkPermission();
      print('🔍 Current Geolocator permission: $permission');

      if (permission == LocationPermission.denied) {
        print('🔍 Permission denied, requesting...');
        permission = await Geolocator.requestPermission();
        print('🔍 Permission after request: $permission');

        if (permission == LocationPermission.denied) {
          throw Exception('Location permission denied. Please grant location permission to use this feature.');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permission permanently denied. Please enable location permission in app settings.');
      }

      print('🔍 Getting current position...');
      final position =
          await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high,
            timeLimit: const Duration(seconds: 15),
          ).timeout(
            const Duration(seconds: 20),
            onTimeout: () {
              throw Exception('Location request timed out. Please try again.');
            },
          );

      print('🔍 Location obtained: ${position.latitude}, ${position.longitude}');
      return Location(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        altitude: position.altitude,
        heading: position.heading,
        speed: position.speed,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      print('🔍 Error getting current location: $e');
      rethrow; // Re-throw to preserve the original error message
    }
  }

  // Start location tracking for sellers
  Future<void> startLocationTracking({
    required Function(Location) onLocationUpdate,
    required Function(String) onError,
  }) async {
    try {
      if (!await checkLocationPermission()) {
        final granted = await requestLocationPermission();
        if (!granted) {
          onError('Location permission denied');
          return;
        }
      }

      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      );

      _positionStreamSubscription = Geolocator.getPositionStream(locationSettings: locationSettings).listen(
        (Position position) {
          final location = Location(
            latitude: position.latitude,
            longitude: position.longitude,
            accuracy: position.accuracy,
            altitude: position.altitude,
            heading: position.heading,
            speed: position.speed,
            timestamp: DateTime.now(),
          );
          onLocationUpdate(location);
        },
        onError: (error) {
          onError('Location tracking error: $error');
        },
      );

      // Also update location every 30 seconds even if not moved much
      _locationUpdateTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
        final location = await getCurrentLocation();
        if (location != null) {
          onLocationUpdate(location);
        }
      });
    } catch (e) {
      onError('Failed to start location tracking: $e');
    }
  }

  // Stop location tracking
  void stopLocationTracking() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    _locationUpdateTimer?.cancel();
    _locationUpdateTimer = null;
  }

  // Update seller location on server
  Future<ApiResponse<SellerLocation>> updateSellerLocation(Location location, {String? status, int? auctionId}) async {
    try {
      final data = {
        'latitude': location.latitude,
        'longitude': location.longitude,
        'accuracy': location.accuracy,
        'altitude': location.altitude,
        'heading': location.heading,
        'speed': location.speed,
        if (status != null) 'status': status,
        if (auctionId != null) 'auction_id': auctionId,
      };

      final response = await _apiService.post('/auctions/location/update/', data: data);

      if (response.isSuccess) {
        final sellerLocation = SellerLocation.fromJson(response.data as Map<String, dynamic>);
        return ApiResponse.success(data: sellerLocation, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update location',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error updating location: ${e.toString()}', statusCode: 500);
    }
  }

  // Get auction location data
  Future<ApiResponse<Map<String, dynamic>>> getAuctionLocation(int auctionId) async {
    try {
      final response = await _apiService.get('/auctions/$auctionId/location/');

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to get auction location',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error getting auction location: ${e.toString()}', statusCode: 500);
    }
  }

  // Toggle location sharing for auction
  Future<ApiResponse<Map<String, dynamic>>> toggleLocationSharing(int auctionId) async {
    try {
      final response = await _apiService.post('/auctions/$auctionId/location/toggle/', data: {});

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to toggle location sharing',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error toggling location sharing: ${e.toString()}', statusCode: 500);
    }
  }

  // Get seller location for auction
  Future<ApiResponse<SellerLocation>> getSellerLocation(String auctionId) async {
    try {
      final response = await _apiService.get('${AppConstants.auctionsEndpoint}/$auctionId/location/');

      if (response.isSuccess) {
        final sellerLocation = SellerLocation.fromJson(response.data as Map<String, dynamic>);
        return ApiResponse.success(data: sellerLocation, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to get seller location',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error getting seller location: ${e.toString()}', statusCode: 500);
    }
  }

  // Get delivery tracking
  Future<ApiResponse<DeliveryTracking>> getDeliveryTracking(String auctionId) async {
    try {
      final response = await _apiService.get('${AppConstants.deliveryEndpoint}/tracking/$auctionId/');

      if (response.isSuccess) {
        final tracking = DeliveryTracking.fromJson(response.data as Map<String, dynamic>);
        return ApiResponse.success(data: tracking, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to get delivery tracking',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error getting delivery tracking: ${e.toString()}', statusCode: 500);
    }
  }

  // Update delivery status
  Future<ApiResponse<DeliveryTracking>> updateDeliveryStatus({
    required String auctionId,
    required String status,
    String? description,
    Location? location,
  }) async {
    try {
      final data = <String, dynamic>{
        'status': status,
        if (description != null) 'description': description,
        if (location != null) 'location': location.toJson(),
      };

      final response = await _apiService.patch('${AppConstants.deliveryEndpoint}/tracking/$auctionId/', data: data);

      if (response.isSuccess) {
        final tracking = DeliveryTracking.fromJson(response.data as Map<String, dynamic>);
        return ApiResponse.success(data: tracking, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update delivery status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error updating delivery status: ${e.toString()}', statusCode: 500);
    }
  }

  // Calculate distance between two locations
  double calculateDistance(Location from, Location to) {
    return Geolocator.distanceBetween(from.latitude, from.longitude, to.latitude, to.longitude);
  }

  // Calculate bearing between two locations
  double calculateBearing(Location from, Location to) {
    return Geolocator.bearingBetween(from.latitude, from.longitude, to.latitude, to.longitude);
  }

  // Format distance for display
  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  // Get estimated travel time (rough calculation)
  Duration estimateTravelTime(double distanceInMeters, {double speedKmh = 30}) {
    final distanceKm = distanceInMeters / 1000;
    final timeHours = distanceKm / speedKmh;
    return Duration(minutes: (timeHours * 60).round());
  }

  // Dispose resources
  void dispose() {
    stopLocationTracking();
  }
}
