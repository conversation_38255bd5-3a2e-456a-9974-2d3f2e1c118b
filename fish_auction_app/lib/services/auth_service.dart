import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../utils/api_response.dart';
import 'api_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();
  User? _currentUser;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // Initialize auth service
  Future<void> initialize() async {
    await _apiService.loadAuthTokens();
    if (_apiService.isAuthenticated) {
      await _loadUserFromStorage();
      if (_currentUser != null) {
        // Verify token is still valid
        final profileResponse = await getUserProfile();
        if (!profileResponse.isSuccess) {
          await logout();
        }
      }
    }
  }

  // Login
  Future<ApiResponse<User>> login({required String username, required String password}) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.authEndpoint}/login/',
        data: {'username': username, 'password': password},
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;

        // Extract tokens
        final tokens = data['tokens'] as Map<String, dynamic>?;
        if (tokens == null) {
          return ApiResponse.error(message: 'Invalid login response: missing tokens', statusCode: 500);
        }

        final accessToken = tokens['access'] as String?;
        final refreshToken = tokens['refresh'] as String?;

        if (accessToken == null || refreshToken == null) {
          return ApiResponse.error(message: 'Invalid login response: missing access or refresh token', statusCode: 500);
        }

        // Extract user data
        final userData = data['user'] as Map<String, dynamic>?;
        if (userData == null) {
          return ApiResponse.error(message: 'Invalid login response: missing user data', statusCode: 500);
        }

        final user = User.fromJson(userData);

        // Save tokens and user
        await _apiService.setAuthTokens(accessToken, refreshToken);
        await _saveUserToStorage(user);
        _currentUser = user;

        return ApiResponse.success(data: user, statusCode: 200);
      } else {
        return ApiResponse.error(message: response.message ?? 'Login failed', statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error(message: 'Login error: ${e.toString()}', statusCode: 500);
    }
  }

  // Register
  Future<ApiResponse<User>> register({
    required String username,
    required String email,
    required String password,
    required String userType,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.authEndpoint}/register/',
        data: {
          'username': username,
          'email': email,
          'password': password,
          'password_confirm': password,
          'user_type': userType,
          if (firstName != null) 'first_name': firstName,
          if (lastName != null) 'last_name': lastName,
          if (phoneNumber != null) 'phone_number': phoneNumber,
        },
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;

        // Extract tokens
        final tokens = data['tokens'] as Map<String, dynamic>?;
        if (tokens == null) {
          return ApiResponse.error(message: 'Invalid registration response: missing tokens', statusCode: 500);
        }

        final accessToken = tokens['access'] as String?;
        final refreshToken = tokens['refresh'] as String?;

        if (accessToken == null || refreshToken == null) {
          return ApiResponse.error(
            message: 'Invalid registration response: missing access or refresh token',
            statusCode: 500,
          );
        }

        // Extract user data
        final userData = data['user'] as Map<String, dynamic>?;
        if (userData == null) {
          return ApiResponse.error(message: 'Invalid registration response: missing user data', statusCode: 500);
        }

        final user = User.fromJson(userData);

        // Save tokens and user
        await _apiService.setAuthTokens(accessToken, refreshToken);
        await _saveUserToStorage(user);
        _currentUser = user;

        return ApiResponse.success(data: user, statusCode: 201);
      } else {
        return ApiResponse.error(message: response.message ?? 'Registration failed', statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error(message: 'Registration error: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user profile
  Future<ApiResponse<User>> getUserProfile() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.authEndpoint}/profile/',
        fromJson: (data) => User.fromJson(data as Map<String, dynamic>),
      );

      if (response.isSuccess && response.data != null) {
        _currentUser = response.data;
        await _saveUserToStorage(_currentUser!);
        return ApiResponse.success(data: _currentUser!, statusCode: 200);
      } else {
        return ApiResponse.error(message: response.message ?? 'Failed to get profile', statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error(message: 'Profile error: ${e.toString()}', statusCode: 500);
    }
  }

  // Update user profile
  Future<ApiResponse<User>> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? address,
    String? city,
    String? country,
    String? preferredLanguage,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (firstName != null) data['first_name'] = firstName;
      if (lastName != null) data['last_name'] = lastName;
      if (phoneNumber != null) data['phone_number'] = phoneNumber;
      if (address != null) data['address'] = address;
      if (city != null) data['city'] = city;
      if (country != null) data['country'] = country;
      if (preferredLanguage != null) data['preferred_language'] = preferredLanguage;

      final response = await _apiService.put(
        '${AppConstants.authEndpoint}/profile/',
        data: data,
        fromJson: (data) => User.fromJson(data as Map<String, dynamic>),
      );

      if (response.isSuccess && response.data != null) {
        _currentUser = response.data;
        await _saveUserToStorage(_currentUser!);
        return ApiResponse.success(data: _currentUser!, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update profile',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Update profile error: ${e.toString()}', statusCode: 500);
    }
  }

  // Change password
  Future<ApiResponse<void>> changePassword({required String oldPassword, required String newPassword}) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.authEndpoint}/change-password/',
        data: {'old_password': oldPassword, 'new_password': newPassword},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: null, statusCode: 200);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to change password',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Change password error: ${e.toString()}', statusCode: 500);
    }
  }

  // Submit verification document (single)
  Future<ApiResponse<Map<String, dynamic>>> submitVerificationDocument({
    required File document,
    required String documentType, // 'government_id' or 'hunting_approval'
  }) async {
    try {
      final response = await _apiService.uploadFile(
        '${AppConstants.authEndpoint}/documents/submit/',
        document,
        fieldName: 'document_file',
        additionalData: {'document_type': documentType},
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      return ApiResponse.error(message: 'Document submission error: ${e.toString()}', statusCode: 500);
    }
  }

  // Submit multiple verification documents at once
  Future<ApiResponse<Map<String, dynamic>>> submitBatchVerificationDocuments({
    required File governmentId,
    required File huntingApproval,
  }) async {
    try {
      final response = await _apiService.uploadMultipleFiles(
        '${AppConstants.authEndpoint}/documents/submit-batch/',
        files: {'government_id': governmentId, 'hunting_approval': huntingApproval},
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      return ApiResponse.error(message: 'Batch document submission error: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user's document verification requests
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserDocumentRequests() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.authEndpoint}/documents/my-requests/',
        fromJson: (data) {
          // Handle both paginated and direct array responses
          if (data is Map<String, dynamic> && data.containsKey('results')) {
            return (data['results'] as List).cast<Map<String, dynamic>>();
          } else if (data is List) {
            return data.cast<Map<String, dynamic>>();
          } else {
            return <Map<String, dynamic>>[];
          }
        },
      );

      return response;
    } catch (e) {
      return ApiResponse.error(message: 'Failed to load document requests: ${e.toString()}', statusCode: 500);
    }
  }

  // Logout
  Future<void> logout() async {
    _currentUser = null;
    await _apiService.clearAuthTokens();
    await _clearUserFromStorage();
  }

  // Private methods
  Future<void> _saveUserToStorage(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userKey, jsonEncode(user.toJson()));
  }

  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userKey);
      if (userJson != null) {
        final userData = jsonDecode(userJson) as Map<String, dynamic>;
        _currentUser = User.fromJson(userData);
      }
    } catch (e) {
      print('Error loading user from storage: $e');
      _currentUser = null;
    }
  }

  Future<void> _clearUserFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userKey);
  }

  // Helper methods
  bool get canBid => _currentUser?.canBid ?? false;
  bool get canSell => _currentUser?.canSell ?? false;
  bool get isVerified => _currentUser?.isVerified ?? false;
  String get userType => _currentUser?.userType ?? '';
  double get walletBalance => _currentUser?.walletBalance ?? 0.0;
  String get kycStatus => _currentUser?.kycStatus ?? 'not_submitted';
}
