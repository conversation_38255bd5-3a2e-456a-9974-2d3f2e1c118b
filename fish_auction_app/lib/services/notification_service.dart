import '../models/notification_model.dart';
import '../utils/api_response.dart';
import 'api_service.dart';
import '../constants/app_constants.dart';

class NotificationService {
  final ApiService _apiService = ApiService();

  // Get notifications
  Future<ApiResponse<List<Notification>>> getNotifications({
    String? status,
    String? channel,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, String>{};
      
      if (status != null) queryParams['status'] = status;
      if (channel != null) queryParams['channel'] = channel;
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final response = await _apiService.get(
        AppConstants.notificationsEndpoint,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List<dynamic>;
        
        final notifications = results
            .map((json) => Notification.fromJson(json as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: notifications,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch notifications',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error fetching notifications: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Get unread notification count
  Future<ApiResponse<int>> getUnreadCount() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.notificationsEndpoint}/unread-count/',
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final count = data['unread_count'] as int;

        return ApiResponse.success(
          data: count,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch unread count',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error fetching unread count: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Mark notification as read
  Future<ApiResponse<Notification>> markAsRead(String notificationId) async {
    try {
      final response = await _apiService.patch(
        '${AppConstants.notificationsEndpoint}/$notificationId/',
        data: {'status': 'read'},
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final notification = Notification.fromJson(data);

        return ApiResponse.success(
          data: notification,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to mark notification as read',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error marking notification as read: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Mark all notifications as read
  Future<ApiResponse<String>> markAllAsRead() async {
    try {
      final response = await _apiService.post(
        '${AppConstants.notificationsEndpoint}/mark-all-read/',
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final message = data['message'] as String;

        return ApiResponse.success(
          data: message,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to mark all notifications as read',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error marking all notifications as read: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Get notification preferences
  Future<ApiResponse<NotificationPreference>> getPreferences() async {
    try {
      final response = await _apiService.get(
        '${AppConstants.notificationsEndpoint}/preferences/',
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final preferences = NotificationPreference.fromJson(data);

        return ApiResponse.success(
          data: preferences,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch notification preferences',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error fetching notification preferences: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Update notification preferences
  Future<ApiResponse<NotificationPreference>> updatePreferences(
    Map<String, dynamic> preferences,
  ) async {
    try {
      final response = await _apiService.patch(
        '${AppConstants.notificationsEndpoint}/preferences/',
        data: preferences,
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final updatedPreferences = NotificationPreference.fromJson(data);

        return ApiResponse.success(
          data: updatedPreferences,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to update notification preferences',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error updating notification preferences: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  // Send test notification
  Future<ApiResponse<String>> sendTestNotification() async {
    try {
      final response = await _apiService.post(
        '${AppConstants.notificationsEndpoint}/test/',
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final message = data['message'] as String;

        return ApiResponse.success(
          data: message,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to send test notification',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error sending test notification: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
