{"@@locale": "en", "@@last_modified": "2024-01-01T00:00:00.000Z", "appName": "Fish Auction", "@appName": {"description": "The name of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "register": "Register", "@register": {"description": "Register button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "username": "Username", "@username": {"description": "Username field label"}, "firstName": "First Name", "@firstName": {"description": "First name field label"}, "lastName": "Last Name", "@lastName": {"description": "Last name field label"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field label"}, "address": "Address", "@address": {"description": "Address field label"}, "city": "City", "@city": {"description": "City field label"}, "country": "Country", "@country": {"description": "Country field label"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message title"}, "success": "Success", "@success": {"description": "Success message title"}, "warning": "Warning", "@warning": {"description": "Warning message title"}, "info": "Information", "@info": {"description": "Information message title"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "search": "Search", "@search": {"description": "Search button text"}, "filter": "Filter", "@filter": {"description": "Filter button text"}, "sort": "Sort", "@sort": {"description": "Sort button text"}, "clear": "Clear", "@clear": {"description": "Clear button text"}, "apply": "Apply", "@apply": {"description": "Apply button text"}, "close": "Close", "@close": {"description": "Close button text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "submit": "Submit", "@submit": {"description": "Submit button text"}, "update": "Update", "@update": {"description": "Update button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "view": "View", "@view": {"description": "View button text"}, "add": "Add", "@add": {"description": "Add button text"}, "remove": "Remove", "@remove": {"description": "Remove button text"}, "select": "Select", "@select": {"description": "Select button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "required": "Required", "@required": {"description": "Required field indicator"}, "optional": "Optional", "@optional": {"description": "Optional field indicator"}, "invalidEmail": "Invalid email address", "@invalidEmail": {"description": "Invalid email validation message"}, "invalidPassword": "Invalid password", "@invalidPassword": {"description": "Invalid password validation message"}, "passwordTooShort": "Password is too short", "@passwordTooShort": {"description": "Password too short validation message"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Passwords mismatch validation message"}, "fieldRequired": "This field is required", "@fieldRequired": {"description": "Required field validation message"}, "networkError": "Network connection error", "@networkError": {"description": "Network error message"}, "serverError": "Server error occurred", "@serverError": {"description": "Server error message"}, "unknownError": "An unknown error occurred", "@unknownError": {"description": "Unknown error message"}, "timeoutError": "Request timeout", "@timeoutError": {"description": "Timeout error message"}, "authError": "Authentication failed", "@authError": {"description": "Authentication error message"}, "loginSuccess": "Login successful", "@loginSuccess": {"description": "Login success message"}, "registrationSuccess": "Registration successful", "@registrationSuccess": {"description": "Registration success message"}, "logoutSuccess": "Logout successful", "@logoutSuccess": {"description": "Logout success message"}, "profileUpdated": "Profile updated successfully", "@profileUpdated": {"description": "Profile update success message"}, "passwordChanged": "Password changed successfully", "@passwordChanged": {"description": "Password change success message"}, "home": "Home", "@home": {"description": "Home navigation tab"}, "auctions": "Auctions", "@auctions": {"description": "Auctions navigation tab"}, "watchlist": "Watchlist", "@watchlist": {"description": "Watchlist screen title"}, "myBids": "My Bids", "@myBids": {"description": "My bids navigation tab"}, "wallet": "Wallet", "@wallet": {"description": "Wallet navigation tab"}, "profile": "Profile", "@profile": {"description": "Profile navigation tab"}, "settings": "Settings", "@settings": {"description": "Settings navigation tab"}, "support": "Support", "@support": {"description": "Support navigation tab"}, "notifications": "Notifications", "@notifications": {"description": "Notifications screen title"}, "rememberMe": "Remember me", "@rememberMe": {"description": "Remember me checkbox text"}, "dontHaveAccount": "Don't have an account? ", "@dontHaveAccount": {"description": "Don't have account text"}, "alreadyHaveAccount": "Already have an account? ", "@alreadyHaveAccount": {"description": "Already have account text"}, "createYourAccount": "Create your account", "@createYourAccount": {"description": "Create account subtitle"}, "enterUsernameOrEmail": "Enter your username or email", "@enterUsernameOrEmail": {"description": "Username/email field hint"}, "pleaseConfirmPassword": "Please confirm your password", "@pleaseConfirmPassword": {"description": "Confirm password validation message"}, "buyFishFromAuctions": "Buy fish from auctions", "@buyFishFromAuctions": {"description": "Buyer account type description"}, "sellFishThroughAuctions": "Sell fish through auctions", "@sellFishThroughAuctions": {"description": "Seller account type description"}, "facilitateFishTrading": "Facilitate fish trading", "@facilitateFishTrading": {"description": "Broker account type description"}, "provideFishingServices": "Provide fishing services", "@provideFishingServices": {"description": "Service provider account type description"}, "acceptTerms": "I accept the terms and conditions", "@acceptTerms": {"description": "Accept terms checkbox text"}, "registrationFailed": "Registration failed", "@registrationFailed": {"description": "Registration failed error message"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "<PERSON><PERSON> failed error message"}, "myAuctions": "My Auctions", "@myAuctions": {"description": "My auctions tab"}, "deliveries": "Deliveries", "@deliveries": {"description": "Deliveries tab"}, "marketplace": "Marketplace", "@marketplace": {"description": "Marketplace tab"}, "myServices": "My Services", "@myServices": {"description": "My services tab"}, "recentAuctions": "Recent Auctions", "@recentAuctions": {"description": "Recent auctions section title"}, "all": "All", "@all": {"description": "All tab"}, "scheduled": "Scheduled", "@scheduled": {"description": "Scheduled tab"}, "earned": "Earned", "@earned": {"description": "Earned tab"}, "ended": "Ended", "@ended": {"description": "Ended tab"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "buyerType": "Buyer", "@buyerType": {"description": "Buyer user type"}, "sellerType": "<PERSON><PERSON>", "@sellerType": {"description": "Seller user type"}, "brokerType": "Broker", "@brokerType": {"description": "Broker user type"}, "serviceProviderType": "Service Provider", "@serviceProviderType": {"description": "Service provider user type"}, "liveAuctions": "Live Auctions", "@liveAuctions": {"description": "Live auctions section title"}, "endingSoon": "Ending Soon", "@endingSoon": {"description": "Ending soon section title"}, "noAuctions": "No auctions available", "@noAuctions": {"description": "No auctions message"}, "categories": "Categories", "@categories": {"description": "Categories section title"}, "readyToSellMessage": "Ready to sell your fresh catch? Create auctions and reach buyers worldwide.", "@readyToSellMessage": {"description": "Welcome message for sellers"}, "facilitateTradingMessage": "Facilitate fish trading and earn commissions on successful deals.", "@facilitateTradingMessage": {"description": "Welcome message for brokers"}, "fishingServicesMessage": "Offer your fishing services and equipment to the community.", "@fishingServicesMessage": {"description": "Welcome message for service providers"}, "adminManageMessage": "Manage the platform and oversee all auction activities.", "@adminManageMessage": {"description": "Welcome message for admins"}, "discoverAuctionsMessage": "Discover fresh fish auctions and place your bids on quality catches.", "@discoverAuctionsMessage": {"description": "Welcome message for buyers"}, "walletBalance": "Wallet Balance", "@walletBalance": {"description": "Wallet balance label"}, "addFunds": "Add Funds", "@addFunds": {"description": "Add funds button text"}, "withdrawFunds": "Withdraw Funds", "@withdrawFunds": {"description": "Withdraw funds button text"}, "amount": "Amount", "@amount": {"description": "Amount field label"}, "pleaseEnterValidAmount": "Please enter a valid amount", "@pleaseEnterValidAmount": {"description": "Invalid amount validation message"}, "minimumTopupAmount": "Minimum top-up amount is $1.00", "@minimumTopupAmount": {"description": "Minimum top-up amount message"}, "addPaymentMethod": "Add Payment Method", "@addPaymentMethod": {"description": "Add payment method button text"}, "kycApproved": "KYC Approved", "@kycApproved": {"description": "KYC approved status"}, "kycPending": "KYC Pending", "@kycPending": {"description": "KYC pending status"}, "kycRejected": "KYC Rejected", "@kycRejected": {"description": "KYC rejected status"}, "kycNotSubmitted": "KYC Not Submitted", "@kycNotSubmitted": {"description": "KYC not submitted status"}, "changePassword": "Change Password", "@changePassword": {"description": "Change password screen title"}, "notificationSettings": "Notification Settings", "@notificationSettings": {"description": "Notification settings option"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy policy option"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Terms of service option"}, "language": "Language", "@language": {"description": "Language setting option"}, "administrator": "Administrator", "@administrator": {"description": "Administrator user type"}, "user": "User", "@user": {"description": "Default user type"}, "placeBid": "Place Bid", "@placeBid": {"description": "Place bid button text"}, "autoBid": "Auto Bid", "@autoBid": {"description": "Auto bid button text"}, "auctionDetails": "Auction Details", "@auctionDetails": {"description": "Auction details tab"}, "description": "Description", "@description": {"description": "Description field label"}, "location": "Location", "@location": {"description": "Location field label"}, "category": "Category", "@category": {"description": "Category field label"}, "weight": "Weight", "@weight": {"description": "Weight field label"}, "startingPrice": "Starting Price", "@startingPrice": {"description": "Starting price field label"}, "currentBid": "Current Bid", "@currentBid": {"description": "Current bid label"}, "timeRemaining": "Time Remaining", "@timeRemaining": {"description": "Time remaining label"}, "bidHistory": "Bid History", "@bidHistory": {"description": "Bid history section title"}, "noBids": "No bids yet", "@noBids": {"description": "No bids message"}, "createAuction": "Create Auction", "@createAuction": {"description": "Create auction button text"}, "title": "Title", "@title": {"description": "Title field label"}, "price": "Price", "@price": {"description": "Price field label"}, "images": "Images", "@images": {"description": "Images field label"}, "publish": "Publish", "@publish": {"description": "Publish button text"}, "draft": "Draft", "@draft": {"description": "Draft button text"}, "brokerDashboard": "Broker Dashboard", "@brokerDashboard": {"description": "Broker dashboard title"}, "welcomeBroker": "Welcome", "@welcomeBroker": {"description": "Welcome message for broker"}, "manageBrokerServices": "Manage broker services and available requests", "@manageBrokerServices": {"description": "Broker services management description"}, "availableRequests": "Available Requests", "@availableRequests": {"description": "Available requests section"}, "activeServices": "Active Services", "@activeServices": {"description": "Active services section"}, "completed": "Completed", "@completed": {"description": "Completed status"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Quick actions section"}, "viewNewRequests": "View new requests", "@viewNewRequests": {"description": "View new requests action"}, "myServicesManage": "Manage active services", "@myServicesManage": {"description": "Manage services action"}, "serviceNotSpecified": "Service not specified", "@serviceNotSpecified": {"description": "Service not specified message"}, "auctionNotSpecified": "Auction: Not specified", "@auctionNotSpecified": {"description": "Auction not specified message"}, "serviceRequestSent": "Service request sent successfully", "@serviceRequestSent": {"description": "Service request sent success message"}, "failedToSendRequest": "Failed to send service request", "@failedToSendRequest": {"description": "Failed to send request error message"}, "specialNotes": "Special Notes", "@specialNotes": {"description": "Special notes field label"}, "bidAmount": "<PERSON><PERSON>", "@bidAmount": {"description": "Bid amount field label"}, "currentPrice": "Current Price", "@currentPrice": {"description": "Current price label"}, "kycVerification": "KYC Verification", "@kycVerification": {"description": "KYC verification section title"}, "submitKyc": "Submit KYC", "@submitKyc": {"description": "Submit KYC button text"}, "noNotifications": "No notifications", "@noNotifications": {"description": "No notifications message"}, "markAllAsRead": "Mark all as read", "@markAllAsRead": {"description": "Mark all notifications as read"}, "currentPassword": "Current Password", "@currentPassword": {"description": "Current password field label"}, "newPassword": "New Password", "@newPassword": {"description": "New password field label"}, "noWatchlist": "No items in watchlist", "@noWatchlist": {"description": "Empty watchlist message"}, "changeAppLanguage": "Change app language", "@changeAppLanguage": {"description": "Language settings subtitle"}}