/// Fish Auction App Localizations
/// Generated file. Do not edit.

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'مزاد الأسماك';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get firstName => 'الاسم الأول';

  @override
  String get lastName => 'اسم العائلة';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get address => 'العنوان';

  @override
  String get city => 'المدينة';

  @override
  String get country => 'البلد';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get ok => 'موافق';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get refresh => 'تحديث';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get clear => 'مسح';

  @override
  String get apply => 'تطبيق';

  @override
  String get close => 'إغلاق';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get submit => 'إرسال';

  @override
  String get update => 'تحديث';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get view => 'عرض';

  @override
  String get add => 'إضافة';

  @override
  String get remove => 'إزالة';

  @override
  String get select => 'اختيار';

  @override
  String get confirm => 'تأكيد';

  @override
  String get required => 'مطلوب';

  @override
  String get optional => 'اختياري';

  @override
  String get invalidEmail => 'عنوان بريد إلكتروني غير صحيح';

  @override
  String get invalidPassword => 'كلمة مرور غير صحيحة';

  @override
  String get passwordTooShort => 'كلمة المرور قصيرة جداً';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get fieldRequired => 'هذا الحقل مطلوب';

  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';

  @override
  String get serverError => 'حدث خطأ في الخادم';

  @override
  String get unknownError => 'حدث خطأ غير معروف';

  @override
  String get timeoutError => 'انتهت مهلة الطلب';

  @override
  String get authError => 'فشل في المصادقة';

  @override
  String get loginSuccess => 'تم تسجيل الدخول بنجاح';

  @override
  String get registrationSuccess => 'تم إنشاء الحساب بنجاح';

  @override
  String get logoutSuccess => 'تم تسجيل الخروج بنجاح';

  @override
  String get profileUpdated => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get passwordChanged => 'تم تغيير كلمة المرور بنجاح';

  @override
  String get home => 'الرئيسية';

  @override
  String get auctions => 'المزادات';

  @override
  String get watchlist => 'قائمة المراقبة';

  @override
  String get myBids => 'عروضي';

  @override
  String get wallet => 'المحفظة';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get support => 'الدعم';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get rememberMe => 'تذكرني';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟ ';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟ ';

  @override
  String get createYourAccount => 'إنشاء حسابك';

  @override
  String get enterUsernameOrEmail => 'أدخل اسم المستخدم أو البريد الإلكتروني';

  @override
  String get pleaseConfirmPassword => 'يرجى تأكيد كلمة المرور';

  @override
  String get buyFishFromAuctions => 'شراء الأسماك من المزادات';

  @override
  String get sellFishThroughAuctions => 'بيع الأسماك من خلال المزادات';

  @override
  String get facilitateFishTrading => 'تسهيل تجارة الأسماك';

  @override
  String get provideFishingServices => 'تقديم خدمات الصيد';

  @override
  String get acceptTerms => 'أوافق على الشروط والأحكام';

  @override
  String get registrationFailed => 'فشل في التسجيل';

  @override
  String get loginFailed => 'فشل في تسجيل الدخول';

  @override
  String get myAuctions => 'مزاداتي';

  @override
  String get deliveries => 'التوصيلات';

  @override
  String get marketplace => 'السوق';

  @override
  String get myServices => 'خدماتي';

  @override
  String get recentAuctions => 'المزادات الحديثة';

  @override
  String get all => 'الكل';

  @override
  String get scheduled => 'مجدولة';

  @override
  String get earned => 'مكتسبة';

  @override
  String get ended => 'منتهية';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get buyerType => 'مشتري';

  @override
  String get sellerType => 'بائع';

  @override
  String get brokerType => 'وسيط';

  @override
  String get serviceProviderType => 'مقدم خدمة';

  @override
  String get liveAuctions => 'المزادات المباشرة';

  @override
  String get endingSoon => 'تنتهي قريباً';

  @override
  String get noAuctions => 'لا توجد مزادات متاحة';

  @override
  String get categories => 'الفئات';

  @override
  String get readyToSellMessage =>
      'مستعد لبيع صيدك الطازج؟ أنشئ مزادات واوصل للمشترين حول العالم.';

  @override
  String get facilitateTradingMessage =>
      'سهّل تجارة الأسماك واكسب عمولات على الصفقات الناجحة.';

  @override
  String get fishingServicesMessage => 'قدم خدمات الصيد والمعدات للمجتمع.';

  @override
  String get adminManageMessage =>
      'إدارة المنصة والإشراف على جميع أنشطة المزادات.';

  @override
  String get discoverAuctionsMessage =>
      'اكتشف مزادات الأسماك الطازجة وقدم عروضك على الصيد عالي الجودة.';

  @override
  String get walletBalance => 'رصيد المحفظة';

  @override
  String get addFunds => 'إضافة أموال';

  @override
  String get withdrawFunds => 'سحب أموال';

  @override
  String get amount => 'المبلغ';

  @override
  String get pleaseEnterValidAmount => 'يرجى إدخال مبلغ صحيح';

  @override
  String get minimumTopupAmount => 'الحد الأدنى لشحن المحفظة هو 1.00 دولار';

  @override
  String get addPaymentMethod => 'إضافة طريقة دفع';

  @override
  String get kycApproved => 'تم اعتماد التحقق';

  @override
  String get kycPending => 'التحقق قيد المراجعة';

  @override
  String get kycRejected => 'تم رفض التحقق';

  @override
  String get kycNotSubmitted => 'لم يتم تقديم التحقق';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get notificationSettings => 'إعدادات الإشعارات';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get termsOfService => 'شروط الخدمة';

  @override
  String get language => 'اللغة';

  @override
  String get administrator => 'مدير';

  @override
  String get user => 'مستخدم';

  @override
  String get placeBid => 'تقديم عرض';

  @override
  String get autoBid => 'عرض تلقائي';

  @override
  String get auctionDetails => 'تفاصيل المزاد';

  @override
  String get description => 'الوصف';

  @override
  String get location => 'الموقع';

  @override
  String get category => 'الفئة';

  @override
  String get weight => 'الوزن';

  @override
  String get startingPrice => 'السعر الابتدائي';

  @override
  String get currentBid => 'العرض الحالي';

  @override
  String get timeRemaining => 'الوقت المتبقي';

  @override
  String get bidHistory => 'تاريخ العروض';

  @override
  String get noBids => 'لا توجد عروض بعد';

  @override
  String get createAuction => 'إنشاء مزاد';

  @override
  String get title => 'العنوان';

  @override
  String get price => 'السعر';

  @override
  String get images => 'الصور';

  @override
  String get publish => 'نشر';

  @override
  String get draft => 'مسودة';

  @override
  String get brokerDashboard => 'لوحة البروكر';

  @override
  String get welcomeBroker => 'مرحباً';

  @override
  String get manageBrokerServices => 'إدارة خدمات البروكر والطلبات المتاحة';

  @override
  String get availableRequests => 'الطلبات المتاحة';

  @override
  String get activeServices => 'الخدمات النشطة';

  @override
  String get completed => 'المكتملة';

  @override
  String get quickActions => 'الإجراءات السريعة';

  @override
  String get viewNewRequests => 'عرض الطلبات الجديدة';

  @override
  String get myServicesManage => 'إدارة الخدمات النشطة';

  @override
  String get serviceNotSpecified => 'خدمة غير محددة';

  @override
  String get auctionNotSpecified => 'المزاد: غير محدد';

  @override
  String get serviceRequestSent => 'تم إرسال طلب الخدمة بنجاح';

  @override
  String get failedToSendRequest => 'فشل في إرسال طلب الخدمة';

  @override
  String get specialNotes => 'ملاحظات خاصة';

  @override
  String get bidAmount => 'مبلغ العرض';

  @override
  String get currentPrice => 'السعر الحالي';

  @override
  String get kycVerification => 'التحقق من الهوية';

  @override
  String get submitKyc => 'إرسال وثائق التحقق';

  @override
  String get noNotifications => 'لا توجد إشعارات';

  @override
  String get markAllAsRead => 'تحديد الكل كمقروء';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get noWatchlist => 'لا توجد عناصر في قائمة المراقبة';

  @override
  String get changeAppLanguage => 'تغيير لغة التطبيق';
}
