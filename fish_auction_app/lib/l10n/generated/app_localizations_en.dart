/// Fish Auction App Localizations
/// Generated file. Do not edit.

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Fish Auction';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get logout => 'Logout';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get username => 'Username';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get address => 'Address';

  @override
  String get city => 'City';

  @override
  String get country => 'Country';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get retry => 'Retry';

  @override
  String get refresh => 'Refresh';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get clear => 'Clear';

  @override
  String get apply => 'Apply';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get submit => 'Submit';

  @override
  String get update => 'Update';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get view => 'View';

  @override
  String get add => 'Add';

  @override
  String get remove => 'Remove';

  @override
  String get select => 'Select';

  @override
  String get confirm => 'Confirm';

  @override
  String get required => 'Required';

  @override
  String get optional => 'Optional';

  @override
  String get invalidEmail => 'Invalid email address';

  @override
  String get invalidPassword => 'Invalid password';

  @override
  String get passwordTooShort => 'Password is too short';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get fieldRequired => 'This field is required';

  @override
  String get networkError => 'Network connection error';

  @override
  String get serverError => 'Server error occurred';

  @override
  String get unknownError => 'An unknown error occurred';

  @override
  String get timeoutError => 'Request timeout';

  @override
  String get authError => 'Authentication failed';

  @override
  String get loginSuccess => 'Login successful';

  @override
  String get registrationSuccess => 'Registration successful';

  @override
  String get logoutSuccess => 'Logout successful';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get passwordChanged => 'Password changed successfully';

  @override
  String get home => 'Home';

  @override
  String get auctions => 'Auctions';

  @override
  String get watchlist => 'Watchlist';

  @override
  String get myBids => 'My Bids';

  @override
  String get wallet => 'Wallet';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get support => 'Support';

  @override
  String get notifications => 'Notifications';

  @override
  String get rememberMe => 'Remember me';

  @override
  String get dontHaveAccount => 'Don\'t have an account? ';

  @override
  String get alreadyHaveAccount => 'Already have an account? ';

  @override
  String get createYourAccount => 'Create your account';

  @override
  String get enterUsernameOrEmail => 'Enter your username or email';

  @override
  String get pleaseConfirmPassword => 'Please confirm your password';

  @override
  String get buyFishFromAuctions => 'Buy fish from auctions';

  @override
  String get sellFishThroughAuctions => 'Sell fish through auctions';

  @override
  String get facilitateFishTrading => 'Facilitate fish trading';

  @override
  String get provideFishingServices => 'Provide fishing services';

  @override
  String get acceptTerms => 'I accept the terms and conditions';

  @override
  String get registrationFailed => 'Registration failed';

  @override
  String get loginFailed => 'Login failed';

  @override
  String get myAuctions => 'My Auctions';

  @override
  String get deliveries => 'Deliveries';

  @override
  String get marketplace => 'Marketplace';

  @override
  String get myServices => 'My Services';

  @override
  String get recentAuctions => 'Recent Auctions';

  @override
  String get all => 'All';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get earned => 'Earned';

  @override
  String get ended => 'Ended';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get buyerType => 'Buyer';

  @override
  String get sellerType => 'Seller';

  @override
  String get brokerType => 'Broker';

  @override
  String get serviceProviderType => 'Service Provider';

  @override
  String get liveAuctions => 'Live Auctions';

  @override
  String get endingSoon => 'Ending Soon';

  @override
  String get noAuctions => 'No auctions available';

  @override
  String get categories => 'Categories';

  @override
  String get readyToSellMessage =>
      'Ready to sell your fresh catch? Create auctions and reach buyers worldwide.';

  @override
  String get facilitateTradingMessage =>
      'Facilitate fish trading and earn commissions on successful deals.';

  @override
  String get fishingServicesMessage =>
      'Offer your fishing services and equipment to the community.';

  @override
  String get adminManageMessage =>
      'Manage the platform and oversee all auction activities.';

  @override
  String get discoverAuctionsMessage =>
      'Discover fresh fish auctions and place your bids on quality catches.';

  @override
  String get walletBalance => 'Wallet Balance';

  @override
  String get addFunds => 'Add Funds';

  @override
  String get withdrawFunds => 'Withdraw Funds';

  @override
  String get amount => 'Amount';

  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';

  @override
  String get minimumTopupAmount => 'Minimum top-up amount is \$1.00';

  @override
  String get addPaymentMethod => 'Add Payment Method';

  @override
  String get kycApproved => 'KYC Approved';

  @override
  String get kycPending => 'KYC Pending';

  @override
  String get kycRejected => 'KYC Rejected';

  @override
  String get kycNotSubmitted => 'KYC Not Submitted';

  @override
  String get changePassword => 'Change Password';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get language => 'Language';

  @override
  String get administrator => 'Administrator';

  @override
  String get user => 'User';

  @override
  String get placeBid => 'Place Bid';

  @override
  String get autoBid => 'Auto Bid';

  @override
  String get auctionDetails => 'Auction Details';

  @override
  String get description => 'Description';

  @override
  String get location => 'Location';

  @override
  String get category => 'Category';

  @override
  String get weight => 'Weight';

  @override
  String get startingPrice => 'Starting Price';

  @override
  String get currentBid => 'Current Bid';

  @override
  String get timeRemaining => 'Time Remaining';

  @override
  String get bidHistory => 'Bid History';

  @override
  String get noBids => 'No bids yet';

  @override
  String get createAuction => 'Create Auction';

  @override
  String get title => 'Title';

  @override
  String get price => 'Price';

  @override
  String get images => 'Images';

  @override
  String get publish => 'Publish';

  @override
  String get draft => 'Draft';

  @override
  String get brokerDashboard => 'Broker Dashboard';

  @override
  String get welcomeBroker => 'Welcome';

  @override
  String get manageBrokerServices =>
      'Manage broker services and available requests';

  @override
  String get availableRequests => 'Available Requests';

  @override
  String get activeServices => 'Active Services';

  @override
  String get completed => 'Completed';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get viewNewRequests => 'View new requests';

  @override
  String get myServicesManage => 'Manage active services';

  @override
  String get serviceNotSpecified => 'Service not specified';

  @override
  String get auctionNotSpecified => 'Auction: Not specified';

  @override
  String get serviceRequestSent => 'Service request sent successfully';

  @override
  String get failedToSendRequest => 'Failed to send service request';

  @override
  String get specialNotes => 'Special Notes';

  @override
  String get bidAmount => 'Bid Amount';

  @override
  String get currentPrice => 'Current Price';

  @override
  String get kycVerification => 'KYC Verification';

  @override
  String get submitKyc => 'Submit KYC';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get noWatchlist => 'No items in watchlist';

  @override
  String get changeAppLanguage => 'Change app language';
}
