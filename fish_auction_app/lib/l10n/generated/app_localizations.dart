/// Fish Auction App Localizations
/// Generated file. Do not edit.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'Fish Auction'**
  String get appName;

  /// Welcome message
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Register button text
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// Logout button text
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Username field label
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// First name field label
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// Last name field label
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// Phone number field label
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// Address field label
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// City field label
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// Country field label
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Yes button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error message title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message title
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Warning message title
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// Information message title
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get info;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// Search button text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Filter button text
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Sort button text
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// Clear button text
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// Apply button text
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Previous button text
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Submit button text
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// Update button text
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// View button text
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Remove button text
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// Select button text
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Required field indicator
  ///
  /// In en, this message translates to:
  /// **'Required'**
  String get required;

  /// Optional field indicator
  ///
  /// In en, this message translates to:
  /// **'Optional'**
  String get optional;

  /// Invalid email validation message
  ///
  /// In en, this message translates to:
  /// **'Invalid email address'**
  String get invalidEmail;

  /// Invalid password validation message
  ///
  /// In en, this message translates to:
  /// **'Invalid password'**
  String get invalidPassword;

  /// Password too short validation message
  ///
  /// In en, this message translates to:
  /// **'Password is too short'**
  String get passwordTooShort;

  /// Passwords mismatch validation message
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// Required field validation message
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get fieldRequired;

  /// Network error message
  ///
  /// In en, this message translates to:
  /// **'Network connection error'**
  String get networkError;

  /// Server error message
  ///
  /// In en, this message translates to:
  /// **'Server error occurred'**
  String get serverError;

  /// Unknown error message
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred'**
  String get unknownError;

  /// Timeout error message
  ///
  /// In en, this message translates to:
  /// **'Request timeout'**
  String get timeoutError;

  /// Authentication error message
  ///
  /// In en, this message translates to:
  /// **'Authentication failed'**
  String get authError;

  /// Login success message
  ///
  /// In en, this message translates to:
  /// **'Login successful'**
  String get loginSuccess;

  /// Registration success message
  ///
  /// In en, this message translates to:
  /// **'Registration successful'**
  String get registrationSuccess;

  /// Logout success message
  ///
  /// In en, this message translates to:
  /// **'Logout successful'**
  String get logoutSuccess;

  /// Profile update success message
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdated;

  /// Password change success message
  ///
  /// In en, this message translates to:
  /// **'Password changed successfully'**
  String get passwordChanged;

  /// Home navigation tab
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Auctions navigation tab
  ///
  /// In en, this message translates to:
  /// **'Auctions'**
  String get auctions;

  /// Watchlist screen title
  ///
  /// In en, this message translates to:
  /// **'Watchlist'**
  String get watchlist;

  /// My bids navigation tab
  ///
  /// In en, this message translates to:
  /// **'My Bids'**
  String get myBids;

  /// Wallet navigation tab
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get wallet;

  /// Profile navigation tab
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Settings navigation tab
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Support navigation tab
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// Notifications screen title
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Remember me checkbox text
  ///
  /// In en, this message translates to:
  /// **'Remember me'**
  String get rememberMe;

  /// Don't have account text
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account? '**
  String get dontHaveAccount;

  /// Already have account text
  ///
  /// In en, this message translates to:
  /// **'Already have an account? '**
  String get alreadyHaveAccount;

  /// Create account subtitle
  ///
  /// In en, this message translates to:
  /// **'Create your account'**
  String get createYourAccount;

  /// Username/email field hint
  ///
  /// In en, this message translates to:
  /// **'Enter your username or email'**
  String get enterUsernameOrEmail;

  /// Confirm password validation message
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get pleaseConfirmPassword;

  /// Buyer account type description
  ///
  /// In en, this message translates to:
  /// **'Buy fish from auctions'**
  String get buyFishFromAuctions;

  /// Seller account type description
  ///
  /// In en, this message translates to:
  /// **'Sell fish through auctions'**
  String get sellFishThroughAuctions;

  /// Broker account type description
  ///
  /// In en, this message translates to:
  /// **'Facilitate fish trading'**
  String get facilitateFishTrading;

  /// Service provider account type description
  ///
  /// In en, this message translates to:
  /// **'Provide fishing services'**
  String get provideFishingServices;

  /// Accept terms checkbox text
  ///
  /// In en, this message translates to:
  /// **'I accept the terms and conditions'**
  String get acceptTerms;

  /// Registration failed error message
  ///
  /// In en, this message translates to:
  /// **'Registration failed'**
  String get registrationFailed;

  /// Login failed error message
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginFailed;

  /// My auctions tab
  ///
  /// In en, this message translates to:
  /// **'My Auctions'**
  String get myAuctions;

  /// Deliveries tab
  ///
  /// In en, this message translates to:
  /// **'Deliveries'**
  String get deliveries;

  /// Marketplace tab
  ///
  /// In en, this message translates to:
  /// **'Marketplace'**
  String get marketplace;

  /// My services tab
  ///
  /// In en, this message translates to:
  /// **'My Services'**
  String get myServices;

  /// Recent auctions section title
  ///
  /// In en, this message translates to:
  /// **'Recent Auctions'**
  String get recentAuctions;

  /// All tab
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Scheduled tab
  ///
  /// In en, this message translates to:
  /// **'Scheduled'**
  String get scheduled;

  /// Earned tab
  ///
  /// In en, this message translates to:
  /// **'Earned'**
  String get earned;

  /// Ended tab
  ///
  /// In en, this message translates to:
  /// **'Ended'**
  String get ended;

  /// Confirm password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Buyer user type
  ///
  /// In en, this message translates to:
  /// **'Buyer'**
  String get buyerType;

  /// Seller user type
  ///
  /// In en, this message translates to:
  /// **'Seller'**
  String get sellerType;

  /// Broker user type
  ///
  /// In en, this message translates to:
  /// **'Broker'**
  String get brokerType;

  /// Service provider user type
  ///
  /// In en, this message translates to:
  /// **'Service Provider'**
  String get serviceProviderType;

  /// Live auctions section title
  ///
  /// In en, this message translates to:
  /// **'Live Auctions'**
  String get liveAuctions;

  /// Ending soon section title
  ///
  /// In en, this message translates to:
  /// **'Ending Soon'**
  String get endingSoon;

  /// No auctions message
  ///
  /// In en, this message translates to:
  /// **'No auctions available'**
  String get noAuctions;

  /// Categories section title
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// Welcome message for sellers
  ///
  /// In en, this message translates to:
  /// **'Ready to sell your fresh catch? Create auctions and reach buyers worldwide.'**
  String get readyToSellMessage;

  /// Welcome message for brokers
  ///
  /// In en, this message translates to:
  /// **'Facilitate fish trading and earn commissions on successful deals.'**
  String get facilitateTradingMessage;

  /// Welcome message for service providers
  ///
  /// In en, this message translates to:
  /// **'Offer your fishing services and equipment to the community.'**
  String get fishingServicesMessage;

  /// Welcome message for admins
  ///
  /// In en, this message translates to:
  /// **'Manage the platform and oversee all auction activities.'**
  String get adminManageMessage;

  /// Welcome message for buyers
  ///
  /// In en, this message translates to:
  /// **'Discover fresh fish auctions and place your bids on quality catches.'**
  String get discoverAuctionsMessage;

  /// Wallet balance label
  ///
  /// In en, this message translates to:
  /// **'Wallet Balance'**
  String get walletBalance;

  /// Add funds button text
  ///
  /// In en, this message translates to:
  /// **'Add Funds'**
  String get addFunds;

  /// Withdraw funds button text
  ///
  /// In en, this message translates to:
  /// **'Withdraw Funds'**
  String get withdrawFunds;

  /// Amount field label
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// Invalid amount validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid amount'**
  String get pleaseEnterValidAmount;

  /// Minimum top-up amount message
  ///
  /// In en, this message translates to:
  /// **'Minimum top-up amount is \$1.00'**
  String get minimumTopupAmount;

  /// Add payment method button text
  ///
  /// In en, this message translates to:
  /// **'Add Payment Method'**
  String get addPaymentMethod;

  /// KYC approved status
  ///
  /// In en, this message translates to:
  /// **'KYC Approved'**
  String get kycApproved;

  /// KYC pending status
  ///
  /// In en, this message translates to:
  /// **'KYC Pending'**
  String get kycPending;

  /// KYC rejected status
  ///
  /// In en, this message translates to:
  /// **'KYC Rejected'**
  String get kycRejected;

  /// KYC not submitted status
  ///
  /// In en, this message translates to:
  /// **'KYC Not Submitted'**
  String get kycNotSubmitted;

  /// Change password screen title
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// Notification settings option
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notificationSettings;

  /// Privacy policy option
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Terms of service option
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Language setting option
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Administrator user type
  ///
  /// In en, this message translates to:
  /// **'Administrator'**
  String get administrator;

  /// Default user type
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get user;

  /// Place bid button text
  ///
  /// In en, this message translates to:
  /// **'Place Bid'**
  String get placeBid;

  /// Auto bid button text
  ///
  /// In en, this message translates to:
  /// **'Auto Bid'**
  String get autoBid;

  /// Auction details tab
  ///
  /// In en, this message translates to:
  /// **'Auction Details'**
  String get auctionDetails;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Location field label
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Weight field label
  ///
  /// In en, this message translates to:
  /// **'Weight'**
  String get weight;

  /// Starting price field label
  ///
  /// In en, this message translates to:
  /// **'Starting Price'**
  String get startingPrice;

  /// Current bid label
  ///
  /// In en, this message translates to:
  /// **'Current Bid'**
  String get currentBid;

  /// Time remaining label
  ///
  /// In en, this message translates to:
  /// **'Time Remaining'**
  String get timeRemaining;

  /// Bid history section title
  ///
  /// In en, this message translates to:
  /// **'Bid History'**
  String get bidHistory;

  /// No bids message
  ///
  /// In en, this message translates to:
  /// **'No bids yet'**
  String get noBids;

  /// Create auction button text
  ///
  /// In en, this message translates to:
  /// **'Create Auction'**
  String get createAuction;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Price field label
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// Images field label
  ///
  /// In en, this message translates to:
  /// **'Images'**
  String get images;

  /// Publish button text
  ///
  /// In en, this message translates to:
  /// **'Publish'**
  String get publish;

  /// Draft button text
  ///
  /// In en, this message translates to:
  /// **'Draft'**
  String get draft;

  /// Broker dashboard title
  ///
  /// In en, this message translates to:
  /// **'Broker Dashboard'**
  String get brokerDashboard;

  /// Welcome message for broker
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcomeBroker;

  /// Broker services management description
  ///
  /// In en, this message translates to:
  /// **'Manage broker services and available requests'**
  String get manageBrokerServices;

  /// Available requests section
  ///
  /// In en, this message translates to:
  /// **'Available Requests'**
  String get availableRequests;

  /// Active services section
  ///
  /// In en, this message translates to:
  /// **'Active Services'**
  String get activeServices;

  /// Completed status
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// Quick actions section
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// View new requests action
  ///
  /// In en, this message translates to:
  /// **'View new requests'**
  String get viewNewRequests;

  /// Manage services action
  ///
  /// In en, this message translates to:
  /// **'Manage active services'**
  String get myServicesManage;

  /// Service not specified message
  ///
  /// In en, this message translates to:
  /// **'Service not specified'**
  String get serviceNotSpecified;

  /// Auction not specified message
  ///
  /// In en, this message translates to:
  /// **'Auction: Not specified'**
  String get auctionNotSpecified;

  /// Service request sent success message
  ///
  /// In en, this message translates to:
  /// **'Service request sent successfully'**
  String get serviceRequestSent;

  /// Failed to send request error message
  ///
  /// In en, this message translates to:
  /// **'Failed to send service request'**
  String get failedToSendRequest;

  /// Special notes field label
  ///
  /// In en, this message translates to:
  /// **'Special Notes'**
  String get specialNotes;

  /// Bid amount field label
  ///
  /// In en, this message translates to:
  /// **'Bid Amount'**
  String get bidAmount;

  /// Current price label
  ///
  /// In en, this message translates to:
  /// **'Current Price'**
  String get currentPrice;

  /// KYC verification section title
  ///
  /// In en, this message translates to:
  /// **'KYC Verification'**
  String get kycVerification;

  /// Submit KYC button text
  ///
  /// In en, this message translates to:
  /// **'Submit KYC'**
  String get submitKyc;

  /// No notifications message
  ///
  /// In en, this message translates to:
  /// **'No notifications'**
  String get noNotifications;

  /// Mark all notifications as read
  ///
  /// In en, this message translates to:
  /// **'Mark all as read'**
  String get markAllAsRead;

  /// Current password field label
  ///
  /// In en, this message translates to:
  /// **'Current Password'**
  String get currentPassword;

  /// New password field label
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// Empty watchlist message
  ///
  /// In en, this message translates to:
  /// **'No items in watchlist'**
  String get noWatchlist;

  /// Language settings subtitle
  ///
  /// In en, this message translates to:
  /// **'Change app language'**
  String get changeAppLanguage;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
