class Notification {
  final String id;
  final String title;
  final String message;
  final String channel;
  final String channelDisplay;
  final String status;
  final String statusDisplay;
  final int? auctionId;
  final String? paymentId;
  final String? externalId;
  final DateTime? scheduledAt;
  final DateTime? sentAt;
  final DateTime? deliveredAt;
  final DateTime? readAt;
  final DateTime createdAt;

  Notification({
    required this.id,
    required this.title,
    required this.message,
    required this.channel,
    required this.channelDisplay,
    required this.status,
    required this.statusDisplay,
    this.auctionId,
    this.paymentId,
    this.externalId,
    this.scheduledAt,
    this.sentAt,
    this.deliveredAt,
    this.readAt,
    required this.createdAt,
  });

  factory Notification.fromJson(Map<String, dynamic> json) {
    return Notification(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      channel: json['channel'] ?? '',
      channelDisplay: json['channel_display'] ?? '',
      status: json['status'] ?? '',
      statusDisplay: json['status_display'] ?? '',
      auctionId: json['auction'],
      paymentId: json['payment'],
      externalId: json['external_id'],
      scheduledAt: json['scheduled_at'] != null ? DateTime.parse(json['scheduled_at']) : null,
      sentAt: json['sent_at'] != null ? DateTime.parse(json['sent_at']) : null,
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'channel': channel,
      'channel_display': channelDisplay,
      'status': status,
      'status_display': statusDisplay,
      'auction': auctionId,
      'payment': paymentId,
      'external_id': externalId,
      'scheduled_at': scheduledAt?.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isRead => status == 'read';
  bool get isUnread => !isRead;

  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Notification copyWith({
    String? id,
    String? title,
    String? message,
    String? channel,
    String? channelDisplay,
    String? status,
    String? statusDisplay,
    int? auctionId,
    String? paymentId,
    String? externalId,
    DateTime? scheduledAt,
    DateTime? sentAt,
    DateTime? deliveredAt,
    DateTime? readAt,
    DateTime? createdAt,
  }) {
    return Notification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      channel: channel ?? this.channel,
      channelDisplay: channelDisplay ?? this.channelDisplay,
      status: status ?? this.status,
      statusDisplay: statusDisplay ?? this.statusDisplay,
      auctionId: auctionId ?? this.auctionId,
      paymentId: paymentId ?? this.paymentId,
      externalId: externalId ?? this.externalId,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Notification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Notification{id: $id, title: $title, status: $status}';
  }
}

class NotificationPreference {
  final String id;
  final bool emailAuctionUpdates;
  final bool emailBidUpdates;
  final bool emailPaymentReminders;
  final bool emailDeliveryUpdates;
  final bool emailMarketing;
  final bool whatsappAuctionUpdates;
  final bool whatsappBidUpdates;
  final bool whatsappPaymentReminders;
  final bool whatsappDeliveryUpdates;
  final bool pushAuctionUpdates;
  final bool pushBidUpdates;
  final bool pushPaymentReminders;
  final bool pushDeliveryUpdates;
  final String quietHoursStart;
  final String quietHoursEnd;
  final String timezone;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationPreference({
    required this.id,
    required this.emailAuctionUpdates,
    required this.emailBidUpdates,
    required this.emailPaymentReminders,
    required this.emailDeliveryUpdates,
    required this.emailMarketing,
    required this.whatsappAuctionUpdates,
    required this.whatsappBidUpdates,
    required this.whatsappPaymentReminders,
    required this.whatsappDeliveryUpdates,
    required this.pushAuctionUpdates,
    required this.pushBidUpdates,
    required this.pushPaymentReminders,
    required this.pushDeliveryUpdates,
    required this.quietHoursStart,
    required this.quietHoursEnd,
    required this.timezone,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationPreference.fromJson(Map<String, dynamic> json) {
    return NotificationPreference(
      id: json['id']?.toString() ?? '',
      emailAuctionUpdates: json['email_auction_updates'] ?? true,
      emailBidUpdates: json['email_bid_updates'] ?? true,
      emailPaymentReminders: json['email_payment_reminders'] ?? true,
      emailDeliveryUpdates: json['email_delivery_updates'] ?? true,
      emailMarketing: json['email_marketing'] ?? false,
      whatsappAuctionUpdates: json['whatsapp_auction_updates'] ?? true,
      whatsappBidUpdates: json['whatsapp_bid_updates'] ?? true,
      whatsappPaymentReminders: json['whatsapp_payment_reminders'] ?? true,
      whatsappDeliveryUpdates: json['whatsapp_delivery_updates'] ?? true,
      pushAuctionUpdates: json['push_auction_updates'] ?? true,
      pushBidUpdates: json['push_bid_updates'] ?? true,
      pushPaymentReminders: json['push_payment_reminders'] ?? true,
      pushDeliveryUpdates: json['push_delivery_updates'] ?? true,
      quietHoursStart: json['quiet_hours_start'] ?? '22:00',
      quietHoursEnd: json['quiet_hours_end'] ?? '08:00',
      timezone: json['timezone'] ?? 'UTC',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email_auction_updates': emailAuctionUpdates,
      'email_bid_updates': emailBidUpdates,
      'email_payment_reminders': emailPaymentReminders,
      'email_delivery_updates': emailDeliveryUpdates,
      'email_marketing': emailMarketing,
      'whatsapp_auction_updates': whatsappAuctionUpdates,
      'whatsapp_bid_updates': whatsappBidUpdates,
      'whatsapp_payment_reminders': whatsappPaymentReminders,
      'whatsapp_delivery_updates': whatsappDeliveryUpdates,
      'push_auction_updates': pushAuctionUpdates,
      'push_bid_updates': pushBidUpdates,
      'push_payment_reminders': pushPaymentReminders,
      'push_delivery_updates': pushDeliveryUpdates,
      'quiet_hours_start': quietHoursStart,
      'quiet_hours_end': quietHoursEnd,
      'timezone': timezone,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
