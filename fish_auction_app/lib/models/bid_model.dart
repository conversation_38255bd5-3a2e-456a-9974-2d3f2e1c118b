import 'user_model.dart';
import 'auction_model.dart';

class Bid {
  final int id;
  final Auction? auction; // Make optional since bids API doesn't include full auction
  final int? auctionId; // Add auction ID for when full auction isn't available
  final User bidder;
  final double amount;
  final bool isAutoBid;
  final bool isWinningBid;
  final DateTime createdAt;

  Bid({
    required this.id,
    this.auction, // Make optional
    this.auctionId, // Add auction ID
    required this.bidder,
    required this.amount,
    required this.isAutoBid,
    required this.isWinningBid,
    required this.createdAt,
  });

  factory Bid.fromJson(Map<String, dynamic> json) {
    return Bid(
      id: json['id'] ?? 0,
      auction: json['auction'] != null ? Auction.fromJson(json['auction']) : null,
      auctionId: json['auction_id'] ?? json['auction']?['id'],
      bidder: User.fromJson(json['bidder'] ?? {}),
      amount: _parseDouble(json['amount']),
      isAutoBid: json['bid_type'] == 'auto' || json['is_auto_bid'] == true,
      isWinningBid: json['is_winning'] ?? json['is_winning_bid'] ?? false,
      createdAt: DateTime.parse(json['timestamp'] ?? json['created_at']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auction': auction?.toJson(),
      'bidder': bidder.toJson(),
      'amount': amount,
      'is_auto_bid': isAutoBid,
      'is_winning_bid': isWinningBid,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get bidTypeDisplay => isAutoBid ? 'Auto Bid' : 'Manual Bid';

  String get statusDisplay {
    if (isWinningBid) return 'Winning';
    if (auction?.isEnded == true) return 'Lost';
    return 'Active';
  }

  String get bidderName => bidder.displayName;

  String get formattedTimestamp {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bid && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Bid{id: $id, amount: $amount, isWinning: $isWinningBid}';
  }
}

class AutoBid {
  final int id;
  final Auction auction;
  final User user;
  final double maxAmount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  AutoBid({
    required this.id,
    required this.auction,
    required this.user,
    required this.maxAmount,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AutoBid.fromJson(Map<String, dynamic> json) {
    return AutoBid(
      id: json['id'] ?? 0,
      auction: Auction.fromJson(json['auction'] ?? {}),
      user: User.fromJson(json['user'] ?? {}),
      maxAmount: _parseDouble(json['max_amount']),
      isActive: json['is_active'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auction': auction.toJson(),
      'user': user.toJson(),
      'max_amount': maxAmount,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get statusDisplay => isActive ? 'Active' : 'Inactive';

  bool get canBid => isActive && maxAmount > auction.currentPrice;

  double get remainingAmount => maxAmount - auction.currentPrice;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AutoBid && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AutoBid{id: $id, maxAmount: $maxAmount, isActive: $isActive}';
  }
}
