class Location {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final double? altitude;
  final double? heading;
  final double? speed;
  final DateTime timestamp;

  Location({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.altitude,
    this.heading,
    this.speed,
    required this.timestamp,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: json['accuracy'] != null ? (json['accuracy'] as num).toDouble() : null,
      altitude: json['altitude'] != null ? (json['altitude'] as num).toDouble() : null,
      heading: json['heading'] != null ? (json['heading'] as num).toDouble() : null,
      speed: json['speed'] != null ? (json['speed'] as num).toDouble() : null,
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'altitude': altitude,
      'heading': heading,
      'speed': speed,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'Location(lat: $latitude, lng: $longitude, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Location &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode ^ timestamp.hashCode;
  }
}

class SellerLocation {
  final String id;
  final String sellerId;
  final String sellerName;
  final Location location;
  final bool isActive;
  final String? status;
  final DateTime createdAt;
  final DateTime updatedAt;

  SellerLocation({
    required this.id,
    required this.sellerId,
    required this.sellerName,
    required this.location,
    required this.isActive,
    this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SellerLocation.fromJson(Map<String, dynamic> json) {
    // Handle both nested location format and flat format from backend
    Location location;
    if (json.containsKey('location') && json['location'] is Map) {
      location = Location.fromJson(json['location'] as Map<String, dynamic>);
    } else {
      // Handle flat format from backend
      location = Location(
        latitude: double.parse(json['latitude']?.toString() ?? '0.0'),
        longitude: double.parse(json['longitude']?.toString() ?? '0.0'),
        accuracy: json['accuracy'] != null ? double.parse(json['accuracy'].toString()) : null,
        altitude: json['altitude'] != null ? double.parse(json['altitude'].toString()) : null,
        heading: json['heading'] != null ? double.parse(json['heading'].toString()) : null,
        speed: json['speed'] != null ? double.parse(json['speed'].toString()) : null,
        timestamp: json['timestamp'] != null
            ? DateTime.parse(json['timestamp'])
            : (json['location_updated_at'] != null ? DateTime.parse(json['location_updated_at']) : DateTime.now()),
      );
    }

    return SellerLocation(
      id: json['id']?.toString() ?? '',
      sellerId: json['seller']?.toString() ?? json['seller_id']?.toString() ?? '',
      sellerName: json['seller_name'] ?? '',
      location: location,
      isActive: json['is_active'] ?? json['is_location_live'] ?? false,
      status: json['status'],
      createdAt: json['timestamp'] != null ? DateTime.parse(json['timestamp']) : DateTime.now(),
      updatedAt: json['timestamp'] != null ? DateTime.parse(json['timestamp']) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'seller_id': sellerId,
      'seller_name': sellerName,
      'location': location.toJson(),
      'is_active': isActive,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get statusDisplay {
    switch (status) {
      case 'preparing':
        return 'Preparing Order';
      case 'ready':
        return 'Ready for Pickup';
      case 'in_transit':
        return 'In Transit';
      case 'delivered':
        return 'Delivered';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  }

  Duration get lastUpdateDuration {
    return DateTime.now().difference(updatedAt);
  }

  String get lastUpdateDisplay {
    final duration = lastUpdateDuration;
    if (duration.inMinutes < 1) {
      return 'Just now';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ago';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h ago';
    } else {
      return '${duration.inDays}d ago';
    }
  }

  bool get isRecentlyUpdated {
    return lastUpdateDuration.inMinutes <= 5;
  }
}

class DeliveryTracking {
  final String id;
  final String auctionId;
  final String buyerId;
  final String sellerId;
  final String status;
  final Location? currentLocation;
  final Location? destinationLocation;
  final List<TrackingEvent> events;
  final DateTime? estimatedDelivery;
  final DateTime? actualDelivery;
  final DateTime createdAt;
  final DateTime updatedAt;

  DeliveryTracking({
    required this.id,
    required this.auctionId,
    required this.buyerId,
    required this.sellerId,
    required this.status,
    this.currentLocation,
    this.destinationLocation,
    required this.events,
    this.estimatedDelivery,
    this.actualDelivery,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DeliveryTracking.fromJson(Map<String, dynamic> json) {
    return DeliveryTracking(
      id: json['id']?.toString() ?? '',
      auctionId: json['auction']?['id']?.toString() ?? json['auction_id']?.toString() ?? '',
      buyerId: json['buyer']?['id']?.toString() ?? json['buyer_id']?.toString() ?? '',
      sellerId: json['seller']?['id']?.toString() ?? json['seller_id']?.toString() ?? '',
      status: json['status'] ?? '',
      currentLocation: json['current_location'] != null ? Location.fromJson(json['current_location']) : null,
      destinationLocation: json['destination_location'] != null
          ? Location.fromJson(json['destination_location'])
          : null,
      events: (json['updates'] as List<dynamic>? ?? [])
          .map((e) => TrackingEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      estimatedDelivery: json['estimated_delivery_time'] != null
          ? DateTime.parse(json['estimated_delivery_time'])
          : null,
      actualDelivery: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  String get statusDisplay {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'picked_up':
        return 'Picked Up';
      case 'in_transit':
        return 'In Transit';
      case 'out_for_delivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'failed':
        return 'Delivery Failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  bool get isActive {
    return ['preparing', 'ready', 'picked_up', 'in_transit', 'out_for_delivery'].contains(status);
  }

  bool get isCompleted {
    return status == 'delivered';
  }

  bool get isFailed {
    return ['failed', 'cancelled'].contains(status);
  }

  TrackingEvent? get latestEvent {
    if (events.isEmpty) return null;
    return events.reduce((a, b) => a.timestamp.isAfter(b.timestamp) ? a : b);
  }
}

class TrackingEvent {
  final String id;
  final String status;
  final String description;
  final Location? location;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  TrackingEvent({
    required this.id,
    required this.status,
    required this.description,
    this.location,
    required this.timestamp,
    this.metadata,
  });

  factory TrackingEvent.fromJson(Map<String, dynamic> json) {
    return TrackingEvent(
      id: json['id']?.toString() ?? '',
      status: json['status'] ?? '',
      description: json['message'] ?? json['description'] ?? '',
      location: (json['latitude'] != null && json['longitude'] != null)
          ? Location(
              latitude: double.parse(json['latitude'].toString()),
              longitude: double.parse(json['longitude'].toString()),
              timestamp: DateTime.parse(json['created_at'] ?? json['timestamp']),
            )
          : null,
      timestamp: DateTime.parse(json['created_at'] ?? json['timestamp']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status,
      'description': description,
      'location': location?.toJson(),
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
