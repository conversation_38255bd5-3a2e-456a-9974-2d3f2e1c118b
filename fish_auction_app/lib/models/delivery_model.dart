import 'package:flutter/material.dart';
import 'auction_model.dart';
import 'user_model.dart';

class Delivery {
  final String id;
  final Auction auction;
  final User seller;
  final User buyer;
  final String status;
  final String pickupAddress;
  final String deliveryAddress;
  final DateTime? estimatedPickupTime;
  final DateTime? estimatedDeliveryTime;
  final DateTime? pickedUpAt;
  final DateTime? deliveredAt;
  final double deliveryCost;
  final String? specialInstructions;
  final String trackingNumber;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<DeliveryUpdate> updates;

  Delivery({
    required this.id,
    required this.auction,
    required this.seller,
    required this.buyer,
    required this.status,
    required this.pickupAddress,
    required this.deliveryAddress,
    this.estimatedPickupTime,
    this.estimatedDeliveryTime,
    this.pickedUpAt,
    this.deliveredAt,
    required this.deliveryCost,
    this.specialInstructions,
    required this.trackingNumber,
    required this.createdAt,
    required this.updatedAt,
    this.updates = const [],
  });

  factory Delivery.fromJson(Map<String, dynamic> json) {
    return Delivery(
      id: json['id'] ?? '',
      auction: Auction.fromJson(json['auction'] ?? {}),
      seller: User.fromJson(json['seller'] ?? {}),
      buyer: User.fromJson(json['buyer'] ?? {}),
      status: json['status'] ?? 'pending',
      pickupAddress: json['pickup_address'] ?? '',
      deliveryAddress: json['delivery_address'] ?? '',
      estimatedPickupTime: json['estimated_pickup_time'] != null ? DateTime.parse(json['estimated_pickup_time']) : null,
      estimatedDeliveryTime: json['estimated_delivery_time'] != null
          ? DateTime.parse(json['estimated_delivery_time'])
          : null,
      pickedUpAt: json['picked_up_at'] != null ? DateTime.parse(json['picked_up_at']) : null,
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      deliveryCost: _parseDouble(json['delivery_cost']),
      specialInstructions: json['special_instructions'],
      trackingNumber: json['tracking_number'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      updates: (json['updates'] as List<dynamic>?)?.map((update) => DeliveryUpdate.fromJson(update)).toList() ?? [],
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auction': auction.toJson(),
      'seller': seller.toJson(),
      'buyer': buyer.toJson(),
      'status': status,
      'pickup_address': pickupAddress,
      'delivery_address': deliveryAddress,
      'estimated_pickup_time': estimatedPickupTime?.toIso8601String(),
      'estimated_delivery_time': estimatedDeliveryTime?.toIso8601String(),
      'picked_up_at': pickedUpAt?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'delivery_cost': deliveryCost,
      'special_instructions': specialInstructions,
      'tracking_number': trackingNumber,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'updates': updates.map((update) => update.toJson()).toList(),
    };
  }

  // Status helpers
  bool get isPending => status == 'pending';
  bool get isPickedUp => status == 'picked_up';
  bool get isInTransit => status == 'in_transit';
  bool get isOutForDelivery => status == 'out_for_delivery';
  bool get isDelivered => status == 'delivered';
  bool get isFailed => status == 'failed';
  bool get isCancelled => status == 'cancelled';

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'picked_up':
        return 'Picked Up';
      case 'in_transit':
        return 'In Transit';
      case 'out_for_delivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'failed':
        return 'Failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  Color get statusColor {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'picked_up':
        return Colors.blue;
      case 'in_transit':
        return Colors.purple;
      case 'out_for_delivery':
        return Colors.indigo;
      case 'delivered':
        return Colors.green;
      case 'failed':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData get statusIcon {
    switch (status) {
      case 'pending':
        return Icons.schedule;
      case 'picked_up':
        return Icons.local_shipping;
      case 'in_transit':
        return Icons.directions_car;
      case 'out_for_delivery':
        return Icons.delivery_dining;
      case 'delivered':
        return Icons.check_circle;
      case 'failed':
        return Icons.error;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  Delivery copyWith({
    String? id,
    Auction? auction,
    User? seller,
    User? buyer,
    String? status,
    String? pickupAddress,
    String? deliveryAddress,
    DateTime? estimatedPickupTime,
    DateTime? estimatedDeliveryTime,
    DateTime? pickedUpAt,
    DateTime? deliveredAt,
    double? deliveryCost,
    String? specialInstructions,
    String? trackingNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<DeliveryUpdate>? updates,
  }) {
    return Delivery(
      id: id ?? this.id,
      auction: auction ?? this.auction,
      seller: seller ?? this.seller,
      buyer: buyer ?? this.buyer,
      status: status ?? this.status,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      estimatedPickupTime: estimatedPickupTime ?? this.estimatedPickupTime,
      estimatedDeliveryTime: estimatedDeliveryTime ?? this.estimatedDeliveryTime,
      pickedUpAt: pickedUpAt ?? this.pickedUpAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      deliveryCost: deliveryCost ?? this.deliveryCost,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      updates: updates ?? this.updates,
    );
  }
}

class DeliveryUpdate {
  final String id;
  final String status;
  final String message;
  final double? latitude;
  final double? longitude;
  final String? photo;
  final User createdBy;
  final DateTime createdAt;

  DeliveryUpdate({
    required this.id,
    required this.status,
    required this.message,
    this.latitude,
    this.longitude,
    this.photo,
    required this.createdBy,
    required this.createdAt,
  });

  factory DeliveryUpdate.fromJson(Map<String, dynamic> json) {
    return DeliveryUpdate(
      id: json['id']?.toString() ?? '',
      status: json['status'] ?? '',
      message: json['message'] ?? '',
      latitude: json['latitude'] != null ? double.tryParse(json['latitude'].toString()) : null,
      longitude: json['longitude'] != null ? double.tryParse(json['longitude'].toString()) : null,
      photo: json['photo'],
      createdBy: User.fromJson(json['created_by'] ?? {}),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status,
      'message': message,
      'latitude': latitude,
      'longitude': longitude,
      'photo': photo,
      'created_by': createdBy.toJson(),
      'created_at': createdAt.toIso8601String(),
    };
  }
}
