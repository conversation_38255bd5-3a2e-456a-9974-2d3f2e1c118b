import 'auction_model.dart';
import 'user_model.dart';

class Payment {
  final String id;
  final Auction auction;
  final User buyer;
  final User seller;
  final double amount;
  final double platformFee;
  final double sellerAmount;
  final String paymentMethod;
  final String status;
  final String? stripePaymentIntentId;
  final String? stripeChargeId;
  final DateTime paymentDeadline;
  final DateTime? paidAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  Payment({
    required this.id,
    required this.auction,
    required this.buyer,
    required this.seller,
    required this.amount,
    required this.platformFee,
    required this.sellerAmount,
    required this.paymentMethod,
    required this.status,
    this.stripePaymentIntentId,
    this.stripeChargeId,
    required this.paymentDeadline,
    this.paidAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'] ?? '',
      auction: Auction.fromJson(json['auction'] ?? {}),
      buyer: User.fromJson(json['buyer'] ?? {}),
      seller: User.fromJson(json['seller'] ?? {}),
      amount: _parseDouble(json['amount']),
      platformFee: _parseDouble(json['platform_fee']),
      sellerAmount: _parseDouble(json['seller_amount']),
      paymentMethod: json['payment_method'] ?? '',
      status: json['status'] ?? '',
      stripePaymentIntentId: json['stripe_payment_intent_id'],
      stripeChargeId: json['stripe_charge_id'],
      paymentDeadline: DateTime.parse(json['payment_deadline']),
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auction': auction.toJson(),
      'buyer': buyer.toJson(),
      'seller': seller.toJson(),
      'amount': amount,
      'platform_fee': platformFee,
      'seller_amount': sellerAmount,
      'payment_method': paymentMethod,
      'status': status,
      'stripe_payment_intent_id': stripePaymentIntentId,
      'stripe_charge_id': stripeChargeId,
      'payment_deadline': paymentDeadline.toIso8601String(),
      'paid_at': paidAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isPending => status == 'pending';
  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isProcessing => status == 'processing';

  bool get isOverdue => DateTime.now().isAfter(paymentDeadline) && isPending;

  Duration get timeRemaining {
    final now = DateTime.now();
    final remaining = paymentDeadline.difference(now);
    return remaining.isNegative ? Duration.zero : remaining;
  }

  String get statusDisplay {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  String get paymentMethodDisplay {
    switch (paymentMethod) {
      case 'wallet':
        return 'Wallet';
      case 'stripe':
        return 'Credit/Debit Card';
      case 'bank_transfer':
        return 'Bank Transfer';
      default:
        return 'Unknown';
    }
  }
}

class WalletTransaction {
  final String id;
  final User user;
  final String transactionType;
  final double amount;
  final String status;
  final String description;
  final String? stripePaymentIntentId;
  final String? stripeChargeId;
  final Auction? auction;
  final DateTime createdAt;
  final DateTime updatedAt;

  WalletTransaction({
    required this.id,
    required this.user,
    required this.transactionType,
    required this.amount,
    required this.status,
    required this.description,
    this.stripePaymentIntentId,
    this.stripeChargeId,
    this.auction,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WalletTransaction.fromJson(Map<String, dynamic> json) {
    return WalletTransaction(
      id: json['id'] ?? '',
      user: User.fromJson(json['user'] ?? {}),
      transactionType: json['transaction_type'] ?? '',
      amount: Payment._parseDouble(json['amount']),
      status: json['status'] ?? '',
      description: json['description'] ?? '',
      stripePaymentIntentId: json['stripe_payment_intent_id'],
      stripeChargeId: json['stripe_charge_id'],
      auction: json['auction'] != null ? Auction.fromJson(json['auction']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  String get transactionTypeDisplay {
    switch (transactionType) {
      case 'deposit':
        return 'Deposit';
      case 'withdrawal':
        return 'Withdrawal';
      case 'payment':
        return 'Payment';
      case 'refund':
        return 'Refund';
      case 'reserve':
        return 'Reserve';
      case 'release':
        return 'Release';
      default:
        return 'Unknown';
    }
  }

  bool get isCredit => amount > 0;
  bool get isDebit => amount < 0;
}

class PaymentMethod {
  final String id;
  final String type;
  final String last4;
  final String brand;
  final int expMonth;
  final int expYear;
  final bool isDefault;

  PaymentMethod({
    required this.id,
    required this.type,
    required this.last4,
    required this.brand,
    required this.expMonth,
    required this.expYear,
    required this.isDefault,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      last4: json['last4'] ?? '',
      brand: json['brand'] ?? '',
      expMonth: json['exp_month'] ?? 0,
      expYear: json['exp_year'] ?? 0,
      isDefault: json['is_default'] ?? false,
    );
  }

  String get displayName {
    return '$brand •••• $last4';
  }

  String get expiryDisplay {
    return '${expMonth.toString().padLeft(2, '0')}/${expYear.toString().substring(2)}';
  }
}

class PaymentIntent {
  final String id;
  final String status;
  final String? clientSecret;
  final double amount;
  final String currency;

  PaymentIntent({
    required this.id,
    required this.status,
    this.clientSecret,
    required this.amount,
    required this.currency,
  });

  factory PaymentIntent.fromJson(Map<String, dynamic> json) {
    return PaymentIntent(
      id: json['id'] ?? '',
      status: json['status'] ?? '',
      clientSecret: json['client_secret'],
      amount: Payment._parseDouble(json['amount']),
      currency: json['currency'] ?? 'usd',
    );
  }

  bool get requiresAction => status == 'requires_action';
  bool get succeeded => status == 'succeeded';
  bool get requiresPaymentMethod => status == 'requires_payment_method';
}
