class User {
  final int id;
  final String username;
  final String email;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String userType;
  final String? profilePicture;
  final String? address;
  final String? city;
  final String? country;
  final double walletBalance;
  final String kycStatus;
  final bool isVerified;
  final bool isActive;
  final String preferredLanguage;
  final DateTime dateJoined;
  final DateTime? lastLogin;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    required this.userType,
    this.profilePicture,
    this.address,
    this.city,
    this.country,
    required this.walletBalance,
    required this.kycStatus,
    required this.isVerified,
    required this.isActive,
    required this.preferredLanguage,
    required this.dateJoined,
    this.lastLogin,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      firstName: json['first_name'],
      lastName: json['last_name'],
      phoneNumber: json['phone_number'],
      userType: json['user_type'] ?? 'buyer',
      profilePicture: json['profile_picture'],
      address: json['address'],
      city: json['city'],
      country: json['country'],
      walletBalance: _parseDouble(json['wallet_balance']),
      kycStatus: json['kyc_status'] ?? 'not_submitted',
      isVerified: json['is_verified'] ?? false,
      isActive: json['is_active'] ?? true,
      preferredLanguage: json['preferred_language'] ?? 'en',
      dateJoined: _parseDateTime(json['date_joined']),
      lastLogin: _parseDateTimeNullable(json['last_login']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String) {
      return DateTime.tryParse(value) ?? DateTime.now();
    }
    return DateTime.now();
  }

  static DateTime? _parseDateTimeNullable(dynamic value) {
    if (value == null) return null;
    if (value is String && value.isNotEmpty) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'phone_number': phoneNumber,
      'user_type': userType,
      'profile_picture': profilePicture,
      'address': address,
      'city': city,
      'country': country,
      'wallet_balance': walletBalance,
      'kyc_status': kycStatus,
      'is_verified': isVerified,
      'is_active': isActive,
      'preferred_language': preferredLanguage,
      'date_joined': dateJoined.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
    };
  }

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    }
    return username;
  }

  String get displayName => fullName.isNotEmpty ? fullName : username;

  // Alias for profilePicture to match UI expectations
  String? get profileImage => profilePicture;

  // Only buyers can bid without verification
  bool get canBid => userType == 'buyer' && isActive;
  // Only sellers need verification to sell
  bool get canSell => userType == 'seller' && isActive && isVerified;
  bool get canManageAuctions => userType == 'admin' || userType == 'broker';

  String get kycStatusDisplay {
    switch (kycStatus) {
      case 'not_submitted':
        return 'Not Submitted';
      case 'pending':
        return 'Pending Review';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  String get userTypeDisplay {
    switch (userType) {
      case 'buyer':
        return 'Buyer';
      case 'seller':
        return 'Seller';
      case 'broker':
        return 'Broker';
      case 'service_provider':
        return 'Service Provider';
      case 'admin':
        return 'Administrator';
      default:
        return 'User';
    }
  }

  User copyWith({
    int? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? userType,
    String? profilePicture,
    String? address,
    String? city,
    String? country,
    double? walletBalance,
    String? kycStatus,
    bool? isVerified,
    bool? isActive,
    String? preferredLanguage,
    DateTime? dateJoined,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      userType: userType ?? this.userType,
      profilePicture: profilePicture ?? this.profilePicture,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      walletBalance: walletBalance ?? this.walletBalance,
      kycStatus: kycStatus ?? this.kycStatus,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      dateJoined: dateJoined ?? this.dateJoined,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User{id: $id, username: $username, email: $email, userType: $userType}';
  }
}
