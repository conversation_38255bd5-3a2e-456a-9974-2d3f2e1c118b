class BrokerService {
  final String id;
  final String name;
  final String nameAr;
  final String serviceType;
  final String description;
  final String descriptionAr;
  final bool isActive;

  BrokerService({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.serviceType,
    required this.description,
    required this.descriptionAr,
    required this.isActive,
  });

  factory BrokerService.fromJson(Map<String, dynamic> json) {
    return BrokerService(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      serviceType: json['service_type'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      isActive: json['is_active'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'service_type': serviceType,
      'description': description,
      'description_ar': descriptionAr,
      'is_active': isActive,
    };
  }
}

class ServiceRequest {
  final String id;
  final String clientId;
  final String auctionId;
  final String serviceId;
  final String locationDescription;
  final double? latitude;
  final double? longitude;
  final String specialInstructions;
  final String status;
  final String? selectedBrokerId;
  final double? selectedQuoteAmount;
  final bool paymentHeld;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Related data
  final BrokerService? service;
  final String? auctionTitle;
  final List<BrokerQuote> quotes;

  ServiceRequest({
    required this.id,
    required this.clientId,
    required this.auctionId,
    required this.serviceId,
    required this.locationDescription,
    this.latitude,
    this.longitude,
    required this.specialInstructions,
    required this.status,
    this.selectedBrokerId,
    this.selectedQuoteAmount,
    required this.paymentHeld,
    required this.createdAt,
    required this.updatedAt,
    this.service,
    this.auctionTitle,
    this.quotes = const [],
  });

  factory ServiceRequest.fromJson(Map<String, dynamic> json) {
    return ServiceRequest(
      id: json['id']?.toString() ?? '',
      clientId: json['client']?.toString() ?? '',
      auctionId: json['auction']?.toString() ?? '',
      serviceId: json['service']?.toString() ?? '',
      locationDescription: json['location_description'] ?? '',
      latitude: _parseDouble(json['latitude']),
      longitude: _parseDouble(json['longitude']),
      specialInstructions: json['special_instructions'] ?? '',
      status: json['status'] ?? '',
      selectedBrokerId: json['selected_broker']?.toString(),
      selectedQuoteAmount: _parseDouble(json['selected_quote_amount']),
      paymentHeld: json['payment_held'] ?? false,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      service: json['service_info'] != null ? BrokerService.fromJson(json['service_info']) : null,
      auctionTitle: json['auction_title'],
      quotes: json['quotes'] != null ? (json['quotes'] as List).map((q) => BrokerQuote.fromJson(q)).toList() : [],
    );
  }

  // Helper method to safely parse double values from various types
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'auction': int.tryParse(auctionId.toString()) ?? auctionId,
      'service': serviceId, // Keep as string since it's a UUID
      'location_description': locationDescription,
      'latitude': latitude,
      'longitude': longitude,
      'special_instructions': specialInstructions,
    };
  }

  String get statusDisplayAr {
    switch (status) {
      case 'pending':
        return 'في انتظار العروض';
      case 'quotes_received':
        return 'تم استلام العروض';
      case 'broker_selected':
        return 'تم اختيار البروكر';
      case 'payment_held':
        return 'تم حجز المبلغ';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }
}

class BrokerQuote {
  final String id;
  final String serviceRequestId;
  final String brokerId;
  final double amount;
  final String estimatedDuration;
  final String notes;
  final String status;
  final DateTime createdAt;

  // Broker info
  final Map<String, dynamic>? brokerInfo;
  final double brokerRating;

  BrokerQuote({
    required this.id,
    required this.serviceRequestId,
    required this.brokerId,
    required this.amount,
    required this.estimatedDuration,
    required this.notes,
    required this.status,
    required this.createdAt,
    this.brokerInfo,
    required this.brokerRating,
  });

  factory BrokerQuote.fromJson(Map<String, dynamic> json) {
    return BrokerQuote(
      id: json['id'] ?? '',
      serviceRequestId: json['service_request'] ?? '',
      brokerId: json['broker'] ?? '',
      amount: _parseDouble(json['amount']),
      estimatedDuration: json['estimated_duration'] ?? '',
      notes: json['notes'] ?? '',
      status: json['status'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      brokerInfo: json['broker_info'],
      brokerRating: _parseDouble(json['broker_rating']),
    );
  }

  // Helper method to safely parse double values
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'service_request': serviceRequestId,
      'amount': amount,
      'estimated_duration': estimatedDuration,
      'notes': notes,
    };
  }

  String get brokerName {
    if (brokerInfo != null) {
      final firstName = brokerInfo!['first_name'] ?? '';
      final lastName = brokerInfo!['last_name'] ?? '';
      final username = brokerInfo!['username'] ?? '';

      if (firstName.isNotEmpty || lastName.isNotEmpty) {
        return '$firstName $lastName'.trim();
      }
      return username;
    }
    return 'Unknown Broker';
  }
}

class ServiceExecution {
  final String id;
  final String serviceRequestId;
  final String brokerId;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final String status;
  final String reportText;
  final List<String> reportImages;
  final bool clientApproved;
  final String clientFeedback;
  final int? clientRating;
  final bool paymentReleased;
  final DateTime? paymentReleasedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional info from API response
  final ServiceRequest? serviceRequestInfo;
  final Map<String, dynamic>? brokerInfo;

  ServiceExecution({
    required this.id,
    required this.serviceRequestId,
    required this.brokerId,
    this.startedAt,
    this.completedAt,
    required this.status,
    required this.reportText,
    required this.reportImages,
    required this.clientApproved,
    required this.clientFeedback,
    this.clientRating,
    required this.paymentReleased,
    this.paymentReleasedAt,
    required this.createdAt,
    required this.updatedAt,
    this.serviceRequestInfo,
    this.brokerInfo,
  });

  factory ServiceExecution.fromJson(Map<String, dynamic> json) {
    return ServiceExecution(
      id: json['id'] ?? '',
      serviceRequestId: json['service_request_info']?['id'] ?? '',
      brokerId: json['broker_info']?['id']?.toString() ?? '',
      startedAt: json['started_at'] != null ? DateTime.parse(json['started_at']) : null,
      completedAt: json['completed_at'] != null ? DateTime.parse(json['completed_at']) : null,
      status: json['status'] ?? '',
      reportText: json['report_text'] ?? '',
      reportImages: json['report_images'] != null ? List<String>.from(json['report_images']) : [],
      clientApproved: json['client_approved'] ?? false,
      clientFeedback: json['client_feedback'] ?? '',
      clientRating: json['client_rating'],
      paymentReleased: json['payment_released'] ?? false,
      paymentReleasedAt: json['payment_released_at'] != null ? DateTime.parse(json['payment_released_at']) : null,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      serviceRequestInfo: json['service_request_info'] != null
          ? ServiceRequest.fromJson(json['service_request_info'])
          : null,
      brokerInfo: json['broker_info'],
    );
  }

  String get statusDisplayAr {
    switch (status) {
      case 'started':
        return 'بدأ التنفيذ';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'client_approved':
        return 'موافقة العميل';
      case 'payment_released':
        return 'تم تحويل المبلغ';
      default:
        return status;
    }
  }

  String get brokerName {
    if (brokerInfo != null) {
      final firstName = brokerInfo!['first_name'] ?? '';
      final lastName = brokerInfo!['last_name'] ?? '';
      final username = brokerInfo!['username'] ?? '';

      if (firstName.isNotEmpty || lastName.isNotEmpty) {
        return '$firstName $lastName'.trim();
      }
      return username;
    }
    return 'Unknown Broker';
  }

  String get serviceName {
    return serviceRequestInfo?.service?.nameAr ?? 'خدمة غير محددة';
  }

  String get auctionTitle {
    return serviceRequestInfo?.auctionTitle ?? 'مزاد غير محدد';
  }
}
