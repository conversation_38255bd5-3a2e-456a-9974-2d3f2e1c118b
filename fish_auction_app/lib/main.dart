import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'constants/app_theme.dart';
import 'constants/app_constants.dart';
import 'providers/auth_provider.dart';
import 'providers/auction_provider.dart';
import 'providers/language_provider.dart';
import 'providers/notification_provider.dart';
import 'services/api_service.dart';
// import 'services/payment_service.dart'; // temporarily disabled
import 'providers/delivery_provider.dart';
import 'providers/broker_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/main/main_screen.dart';
import 'screens/main/profile_screen.dart';
import 'screens/payment/payment_screen.dart';
import 'screens/location/seller_location_screen.dart';
import 'models/auction_model.dart';
import 'l10n/generated/app_localizations.dart';

void main() async {
  // Add error handling for the entire app
  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      // Initialize API service and load auth tokens
      ApiService().initialize();
      await ApiService().loadAuthTokens();

      // Initialize Payment service (includes Stripe) - temporarily disabled
      // try {
      //   await PaymentService().initializeStripe();
      // } catch (e) {
      //   print('Warning: Failed to initialize Stripe: $e');
      // }

      // Set preferred orientations
      try {
        await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
      } catch (e) {
        print('Warning: Failed to set preferred orientations: $e');
      }

      runApp(const FishAuctionApp());
    },
    (error, stackTrace) {
      // Handle uncaught errors
      print('Uncaught error: $error');
      print('Stack trace: $stackTrace');
    },
  );
}

class FishAuctionApp extends StatelessWidget {
  const FishAuctionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => AuctionProvider()),
        ChangeNotifierProvider(create: (_) => DeliveryProvider()),
        ChangeNotifierProvider(create: (_) => BrokerProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
      ],
      child: Consumer2<AuthProvider, LanguageProvider>(
        builder: (context, authProvider, languageProvider, child) {
          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,

            // Theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.light, // For now, always use light theme
            // Localization
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: LanguageProvider.supportedLocales,
            locale: languageProvider.currentLocale,

            // Navigation
            home: const AppNavigator(),

            // Routes
            routes: {
              '/login': (context) => const LoginScreen(),
              '/main': (context) => const MainScreen(),
              '/profile': (context) => const ProfileScreen(),
            },

            // Handle routes with arguments
            onGenerateRoute: (settings) {
              if (settings.name == '/payment') {
                final auction = settings.arguments as Auction;
                return MaterialPageRoute(builder: (context) => PaymentScreen(auction: auction));
              } else if (settings.name == '/seller-location') {
                final auction = settings.arguments as Auction;
                return MaterialPageRoute(builder: (context) => SellerLocationScreen(auction: auction));
              }
              return null;
            },

            // Builder for global configurations
            builder: (context, child) {
              return Directionality(
                textDirection: languageProvider.textDirection,
                child: MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                    textScaler: const TextScaler.linear(1.0), // Prevent text scaling for elderly users
                  ),
                  child: child!,
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class AppNavigator extends StatefulWidget {
  const AppNavigator({super.key});

  @override
  State<AppNavigator> createState() => _AppNavigatorState();
}

class _AppNavigatorState extends State<AppNavigator> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });
  }

  Future<void> _initializeApp() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

    // Initialize providers
    await Future.wait([authProvider.initialize(), languageProvider.initialize(), notificationProvider.initialize()]);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Show splash screen while initializing
        if (!authProvider.isInitialized) {
          return const SplashScreen();
        }

        // Navigate based on authentication status
        if (authProvider.isLoggedIn) {
          return const MainScreen();
        } else {
          return const LoginScreen();
        }
      },
    );
  }
}
