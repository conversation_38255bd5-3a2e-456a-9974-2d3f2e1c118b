import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../../constants/app_colors.dart';

class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingText;
  final Color? backgroundColor;
  final Color? spinnerColor;
  final double? spinnerSize;

  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.loadingText,
    this.backgroundColor,
    this.spinnerColor,
    this.spinnerSize,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? AppColors.overlay,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SpinKitFadingCircle(
                    color: spinnerColor ?? AppColors.primary,
                    size: spinnerSize ?? 50.0,
                  ),
                  if (loadingText != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      loadingText!,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppColors.textLight,
                          ),
                    ),
                  ],
                ],
              ),
            ),
          ),
      ],
    );
  }
}

class LoadingButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;

  const LoadingButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.primary,
          foregroundColor: textColor ?? AppColors.textLight,
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.textLight),
                ),
              )
            : Text(text),
      ),
    );
  }
}

class LoadingIndicator extends StatelessWidget {
  final Color? color;
  final double? size;
  final String? text;

  const LoadingIndicator({
    super.key,
    this.color,
    this.size,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SpinKitFadingCircle(
          color: color ?? AppColors.primary,
          size: size ?? 50.0,
        ),
        if (text != null) ...[
          const SizedBox(height: 16),
          Text(
            text!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color ?? AppColors.primary,
                ),
          ),
        ],
      ],
    );
  }
}

class LoadingCard extends StatelessWidget {
  final double? width;
  final double? height;
  final String? text;

  const LoadingCard({
    super.key,
    this.width,
    this.height,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height ?? 200,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: LoadingIndicator(
          text: text ?? 'Loading...',
        ),
      ),
    );
  }
}

class LoadingListItem extends StatelessWidget {
  final double? height;

  const LoadingListItem({
    super.key,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 80,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: const Center(
        child: SpinKitThreeBounce(
          color: AppColors.primary,
          size: 20.0,
        ),
      ),
    );
  }
}

class FullScreenLoading extends StatelessWidget {
  final String? text;
  final Color? backgroundColor;

  const FullScreenLoading({
    super.key,
    this.text,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? AppColors.background,
      body: Center(
        child: LoadingIndicator(
          text: text ?? 'Loading...',
        ),
      ),
    );
  }
}
