import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/location_model.dart';

class DeliveryStepper extends StatelessWidget {
  final DeliveryTracking? deliveryTracking;
  final bool showEstimatedTimes;
  final bool isCompact;

  const DeliveryStepper({
    super.key,
    this.deliveryTracking,
    this.showEstimatedTimes = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final steps = _getDeliverySteps();
    final currentStepIndex = _getCurrentStepIndex();

    return Container(
      padding: EdgeInsets.all(isCompact ? AppConstants.smallPadding : AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isCompact) ...[
            Row(
              children: [
                const Icon(Icons.timeline, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text(
                  'Delivery Progress',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
          ],
          
          // Progress indicator
          if (!isCompact)
            _buildProgressIndicator(currentStepIndex, steps.length),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Steps
          Column(
            children: steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isCompleted = index < currentStepIndex;
              final isCurrent = index == currentStepIndex;
              final isLast = index == steps.length - 1;
              
              return _buildStepItem(
                step: step,
                isCompleted: isCompleted,
                isCurrent: isCurrent,
                isLast: isLast,
                index: index,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  List<DeliveryStep> _getDeliverySteps() {
    return [
      DeliveryStep(
        title: 'Order Confirmed',
        description: 'Your payment has been received',
        icon: Icons.check_circle,
        status: 'pending',
      ),
      DeliveryStep(
        title: 'Preparing Order',
        description: 'Seller is preparing your fish',
        icon: Icons.kitchen,
        status: 'preparing',
      ),
      DeliveryStep(
        title: 'Ready for Pickup',
        description: 'Order is ready for collection',
        icon: Icons.inventory,
        status: 'ready',
      ),
      DeliveryStep(
        title: 'In Transit',
        description: 'Your order is on the way',
        icon: Icons.local_shipping,
        status: 'in_transit',
      ),
      DeliveryStep(
        title: 'Delivered',
        description: 'Order has been delivered',
        icon: Icons.home,
        status: 'delivered',
      ),
    ];
  }

  int _getCurrentStepIndex() {
    if (deliveryTracking == null) return 0;
    
    final status = deliveryTracking!.status;
    switch (status) {
      case 'pending':
        return 0;
      case 'preparing':
        return 1;
      case 'ready':
      case 'picked_up':
        return 2;
      case 'in_transit':
      case 'out_for_delivery':
        return 3;
      case 'delivered':
        return 4;
      default:
        return 0;
    }
  }

  Widget _buildProgressIndicator(int currentStep, int totalSteps) {
    final progress = (currentStep + 1) / totalSteps;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${((progress * 100).round())}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.smallPadding),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildStepItem({
    required DeliveryStep step,
    required bool isCompleted,
    required bool isCurrent,
    required bool isLast,
    required int index,
  }) {
    final stepColor = isCompleted 
        ? AppColors.success 
        : isCurrent 
            ? AppColors.primary 
            : Colors.grey[400]!;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Step indicator column
        Column(
          children: [
            // Step circle
            Container(
              width: isCompact ? 24 : 32,
              height: isCompact ? 24 : 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: stepColor,
                border: Border.all(
                  color: stepColor,
                  width: 2,
                ),
              ),
              child: Icon(
                isCompleted 
                    ? Icons.check 
                    : isCurrent 
                        ? step.icon 
                        : step.icon,
                size: isCompact ? 14 : 18,
                color: Colors.white,
              ),
            ),
            
            // Connecting line
            if (!isLast)
              Container(
                width: 2,
                height: isCompact ? 40 : 60,
                color: isCompleted ? AppColors.success : Colors.grey[300],
              ),
          ],
        ),
        
        SizedBox(width: isCompact ? AppConstants.smallPadding : AppConstants.defaultPadding),
        
        // Step content
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(
              bottom: isLast ? 0 : (isCompact ? 20 : 40),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step title
                Text(
                  step.title,
                  style: TextStyle(
                    fontSize: isCompact ? 14 : 16,
                    fontWeight: isCurrent ? FontWeight.bold : FontWeight.w500,
                    color: isCompleted || isCurrent ? Colors.black : Colors.grey[600],
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Step description
                Text(
                  step.description,
                  style: TextStyle(
                    fontSize: isCompact ? 12 : 14,
                    color: Colors.grey[600],
                  ),
                ),
                
                // Timestamp for completed steps
                if (isCompleted && deliveryTracking != null)
                  _buildStepTimestamp(step.status),
                
                // Estimated time for current step
                if (isCurrent && showEstimatedTimes && deliveryTracking != null)
                  _buildEstimatedTime(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStepTimestamp(String status) {
    final event = deliveryTracking!.events
        .where((e) => e.status == status)
        .isNotEmpty 
            ? deliveryTracking!.events.firstWhere((e) => e.status == status)
            : null;
    
    if (event == null) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        event.formattedTime,
        style: TextStyle(
          fontSize: 12,
          color: AppColors.success,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildEstimatedTime() {
    if (deliveryTracking?.estimatedDelivery == null) {
      return const SizedBox.shrink();
    }
    
    final now = DateTime.now();
    final estimated = deliveryTracking!.estimatedDelivery!;
    final difference = estimated.difference(now);
    
    String timeText;
    if (difference.inDays > 0) {
      timeText = 'in ${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      timeText = 'in ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      timeText = 'in ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      timeText = 'soon';
    }
    
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          'Estimated $timeText',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

class DeliveryStep {
  final String title;
  final String description;
  final IconData icon;
  final String status;

  DeliveryStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.status,
  });
}
