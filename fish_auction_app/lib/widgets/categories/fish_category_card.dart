import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';

class FishCategoryCard extends StatelessWidget {
  final AuctionCategory category;
  final VoidCallback? onTap;
  final bool isCompact;

  const FishCategoryCard({super.key, required this.category, this.onTap, this.isCompact = false});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 8, offset: const Offset(0, 2))],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section
            Expanded(flex: 3, child: _buildImageSection(context)),

            // Content Section
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      category.getLocalizedName(Localizations.localeOf(context).languageCode),
                      style: Theme.of(
                        context,
                      ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.textPrimary),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (category.description != null && category.description!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        category.getLocalizedDescription(Localizations.localeOf(context).languageCode) ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(AppConstants.borderRadius),
        topRight: Radius.circular(AppConstants.borderRadius),
      ),
      child: SizedBox(width: double.infinity, height: double.infinity, child: _buildImage()),
    );
  }

  Widget _buildImage() {
    // Use category image if available, otherwise use default fish images
    if (category.icon != null && category.icon!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: category.icon!,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildPlaceholderImage(),
        errorWidget: (context, url, error) => _buildDefaultCategoryImage(),
      );
    } else {
      return _buildDefaultCategoryImage();
    }
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: AppColors.background,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildDefaultCategoryImage() {
    // Use real fish images with colored overlay based on category name
    final categoryData = _getCategoryImageData();

    return Stack(
      fit: StackFit.expand,
      children: [
        // Background fish image
        CachedNetworkImage(
          imageUrl: categoryData['imageUrl'] as String,
          fit: BoxFit.cover,
          placeholder: (context, url) => _buildPlaceholderImage(),
          errorWidget: (context, url, error) => _buildFallbackGradient(categoryData),
        ),

        // Colored overlay with opacity
        Container(decoration: BoxDecoration(color: (categoryData['overlayColor'] as Color).withOpacity(0.7))),

        // Icon in center
        Center(
          child: Icon(
            categoryData['icon'] as IconData,
            size: 48,
            color: Colors.white,
            shadows: [Shadow(color: Colors.black.withOpacity(0.3), blurRadius: 4, offset: const Offset(0, 2))],
          ),
        ),
      ],
    );
  }

  Widget _buildFallbackGradient(Map<String, dynamic> categoryData) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [(categoryData['overlayColor'] as Color).withOpacity(0.8), (categoryData['overlayColor'] as Color)],
        ),
      ),
    );
  }

  Map<String, dynamic> _getCategoryImageData() {
    final categoryName = category.name.toLowerCase();

    // Real fish images from internet with appropriate overlay colors
    if (categoryName.contains('sea') || categoryName.contains('marine') || categoryName.contains('ocean')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.waves,
        'overlayColor': Colors.blue.shade600,
      };
    } else if (categoryName.contains('tuna')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.sailing,
        'overlayColor': Colors.indigo.shade600,
      };
    } else if (categoryName.contains('salmon')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1599084993091-1cb5c0721cc6?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.phishing,
        'overlayColor': Colors.pink.shade600,
      };
    } else if (categoryName.contains('fresh') || categoryName.contains('river') || categoryName.contains('lake')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.water,
        'overlayColor': Colors.teal.shade600,
      };
    } else if (categoryName.contains('shell') || categoryName.contains('crab') || categoryName.contains('lobster')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.set_meal,
        'overlayColor': Colors.orange.shade600,
      };
    } else if (categoryName.contains('processed') ||
        categoryName.contains('frozen') ||
        categoryName.contains('canned')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.inventory_2,
        'overlayColor': Colors.purple.shade600,
      };
    } else if (categoryName.contains('mackerel')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.sailing,
        'overlayColor': Colors.cyan.shade600,
      };
    } else if (categoryName.contains('cod') || categoryName.contains('white fish')) {
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.phishing,
        'overlayColor': Colors.blueGrey.shade600,
      };
    } else {
      // Default fish image
      return {
        'imageUrl': 'https://images.unsplash.com/photo-1535591273668-578e31182c4f?w=400&h=300&fit=crop&crop=center',
        'icon': Icons.phishing,
        'overlayColor': AppColors.primary,
      };
    }
  }
}
