import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auction_provider.dart';
import 'package:provider/provider.dart';

class AutoBidManager extends StatefulWidget {
  final Auction auction;

  const AutoBidManager({
    super.key,
    required this.auction,
  });

  @override
  State<AutoBidManager> createState() => _AutoBidManagerState();
}

class _AutoBidManagerState extends State<AutoBidManager> {
  final TextEditingController _maxAmountController = TextEditingController();
  bool _isLoading = false;
  Map<String, dynamic>? _currentAutoBid;

  @override
  void initState() {
    super.initState();
    _loadCurrentAutoBid();
  }

  @override
  void dispose() {
    _maxAmountController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentAutoBid() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      await auctionProvider.loadUserAutoBid(widget.auction.id);
      
      if (mounted) {
        setState(() {
          _currentAutoBid = auctionProvider.userAutoBid;
          if (_currentAutoBid != null && _currentAutoBid!['max_amount'] != null) {
            final maxAmount = _currentAutoBid!['max_amount'];
            _maxAmountController.text = maxAmount?.toString() ?? '';
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: AppConstants.defaultPadding),

          if (_currentAutoBid != null) ...[
            _buildCurrentAutoBid(),
            const SizedBox(height: AppConstants.defaultPadding),
          ],

          _buildAutoBidForm(),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildAutoBidInfo(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.warning.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_mode,
            color: AppColors.warning,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Auto Bidding',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.warning,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Automatically bid up to your maximum amount',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentAutoBid() {
    if (_currentAutoBid == null) return const SizedBox.shrink();

    final isActive = _currentAutoBid!['is_active'] == true;
    final maxAmountValue = _currentAutoBid!['max_amount'];
    final maxAmount = maxAmountValue != null ? double.tryParse(maxAmountValue.toString()) ?? 0.0 : 0.0;
    final remainingAmount = maxAmount - widget.auction.currentPrice;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: isActive 
            ? AppColors.success.withOpacity(0.1)
            : AppColors.textSecondary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: isActive 
              ? AppColors.success.withOpacity(0.3)
              : AppColors.textSecondary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: isActive ? AppColors.success : AppColors.textSecondary,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                isActive ? 'Auto Bid Active' : 'Auto Bid Inactive',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: isActive ? AppColors.success : AppColors.textSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Maximum Amount',
                  '\$${maxAmount.toStringAsFixed(2)}',
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'Remaining',
                  '\$${remainingAmount.toStringAsFixed(2)}',
                  remainingAmount > 0 ? AppColors.success : AppColors.error,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _updateAutoBid,
                  icon: const Icon(Icons.edit),
                  label: const Text('Update'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _cancelAutoBid,
                  icon: const Icon(Icons.cancel),
                  label: const Text('Cancel'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.error,
                    side: BorderSide(color: AppColors.error),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildAutoBidForm() {
    final minAmount = widget.auction.currentPrice + widget.auction.bidIncrement;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentAutoBid != null ? 'Update Auto Bid' : 'Set Auto Bid',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Text(
            'Current Price: \$${widget.auction.currentPrice.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Minimum Amount: \$${minAmount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          TextField(
            controller: _maxAmountController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Maximum Bid Amount',
              prefixText: '\$',
              border: OutlineInputBorder(),
              helperText: 'We will automatically bid up to this amount',
            ),
          ),
          const SizedBox(height: 16),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _setAutoBid,
              child: Text(_currentAutoBid != null ? 'Update Auto Bid' : 'Set Auto Bid'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoBidInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.info.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.info.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.info,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'How Auto Bidding Works',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.info,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          const Text(
            '• We automatically place bids on your behalf\n'
            '• Bids are placed only when necessary to maintain your lead\n'
            '• You will never pay more than your maximum amount\n'
            '• You can update or cancel anytime\n'
            '• Auto bids are placed in minimum increments',
            style: TextStyle(
              color: AppColors.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _setAutoBid() async {
    final maxAmount = double.tryParse(_maxAmountController.text);
    final minAmount = widget.auction.currentPrice + widget.auction.bidIncrement;

    if (maxAmount == null || maxAmount < minAmount) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please enter a valid amount (minimum \$${minAmount.toStringAsFixed(2)})'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.setAutoBid(widget.auction.id, maxAmount);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Auto bid set successfully!'),
              backgroundColor: AppColors.success,
            ),
          );
          await _loadCurrentAutoBid();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(auctionProvider.error ?? 'Failed to set auto bid'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _updateAutoBid() async {
    await _setAutoBid();
  }

  Future<void> _cancelAutoBid() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.cancelAutoBid(widget.auction.id);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Auto bid cancelled successfully!'),
              backgroundColor: AppColors.success,
            ),
          );
          setState(() {
            _currentAutoBid = null;
            _maxAmountController.clear();
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(auctionProvider.error ?? 'Failed to cancel auto bid'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
