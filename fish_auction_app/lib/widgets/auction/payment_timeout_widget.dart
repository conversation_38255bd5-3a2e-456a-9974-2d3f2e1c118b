import 'dart:async';
import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';

class PaymentTimeoutWidget extends StatefulWidget {
  final Auction auction;
  final VoidCallback? onPayNow;
  final VoidCallback? onTimeExpired;

  const PaymentTimeoutWidget({
    super.key,
    required this.auction,
    this.onPayNow,
    this.onTimeExpired,
  });

  @override
  State<PaymentTimeoutWidget> createState() => _PaymentTimeoutWidgetState();
}

class _PaymentTimeoutWidgetState extends State<PaymentTimeoutWidget> {
  Timer? _countdownTimer;
  Duration _timeRemaining = Duration.zero;
  bool _isExpired = false;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _updateTimeRemaining();
    
    _countdownTimer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        _updateTimeRemaining();
        
        if (_timeRemaining.inSeconds <= 0 && !_isExpired) {
          setState(() => _isExpired = true);
          widget.onTimeExpired?.call();
          timer.cancel();
        }
      },
    );
  }

  void _updateTimeRemaining() {
    if (widget.auction.payment_deadline == null) {
      setState(() {
        _timeRemaining = Duration.zero;
        _isExpired = true;
      });
      return;
    }

    final now = DateTime.now();
    final deadline = widget.auction.payment_deadline!;
    final remaining = deadline.difference(now);

    setState(() {
      _timeRemaining = remaining.isNegative ? Duration.zero : remaining;
      _isExpired = remaining.isNegative;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.auction.payment_received) {
      return _buildPaymentCompleted();
    }

    if (_isExpired) {
      return _buildExpiredPayment();
    }

    return _buildActiveCountdown();
  }

  Widget _buildActiveCountdown() {
    final urgency = _getUrgencyLevel();
    final color = _getUrgencyColor(urgency);

    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color, width: 2),
      ),
      child: Column(
        children: [
          // Header
          Row(
            children: [
              Icon(
                _getUrgencyIcon(urgency),
                color: color,
                size: 24,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Text(
                  _getUrgencyTitle(urgency),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Countdown Display
          _buildCountdownDisplay(color),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Progress Bar
          _buildProgressBar(color),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Message
          Text(
            _getUrgencyMessage(urgency),
            style: TextStyle(
              fontSize: 14,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Pay Now Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: widget.onPayNow,
              icon: const Icon(Icons.payment),
              label: Text('Pay \$${widget.auction.currentPrice.toStringAsFixed(2)} Now'),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownDisplay(Color color) {
    final hours = _timeRemaining.inHours;
    final minutes = _timeRemaining.inMinutes % 60;
    final seconds = _timeRemaining.inSeconds % 60;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTimeUnit(hours.toString().padLeft(2, '0'), 'Hours', color),
        _buildTimeSeparator(color),
        _buildTimeUnit(minutes.toString().padLeft(2, '0'), 'Minutes', color),
        _buildTimeSeparator(color),
        _buildTimeUnit(seconds.toString().padLeft(2, '0'), 'Seconds', color),
      ],
    );
  }

  Widget _buildTimeUnit(String value, String label, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSeparator(Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        ':',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  Widget _buildProgressBar(Color color) {
    final totalMinutes = 20.0; // 20 minutes total
    final remainingMinutes = _timeRemaining.inMinutes.toDouble();
    final progress = (totalMinutes - remainingMinutes) / totalMinutes;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Time Used',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildExpiredPayment() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.error, width: 2),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.timer_off,
                color: AppColors.error,
                size: 24,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              const Expanded(
                child: Text(
                  'Payment Deadline Expired',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          const Text(
            'Your payment deadline has passed. The auction may be reassigned to the next highest bidder.',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // Refresh or check status
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Check Status'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.error,
                side: const BorderSide(color: AppColors.error),
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentCompleted() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.success, width: 2),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 24,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          const Expanded(
            child: Text(
              'Payment Completed Successfully',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ),
        ],
      ),
    );
  }

  PaymentUrgency _getUrgencyLevel() {
    final minutes = _timeRemaining.inMinutes;
    
    if (minutes <= 2) {
      return PaymentUrgency.critical;
    } else if (minutes <= 5) {
      return PaymentUrgency.urgent;
    } else if (minutes <= 10) {
      return PaymentUrgency.warning;
    } else {
      return PaymentUrgency.normal;
    }
  }

  Color _getUrgencyColor(PaymentUrgency urgency) {
    switch (urgency) {
      case PaymentUrgency.critical:
        return AppColors.error;
      case PaymentUrgency.urgent:
        return Colors.deepOrange;
      case PaymentUrgency.warning:
        return AppColors.warning;
      case PaymentUrgency.normal:
        return AppColors.info;
    }
  }

  IconData _getUrgencyIcon(PaymentUrgency urgency) {
    switch (urgency) {
      case PaymentUrgency.critical:
        return Icons.warning;
      case PaymentUrgency.urgent:
        return Icons.timer;
      case PaymentUrgency.warning:
        return Icons.schedule;
      case PaymentUrgency.normal:
        return Icons.payment;
    }
  }

  String _getUrgencyTitle(PaymentUrgency urgency) {
    switch (urgency) {
      case PaymentUrgency.critical:
        return 'URGENT: Payment Required!';
      case PaymentUrgency.urgent:
        return 'Payment Deadline Soon';
      case PaymentUrgency.warning:
        return 'Payment Required';
      case PaymentUrgency.normal:
        return 'Complete Your Payment';
    }
  }

  String _getUrgencyMessage(PaymentUrgency urgency) {
    switch (urgency) {
      case PaymentUrgency.critical:
        return 'Pay immediately or lose your winning bid!';
      case PaymentUrgency.urgent:
        return 'Only a few minutes left to complete payment.';
      case PaymentUrgency.warning:
        return 'Please complete your payment soon to secure your purchase.';
      case PaymentUrgency.normal:
        return 'You have time to complete your payment securely.';
    }
  }
}

enum PaymentUrgency {
  normal,
  warning,
  urgent,
  critical,
}
