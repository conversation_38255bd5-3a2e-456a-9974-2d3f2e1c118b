import 'package:flutter/material.dart';
import 'dart:async';
import '../../services/websocket_service.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/bid_model.dart';
import '../../providers/auction_provider.dart';
import 'package:provider/provider.dart';

class LiveBiddingHistory extends StatefulWidget {
  final int auctionId;
  final bool showAutoBids;

  const LiveBiddingHistory({
    super.key,
    required this.auctionId,
    this.showAutoBids = true,
  });

  @override
  State<LiveBiddingHistory> createState() => _LiveBiddingHistoryState();
}

class _LiveBiddingHistoryState extends State<LiveBiddingHistory> {
  final WebSocketService _webSocketService = WebSocketService();
  final ScrollController _scrollController = ScrollController();
  StreamSubscription<List<Map<String, dynamic>>>? _bidsSubscription;
  List<Map<String, dynamic>> _liveBids = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeLiveBidding();
  }

  @override
  void dispose() {
    _bidsSubscription?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeLiveBidding() async {
    // Load initial bids from API
    await _loadInitialBids();
    
    // Listen to live bid updates
    _bidsSubscription = _webSocketService.liveBidsStream.listen(
      (bids) {
        if (mounted) {
          setState(() {
            _liveBids = bids;
          });
          
          // Auto-scroll to top when new bid arrives
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        }
      },
    );
  }

  Future<void> _loadInitialBids() async {
    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      await auctionProvider.loadAuctionBids(widget.auctionId);
      
      if (mounted) {
        setState(() {
          // Convert API bids to live bid format
          _liveBids = auctionProvider.auctionBids.map((bid) => {
            'id': bid.id,
            'amount': bid.amount.toString(),
            'bidder': bid.bidderName,
            'timestamp': bid.createdAt.toIso8601String(),
            'bid_type': bid.isAutoBid ? 'auto' : 'manual',
            'is_winning': bid.isWinningBid,
          }).toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_liveBids.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: _buildBidsList(),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: AppColors.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.live_tv,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Live Bidding (${_liveBids.length})',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.success,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            'LIVE',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.success,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBidsList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _liveBids.length,
      itemBuilder: (context, index) {
        final bid = _liveBids[index];
        return _buildBidItem(bid, index);
      },
    );
  }

  Widget _buildBidItem(Map<String, dynamic> bid, int index) {
    final isAutoBid = bid['bid_type'] == 'auto';
    final isWinning = bid['is_winning'] == true;
    final timestamp = DateTime.tryParse(bid['timestamp'] ?? '') ?? DateTime.now();
    final timeAgo = _formatTimeAgo(timestamp);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isWinning 
            ? AppColors.success.withOpacity(0.1)
            : AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isWinning 
              ? AppColors.success.withOpacity(0.3)
              : AppColors.border,
          width: isWinning ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Bid rank
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isWinning ? AppColors.success : AppColors.textSecondary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: AppColors.textLight,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Bid details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '\$${bid['amount']}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isWinning ? AppColors.success : AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (isWinning)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          'WINNING',
                          style: TextStyle(
                            color: AppColors.textLight,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      bid['bidder'] ?? 'Anonymous',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (isAutoBid)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: AppColors.warning.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'AUTO',
                          style: TextStyle(
                            color: AppColors.warning,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    const Spacer(),
                    Text(
                      timeAgo,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textHint,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.gavel_outlined,
            size: 64,
            color: AppColors.textHint,
          ),
          const SizedBox(height: 16),
          Text(
            'No bids yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Be the first to place a bid!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textHint,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
