import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auction_provider.dart';
import '../../l10n/generated/app_localizations.dart';
import '../common/loading_overlay.dart';

class AuctionStatusManager extends StatefulWidget {
  final Auction auction;
  final VoidCallback? onStatusChanged;

  const AuctionStatusManager({super.key, required this.auction, this.onStatusChanged});

  @override
  State<AuctionStatusManager> createState() => _AuctionStatusManagerState();
}

class _AuctionStatusManagerState extends State<AuctionStatusManager> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return LoadingOverlay(
      isLoading: _isLoading,
      child: Card(
        margin: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Status
              Row(
                children: [
                  Icon(_getStatusIcon(widget.auction.status), color: _getStatusColor(widget.auction.status), size: 24),
                  const SizedBox(width: AppConstants.smallPadding),
                  Text(
                    'Current Status: ${_getStatusDisplayName(widget.auction.status)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Status Description
              Text(
                _getStatusDescription(widget.auction.status),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              ),

              const SizedBox(height: AppConstants.largePadding),

              // Action Buttons
              _buildActionButtons(localizations),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(AppLocalizations localizations) {
    final status = widget.auction.status;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (status == 'draft') ...[
          // Draft actions
          ElevatedButton.icon(
            onPressed: () => _scheduleAuction(),
            icon: const Icon(Icons.schedule),
            label: const Text('Schedule Auction'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.warning, foregroundColor: Colors.white),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          ElevatedButton.icon(
            onPressed: () => _makeAuctionLive(),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Make Live Now'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
          ),
        ],

        if (status == 'scheduled') ...[
          // Scheduled actions
          ElevatedButton.icon(
            onPressed: () => _makeAuctionLive(),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Start Auction Now'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          OutlinedButton.icon(
            onPressed: () => _reactivateAuction(),
            icon: const Icon(Icons.edit),
            label: const Text('Back to Draft'),
          ),
        ],

        if (status == 'live') ...[
          // Close & Notify Winner button - only appears when target price is reached
          if (_canCloseAuction()) ...[
            ElevatedButton.icon(
              onPressed: () => _showCloseAndNotifyConfirmation(),
              icon: const Icon(Icons.emoji_events),
              label: const Text('Close & Notify Winner'),
              style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
            ),
            const SizedBox(height: AppConstants.smallPadding),
          ],

          // Stop auction button (always available) - stops without winner notification
          ElevatedButton.icon(
            onPressed: () => _showStopAuctionConfirmation(),
            icon: const Icon(Icons.stop_circle),
            label: const Text('Stop Auction (Cancel)'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error, foregroundColor: Colors.white),
          ),
        ],

        if (status == 'ended') ...[
          // Ended actions
          Text(
            'This auction has ended. No further actions available.',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary, fontStyle: FontStyle.italic),
            textAlign: TextAlign.center,
          ),
        ],

        if (status == 'cancelled') ...[
          // Cancelled actions
          ElevatedButton.icon(
            onPressed: () => _reactivateAuction(),
            icon: const Icon(Icons.refresh),
            label: const Text('Reactivate Auction'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary, foregroundColor: Colors.white),
          ),
        ],

        // Cancel button (available for draft and scheduled)
        if (status == 'draft' || status == 'scheduled') ...[
          const SizedBox(height: AppConstants.smallPadding),
          OutlinedButton.icon(
            onPressed: () => _showCancelConfirmation(),
            icon: const Icon(Icons.cancel),
            label: const Text('Cancel Auction'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: const BorderSide(color: AppColors.error),
            ),
          ),
        ],
      ],
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'draft':
        return Icons.edit_note;
      case 'scheduled':
        return Icons.schedule;
      case 'live':
        return Icons.gavel;
      case 'ended':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'draft':
        return AppColors.textSecondary;
      case 'scheduled':
        return AppColors.warning;
      case 'live':
        return AppColors.liveAuction;
      case 'ended':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'scheduled':
        return 'Scheduled';
      case 'live':
        return 'Live';
      case 'ended':
        return 'Ended';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.toUpperCase();
    }
  }

  String _getStatusDescription(String status) {
    switch (status) {
      case 'draft':
        return 'This auction is in draft mode. You can edit details and schedule it when ready.';
      case 'scheduled':
        return 'This auction is scheduled and will start automatically at the specified time.';
      case 'live':
        return 'This auction is currently live and accepting bids from buyers.';
      case 'ended':
        return 'This auction has ended. Check if there\'s a winner and process payment.';
      case 'cancelled':
        return 'This auction has been cancelled. You can reactivate it if needed.';
      default:
        return 'Unknown status';
    }
  }

  Future<void> _scheduleAuction() async {
    setState(() => _isLoading = true);

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.scheduleAuction(widget.auction.id);

      if (success) {
        _showSuccessMessage('Auction scheduled successfully!');
        widget.onStatusChanged?.call();
      } else {
        _showErrorMessage(auctionProvider.error ?? 'Failed to schedule auction');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _makeAuctionLive() async {
    setState(() => _isLoading = true);

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.makeAuctionLive(widget.auction.id);

      if (success) {
        _showSuccessMessage('Auction is now live!');
        widget.onStatusChanged?.call();
      } else {
        _showErrorMessage(auctionProvider.error ?? 'Failed to make auction live');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _reactivateAuction() async {
    setState(() => _isLoading = true);

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.reactivateAuction(widget.auction.id);

      if (success) {
        _showSuccessMessage('Auction reactivated successfully!');
        widget.onStatusChanged?.call();
      } else {
        _showErrorMessage(auctionProvider.error ?? 'Failed to reactivate auction');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showStopAuctionConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Stop Auction'),
        content: const Text(
          'Are you sure you want to stop this auction? This action cannot be undone. '
          'The auction will be stopped immediately and all bids will be canceled. No winner will be declared.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Keep Auction')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _stopAuctionWithoutWinner();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error, foregroundColor: Colors.white),
            child: const Text('Stop Auction'),
          ),
        ],
      ),
    );
  }

  void _showCancelConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Auction'),
        content: const Text(
          'Are you sure you want to cancel this auction? This will remove it from the marketplace. '
          'You can reactivate it later if needed.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('No, Keep It')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _cancelAuction();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error, foregroundColor: Colors.white),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _stopAuctionWithoutWinner() async {
    setState(() => _isLoading = true);

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      // Use cancelAuction to stop auction without declaring a winner
      final success = await auctionProvider.cancelAuction(widget.auction.id);

      if (success) {
        _showSuccessMessage('Auction stopped successfully! All bids have been canceled.');
        widget.onStatusChanged?.call();
      } else {
        _showErrorMessage(auctionProvider.error ?? 'Failed to stop auction');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _cancelAuction() async {
    setState(() => _isLoading = true);

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.cancelAuction(widget.auction.id);

      if (success) {
        _showSuccessMessage('Auction cancelled successfully!');
        widget.onStatusChanged?.call();
      } else {
        _showErrorMessage(auctionProvider.error ?? 'Failed to cancel auction');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.success));
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.error));
  }

  // Check if auction can be closed with winner notification (only when target price is reached)
  bool _canCloseAuction() {
    final auction = widget.auction;

    // Check if target price is reached
    final targetPriceReached = auction.targetPrice != null && auction.currentPrice >= auction.targetPrice!;

    // Check if there are any bids
    final hasBids = auction.totalBids > 0;

    // Only allow closing with winner notification when target price is reached
    return targetPriceReached && hasBids;
  }

  void _showCloseAndNotifyConfirmation() {
    final auction = widget.auction;
    final targetPriceReached = auction.targetPrice != null && auction.currentPrice >= auction.targetPrice!;
    final timeEnded = auction.endTime != null && DateTime.now().isAfter(auction.endTime!);

    String reason = '';
    if (targetPriceReached && timeEnded) {
      reason = 'Target price reached and time ended';
    } else if (targetPriceReached) {
      reason = 'Target price of \$${auction.targetPrice!.toStringAsFixed(2)} reached';
    } else if (timeEnded) {
      reason = 'Auction time has ended';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.emoji_events, color: AppColors.success),
            SizedBox(width: 8),
            Text('Close Auction'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ready to close this auction!\n'),
            Text('Reason: $reason'),
            Text('\nCurrent winning bid: \$${auction.currentPrice.toStringAsFixed(2)}'),
            Text('Total bids: ${auction.totalBids}'),
            const Text('\nThe winner will be notified via WhatsApp and the auction will be marked as completed.'),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _closeAndNotifyWinner();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.success, foregroundColor: Colors.white),
            child: const Text('Close & Notify Winner'),
          ),
        ],
      ),
    );
  }

  Future<void> _closeAndNotifyWinner() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
      final success = await auctionProvider.closeAuction(widget.auction.id);

      if (mounted) {
        if (success) {
          // Refresh earned auctions for the winner
          await auctionProvider.loadEarnedAuctions();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Auction closed successfully! Winner has been notified.'),
                backgroundColor: AppColors.success,
              ),
            );

            widget.onStatusChanged?.call();

            // Navigate back to home after successful closure
            Navigator.of(context).popUntil((route) => route.isFirst);
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(auctionProvider.error ?? 'Failed to close auction'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error closing auction: $e'), backgroundColor: AppColors.error));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
