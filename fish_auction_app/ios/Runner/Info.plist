<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Fish Auction App</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>fish_auction_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Disable state restoration to prevent crashes -->
	<key>UIStateRestorationViewControllerStoryboardKey</key>
	<false/>
	<key>UIApplicationSupportsStateRestoration</key>
	<false/>

	<!-- Camera Permission -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to take photos of documents for KYC verification and auction listings.</string>

	<!-- Photo Library Permission -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select images for KYC verification and auction listings.</string>

	<!-- Location Permission (for auction location) -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to your location to show nearby auctions and set auction locations.</string>

	<!-- Local Network Permission (for development debugging) -->
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app uses local network for development debugging and testing purposes.</string>

	<!-- Microphone Permission (if needed for video features) -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app may need access to microphone for video features in auctions.</string>

</dict>
</plist>
