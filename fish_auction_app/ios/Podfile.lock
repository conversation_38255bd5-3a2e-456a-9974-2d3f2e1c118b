PODS:
  - Flutter (1.0.0)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (6.1.0):
    - GoogleMaps (~> 9.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleMaps (9.4.0):
    - GoogleMaps/Maps (= 9.4.0)
  - GoogleMaps/Maps (9.4.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Stripe (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.27.0)
    - StripeApplePay (~> 23.27.0)
    - StripeFinancialConnections (~> 23.27.0)
    - StripePayments (~> 23.27.0)
    - StripePaymentSheet (~> 23.27.0)
    - StripePaymentsUI (~> 23.27.0)
  - StripeApplePay (23.27.6):
    - StripeCore (= 23.27.6)
  - StripeCore (23.27.6)
  - StripeFinancialConnections (23.27.6):
    - StripeCore (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripePayments (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments/Stripe3DS2 (= 23.27.6)
  - StripePayments/Stripe3DS2 (23.27.6):
    - StripeCore (= 23.27.6)
  - StripePaymentSheet (23.27.6):
    - StripeApplePay (= 23.27.6)
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripePaymentsUI (= 23.27.6)
  - StripePaymentsUI (23.27.6):
    - StripeCore (= 23.27.6)
    - StripePayments (= 23.27.6)
    - StripeUICore (= 23.27.6)
  - StripeUICore (23.27.6):
    - StripeCore (= 23.27.6)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  Google-Maps-iOS-Utils: 0a484b05ed21d88c9f9ebbacb007956edd508a96
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleMaps: 0608099d4870cac8754bdba9b6953db543432438
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  Stripe: 9fec845645e39f371e6898926d096fd9c2feb5a5
  stripe_ios: 03c617acee72e48a2d055d096a4b0ed2afebb256
  StripeApplePay: 5f017e8dfe259fafbab70137776189deef754bb2
  StripeCore: 01ec57f0dddfe742054dc6a322f811426c25313d
  StripeFinancialConnections: 56698cb6274bf89fb8c76b934f6156f368e97765
  StripePayments: 6adf11faf1b7038e77aa97019410305c6adca79d
  StripePaymentSheet: 3eaf870c4388e44b0cc37e4c69d00b6957fd8bd7
  StripePaymentsUI: 59ccddeacad592b09fa67e8d641340820ddb4751
  StripeUICore: 879bbf5889265db13f52fac8aad7a176ba62481f
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: fc9d7dc69b2a23e64aec3f99331110a342cff167

COCOAPODS: 1.16.2
