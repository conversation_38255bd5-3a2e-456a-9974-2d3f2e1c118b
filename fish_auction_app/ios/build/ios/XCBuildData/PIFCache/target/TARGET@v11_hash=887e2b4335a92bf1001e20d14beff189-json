{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1ed7eaf81990a26698a772b0fa91203", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/StripePaymentsUI/StripePaymentsUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripePaymentsUI", "PRODUCT_NAME": "StripePaymentsUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc663b57e7cf553f2842b428e73a5ceb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ec00bf74bb44a05e3bdbc80cd74a828", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripePaymentsUI/StripePaymentsUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI.modulemap", "PRODUCT_MODULE_NAME": "StripePaymentsUI", "PRODUCT_NAME": "StripePaymentsUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a30c137d50db3462bd0455c58cd1610e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ec00bf74bb44a05e3bdbc80cd74a828", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripePaymentsUI/StripePaymentsUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI.modulemap", "PRODUCT_MODULE_NAME": "StripePaymentsUI", "PRODUCT_NAME": "StripePaymentsUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c947e4f17cdb17941993590f5726da00", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981dad1d22652eb69aabdfd0f86482b173", "guid": "bfdfe7dc352907fc980b868725387e989f37e7db719c288459e612d3f16dde75", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a124ae07e71b601e859b0f4d2be636ce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986b7a4e0f97c70a4422d682e47ce760bf", "guid": "bfdfe7dc352907fc980b868725387e988c14ab2a8059d24bc7fb420a174b8fec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f8876da63b4fe70c475038bb7c54a38", "guid": "bfdfe7dc352907fc980b868725387e981322ef81b467c6d70733ecd9ff0fc15b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4f7ad5cfdc8cbf8e397b5e48f925edb", "guid": "bfdfe7dc352907fc980b868725387e98c063adc9a6099bfe0efc39f1cbf8418a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986642735e6e80b5d91e9835ba3a2f9338", "guid": "bfdfe7dc352907fc980b868725387e983c1a9dd6e663c3071cfc778261ca2c30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850fe726d41fda2d430189616f27729e1", "guid": "bfdfe7dc352907fc980b868725387e98090ba1477479e0377851368f4e30f586"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a38412f56b40a95c9d21a74b41e7f14", "guid": "bfdfe7dc352907fc980b868725387e98dd17836342188b11827683a97a89baee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbb793973caccb02bb1c3ee950267fcb", "guid": "bfdfe7dc352907fc980b868725387e9861754d4307e2041b6dbe56e700d10e52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f76d1d07bb9a155e48b66ef287a329", "guid": "bfdfe7dc352907fc980b868725387e9894b6c79a8e811fe4c21453c2eb6006f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad7f0e7263c4e02ae26fa189b81ffd3a", "guid": "bfdfe7dc352907fc980b868725387e988b2cf85fa877d054437c39ceb73ac04f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98254da190609b88240b522e816d976672", "guid": "bfdfe7dc352907fc980b868725387e984d3622e002d188db0af75e68b80b0956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c34294179ef058e2916ac7073951c759", "guid": "bfdfe7dc352907fc980b868725387e985d14fff83cbc903e06db01d2217f101f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d5bb8f462ea54a75b635022b2d2dc4", "guid": "bfdfe7dc352907fc980b868725387e980e6988c32914a0bce9387fb7adf50222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d045afd6daec5d5687fff0572bce6d1", "guid": "bfdfe7dc352907fc980b868725387e9895f0a9da1b23cbeb2516ce38a0492893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a940c229e4065ed0ea42c09a61758f", "guid": "bfdfe7dc352907fc980b868725387e98579327a9371ad693d20a70027a41533a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98812e291c224d8906b819a8babe074586", "guid": "bfdfe7dc352907fc980b868725387e9858e52fcee4dffc32cd30072782b7779d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beb8685452b92b71ffb2d8bb64234260", "guid": "bfdfe7dc352907fc980b868725387e98578bd825ee254d7d885edc9cb155a379"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0a855f2de54c4d1af4b878158de46e2", "guid": "bfdfe7dc352907fc980b868725387e98d8a2c00f7cf82e36aa5a00e10aa5b862"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caf52d19f09987aaca813d7868662044", "guid": "bfdfe7dc352907fc980b868725387e9899717be87d93c27e7a62679cedb04fd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d6a2afc4b4808fdc15c1c6376bc84aa", "guid": "bfdfe7dc352907fc980b868725387e984564552f01c0256ab1ecacf2f96f5f0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ac755d6f1d72f3660e4a1eb0cfd462", "guid": "bfdfe7dc352907fc980b868725387e98a26859d6d56ee10e958cb70ebd49802a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b23ceccd5043a3579f755685dd72e62", "guid": "bfdfe7dc352907fc980b868725387e98a01b5a11ea822872ba4a29fa366ec44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d347234d4c0a206b2f27e3a09ba6053", "guid": "bfdfe7dc352907fc980b868725387e98c9c1b3d17e7646a57346eb329c25a0a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8ab4da87f60f5f704a9cd4ca57b7f36", "guid": "bfdfe7dc352907fc980b868725387e983ed9dc6615584db90ed598cfa1008d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaf99f909434d1d93f43cccff5dcff1e", "guid": "bfdfe7dc352907fc980b868725387e985ad8551e42aa875e7cf9b91eb9e11d1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2f4c7e1b512f6a1848c7a9c48f03f2", "guid": "bfdfe7dc352907fc980b868725387e98e15f9ad672a87c25921f2cd8cce4d175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98801b02c7363ee84594fec8af769f4fff", "guid": "bfdfe7dc352907fc980b868725387e988e1b2f37aee9be394485a4384567d11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f8df9410df74c74bbc2b8b9b9cc7f86", "guid": "bfdfe7dc352907fc980b868725387e98f3b8cafab9de2d17f242193afc3b65a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4f4de2ad7cce7c621c3f68d44431a6", "guid": "bfdfe7dc352907fc980b868725387e985130e6a6740c1a78952a7b74f3a46f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2ed5812bc056c7c189be341abd0e4f1", "guid": "bfdfe7dc352907fc980b868725387e987dc341bf8db1cd4a33657df477151936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b526f0ec897d6937b25bfc3fbd8262e", "guid": "bfdfe7dc352907fc980b868725387e98d1d350d86028473451f781a5f692ea14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6073418b6de8194d1f9cdc89297f3c", "guid": "bfdfe7dc352907fc980b868725387e9801efaff61d79f40183880dcd21f8bb64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e1edc8cd4317aaaa92652b1e7e4aecd", "guid": "bfdfe7dc352907fc980b868725387e98f4b3813c24a17894f3e31fc8de577deb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d8dce029284b3e206f8ba6dc9de7125", "guid": "bfdfe7dc352907fc980b868725387e9872114c18d8bc7bdd497a64f5ca92004d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ece59cf47de120111fd004a6bad0bae", "guid": "bfdfe7dc352907fc980b868725387e987d8d2e48d0cbc66bc581b107908ee42c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c1519f101e9a76e2dc24edc1cc5f34", "guid": "bfdfe7dc352907fc980b868725387e981d9223db38cba50c9d14de9089a328e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d882a2a716c7aa3661bfcfe08ec779", "guid": "bfdfe7dc352907fc980b868725387e9807d997db319290b0eb2436fe9937b0bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0fc4d955959ed1dac8bb3053277d1a1", "guid": "bfdfe7dc352907fc980b868725387e98363ec1715c31942aed5b9754beee181a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e47ba5f55db95fa93008c26e163a455", "guid": "bfdfe7dc352907fc980b868725387e982e1394129babdcc3d2edc540cc0b504c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98115eca3f8995f4b43d37e783aa471fd6", "guid": "bfdfe7dc352907fc980b868725387e98f10079e986097219da9d241521da0c81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4c38c9b8de660d68c7797ea49483fdd", "guid": "bfdfe7dc352907fc980b868725387e98a4f5164ab07ef482eb7a8d62bdc3dda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98023c7a2f24ecff8eee47a1a25bd385ee", "guid": "bfdfe7dc352907fc980b868725387e98ced16c0e5f6707ce64472587ec6420f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98306fe137b10c25b9634cd2a252f34221", "guid": "bfdfe7dc352907fc980b868725387e980d84069097e245652b3626fb50c024ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d67316c83c76de640ee05b3a12a26e", "guid": "bfdfe7dc352907fc980b868725387e98c54629216ec920efd50388b409d2bfa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41a718d2b0e7e2158e6a6c49214d872", "guid": "bfdfe7dc352907fc980b868725387e9865744b93086146270fff3dc77c4af9aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98debd4e196953a04c9f85e85c71f5d6a9", "guid": "bfdfe7dc352907fc980b868725387e983d73c567a7a7501f024bc2af2487d6e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2d23e81cae96b578d027327671e095d", "guid": "bfdfe7dc352907fc980b868725387e9807e489089473b4edbec576f4a286cedc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983522415626790da1e95e8347c06363c9", "guid": "bfdfe7dc352907fc980b868725387e98aa332576b0450e576c3eed245d97e0cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e306fa9f8132173218418fbaa5a0a2a", "guid": "bfdfe7dc352907fc980b868725387e9851a934fdce5f2fe562015b523dde63a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d9e3f11315522040267ea23a243c2e", "guid": "bfdfe7dc352907fc980b868725387e98ee581f14d8158e96f7c2fd9f29124e95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2266c5018616901c3eaa0b1cce0f085", "guid": "bfdfe7dc352907fc980b868725387e981e9a9e59cc4d5d3b387c893cf9504221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db5e36ccb872fdc674c379c67fb0965a", "guid": "bfdfe7dc352907fc980b868725387e98efc23a6d320bd447caaf252da87e2eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828454811e8c3df60a218748401687855", "guid": "bfdfe7dc352907fc980b868725387e980a5436833f70db05fdcb1032c9ac57ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173fef8c74e7cdf86c999dca4ac7421d", "guid": "bfdfe7dc352907fc980b868725387e984bceee9afd2ada2bb1a58f81235ac3bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60ef77b90569ce23fec5ff71d4d47b2", "guid": "bfdfe7dc352907fc980b868725387e9884698f513b5d763d861f1b5cbd65bf9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982031cf8fc51c66dc31427ca97aa39493", "guid": "bfdfe7dc352907fc980b868725387e982e1cc5e8ad967f8a8703ad41d96aee3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988595126eb02f537669ffabf17a3eb4b5", "guid": "bfdfe7dc352907fc980b868725387e98826d46f1e1813b60ffef781acb959304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830011f9507d394d4bf3e1be332d2d023", "guid": "bfdfe7dc352907fc980b868725387e98ff338d261c3b0ef308444c96b939cc07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5690e1671fb1e2eb992b7296cea79bf", "guid": "bfdfe7dc352907fc980b868725387e981e30ebc4749107f5773f0e8979c6610a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987acec1a99cd9348eac38b4359250977d", "guid": "bfdfe7dc352907fc980b868725387e98daa442188adb912697c4b8b4063f79c6"}], "guid": "bfdfe7dc352907fc980b868725387e9831cd1a9b63cacb06f849085439d95101", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e15b2e50aee441247a5571813c896c4a", "guid": "bfdfe7dc352907fc980b868725387e98e562749b0d7f1292ea7ad8d04acef793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa46dc51df10a62d9f343ee40844b85", "guid": "bfdfe7dc352907fc980b868725387e980d76f6292af77ddaa32613e164dc6dd7"}], "guid": "bfdfe7dc352907fc980b868725387e9864f91021b37f2e947b4900b0f26ea902", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9839dc06bdbc0303ef2813281dde97e8b2", "targetReference": "bfdfe7dc352907fc980b868725387e98821f5b3e5beb50f85c2c602e7f37448c"}], "guid": "bfdfe7dc352907fc980b868725387e98eebd28a9fa9b529e2e12961e4ab9e882", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98821f5b3e5beb50f85c2c602e7f37448c", "name": "StripePaymentsUI-StripePaymentsUIBundle"}, {"guid": "bfdfe7dc352907fc980b868725387e98f17b608a8faeb01ef6ec76d52489fd0b", "name": "StripeUICore"}], "guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e5ddfa1cdd414dbb19163eec79823d4", "name": "StripePaymentsUI.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}