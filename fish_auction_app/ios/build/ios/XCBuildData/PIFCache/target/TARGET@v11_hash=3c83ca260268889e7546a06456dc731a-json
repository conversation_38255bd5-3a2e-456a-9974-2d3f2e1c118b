{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98979a18f27cc916d53fb37832c2cdf107", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/StripeCore", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_OPTIMIZATION_LEVEL": "0", "IBSC_MODULE": "StripeCore", "INFOPLIST_FILE": "Target Support Files/StripeCore/ResourceBundle-StripeCoreBundle-StripeCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "StripeCoreBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a4cde0c0f2cad294823c0820bf60833a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987f58c3a7455c85d30f813f6e52b753c1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/StripeCore", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "StripeCore", "INFOPLIST_FILE": "Target Support Files/StripeCore/ResourceBundle-StripeCoreBundle-StripeCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "StripeCoreBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986b8b45abd2242f5a41e7467f04d67437", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987f58c3a7455c85d30f813f6e52b753c1", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/StripeCore", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "StripeCore", "INFOPLIST_FILE": "Target Support Files/StripeCore/ResourceBundle-StripeCoreBundle-StripeCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "StripeCoreBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985e800534a9a867fe2a64e59f3bdaa025", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819a6b43500eefa1680545a8925114669", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9814e4f45a312776335e5f07feb31ad7c1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98757310910de8d007efb6742d62f411b4", "guid": "bfdfe7dc352907fc980b868725387e98c033384d0224c77d7f2954ba83d07887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846be68828daaee9554376d246f31efcc", "guid": "bfdfe7dc352907fc980b868725387e988e05fb206c513b89f390e414332edf6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852107e98153f1d425ef49d38bfe269d9", "guid": "bfdfe7dc352907fc980b868725387e981b675638b542557f1ae76feeaca7fd6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c2a9746dd7cb2c6defa377bc85bab63", "guid": "bfdfe7dc352907fc980b868725387e98d0472712003ca6be708c48f76906c4fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a5d54c237e42257321278a0b3fa6ff5", "guid": "bfdfe7dc352907fc980b868725387e98ca09ddf2486282f3bd835e897a4dc634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988738e8054f9087306438b75de35e953a", "guid": "bfdfe7dc352907fc980b868725387e984cf65b26abad44e40e0f982de4f1bb15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a4fd8f4dbc72fabdd1d9d82e3734b9e", "guid": "bfdfe7dc352907fc980b868725387e9870129c5735f7f6dd4c837f53e3778d47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e1812b7d1bd4e8fa0c868f12f16ed0", "guid": "bfdfe7dc352907fc980b868725387e98bca3b1604cfbaca1f21aa78d750e912f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3ad9c103773872a82a478b8576f7e36", "guid": "bfdfe7dc352907fc980b868725387e98b8dacb9f9a5623a940b132c07b8bfcdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a591e627ce4dfadab576878b63782bd3", "guid": "bfdfe7dc352907fc980b868725387e98f923989c2b883f1b3e293a615087e08e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45fef097f81ec194776b55b33c12afa", "guid": "bfdfe7dc352907fc980b868725387e988d42ca9fe109076a578a34dac648531a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b0496ec94e338b6da6b3ce83a18c89d", "guid": "bfdfe7dc352907fc980b868725387e98129d25d3265761ab1a47ad6da0046954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c72492f5e862e84a993818d4875d370a", "guid": "bfdfe7dc352907fc980b868725387e98994522359c40aace57242ba6e5abd077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ec6a136dfbf3c64d4efd9dffa7db02", "guid": "bfdfe7dc352907fc980b868725387e9816c8a26bab7e1a618f2ca78a32ee1364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fcb266df982126aa544e9a97ebf68ff", "guid": "bfdfe7dc352907fc980b868725387e98b8693cf95744ccc19e6fce90e41bbee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0982972da282e7891b4ac8c8217fe86", "guid": "bfdfe7dc352907fc980b868725387e98efd1dd62d784b230eb85181110694691"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8104ffdb867ef0c3edbf1ae32de614a", "guid": "bfdfe7dc352907fc980b868725387e989ba943a0120abfe3d57b5206d4213e97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988735dd2789edc9615a57af320742c00f", "guid": "bfdfe7dc352907fc980b868725387e98fef2bed908ee8c35305cd4041afa72b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46f63c78c6ace31648b194e3ed222f7", "guid": "bfdfe7dc352907fc980b868725387e9821506581e98d40edf89071d6d9809843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b0aa02ab966366e081042b09fe42c6e", "guid": "bfdfe7dc352907fc980b868725387e98e42e4b94e90cd847ecbf058081bf7fe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f560950dc8aff385ea510a9ee7511696", "guid": "bfdfe7dc352907fc980b868725387e98cfbdcde20116d174c0288a16ce5c2bfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7e9be0ce018b1b41b2b2806bf7faa8", "guid": "bfdfe7dc352907fc980b868725387e98712b8f21b08f23929663b4b8b09901d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e55e085242e77615141b02e90bc63d", "guid": "bfdfe7dc352907fc980b868725387e984da79e28b8317e5afa57a831ae8fad6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd5827391087f5f4c3b61894d445a64", "guid": "bfdfe7dc352907fc980b868725387e9862a2f25c1d2be2ff0a40a826d9cc0233"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98519eab526754c540a8f7c3e43a9ac83c", "guid": "bfdfe7dc352907fc980b868725387e988738357915ce88d470f9ee768fb2ca79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b7f88bdcf570347bcf0e1fd1545127", "guid": "bfdfe7dc352907fc980b868725387e98ec3caf46d118ba1e1c70cb4eb79a93b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edd34ae09c628831f129cb6f58be3d7e", "guid": "bfdfe7dc352907fc980b868725387e9837255cbae6569ca0c972a0dad1656d2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f4abb578af4dbae4ae04441e5c63294", "guid": "bfdfe7dc352907fc980b868725387e98646f3bd251ddbbea2278b7396d5f7ffc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a9906d2c78bae140a37c67dee7bcc6", "guid": "bfdfe7dc352907fc980b868725387e9821b009f2d827b7e0b3a113bc8801e937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a40f013070fbc655b609ffffa3cda96", "guid": "bfdfe7dc352907fc980b868725387e984999545d684dea634c5eac6de708372f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f711e69fc0ab6371c6417aed743c38cc", "guid": "bfdfe7dc352907fc980b868725387e9893991665f2db3da802214df1f741d9b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c4e59222cfec25841c6dc1efefc085b", "guid": "bfdfe7dc352907fc980b868725387e98908c3e07b7b58a8fe8fa303b924b4c81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a20c502db3bb7102f705c3f2e25c0479", "guid": "bfdfe7dc352907fc980b868725387e98c9ad87e1719790c6a2395393a085ed21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961ed5c62a6e40c72ac236465c74626e", "guid": "bfdfe7dc352907fc980b868725387e98a0115a4e5c7b6d1ca903a164e172b256"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea76d6419af9c42e2d903f42adfecad", "guid": "bfdfe7dc352907fc980b868725387e98bc0b94bac4d3d4e83d5ec683829fe2fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1330aad461a2ccd84e7795265bf7a79", "guid": "bfdfe7dc352907fc980b868725387e986f758c5ecd1792577feb7d79eec7428d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491d58d2f4d0dd1eaef4ae2bd124a727", "guid": "bfdfe7dc352907fc980b868725387e9831d380f6edf52d179e014a4232d710dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982065984324373a31438b3a2355af45ee", "guid": "bfdfe7dc352907fc980b868725387e98ddc7ebbb9f81cc6772664774918e6f4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf14d410f49027ab7bf5782690224e6", "guid": "bfdfe7dc352907fc980b868725387e98a4aeb967290856e51fb0b4e06b9c1993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263633cb6d79ecdbabb201f698a833e6", "guid": "bfdfe7dc352907fc980b868725387e98d54c57a857aca1f95f6dcb110ea5c4b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d4d431e13a500b5ced4db1d9ea3e15", "guid": "bfdfe7dc352907fc980b868725387e985e906b5187cb9ae520e92049f3ce1d55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bc00c6464e270ed5c0625d51d6a740", "guid": "bfdfe7dc352907fc980b868725387e9897418b7a6e2d902b64e8fc6ec97284d1"}], "guid": "bfdfe7dc352907fc980b868725387e98909ec51e7c38c5164571a5a163727c91", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98187aa9b7cb03205c0a20870023770d0e", "name": "StripeCore-StripeCoreBundle", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b7035e05eedfe274ce87acdca59366f8", "name": "StripeCoreBundle.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}