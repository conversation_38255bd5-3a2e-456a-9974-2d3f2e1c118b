{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b6eee2971314d2ee9098e9ef70212d6", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_OPTIMIZATION_LEVEL": "0", "IBSC_MODULE": "permission_handler_apple", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "permission_handler_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9839aba0c363b138f286c434f997817770", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d64903c2e2bef2df1593f5cda6e0f319", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "permission_handler_apple", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "permission_handler_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982bc4f187bdc4d27736dd098f56ab2c37", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d64903c2e2bef2df1593f5cda6e0f319", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/permission_handler_apple", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "permission_handler_apple", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "permission_handler_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98702bf462c6137995a000c5f56f090b59", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98bfe647a262378893d1ed972f3d5b0062", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98feac75d78790c97240b1820eb4c6ce4d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c7ba444078dd1a0f6caa3f729ec0287", "guid": "bfdfe7dc352907fc980b868725387e982566f78fd43cb46c846adbfcf22566cc"}], "guid": "bfdfe7dc352907fc980b868725387e98c80c391df58a54113e8648819530b643", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983e9a904e8a35cb34b69458780be142b3", "name": "permission_handler_apple_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}