{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd04e5a82c68d8996f6d3ca7f0684ae6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9836eb6f7e3befa3f51b0725c60b8b65f8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd03e6d7c52c23b2bec3fbdaf70a744c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857ea356f72848ad9dd1d7886ae89b8b8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd03e6d7c52c23b2bec3fbdaf70a744c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef39f4071a1f080a58cb0eb4ee4715f2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a6c76d3bc5801a53977326cebdb98c5", "guid": "bfdfe7dc352907fc980b868725387e989029f16863633fde06fb34668b461fd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5529e28ba0a71d679125becb98042c", "guid": "bfdfe7dc352907fc980b868725387e98c841e69f9e2343bed754490c46ce5a4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae89845cc301f5e325580de206dfe5c", "guid": "bfdfe7dc352907fc980b868725387e98907712ef287ced7710ce9af07fe3a03f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9898dce07d0728c65367da583b127c2d1a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc2b71325cd405b25384d4f17ff93397", "guid": "bfdfe7dc352907fc980b868725387e9884ad856d9e9d1d756bf6695aad595204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6e261913875481e73f136bb37b631dc", "guid": "bfdfe7dc352907fc980b868725387e98dec2f68f2ea70a951f8872dc87c73019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eee43dcdd2ea42bbb4645ff77b2059e", "guid": "bfdfe7dc352907fc980b868725387e98659d55a8346b36312c99af55b453b827"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f26e9866d263592b57adc6f344959d", "guid": "bfdfe7dc352907fc980b868725387e98467eccff80b764cbe5aacf879bf2b784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870b5bc39decd009a7654f0143497fd1c", "guid": "bfdfe7dc352907fc980b868725387e98e2bccde5beb4836dd29dc1cc97d2df0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5cdbe5fae370db6318ea7eaf260b6b", "guid": "bfdfe7dc352907fc980b868725387e983b3865fabc1c106be5b76d1ee3e0bdb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd9e1455602a80979899cc1623c0f1f", "guid": "bfdfe7dc352907fc980b868725387e984f633e276446733f9a6773b4d3e5df6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981233f242dc28e886da19ce43e02b3f65", "guid": "bfdfe7dc352907fc980b868725387e98a90dd8ec4d4907493deed8ce934a042b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a95d54b9191c92eda8d027c7b91d18", "guid": "bfdfe7dc352907fc980b868725387e98cfdb89de0ee9a71ef5722b4bbb45aea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cdb8a2fdb67ee03fae815ad5081978b", "guid": "bfdfe7dc352907fc980b868725387e9822d57a76b941933c846b94d2dd43dcfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989583fc7a85cc28922f100ea6d087e0d6", "guid": "bfdfe7dc352907fc980b868725387e989a6bae956c7af65818c002b186b9b560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d1e736a72a012072822aa73a5d3405", "guid": "bfdfe7dc352907fc980b868725387e983a34a5d355aba6ddaf75ee0df27863e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980957847233917731c8fbe52c76dca1de", "guid": "bfdfe7dc352907fc980b868725387e982cfd2465585672b1ff1da8fe9430e0e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98669474834b78d9837def214235f48f39", "guid": "bfdfe7dc352907fc980b868725387e98eee0428d23e37f2ab5882fc8ec5740fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98551244b7a8a59d08f25ef9d3b63d576d", "guid": "bfdfe7dc352907fc980b868725387e985e5db6150a15f5e6006331d46c10a944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c6e3cb437971ecb025d65515593389", "guid": "bfdfe7dc352907fc980b868725387e989693d8f71e75021158a694e8df3db4d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888087c8f4173c535bad41023508c259f", "guid": "bfdfe7dc352907fc980b868725387e986c7e775b9f071dbce546ad0b74f58417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90cae712dc8933ac0704f276006d80f", "guid": "bfdfe7dc352907fc980b868725387e98fc9689422e55b1bc6fe0c0b5058f31c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089c1490ca016408d4b1a93eea5aae8b", "guid": "bfdfe7dc352907fc980b868725387e98232b309932a7eec20e480bb6a943e580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb022759c21dd8b151b3ecd6f3ef58f4", "guid": "bfdfe7dc352907fc980b868725387e98f6b6142018e70ff68879beeaff945ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf52cab5d85c1a921fb619b929dc17e0", "guid": "bfdfe7dc352907fc980b868725387e9880c95430bd654533febb47082782e7c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5a00966f852c7eee073ed56eb7a0787", "guid": "bfdfe7dc352907fc980b868725387e9875d850164924635e1fae331731b54ba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879169d3953dc87bd6eef8b22744a4825", "guid": "bfdfe7dc352907fc980b868725387e98a20f2e1bdf4842ec1d20f9f90d805e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ab2c79bec07757f810c72b8056ea3c", "guid": "bfdfe7dc352907fc980b868725387e98d6dbf69eb589f95c121d421b70f01185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec8c3384eb70b5fc8a0aa4a585099ebb", "guid": "bfdfe7dc352907fc980b868725387e98349847515150dbacac9ae05ebb6f778b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee8fbc370d5128bf153303a2724b749", "guid": "bfdfe7dc352907fc980b868725387e98c77e03a4ece77cc6ec67eafce5f2af1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b33120e7ac3360356f3d57cb348cc5", "guid": "bfdfe7dc352907fc980b868725387e9813478e08af6335cc98bd501c2007b83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df226dd8c4baef052c6f57a66ac30c6e", "guid": "bfdfe7dc352907fc980b868725387e98a8c5b984e21308b0dce728b89334bdb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fdac337f60745410b9625e98bafe4fd", "guid": "bfdfe7dc352907fc980b868725387e980144398821435a096666d62e53ca96d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3ca1faa15983095faf8c908f54c61d", "guid": "bfdfe7dc352907fc980b868725387e9887e1c74ad410f7b9868efb59ed6401c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa90555e72aaedbdeed0cc5d39bb604", "guid": "bfdfe7dc352907fc980b868725387e9857b0eba5f8ced115900a6e575e5166a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d13b08aa59cda26720ade9f836350476", "guid": "bfdfe7dc352907fc980b868725387e98d5a39b28df8ca17f843ebabc37551c59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6830f58e0a258b872e2e71ece1c18d", "guid": "bfdfe7dc352907fc980b868725387e989a5a93ab63725ff997e93d5c9d3b4c04"}], "guid": "bfdfe7dc352907fc980b868725387e98506c3693b2724c8c2352323373918cbf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e15b2e50aee441247a5571813c896c4a", "guid": "bfdfe7dc352907fc980b868725387e9868c4c0689854101972cc744a0fe7a4be"}], "guid": "bfdfe7dc352907fc980b868725387e9833fe4021362c657e0e04b36febee34e0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9874e6a6bbfb3914a1da229b6bc7b4dece", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}