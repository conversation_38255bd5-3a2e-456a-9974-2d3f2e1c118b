{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e990f7e17c25d7f4b4123a2f26b59a3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e7f69a617d13e94afd8b7b872d88849", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834cef0ea3c75ef139a95150412c06a50", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9858b1f01777d5239ab53386e75c4135b3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834cef0ea3c75ef139a95150412c06a50", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c14912cec2f8477478ba8fcad4e62903", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b16dce52d28622452c1a01f6b8bd02c1", "guid": "bfdfe7dc352907fc980b868725387e98be072a89e6d0fe550a8b100ea2a528c6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988465675e83c6a81b0ea7351b9d08a0b6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b82451ad931405a291ce7d05ae7dfcf", "guid": "bfdfe7dc352907fc980b868725387e988f487fb86f8b40c9112cdb4f2664e474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d34a5af25ab2dd6060bb33e5b45bc5a3", "guid": "bfdfe7dc352907fc980b868725387e98c459105ac151836596b71f046f72858b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981345797baf6cdee8bb44a95e91ebd758", "guid": "bfdfe7dc352907fc980b868725387e985deb2dabf54c64dab5a6b70bd4b57e65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811abc518d834fb146525eef98a071164", "guid": "bfdfe7dc352907fc980b868725387e98983b5a36c06e91f5ffcbbeb5a7578f0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f56907c68b3533b818a771734df8f10", "guid": "bfdfe7dc352907fc980b868725387e98ae42606e7091b7dd9e4b8c4d7aaca118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847e707c886a5dc88357b6dc22d6a8d93", "guid": "bfdfe7dc352907fc980b868725387e98e3d80def98a9b74518d23a7f4c75ff1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f12ecc554d36c586502bfa55db3969f", "guid": "bfdfe7dc352907fc980b868725387e98ffc15e3debf7ae6633bfc0692ec58995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b1dc5219e0521978d5d674b2b82be2f", "guid": "bfdfe7dc352907fc980b868725387e987d0ba4908154ce722bbac92fda76abb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f68b306553ec308cb7866d52083cc1f", "guid": "bfdfe7dc352907fc980b868725387e98a38ea385caf2e1291f600d64829d2832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc96d3ee9057855a4afdf8966b2e34c", "guid": "bfdfe7dc352907fc980b868725387e987e0f7fe8680036186ebbc6a96b7e3673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833187daaa2f45c9ca97173a7f83a929b", "guid": "bfdfe7dc352907fc980b868725387e98e31b756b9d4c390e39a30fedd6e9134a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841f10f8206f06291b3ab05029d431243", "guid": "bfdfe7dc352907fc980b868725387e98c6bf138c315d2675672d6bf955dcd731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987516e3df68153e731ae6a2828a8f02ff", "guid": "bfdfe7dc352907fc980b868725387e98b4da70eb77ef2186ee8f376096de3cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98528bb48b7a6f2a4c29dbdf2453fdec36", "guid": "bfdfe7dc352907fc980b868725387e988c49bed739a0ed9572f56e4676a7de76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881eddf92bdba167cf660c22761a8a1ba", "guid": "bfdfe7dc352907fc980b868725387e98187b9ccbf26ad74ccde5a386cdc2adf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be17ce3d34cbfceec8c57d5ef4434940", "guid": "bfdfe7dc352907fc980b868725387e98c4181d2ec53871d78b4634c7ae4e7923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff5e9ae0e0106a0d7c183962cd640de", "guid": "bfdfe7dc352907fc980b868725387e9857e9604de3a6595958b174eea3bf8974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d314e79d3daa31257886b7675b2129", "guid": "bfdfe7dc352907fc980b868725387e9819afd6cde88ad66ae33950eebfec866d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd89e0f9c374197652b536091ed3267", "guid": "bfdfe7dc352907fc980b868725387e98fba73bca5290d0b091193b7bbd0f3491"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802a78b45d32fe0c1f966e6a359ef8c7", "guid": "bfdfe7dc352907fc980b868725387e989925cb722051070200b35e1f177f2df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828692e1990459aa516c254ef6d1d08a5", "guid": "bfdfe7dc352907fc980b868725387e98d6953e836ee8ad9ee46f51582e57141b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877d8e6588166edc0e06f6ddab4040914", "guid": "bfdfe7dc352907fc980b868725387e98778d6550062a43cca7e9d90ee62df41b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd795dd7bf4fe2c79bf751c6a48597f3", "guid": "bfdfe7dc352907fc980b868725387e9884f4db59a1185c7f74803cfb201a3061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d120b464264c55f328b46aab95ed5b6c", "guid": "bfdfe7dc352907fc980b868725387e9856925cb134dc6e08e0c173a6bbfde276"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da3acbc48b5a5ed0cffeadebdd64224", "guid": "bfdfe7dc352907fc980b868725387e98391d63182d1ce51bd24c9b8c1430726f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d44ea2173ec31ffbf40254390331f0", "guid": "bfdfe7dc352907fc980b868725387e98ca76965a1b8077e099dd1cdb65982d9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a01107427d354b5779acf0bed657c643", "guid": "bfdfe7dc352907fc980b868725387e980a92acf39a484682e9923e0be4dadc29"}], "guid": "bfdfe7dc352907fc980b868725387e986d8068e1d33d308581ab9e52470188c7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880b44b1857fb7a32d170244d35e9160e", "guid": "bfdfe7dc352907fc980b868725387e986aea2d47981e022927705b13ce0658f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814ec165a455a4d60c625d1936f181ec9", "guid": "bfdfe7dc352907fc980b868725387e98778b4d3b24ecdc5e3891044fc6a6ee60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e15b2e50aee441247a5571813c896c4a", "guid": "bfdfe7dc352907fc980b868725387e98b210585548204b2596ea830231b2d385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4adbfdc51f708de54517527b1fea495", "guid": "bfdfe7dc352907fc980b868725387e98f969ea9bf88790125dc1fe484757c40c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eeb40f70e22b3d0ab59ca4ce76e9025", "guid": "bfdfe7dc352907fc980b868725387e98e4133c4d2fb6e69fab13a77ac3cacd65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ab2401cc47186c0625e6b4d012252d", "guid": "bfdfe7dc352907fc980b868725387e985909a751028f0b6da34d292d2c072f1a"}], "guid": "bfdfe7dc352907fc980b868725387e9812334a679207167b63ee32d3951df1ac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898b3b23f0b2da0410910b848fdf5d37b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}