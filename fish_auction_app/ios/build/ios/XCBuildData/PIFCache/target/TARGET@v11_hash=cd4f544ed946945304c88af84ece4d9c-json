{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9822041d8b5e010a74fb445b14a8961d4e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f17fa172e6fae8dbe417be3738d59376", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98721d29ee5932ea1923011b3246d87011", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c3d55f9c79a53bc31e59c0e1160e91", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98721d29ee5932ea1923011b3246d87011", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8ea013d7af21f519d28e1c5290c609f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985feec401cb02ea869e4e91b02b6decac", "guid": "bfdfe7dc352907fc980b868725387e98ea3689c757b88a537a2c65679cc1b2ac", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988a525b8254149b133d17d648f5576e82", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6642ca7fdb82e916d7bc9af75b0eae4", "guid": "bfdfe7dc352907fc980b868725387e98e8f3d1ad5eaa000b8ca77094f5534ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d206c1e482d33a4cc178ac52ba88338", "guid": "bfdfe7dc352907fc980b868725387e98097ea1aae8e885fd0229e830170c71a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53469c1b8a98ca642f1d357f86a83d4", "guid": "bfdfe7dc352907fc980b868725387e987beae34a4ec182174b83acb64f182f93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981429363f0421a70b7b37541def80fd46", "guid": "bfdfe7dc352907fc980b868725387e98184c19a2fdbcb85225e72138cbd035e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822d103654c06e76dfe623cfcd13390dd", "guid": "bfdfe7dc352907fc980b868725387e98373dc3bb79b4d362e9eaf886a87fed99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e138a71aa1d8e4d3d7a02192a9080e0", "guid": "bfdfe7dc352907fc980b868725387e98e1ffe658a25fff0e312a99db823337b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46f194ee3bec7e6fd903d5cec574ce7", "guid": "bfdfe7dc352907fc980b868725387e983044b669e304bcca01679b3f3100e1de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982501c226b77dd983bafd52296e82d0d5", "guid": "bfdfe7dc352907fc980b868725387e985bd82f2fc75a88691f0a89f762fde8c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b42eff8995bbd8ec601d39fe7bd376b", "guid": "bfdfe7dc352907fc980b868725387e98895aa23b8556153ffcd3b9d36361f5c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886927d545c0c80ffdce949f3ac854a07", "guid": "bfdfe7dc352907fc980b868725387e98fa11927fc68e67537f05f2fee85e1c86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9f88e1b893934441750e0965afa96c6", "guid": "bfdfe7dc352907fc980b868725387e98dcbfcb1006134a29e3c3cc30cb297e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5c13b1e8a47de932495782f260e2dc9", "guid": "bfdfe7dc352907fc980b868725387e98911ee3c1ab2c204d03125f4537155d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb617a77b2f36b8744e7ec84a379aa2f", "guid": "bfdfe7dc352907fc980b868725387e987bc7919fc159736216109e5cc4d3fc12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e43348911c2d14149264d588d794a02", "guid": "bfdfe7dc352907fc980b868725387e980363c00b523f45c78c14dcab87142970"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988500e930d7971691bafae6de16203fc9", "guid": "bfdfe7dc352907fc980b868725387e9858871a8a346f1d3f44a8ab48572fa4f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833eb4a58f9d9eaffad193b3bc5bcdfeb", "guid": "bfdfe7dc352907fc980b868725387e98561546663b5a92ff9a06fd7a344de76e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e07441f4cc144ba2a845305e791868", "guid": "bfdfe7dc352907fc980b868725387e98ff62f0b40e7e73caddff0212af1531b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5872907cb5215a7a0cceba39ead50a4", "guid": "bfdfe7dc352907fc980b868725387e98153f03ac565667bde26315ecf8b261cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04d4629a9bc75315342cd4f65da29c2", "guid": "bfdfe7dc352907fc980b868725387e9879e3aa05bfe37eaf7c6bc14d5c8dfa6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98906fd6a8e6d240a3c70f61b4cc551969", "guid": "bfdfe7dc352907fc980b868725387e986089adb508c9c8557319a60fe652fa0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ac17e1d45b6bdbd6badb28649a8fd2", "guid": "bfdfe7dc352907fc980b868725387e98d6fac7e6f5f6eb020e6449c4b9c3bc6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a13f98e81cea4b308003cefbed4640", "guid": "bfdfe7dc352907fc980b868725387e981f7fbe0f415b8d501d48a98d8e4a08b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc788ec01b7f0865d2c4a921fa8ae57a", "guid": "bfdfe7dc352907fc980b868725387e98a0cb64f034c594de3df12c74f43726d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9a3468517cac4905a569701507656d", "guid": "bfdfe7dc352907fc980b868725387e98c68133d64fd0eb6cb2318030e3997137"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113bf08232d98ff33d7e18df44ca895a", "guid": "bfdfe7dc352907fc980b868725387e9877e3cf420d57beeeda96cb0b6e897be9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849cb34ee55af04d39b9cad7d8349b91", "guid": "bfdfe7dc352907fc980b868725387e988cfade999636186fbd78f14b46a6b8a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983722a19558a2a06627ea261744ff7269", "guid": "bfdfe7dc352907fc980b868725387e982a7baf829df59ccad67ff5720f3f1f7f"}], "guid": "bfdfe7dc352907fc980b868725387e9854dbee84f95a4094b944d655521b6261", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e279d2ef8f6a570cb8a154fae623be7b", "guid": "bfdfe7dc352907fc980b868725387e98317b4f4463528c97c920b7ba931384b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c7d4a5db1f869d66a6725548d888a5b", "guid": "bfdfe7dc352907fc980b868725387e983b61d49a91a8fa0c57e7104ba5a98d42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e15b2e50aee441247a5571813c896c4a", "guid": "bfdfe7dc352907fc980b868725387e98876035e971384239c703f55af6214426"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066c8465bdfe51a5cac4e50d40692291", "guid": "bfdfe7dc352907fc980b868725387e98837c41fbe46359e88adfc31c4194c4ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa46dc51df10a62d9f343ee40844b85", "guid": "bfdfe7dc352907fc980b868725387e98f0fb9764fe2fddcd363a86f82969ee91"}], "guid": "bfdfe7dc352907fc980b868725387e98fd4b477b76d45d143951238d14c839a6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985dd9787e78eccfac488b6e83b7d0e05d", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e981f54495842232af33ada12b6fa0095e6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}