{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9854b3aa03640f6ca0172eab79684ecd53", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98462cfde20fd93a54e7fb76d78df3b3f5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98462cfde20fd93a54e7fb76d78df3b3f5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987372670d536be0e774b647e16f7a8383", "guid": "bfdfe7dc352907fc980b868725387e98c6e36a7f9d2852e5355604eb470af8c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aad8363465a60cdc9b1b302ef35ee7b8", "guid": "bfdfe7dc352907fc980b868725387e982d6ffe12289aca5025fdaba6e84f4d8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafaaa3227179c58fc2ba53f8605eb1f", "guid": "bfdfe7dc352907fc980b868725387e988d922fd23d64f3cf0b0882adf13544c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d828073f35c86e732f464bfc7ba30150", "guid": "bfdfe7dc352907fc980b868725387e98c3df6784226c67733d0d457a65fc2d0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873c1bd1829a3cbbc4a1f667e823307c4", "guid": "bfdfe7dc352907fc980b868725387e982299cd2b664aa16903d22074c3f24290", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df66edb7f9e8f47a63230ea3e849cfcd", "guid": "bfdfe7dc352907fc980b868725387e98d3ddefdf492b7d6f383d8122bcf18954", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899821da23b36efa1c1096db260d88fcf", "guid": "bfdfe7dc352907fc980b868725387e9811168c4e51d3be8c4a6057de91f4e94b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f554e19436f2a00676bdf22862fb496f", "guid": "bfdfe7dc352907fc980b868725387e98206f8363ce0d9a10dd81cd970ea574b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b88ef391fc8ecd15652e73619ac8516", "guid": "bfdfe7dc352907fc980b868725387e988737d10fa9db0b3ad67151ae9125d37f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f0ff6ef351d7d5272901e3f86962f6", "guid": "bfdfe7dc352907fc980b868725387e9868063ac66fd75f63ef938b02c9f91356", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6175e4be8da0d39c30fd73e7a7f188f", "guid": "bfdfe7dc352907fc980b868725387e98adf966cbedbd9ce60a3e5c559d9b66e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd430e9cb2f2f4a1122156237a945a26", "guid": "bfdfe7dc352907fc980b868725387e98e2967a18e98c26b2239c05679a26dad8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b21a1ada878551ec5b473fea971d537c", "guid": "bfdfe7dc352907fc980b868725387e987b40b278ba9d7e0004e6c428dff750a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49bd00b1892ea4c1f64c4587254b542", "guid": "bfdfe7dc352907fc980b868725387e98e217361329b08953a940edab2b773e4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e1a681f11bf384a4330f8290abdfde7", "guid": "bfdfe7dc352907fc980b868725387e98436dd21943244e693a33f251fece4dd1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c68aa391f4d0b310cf74be7140fed4", "guid": "bfdfe7dc352907fc980b868725387e98878d7c8e836b291b4dde924e12ca0b0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98826a342c2f9ca1ad58f13178d00c6e18", "guid": "bfdfe7dc352907fc980b868725387e986f096fba59193fe1ee7df3a70b0f901d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3339c49f148babc0be10ca7b0b7eca7", "guid": "bfdfe7dc352907fc980b868725387e98511717bd11cfb8caddad10ed3567707a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d29d2a2a88587974f2b72fd34143a66b", "guid": "bfdfe7dc352907fc980b868725387e986baee6b26851a689aaef1b9a62951a10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac9af7721592a3669bf51c3aef2223f", "guid": "bfdfe7dc352907fc980b868725387e98ac4f9724a37a0e68b4b6e48434c938d8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98acde1abf39f1b7f6a1edd67dbeaba71e", "guid": "bfdfe7dc352907fc980b868725387e98c3f92a9391b2b9ba85705071432f0bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9c145eb6220b650a42dc31d8341efa", "guid": "bfdfe7dc352907fc980b868725387e98cf45c877983f1d20789191b4ef2c5cf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a12b820ce1390c70ccfd021a2aef2e", "guid": "bfdfe7dc352907fc980b868725387e988e133a59ef1e6091ba89114b030ba339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2ea3ac80832dc9acff66882cae64cc3", "guid": "bfdfe7dc352907fc980b868725387e98581c96c6667e2190c297360b60ea97ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd28f2af23daaee456d464ba9770a625", "guid": "bfdfe7dc352907fc980b868725387e985259d079adf2d65a79e21a55e658feec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98271e15c7a7b5d4211a1b549040cf5e07", "guid": "bfdfe7dc352907fc980b868725387e98c3f59e1387554a8ce8889d67d275ce01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bf974257b6800ede2aad07e4fa06ae0", "guid": "bfdfe7dc352907fc980b868725387e98ba71e067874d5a0b37aeb7f92850ab62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d793e96c9b4e92bfc404c3a2d7847b82", "guid": "bfdfe7dc352907fc980b868725387e988aa0d0829d5d262970a94e25afcb21b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988220c398cc0924d461bfc9e185bf6d8e", "guid": "bfdfe7dc352907fc980b868725387e98205d482e78c9fd95d14b870bf1d71614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98800bd8866cea25a499266d25753358fd", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864635ddc7e6de0598dffaf13580e0542", "guid": "bfdfe7dc352907fc980b868725387e9885a036e4ccf95f72a0e46b08ee436b97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98646187d719ef1555c358794c8aa088cd", "guid": "bfdfe7dc352907fc980b868725387e982d9805d3d8a4bd80b826a4335788db51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246317113b25e4067769460a20108f69", "guid": "bfdfe7dc352907fc980b868725387e98118e08d364fdab27cac8ed4c3f312aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989869a5f735d08e30ea6fd6ca630873aa", "guid": "bfdfe7dc352907fc980b868725387e9839c38227eaa4decbdd44f7bd71734718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fae2209734552d15cb83e734417259b", "guid": "bfdfe7dc352907fc980b868725387e9861583e70eaaecc66bc0eebcc55290bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38b475dba219cb62fa6620a84334d4c", "guid": "bfdfe7dc352907fc980b868725387e987894d7860cc4e1c593c13cd2c1de5cdc"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e15b2e50aee441247a5571813c896c4a", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}