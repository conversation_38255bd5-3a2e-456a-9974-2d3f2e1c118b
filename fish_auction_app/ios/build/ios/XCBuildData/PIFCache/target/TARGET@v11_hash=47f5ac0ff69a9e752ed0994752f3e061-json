{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846d2646a739ca2e4f36de02d1630e6e4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cc5af00f2de05fab9feca852e8046c91", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f12843a9113fff207a055b55d47e496d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fffb172ed865de850dc4d79cf154469", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f12843a9113fff207a055b55d47e496d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Google-Maps-iOS-Utils/Google-Maps-iOS-Utils.modulemap", "PRODUCT_MODULE_NAME": "GoogleMapsUtils", "PRODUCT_NAME": "GoogleMapsUtils", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e47fe7064d9aaea4b2d376a9a0b75bb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f22884acd1e41455abaa7666f1e5227", "guid": "bfdfe7dc352907fc980b868725387e980bc126af0d50c33b848e3d4df147eba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d291064f5586d67c2c3a8e70e2472f", "guid": "bfdfe7dc352907fc980b868725387e981ca15603b15285cb017f21c30281bb92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854352c4c13a1f1eb08f3a1c45d4a35ee", "guid": "bfdfe7dc352907fc980b868725387e98fb6b7e37229435930de9154a3286013c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d0710acd453062c770e0be21e14a87", "guid": "bfdfe7dc352907fc980b868725387e98757b3bb2a60f425321166ba3d41dc529", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822745f4ce4b2360feeabeaea24292171", "guid": "bfdfe7dc352907fc980b868725387e9857ff4c86f8be647184ef8a336bfb36aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef786f9356d82244e2bb0c0aeb68e11a", "guid": "bfdfe7dc352907fc980b868725387e98a99d9f2e0e182d25b53e4c4d487dcf8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ab53b4cb731acd0b0f609d4eb9db65", "guid": "bfdfe7dc352907fc980b868725387e9854994ff5ca73efed5a8dccc130de0b3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccff355de5866fe8d5f71852371d6b64", "guid": "bfdfe7dc352907fc980b868725387e984bb8c4b188e3c079e28b981018beb3d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae73b79a4881cd4ee949dcbe603ae00e", "guid": "bfdfe7dc352907fc980b868725387e98864fa0da1684e4ae480cf9555fcac616", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4452b8b4c8d86f1c6d89d83f330ef80", "guid": "bfdfe7dc352907fc980b868725387e98c42ba9108b307fd0dc79a78cfdf30352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e1e992f9f51ee833b22f5653f96c31", "guid": "bfdfe7dc352907fc980b868725387e9800a4340d964387194f8caae468df01f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98381d54ccd716b10d5d9bf23acd4bc9cb", "guid": "bfdfe7dc352907fc980b868725387e98a56c8961b713f77576696b87b5e385a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882bcadf1cdb44bbc893164281e15c333", "guid": "bfdfe7dc352907fc980b868725387e9826190ea268930b892c5ea0ab976ef93f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb4ec32e95b5495b3743c354f7b63b5e", "guid": "bfdfe7dc352907fc980b868725387e98f82280d55c8416b3b83ec7d4c804903e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98660f461197773e83af838015629ea64d", "guid": "bfdfe7dc352907fc980b868725387e98d6ee06b1efe28a4d9404588761999ed3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98182a606ec2e379a68d6135f6de81c770", "guid": "bfdfe7dc352907fc980b868725387e9878109cb5d165bdd12bd3d56c92d0a5f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985239fcae2e0032c225a2c0786b18385d", "guid": "bfdfe7dc352907fc980b868725387e985c40bc039fc56a1997df86b0e5388485", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4c9133ac9c93cec87c997ae7a3b012", "guid": "bfdfe7dc352907fc980b868725387e986e77d62d9c982dcfc2b06a66beee49e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a881a560c978df294e70ad51a260ec0", "guid": "bfdfe7dc352907fc980b868725387e9827bd198a2b0d74f76aa79029bfbdb9c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cef6b4366ee97c0600e3f3479fd5a24e", "guid": "bfdfe7dc352907fc980b868725387e98aed3413fb0764d3ea679da9ad0eb79fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831a7f4e55c5d014f330053e867d902d5", "guid": "bfdfe7dc352907fc980b868725387e98a589f08bc84633c06e38d4335493f190", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d5596466e8186836a366304dd4000e", "guid": "bfdfe7dc352907fc980b868725387e98cceb9b2b981481a423868c4c3978c6fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e49f9037f0fdc9d08c18d9e3f52ece9", "guid": "bfdfe7dc352907fc980b868725387e9832130177668ab910d027344f5f8f26a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bed67b488a9a75f6ec407adc70d1463", "guid": "bfdfe7dc352907fc980b868725387e9808b17a852eb76e71ace8b551f4b8da56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6987fc7cb3f16eebdd0417cd232d22", "guid": "bfdfe7dc352907fc980b868725387e98c5bf3360f3cf8db3366045eb6bb41585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98480b11af5b3f54981b9e9a1af7b5bd62", "guid": "bfdfe7dc352907fc980b868725387e98f07567e7a3e15cfbeddb3b1b61af43f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437a8fecdf3a77bfcebfd7d65615ab56", "guid": "bfdfe7dc352907fc980b868725387e98dacb542c669bb0bdc627ad2670436a9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276ccc766e81117293437b45ddad46c1", "guid": "bfdfe7dc352907fc980b868725387e98f6d40f4660d3b9adfcbdcae7a34f5a07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733e5235089450ac1934c09857d4f0f8", "guid": "bfdfe7dc352907fc980b868725387e9877533e4ab9406d96a2586427bf12e534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152c85598362f3a5ea31f7bf00c6dde5", "guid": "bfdfe7dc352907fc980b868725387e98a6122e76d9dbde6b41e7eb6580881314", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e50b00c754865470855be66f6cbdba", "guid": "bfdfe7dc352907fc980b868725387e98b11117b5c6d6b59b839b5ba3c9b5c07c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c66a31061f29513ae75163e00d143f0", "guid": "bfdfe7dc352907fc980b868725387e98aad7a1aa688219c7eb5540dae7040fe2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f10b2719212198c891f00dd3942d8bc", "guid": "bfdfe7dc352907fc980b868725387e98e2eba1fb0a80de1c41fe92f112e46a0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa76f2ec041d0a1f32bdd36e94de11e2", "guid": "bfdfe7dc352907fc980b868725387e988908258bafaa5fa978fe4f612f3c936b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bed6c5ea67087e4f0fd7cf561da85fd", "guid": "bfdfe7dc352907fc980b868725387e9866d13c12cdd1261ed652fc64e5b9a98d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896cc333731ead88347756708c4ce886f", "guid": "bfdfe7dc352907fc980b868725387e985e1faa9bb06604de4c24ad2c65202583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc95561bae6372d93b5f6cc25f55a24", "guid": "bfdfe7dc352907fc980b868725387e98eb487c20e34367d3865c5c8190669bec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce491c6de9bacd111db304955b017a62", "guid": "bfdfe7dc352907fc980b868725387e98159f17ec64730260abf74d3c8325430d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a477b6a3d8e75844291b05d0a5ccb755", "guid": "bfdfe7dc352907fc980b868725387e98c7f89498bacbc50d4ad578ba808bd591", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db16af7793011f78008cf189d6e53320", "guid": "bfdfe7dc352907fc980b868725387e989078881955b210a7c2e0c00368fed91a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae30c9881654a46e49ed7ee0960a77e", "guid": "bfdfe7dc352907fc980b868725387e98a1e0f65fe50d40deb6c04a606e08f942", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9e54c715c5a8a59f097b67c6efed33", "guid": "bfdfe7dc352907fc980b868725387e986bff559e40fe5da84fce2ecc16bf2364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830d07610fbb7e36df272f6d81cda3350", "guid": "bfdfe7dc352907fc980b868725387e98d006be67e37ec81940472c03654bfd2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adeeec47dce131c6518e317ef0907dd2", "guid": "bfdfe7dc352907fc980b868725387e986de07e074d8c3f5becb31e15cf4fafa3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c1149dd753655da6258a24172a4e392", "guid": "bfdfe7dc352907fc980b868725387e983e3e7c27f2adf38629d5a4be2efb0064", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9846c6b2f7df326fb256cdde26a12e233f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888bc5a046a5b2f6ab3ade33fc655cf73", "guid": "bfdfe7dc352907fc980b868725387e98a5648b9081186db166e85cac17d23a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a64ae36d265bf00a51b9663ad1e23cec", "guid": "bfdfe7dc352907fc980b868725387e986a3528c9c3e28232f39ac86347327a86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b6b6b641dc5c2168755566b735d3bc", "guid": "bfdfe7dc352907fc980b868725387e98bd963f9860264537e25b92949fd28b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814af6c87c356306ed782916be5485531", "guid": "bfdfe7dc352907fc980b868725387e982ce5b64449a184a834f87567cc1a64f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985407a72194382209c5afacc450a7c937", "guid": "bfdfe7dc352907fc980b868725387e9818e0da3e9a9cd374dd9fac5b161a1ce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98128f8e5fc7ff5f82e3beb6de445d1659", "guid": "bfdfe7dc352907fc980b868725387e986c0c5d31a31deb997ac5ffd774c096c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49e2a6a229cc7c18b37fbf9ea9dfb35", "guid": "bfdfe7dc352907fc980b868725387e984b0d38cecc626956a13db411295e7f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e77d1966efb156b0f21b36901432bde", "guid": "bfdfe7dc352907fc980b868725387e98cf1119d1216c5ae8957933ef41771e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985935606b2c7cc090a7b7d2272f13a70d", "guid": "bfdfe7dc352907fc980b868725387e9897ba994fb7fd177e59b87839993d3a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dab6602b09b7ab818809966e6fe8209a", "guid": "bfdfe7dc352907fc980b868725387e98171b9fefae944ae31e11fdd309128dd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865386d33d4c67122f8e0b12364976f20", "guid": "bfdfe7dc352907fc980b868725387e98c68cb341553da18a9ad8bbe166319303"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c135741b36dda023125e3abe7dbe8659", "guid": "bfdfe7dc352907fc980b868725387e983ac583f1f547e3a7c184751c981a64f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b614c9796e57a2a5924c4ab365f172", "guid": "bfdfe7dc352907fc980b868725387e985bca98380c04be8f540b8aa4730622d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e6c99a4aa03890da89b06b4a6e966b", "guid": "bfdfe7dc352907fc980b868725387e9858f5469e53c6eb704ca502a275e6a6b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f35886cce102053166c906ed1aa1b7d4", "guid": "bfdfe7dc352907fc980b868725387e9826a05f7301ee9e3061d4940fc87a34c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf711a8077d4a0b3950972554525088", "guid": "bfdfe7dc352907fc980b868725387e98a2f196effe5382f24ec889260b442d3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1bfba59680753ff11cafa7b8dbe1f88", "guid": "bfdfe7dc352907fc980b868725387e9814cb6f649022a81887e5544fba87bf35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a7089a2d7ad711a6af4e456247d907", "guid": "bfdfe7dc352907fc980b868725387e98c36afd8100388ee352795a5c47d248a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad22f32d9d41e74a02a6e8621a53bfa", "guid": "bfdfe7dc352907fc980b868725387e98fdf461cd989237ea6c04a23fdffb89f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892fe2ec92d587e3135248d9dc7a21e8e", "guid": "bfdfe7dc352907fc980b868725387e98a24f1fc1a3933e455c4dcf199bfc1fdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2dbb9eda784bb97e3544007d35ed692", "guid": "bfdfe7dc352907fc980b868725387e98500989c389ee0ad0d93bcb2b5ce48af4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5d4b89864ff14a54574285e6b28778", "guid": "bfdfe7dc352907fc980b868725387e98142ea60047f99126147226666c395e86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98380116951cb7c41aef19efeecd453792", "guid": "bfdfe7dc352907fc980b868725387e989ffbaa49f89e709fa1f9f5e1413e5dcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a911e1743f4b6a2fa64e628b2a1da9f6", "guid": "bfdfe7dc352907fc980b868725387e986efd791bee701e9f7f1a350aa84e8e57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7455f581d7b328959d2d5910d1cf630", "guid": "bfdfe7dc352907fc980b868725387e9857b8053922b8ac7a29f179ac070c1298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c1709ef5b312f94a47cc415ce7af820", "guid": "bfdfe7dc352907fc980b868725387e98313edb0ca3ceb578e58610ce347dbf6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98695997eefac54fd24b4a6065afa5b839", "guid": "bfdfe7dc352907fc980b868725387e98f387132460848715f54a127a4070430d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024346c1039b5699075ff28ffebc1c89", "guid": "bfdfe7dc352907fc980b868725387e98436f37718c13e842b0021a4a09e0a6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984652c4a0a158b59e6d5df79bf602b9b7", "guid": "bfdfe7dc352907fc980b868725387e981073f7da921d217f8e91a9f34e3af3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6d09f1a14a9cc3e55ac35e9f4bd6f3", "guid": "bfdfe7dc352907fc980b868725387e98e3440c233bc1cce5c543073c1817adc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98685e873cc1660e416b15131264067038", "guid": "bfdfe7dc352907fc980b868725387e98f82327c35e70a4c8f1fd3287cbb32697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32d223e6aa053a0c90ff375be883f74", "guid": "bfdfe7dc352907fc980b868725387e987869b0dc1ea0115c3f32945fa2fee7aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7d22f17a118bd98141721699801bfc2", "guid": "bfdfe7dc352907fc980b868725387e9871ab7fd758afe96e07ce994494ff4a8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98621fe70e1901a4c38e9e255a18918680", "guid": "bfdfe7dc352907fc980b868725387e98374624320f563fa430ce45bc02cee4eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806a95d9bb2a95f8116b05b9c374645c2", "guid": "bfdfe7dc352907fc980b868725387e98527d91d607a1ac26c4ca6398535c2ee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bde1fa059b5713bbcaafc9399f72ced", "guid": "bfdfe7dc352907fc980b868725387e980eec9122b44891305168328f7a58d9ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981246c580eed66554a6e4ade014212396", "guid": "bfdfe7dc352907fc980b868725387e98d9ab5ed66bce2db3fee941bac5b2b989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ecc980c573d9cba4f4cf143911aa7a", "guid": "bfdfe7dc352907fc980b868725387e984e599cf6715af1d7fabb834b2ce20681"}], "guid": "bfdfe7dc352907fc980b868725387e9853492ae621ed1bde48851eb95822b09e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e15b2e50aee441247a5571813c896c4a", "guid": "bfdfe7dc352907fc980b868725387e98a2d747c9a6b606cf296f76ca22c5d28a"}], "guid": "bfdfe7dc352907fc980b868725387e98970b78dabbf8d927a006ff56bd237725", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983343457f836ba04799ea32f41febf5e4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988d4f056f23e4e16df108000d3c5e64e7", "name": "GoogleMapsUtils.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}