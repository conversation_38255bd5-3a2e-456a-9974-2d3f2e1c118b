# Fish Auction App - Complete Setup Guide

A comprehensive Flutter application for fish auctions with real-time bidding, payment processing, and delivery tracking. This guide covers the complete setup process including the Django backend with WebSocket support.

## 🎯 Overview
source venv/bin/activate && daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application

ngrok http 8000

The Fish Auction platform consists of:
- **Flutter Mobile/Web App** - User interface for buyers and sellers
- **Django Backend** - REST API with WebSocket support for real-time features
- **Redis** - Message broker for WebSocket connections and caching
- **SQLite/PostgreSQL** - Database for storing auction data
- **Celery** - Background task processing for automated auctions

## 🚀 Quick Start

### Prerequisites

#### Backend Requirements
- Python 3.8+
- Redis Server
- Git

#### Frontend Requirements
- Flutter SDK 3.0+
- Dart SDK 3.0+
- Android Studio / VS Code
- Chrome browser (for web testing)
