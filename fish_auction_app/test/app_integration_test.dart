import 'package:flutter_test/flutter_test.dart';
import 'package:fish_auction_app/main.dart';
import 'package:fish_auction_app/providers/auth_provider.dart';
import 'package:fish_auction_app/providers/auction_provider.dart';
import 'package:fish_auction_app/models/user_model.dart';
import 'package:fish_auction_app/models/auction_model.dart';
import 'package:fish_auction_app/models/bid_model.dart';
import 'package:fish_auction_app/utils/api_response.dart';

void main() {
  group('Fish Auction App Integration Tests', () {
    testWidgets('App should start with splash screen', (WidgetTester tester) async {
      await tester.pumpWidget(const FishAuctionApp());
      
      // Should show splash screen initially
      expect(find.text('Fish Auction'), findsOneWidget);
      expect(find.text('Loading...'), findsOneWidget);
    });

    testWidgets('Should navigate to login screen when not authenticated', (WidgetTester tester) async {
      await tester.pumpWidget(const FishAuctionApp());
      
      // Wait for initialization
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Should show login screen
      expect(find.text('Login'), findsWidgets);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
    });

    testWidgets('Login form should validate inputs', (WidgetTester tester) async {
      await tester.pumpWidget(const FishAuctionApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Try to login without entering credentials
      final loginButton = find.text('Login').last;
      await tester.tap(loginButton);
      await tester.pump();
      
      // Should show validation errors
      expect(find.text('This field is required'), findsWidgets);
    });

    testWidgets('Should navigate to register screen', (WidgetTester tester) async {
      await tester.pumpWidget(const FishAuctionApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Tap register link
      final registerLink = find.text('Register');
      await tester.tap(registerLink);
      await tester.pumpAndSettle();
      
      // Should show register screen
      expect(find.text('Create your account'), findsOneWidget);
      expect(find.text('Account Type'), findsOneWidget);
    });

    testWidgets('Register form should show user type options', (WidgetTester tester) async {
      await tester.pumpWidget(const FishAuctionApp());
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Navigate to register
      await tester.tap(find.text('Register'));
      await tester.pumpAndSettle();
      
      // Should show user type options
      expect(find.text('Buyer'), findsOneWidget);
      expect(find.text('Seller'), findsOneWidget);
      expect(find.text('Broker'), findsOneWidget);
      expect(find.text('Service Provider'), findsOneWidget);
    });
  });

  group('Model Tests', () {
    test('User model should serialize/deserialize correctly', () {
      final userJson = {
        'id': 1,
        'username': 'testuser',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'user_type': 'buyer',
        'is_verified': true,
        'wallet_balance': 100.50,
        'kyc_status': 'approved',
        'phone_number': '+**********',
        'address': '123 Test St',
        'city': 'Test City',
        'country': 'Test Country',
        'preferred_language': 'en',
        'profile_picture': 'https://example.com/image.jpg',
        'date_joined': '2024-01-01T00:00:00Z',
      };

      final user = User.fromJson(userJson);
      
      expect(user.id, equals(1));
      expect(user.username, equals('testuser'));
      expect(user.email, equals('<EMAIL>'));
      expect(user.userType, equals('buyer'));
      expect(user.isVerified, equals(true));
      expect(user.walletBalance, equals(100.50));
      expect(user.kycStatus, equals('approved'));
      expect(user.displayName, equals('Test User'));
      
      final serialized = user.toJson();
      expect(serialized['username'], equals('testuser'));
      expect(serialized['email'], equals('<EMAIL>'));
    });

    test('Auction model should serialize/deserialize correctly', () {
      final categoryJson = {
        'id': 1,
        'name': 'Fresh Sea Fish',
        'name_ar': 'أسماك البحر الطازجة',
        'is_active': true,
      };

      final sellerJson = {
        'id': 1,
        'username': 'seller',
        'email': '<EMAIL>',
        'first_name': 'Seller',
        'last_name': 'User',
        'user_type': 'seller',
        'is_verified': true,
        'wallet_balance': 0.0,
        'kyc_status': 'approved',
        'date_joined': '2024-01-01T00:00:00Z',
      };

      final auctionJson = {
        'id': 1,
        'title': 'Fresh Tuna',
        'title_ar': 'تونة طازجة',
        'description': 'High quality tuna',
        'description_ar': 'تونة عالية الجودة',
        'seller': sellerJson,
        'category': categoryJson,
        'starting_price': 50.0,
        'current_price': 75.0,
        'reserve_price': 100.0,
        'bid_increment': 5.0,
        'status': 'live',
        'start_time': '2024-01-01T10:00:00Z',
        'end_time': '2024-01-01T18:00:00Z',
        'total_bids': 5,
        'images': ['https://example.com/image1.jpg'],
        'location': 'Test Harbor',
        'fish_type': 'Tuna',
        'quantity': 10.0,
        'unit': 'kg',
        'quality_grade': 'A',
        'special_notes': 'Fresh catch',
        'is_watched': false,
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
      };

      final auction = Auction.fromJson(auctionJson);
      
      expect(auction.id, equals(1));
      expect(auction.title, equals('Fresh Tuna'));
      expect(auction.currentPrice, equals(75.0));
      expect(auction.status, equals('live'));
      expect(auction.fishType, equals('Tuna'));
      expect(auction.quantity, equals(10.0));
      expect(auction.qualityGrade, equals('A'));
      expect(auction.formattedQuantity, equals('10.0 kg'));
      expect(auction.primaryImage, equals('https://example.com/image1.jpg'));
      expect(auction.sellerName, equals('Seller User'));
      
      final serialized = auction.toJson();
      expect(serialized['title'], equals('Fresh Tuna'));
      expect(serialized['current_price'], equals(75.0));
    });

    test('Bid model should serialize/deserialize correctly', () {
      final bidderJson = {
        'id': 2,
        'username': 'bidder',
        'email': '<EMAIL>',
        'first_name': 'Bidder',
        'last_name': 'User',
        'user_type': 'buyer',
        'is_verified': true,
        'wallet_balance': 200.0,
        'kyc_status': 'approved',
        'date_joined': '2024-01-01T00:00:00Z',
      };

      final auctionJson = {
        'id': 1,
        'title': 'Test Auction',
        'title_ar': 'مزاد تجريبي',
        'description': 'Test auction description',
        'description_ar': 'وصف المزاد التجريبي',
        'seller': bidderJson,
        'category': {'id': 1, 'name': 'Test', 'name_ar': 'تجربة', 'is_active': true},
        'starting_price': 50.0,
        'current_price': 75.0,
        'bid_increment': 5.0,
        'status': 'live',
        'start_time': '2024-01-01T10:00:00Z',
        'end_time': '2024-01-01T18:00:00Z',
        'total_bids': 1,
        'images': [],
        'fish_type': 'Test Fish',
        'quantity': 1.0,
        'unit': 'kg',
        'quality_grade': 'A',
        'is_watched': false,
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
      };

      final bidJson = {
        'id': 1,
        'auction': auctionJson,
        'bidder': bidderJson,
        'amount': 80.0,
        'is_auto_bid': false,
        'is_winning_bid': true,
        'created_at': '2024-01-01T12:00:00Z',
      };

      final bid = Bid.fromJson(bidJson);
      
      expect(bid.id, equals(1));
      expect(bid.amount, equals(80.0));
      expect(bid.bidTypeDisplay, equals('Manual Bid'));
      expect(bid.isAutoBid, equals(false));
      expect(bid.isWinningBid, equals(true));
      expect(bid.bidderName, equals('Bidder User'));
      expect(bid.bidTypeDisplay, equals('Manual Bid'));
      expect(bid.statusDisplay, equals('Winning'));
      
      final serialized = bid.toJson();
      expect(serialized['amount'], equals(80.0));
      expect(serialized['is_auto_bid'], equals(false));
    });

    test('ApiResponse should handle success and error cases', () {
      // Test success response
      final successResponse = ApiResponse.success(
        data: {'message': 'Success'},
        statusCode: 200,
      );
      
      expect(successResponse.isSuccess, equals(true));
      expect(successResponse.isError, equals(false));
      expect(successResponse.data, equals({'message': 'Success'}));
      expect(successResponse.statusCode, equals(200));
      
      // Test error response
      final errorResponse = ApiResponse.error(
        message: 'Something went wrong',
        statusCode: 400,
      );
      
      expect(errorResponse.isSuccess, equals(false));
      expect(errorResponse.isError, equals(true));
      expect(errorResponse.message, equals('Something went wrong'));
      expect(errorResponse.statusCode, equals(400));
    });
  });

  group('Provider Tests', () {
    test('AuthProvider should initialize correctly', () {
      final authProvider = AuthProvider();
      
      expect(authProvider.user, isNull);
      expect(authProvider.isLoading, equals(false));
      expect(authProvider.isLoggedIn, equals(false));
      expect(authProvider.isInitialized, equals(false));
    });

    test('AuctionProvider should initialize correctly', () {
      final auctionProvider = AuctionProvider();
      
      expect(auctionProvider.auctions, isEmpty);
      expect(auctionProvider.categories, isEmpty);
      expect(auctionProvider.watchlist, isEmpty);
      expect(auctionProvider.userBids, isEmpty);
      expect(auctionProvider.selectedAuction, isNull);
      expect(auctionProvider.isLoading, equals(false));
      expect(auctionProvider.hasMoreData, equals(true));
    });
  });
}
