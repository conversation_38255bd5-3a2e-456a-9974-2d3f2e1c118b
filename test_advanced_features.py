#!/usr/bin/env python3
"""
Comprehensive Advanced Features Testing for Fish Auction Platform
Tests all advanced functionality including WebSockets, automation, payments, etc.
"""

import requests
import json
import time
import asyncio
import websockets
from datetime import datetime, timedelta
from decimal import Decimal

BASE_URL = "http://127.0.0.1:8000/api"
WS_URL = "ws://127.0.0.1:8000/ws"

class AdvancedFeaturesTest:
    def __init__(self):
        self.base_url = BASE_URL
        self.ws_url = WS_URL
        self.session = requests.Session()
        self.tokens = {}
        self.test_users = {}
        self.test_auction_id = None
        
    def log(self, message):
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        
    def setup_test_users(self):
        """Setup test users for advanced testing"""
        self.log("Setting up test users...")
        
        # Login existing users or create new ones
        test_credentials = [
            {"username": "buyer_advanced", "password": "testpass123", "user_type": "buyer"},
            {"username": "seller_advanced", "password": "testpass123", "user_type": "seller"},
            {"username": "broker_advanced", "password": "testpass123", "user_type": "broker"}
        ]
        
        for cred in test_credentials:
            # Try to login first
            response = self.session.post(f"{self.base_url}/auth/login/", json=cred)
            if response.status_code == 200:
                token_data = response.json()
                self.tokens[cred['user_type']] = token_data["tokens"]["access"]
                self.log(f"✅ Logged in existing {cred['user_type']}")
            else:
                self.log(f"⚠️ Could not login {cred['user_type']}, will use existing tokens")
    
    def test_websocket_connection(self):
        """Test WebSocket connection for real-time updates"""
        self.log("Testing WebSocket Connection...")
        
        async def test_ws():
            try:
                # Test auction room connection
                uri = f"{self.ws_url}/auction/1/"
                async with websockets.connect(uri) as websocket:
                    self.log("✅ WebSocket connection established")
                    
                    # Send a test message
                    test_message = {
                        "type": "join_auction",
                        "auction_id": 1
                    }
                    await websocket.send(json.dumps(test_message))
                    
                    # Wait for response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        self.log(f"✅ WebSocket response received: {response[:100]}...")
                    except asyncio.TimeoutError:
                        self.log("⚠️ WebSocket response timeout (may be normal)")
                        
            except Exception as e:
                self.log(f"❌ WebSocket connection failed: {str(e)}")
        
        try:
            asyncio.run(test_ws())
        except Exception as e:
            self.log(f"❌ WebSocket test failed: {str(e)}")
    
    def test_automated_bidding(self):
        """Test automated bidding agent functionality"""
        self.log("Testing Automated Bidding...")
        
        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for automated bidding test")
            return
            
        # Get a live auction
        response = self.session.get(f"{self.base_url}/auctions/")
        if response.status_code != 200:
            self.log("❌ Could not fetch auctions")
            return
            
        auctions = response.json()
        live_auctions = [a for a in auctions.get('results', []) if a.get('status') == 'live']
        
        if not live_auctions:
            self.log("⚠️ No live auctions for automated bidding test")
            return
            
        auction_id = live_auctions[0]['id']
        
        # Set up auto-bid
        auto_bid_data = {
            "max_amount": "100.00",
            "bid_increment": "2.00"
        }
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        response = self.session.post(
            f"{self.base_url}/auctions/{auction_id}/auto-bid/", 
            json=auto_bid_data, 
            headers=headers
        )
        
        if response.status_code == 201:
            self.log("✅ Auto-bid agent configured successfully")
        else:
            self.log(f"❌ Auto-bid setup failed: {response.text}")
    
    def test_payment_processing(self):
        """Test payment processing and wallet operations"""
        self.log("Testing Payment Processing...")
        
        for user_type in ["buyer", "seller", "broker"]:
            if user_type not in self.tokens:
                continue
                
            headers = {"Authorization": f"Bearer {self.tokens[user_type]}"}
            
            # Test wallet balance
            response = self.session.get(f"{self.base_url}/payments/wallet/balance/", headers=headers)
            if response.status_code == 200:
                balance_data = response.json()
                self.log(f"✅ {user_type} wallet balance: ${balance_data.get('balance', 0)}")
            else:
                self.log(f"❌ Failed to get {user_type} wallet balance")
                
            # Test wallet transactions
            response = self.session.get(f"{self.base_url}/payments/wallet/transactions/", headers=headers)
            if response.status_code == 200:
                transactions = response.json()
                self.log(f"✅ {user_type} has {len(transactions.get('results', []))} transactions")
            else:
                self.log(f"❌ Failed to get {user_type} transactions")
                
            # Test wallet top-up (would normally integrate with Stripe)
            topup_data = {
                "amount": "50.00",
                "payment_method": "test_card"
            }
            response = self.session.post(f"{self.base_url}/payments/wallet/topup/", json=topup_data, headers=headers)
            if response.status_code in [200, 201]:
                self.log(f"✅ {user_type} wallet top-up initiated")
            else:
                self.log(f"⚠️ {user_type} wallet top-up: {response.status_code}")
    
    def test_notification_system(self):
        """Test notification system"""
        self.log("Testing Notification System...")
        
        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for notification test")
            return
            
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Get notifications
        response = self.session.get(f"{self.base_url}/notifications/", headers=headers)
        if response.status_code == 200:
            notifications = response.json()
            self.log(f"✅ Retrieved {len(notifications.get('results', []))} notifications")
        else:
            self.log(f"❌ Failed to get notifications: {response.text}")
            
        # Test notification preferences
        prefs_data = {
            "email_notifications": True,
            "sms_notifications": True,
            "push_notifications": True,
            "auction_updates": True,
            "bid_updates": True,
            "payment_updates": True
        }
        response = self.session.post(f"{self.base_url}/notifications/preferences/", json=prefs_data, headers=headers)
        if response.status_code in [200, 201]:
            self.log("✅ Notification preferences updated")
        else:
            self.log(f"⚠️ Notification preferences: {response.status_code}")
    
    def test_delivery_tracking(self):
        """Test delivery tracking system"""
        self.log("Testing Delivery Tracking...")

        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for delivery test")
            return

        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}

        # Test delivery providers
        response = self.session.get(f"{self.base_url}/delivery/providers/")
        if response.status_code == 200:
            providers = response.json()
            self.log(f"✅ Retrieved {len(providers.get('results', []))} delivery providers")
        else:
            self.log(f"⚠️ Delivery providers: {response.status_code}")

        # Test user deliveries
        response = self.session.get(f"{self.base_url}/delivery/", headers=headers)
        if response.status_code == 200:
            deliveries = response.json()
            self.log(f"✅ Retrieved {len(deliveries.get('results', []))} delivery records")
        else:
            self.log(f"⚠️ Delivery tracking: {response.status_code}")

        # Test vessels
        response = self.session.get(f"{self.base_url}/delivery/vessels/", headers=headers)
        if response.status_code == 200:
            vessels = response.json()
            self.log(f"✅ Retrieved {len(vessels.get('results', []))} vessels")
        else:
            self.log(f"⚠️ Vessels: {response.status_code}")
    
    def test_kyc_verification(self):
        """Test KYC verification system"""
        self.log("Testing KYC Verification...")
        
        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for KYC test")
            return
            
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Check KYC status
        response = self.session.get(f"{self.base_url}/auth/profile/", headers=headers)
        if response.status_code == 200:
            profile = response.json()
            kyc_status = profile.get('kyc_status', 'not_submitted')
            self.log(f"✅ KYC Status: {kyc_status}")
        else:
            self.log(f"❌ Failed to get KYC status")
    
    def test_auction_lifecycle(self):
        """Test complete auction lifecycle"""
        self.log("Testing Auction Lifecycle...")
        
        # Get auctions in different states
        response = self.session.get(f"{self.base_url}/auctions/")
        if response.status_code == 200:
            auctions = response.json()
            auction_states = {}
            
            for auction in auctions.get('results', []):
                status = auction.get('status', 'unknown')
                auction_states[status] = auction_states.get(status, 0) + 1
            
            self.log(f"✅ Auction states: {auction_states}")
        else:
            self.log(f"❌ Failed to get auction lifecycle data")
    
    def test_watchlist_functionality(self):
        """Test auction watchlist"""
        self.log("Testing Watchlist Functionality...")

        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for watchlist test")
            return

        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}

        # Get watchlist
        response = self.session.get(f"{self.base_url}/auctions/watchlist/", headers=headers)
        if response.status_code == 200:
            watchlist = response.json()
            self.log(f"✅ Watchlist has {len(watchlist.get('results', []))} items")
        else:
            self.log(f"⚠️ Watchlist test: {response.status_code}")

    def test_ai_support_bot(self):
        """Test AI support bot functionality"""
        self.log("Testing AI Support Bot...")

        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for AI bot test")
            return

        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}

        # Test chat with bot
        chat_data = {
            "message": "Hello, I need help with bidding"
        }
        response = self.session.post(f"{self.base_url}/support/chat/", json=chat_data, headers=headers)
        if response.status_code == 200:
            chat_response = response.json()
            self.log(f"✅ AI Bot responded: {chat_response.get('response', '')[:50]}...")
        else:
            self.log(f"⚠️ AI Bot test: {response.status_code}")

        # Test chat sessions
        response = self.session.get(f"{self.base_url}/support/sessions/", headers=headers)
        if response.status_code == 200:
            sessions = response.json()
            self.log(f"✅ Retrieved {len(sessions.get('results', []))} chat sessions")
        else:
            self.log(f"⚠️ Chat sessions: {response.status_code}")

    def test_enhanced_kyc_verification(self):
        """Test enhanced KYC verification"""
        self.log("Testing Enhanced KYC Verification...")

        if "buyer" not in self.tokens:
            self.log("❌ No buyer token for KYC test")
            return

        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}

        # Check KYC status
        response = self.session.get(f"{self.base_url}/auth/profile/", headers=headers)
        if response.status_code == 200:
            profile = response.json()
            kyc_status = profile.get('kyc_status', 'not_submitted')
            self.log(f"✅ KYC Status: {kyc_status}")

            # Test KYC pending list (admin only - will fail for regular user)
            response = self.session.get(f"{self.base_url}/auth/kyc/pending/", headers=headers)
            if response.status_code == 403:
                self.log("✅ KYC pending list properly restricted to admins")
            else:
                self.log(f"⚠️ KYC pending list: {response.status_code}")
        else:
            self.log(f"❌ Failed to get KYC status")
    
    def run_all_advanced_tests(self):
        """Run all advanced feature tests"""
        self.log("🚀 Starting Advanced Features Testing")
        self.log("=" * 60)
        
        try:
            self.setup_test_users()
            time.sleep(1)
            
            self.test_websocket_connection()
            time.sleep(1)
            
            self.test_automated_bidding()
            time.sleep(1)
            
            self.test_payment_processing()
            time.sleep(1)
            
            self.test_notification_system()
            time.sleep(1)
            
            self.test_delivery_tracking()
            time.sleep(1)
            
            self.test_kyc_verification()
            time.sleep(1)
            
            self.test_auction_lifecycle()
            time.sleep(1)
            
            self.test_watchlist_functionality()
            time.sleep(1)

            self.test_ai_support_bot()
            time.sleep(1)

            self.test_enhanced_kyc_verification()

            self.log("=" * 60)
            self.log("🎉 Advanced Features Testing Complete!")
            
        except Exception as e:
            self.log(f"❌ Advanced testing failed: {str(e)}")

if __name__ == "__main__":
    tester = AdvancedFeaturesTest()
    tester.run_all_advanced_tests()
