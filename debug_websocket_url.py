#!/usr/bin/env python3
"""
Debug WebSocket URL and connection issues
"""

import os
import sys
import django
import asyncio
import websockets
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction

async def test_websocket_urls():
    """Test different WebSocket URL formats"""
    print("🔍 Testing WebSocket URL Formats")
    print("=" * 40)
    
    # Find auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live auction found")
        return
    
    auction_id = auction.id
    print(f"✅ Testing with auction ID: {auction_id}")
    print()
    
    # Test different URL formats
    test_urls = [
        f"ws://localhost:8000/ws/auction/{auction_id}/",
        f"ws://127.0.0.1:8000/ws/auction/{auction_id}/",
        f"ws://localhost:8000/ws/auction/{auction_id}",  # Without trailing slash
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"🧪 Test {i}: {url}")
        try:
            async with websockets.connect(url, timeout=5) as websocket:
                print(f"✅ Connection successful!")
                
                # Send test message
                test_message = {
                    "type": "join_auction",
                    "auction_id": auction_id
                }
                await websocket.send(json.dumps(test_message))
                print(f"📤 Sent join message")
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    print(f"📨 Received: {response}")
                except asyncio.TimeoutError:
                    print("⏰ No response (timeout)")
                
                print("✅ Test passed!")
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
        
        print()

def check_django_websocket_setup():
    """Check Django WebSocket configuration"""
    print("🔍 Checking Django WebSocket Setup")
    print("=" * 40)
    
    # Check ASGI application
    from django.conf import settings
    asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
    print(f"ASGI Application: {asgi_app}")
    
    # Check if Daphne is running
    import subprocess
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if 'daphne' in result.stdout:
            print("✅ Daphne server is running")
        elif 'runserver' in result.stdout:
            print("⚠️ Django runserver is running (may not support WebSockets)")
            print("   Consider using: python3 manage.py runserver --noreload")
        else:
            print("❌ No Django server found running")
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
    
    # Check channel layers
    from channels.layers import get_channel_layer
    channel_layer = get_channel_layer()
    print(f"Channel Layer: {type(channel_layer).__name__}")
    
    # Test Redis connection
    try:
        from asgiref.sync import async_to_sync
        async_to_sync(channel_layer.group_send)(
            'test_group',
            {'type': 'test_message', 'message': 'test'}
        )
        print("✅ Redis connection working")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")

def check_flutter_websocket_config():
    """Check Flutter WebSocket configuration"""
    print("\n🔍 Checking Flutter WebSocket Config")
    print("=" * 40)
    
    try:
        with open('fish_auction_app/lib/constants/app_constants.dart', 'r') as f:
            content = f.read()
        
        import re
        base_url_match = re.search(r"baseUrl = '([^']+)'", content)
        ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
        
        if base_url_match and ws_url_match:
            base_url = base_url_match.group(1)
            ws_url = ws_url_match.group(1)
            
            print(f"Base URL: {base_url}")
            print(f"WebSocket URL: {ws_url}")
            
            # Check URL format
            if ws_url.startswith('ws://'):
                print("✅ WebSocket URL uses correct protocol")
            else:
                print("❌ WebSocket URL should start with 'ws://'")
            
            if ws_url.endswith('/ws'):
                print("✅ WebSocket URL format looks correct")
            else:
                print("⚠️ WebSocket URL format might be incorrect")
                
        else:
            print("❌ Could not find URLs in Flutter constants")
            
    except Exception as e:
        print(f"❌ Error reading Flutter constants: {e}")

def provide_solutions():
    """Provide solutions for WebSocket issues"""
    print("\n🔧 WebSocket Connection Solutions")
    print("=" * 40)
    
    print("1. 🚀 Restart Django with WebSocket support:")
    print("   # Stop current Django server")
    print("   # Start with Daphne (ASGI server):")
    print("   daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application")
    print("   # OR use runserver with --noreload:")
    print("   python3 manage.py runserver 0.0.0.0:8000 --noreload")
    print()
    
    print("2. 🔍 Debug WebSocket in Flutter:")
    print("   - Check browser Developer Tools")
    print("   - Network tab → Filter by 'WS'")
    print("   - Look for WebSocket connection attempts")
    print("   - Check for 404 or connection errors")
    print()
    
    print("3. 🧪 Test WebSocket manually:")
    print("   # Use websocat tool:")
    print("   websocat ws://localhost:8000/ws/auction/20/")
    print("   # Or use browser console:")
    print("   new WebSocket('ws://localhost:8000/ws/auction/20/')")
    print()
    
    print("4. 🔧 Fix Flutter WebSocket URL:")
    print("   - Ensure wsUrl = 'ws://localhost:8000/ws'")
    print("   - Check for extra characters or wrong protocol")
    print("   - Restart Flutter app after changes")

if __name__ == "__main__":
    print("🔍 WebSocket URL Debugger")
    print("=" * 30)
    
    # Check Django setup
    check_django_websocket_setup()
    
    # Check Flutter config
    check_flutter_websocket_config()
    
    # Test WebSocket connections
    print("\n🧪 Testing WebSocket Connections")
    print("=" * 40)
    asyncio.run(test_websocket_urls())
    
    # Provide solutions
    provide_solutions()
