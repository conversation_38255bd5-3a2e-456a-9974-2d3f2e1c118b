// Test bid parsing with actual API response
import 'dart:convert';

void main() {
  // Sample API response from /auctions/22/bids/
  final apiResponse = '''
  {
    "id": 3,
    "amount": "12.00",
    "bid_type": "manual",
    "timestamp": "2025-06-17T12:56:47.740055Z",
    "is_winning": false,
    "bidder": {
      "id": 26,
      "username": "rabie3",
      "email": "<EMAIL>",
      "first_name": "rabie",
      "last_name": "housainii",
      "user_type": "buyer"
    }
  }
  ''';
  
  final json = jsonDecode(apiResponse);
  print('Parsing bid JSON: $json');
  
  // Test the parsing logic
  final id = json['id'] ?? 0;
  final amount = double.tryParse(json['amount'].toString()) ?? 0.0;
  final isAutoBid = json['bid_type'] == 'auto' || json['is_auto_bid'] == true;
  final isWinning = json['is_winning'] ?? json['is_winning_bid'] ?? false;
  final timestamp = json['timestamp'] ?? json['created_at'];
  
  print('Parsed values:');
  print('- ID: $id');
  print('- Amount: $amount');
  print('- Is Auto Bid: $isAutoBid');
  print('- Is Winning: $isWinning');
  print('- Timestamp: $timestamp');
}
