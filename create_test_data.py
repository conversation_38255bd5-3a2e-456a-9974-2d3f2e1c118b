#!/usr/bin/env python3
"""
Create test data for Fish Auction Platform
"""

import os
import sys
import django
from datetime import datetime, timedelta
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import FishCategory, Auction
from marketplace.models import ServiceCategory, MarketplaceListing
from accounts.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io


def create_test_image(name="test.jpg", size=(100, 100), color=(255, 0, 0)):
    """Create a test image file"""
    image = Image.new('RGB', size, color)
    image_io = io.BytesIO()
    image.save(image_io, format='JPEG')
    image_io.seek(0)
    return SimpleUploadedFile(name, image_io.getvalue(), content_type='image/jpeg')


def create_fish_categories():
    """Create fish categories"""
    categories = [
        {'name': 'Salmon', 'description': 'Atlantic and Pacific salmon varieties'},
        {'name': 'Tuna', 'description': 'Various tuna species'},
        {'name': 'Cod', 'description': 'Atlantic and Pacific cod'},
        {'name': 'Mackerel', 'description': 'Fresh mackerel'},
        {'name': 'Sardines', 'description': 'Small pelagic fish'},
        {'name': 'Shrimp', 'description': 'Various shrimp species'},
        {'name': 'Lobster', 'description': 'Fresh lobster'},
        {'name': 'Crab', 'description': 'Various crab species'},
    ]
    
    for cat_data in categories:
        category, created = FishCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        if created:
            print(f"Created fish category: {category.name}")


def create_service_categories():
    """Create service categories"""
    categories = [
        {'name': 'Delivery', 'description': 'Fish delivery services'},
        {'name': 'Processing', 'description': 'Fish processing and cleaning'},
        {'name': 'Storage', 'description': 'Cold storage services'},
        {'name': 'Transportation', 'description': 'Fish transportation services'},
        {'name': 'Equipment Rental', 'description': 'Fishing equipment rental'},
    ]
    
    for cat_data in categories:
        category, created = ServiceCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        if created:
            print(f"Created service category: {category.name}")


def create_test_seller():
    """Create a test seller user"""
    seller, created = User.objects.get_or_create(
        username='test_seller',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Seller',
            'user_type': 'seller',
            'phone_number': '+1234567890',
            'is_verified': True
        }
    )
    if created:
        seller.set_password('testpass123')
        seller.save()
        print(f"Created test seller: {seller.username}")
    return seller


def create_sample_auctions():
    """Create sample auctions"""
    seller = create_test_seller()
    salmon_category = FishCategory.objects.get(name='Salmon')
    
    # Create a test image
    test_image = create_test_image()
    
    auction_data = {
        'seller': seller,
        'title': 'Fresh Atlantic Salmon - Premium Quality',
        'description': 'High-quality Atlantic salmon, freshly caught this morning. Perfect for restaurants and fish markets.',
        'fish_category': salmon_category,
        'fish_type': 'Atlantic Salmon',
        'weight': Decimal('25.5'),
        'quantity': 10,
        'catch_date': datetime.now().date(),
        'catch_location': 'North Atlantic Ocean',
        'auction_type': 'live',
        'starting_price': Decimal('15.00'),
        'reserve_price': Decimal('20.00'),
        'buy_now_price': Decimal('30.00'),
        'bid_increment': Decimal('1.00'),
        'start_time': datetime.now() + timedelta(minutes=5),
        'end_time': datetime.now() + timedelta(hours=2),
        'main_image': test_image,
        'status': 'scheduled'
    }
    
    auction, created = Auction.objects.get_or_create(
        title=auction_data['title'],
        defaults=auction_data
    )
    if created:
        print(f"Created sample auction: {auction.title}")


def main():
    """Main function to create all test data"""
    print("Creating test data for Fish Auction Platform...")
    
    try:
        create_fish_categories()
        create_service_categories()
        create_sample_auctions()
        print("\n✅ Test data created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating test data: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
