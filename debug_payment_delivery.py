#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from auctions.models import Auction, FishCategory
from payments.models import Payment
from payments.services import PaymentService
from delivery.models import Delivery
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from datetime import timedelta

User = get_user_model()

def debug_payment_delivery():
    print("🔍 Debugging Payment and Delivery Creation")
    print("=" * 50)
    
    try:
        # Get test users
        buyer = User.objects.get(username='delivery_test_buyer')
        seller = User.objects.get(username='delivery_test_seller')
        print(f"✅ Found buyer: {buyer.username} (Balance: ${buyer.wallet_balance})")
        print(f"✅ Found seller: {seller.username}")
        
        # Get test auction
        auction = Auction.objects.filter(title__contains="Test Tuna for Delivery").first()
        if not auction:
            print("❌ No test auction found")
            return
        
        print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
        print(f"   Winner: {auction.winner.username if auction.winner else 'None'}")
        print(f"   Payment received: {auction.payment_received}")
        
        # Check if payment already exists
        try:
            payment = Payment.objects.get(auction=auction)
            print(f"✅ Payment already exists: {payment.id}")
            print(f"   Status: {payment.status}")
            print(f"   Amount: ${payment.amount}")
        except Payment.DoesNotExist:
            print("📝 Creating new payment...")
            
            # Create payment using service
            payment_service = PaymentService()
            payment = payment_service.create_payment(auction, buyer)
            print(f"✅ Payment created: {payment.id}")
            print(f"   Amount: ${payment.amount}")
            print(f"   Buyer balance before: ${buyer.wallet_balance}")
            
            # Process payment
            try:
                processed_payment = payment_service.process_wallet_payment(payment)
                print(f"✅ Payment processed successfully")
                print(f"   Payment status: {processed_payment.status}")
                
                # Refresh buyer balance
                buyer.refresh_from_db()
                print(f"   Buyer balance after: ${buyer.wallet_balance}")
                
                # Check auction status
                auction.refresh_from_db()
                print(f"   Auction payment received: {auction.payment_received}")
                
                # Check if delivery was created
                try:
                    delivery = Delivery.objects.get(auction=auction)
                    print(f"✅ Delivery created: {delivery.tracking_number}")
                    print(f"   Status: {delivery.status}")
                    print(f"   Pickup address: {delivery.pickup_address}")
                    print(f"   Delivery address: {delivery.delivery_address}")
                except Delivery.DoesNotExist:
                    print("❌ No delivery created")
                    
                    # Try to create delivery manually
                    print("📝 Attempting to create delivery manually...")
                    try:
                        delivery = Delivery.objects.create(
                            auction=auction,
                            payment=processed_payment,
                            seller=seller,
                            buyer=buyer,
                            pickup_address=f"Seller location: {auction.catch_location}",
                            delivery_address="Buyer address (to be updated)",
                            status='pending',
                            estimated_pickup_time=timezone.now() + timedelta(hours=2),
                            estimated_delivery_time=timezone.now() + timedelta(days=1),
                            delivery_cost=0,
                            special_instructions=f"Delivery for auction: {auction.title}"
                        )
                        print(f"✅ Manual delivery created: {delivery.tracking_number}")
                    except Exception as e:
                        print(f"❌ Manual delivery creation failed: {e}")
                        import traceback
                        traceback.print_exc()
                
            except Exception as e:
                print(f"❌ Payment processing failed: {e}")
                import traceback
                traceback.print_exc()
        
        # List all deliveries
        print(f"\n📦 All deliveries in system:")
        deliveries = Delivery.objects.all()
        for delivery in deliveries:
            print(f"   - {delivery.tracking_number}: {delivery.auction.title} ({delivery.status})")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_payment_delivery()
