#!/usr/bin/env python3
"""
Create a test live auction for testing auto-bids
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, FishCategory
from accounts.models import User
from decimal import Decimal
from django.utils import timezone

def main():
    print("🎯 Creating test live auction...")
    
    # Get a seller
    seller = User.objects.filter(user_type='seller').first()
    if not seller:
        print("❌ No seller found!")
        return
    
    # Get or create a fish category
    category, created = FishCategory.objects.get_or_create(
        name="Test Fish",
        defaults={'description': 'Test fish category'}
    )
    
    # Create a live auction
    auction = Auction.objects.create(
        title="Live Test Fish Auction",
        description="A test auction for auto-bid testing",
        fish_category=category,
        seller=seller,
        starting_price=Decimal('10.00'),
        current_price=Decimal('10.00'),
        reserve_price=Decimal('15.00'),
        target_price=Decimal('50.00'),
        bid_increment=Decimal('1.00'),
        start_time=timezone.now() - timedelta(minutes=5),  # Started 5 minutes ago
        end_time=timezone.now() + timedelta(hours=2),      # Ends in 2 hours
        status='live',
        fish_type="Test Fish",
        weight=Decimal('2.5'),
        catch_date=timezone.now().date(),
        catch_location="Test Location",
        auction_type='standard'
    )
    
    print(f"✅ Created live auction: {auction.title}")
    print(f"   - ID: {auction.id}")
    print(f"   - Current Price: ${auction.current_price}")
    print(f"   - Status: {auction.status}")
    print(f"   - End Time: {auction.end_time}")
    
    # Show available buyers for testing
    buyers = User.objects.filter(user_type='buyer')[:5]
    print(f"\n👥 Available buyers for testing:")
    for buyer in buyers:
        print(f"   - {buyer.username}")
    
    print(f"\n🔗 You can now test auto-bids on auction ID: {auction.id}")
    print("   Use the Flutter app or API to set auto-bids and place manual bids!")

if __name__ == "__main__":
    main()
