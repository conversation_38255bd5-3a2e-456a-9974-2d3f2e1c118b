#!/usr/bin/env python3
"""
Test script to verify payment transaction logic is fixed
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from accounts.models import User
from auctions.models import Auction
from payments.models import WalletTransaction, Payment
from payments.services import PaymentService
from decimal import Decimal

def test_payment_transactions():
    """Test that payment transactions show correct amounts"""
    
    print("🧪 Testing Payment Transaction Logic")
    print("=" * 50)
    
    try:
        # Get test users
        buyer = User.objects.get(username='rabie4')
        seller = User.objects.get(username='azmi')
        auction = Auction.objects.get(title='second auction')
        
        print(f"👤 Buyer: {buyer.username}")
        print(f"👤 Seller: {seller.username}")
        print(f"🏷️ Auction: {auction.title}")
        print(f"💰 Auction Price: ${auction.current_price}")
        print()
        
        # Record initial balances
        initial_buyer_balance = buyer.wallet_balance
        initial_seller_balance = seller.wallet_balance
        
        print(f"💳 Initial Balances:")
        print(f"   Buyer: ${initial_buyer_balance}")
        print(f"   Seller: ${initial_seller_balance}")
        print()
        
        # Clear old transactions for clean test
        print("🧹 Clearing old transactions...")
        WalletTransaction.objects.filter(user__in=[buyer, seller], auction=auction).delete()
        Payment.objects.filter(auction=auction).delete()
        
        # Ensure buyer has enough balance
        if buyer.wallet_balance < auction.current_price:
            print(f"💰 Adding funds to buyer wallet...")
            payment_service = PaymentService()
            payment_service.add_wallet_funds(
                buyer, 
                float(auction.current_price) + 100, 
                "Test funds for payment testing"
            )
            buyer.refresh_from_db()
            print(f"   New buyer balance: ${buyer.wallet_balance}")
        
        # Create and process payment
        print("💳 Creating payment...")
        payment_service = PaymentService()
        payment = payment_service.create_payment(auction, buyer)
        
        print(f"📄 Payment created:")
        print(f"   Amount: ${payment.amount}")
        print(f"   Platform Fee: ${payment.platform_fee}")
        print(f"   Seller Amount: ${payment.seller_amount}")
        print()
        
        # Process the payment
        print("⚡ Processing payment...")
        processed_payment = payment_service.process_wallet_payment(payment)
        
        # Refresh user balances
        buyer.refresh_from_db()
        seller.refresh_from_db()
        
        print("✅ Payment processed!")
        print()
        
        # Show final balances
        print(f"💳 Final Balances:")
        print(f"   Buyer: ${buyer.wallet_balance} (change: ${buyer.wallet_balance - initial_buyer_balance})")
        print(f"   Seller: ${seller.wallet_balance} (change: ${seller.wallet_balance - initial_seller_balance})")
        print()
        
        # Show transactions
        print("📊 Transaction History:")
        buyer_transactions = WalletTransaction.objects.filter(user=buyer, auction=auction).order_by('-created_at')
        seller_transactions = WalletTransaction.objects.filter(user=seller, auction=auction).order_by('-created_at')
        
        print(f"   Buyer Transactions:")
        for tx in buyer_transactions:
            print(f"     {tx.transaction_type}: ${tx.amount} - {tx.description}")
        
        print(f"   Seller Transactions:")
        for tx in seller_transactions:
            print(f"     {tx.transaction_type}: ${tx.amount} - {tx.description}")
        
        print()
        
        # Verify logic
        expected_buyer_change = -auction.current_price
        expected_seller_change = payment.seller_amount
        actual_buyer_change = buyer.wallet_balance - initial_buyer_balance
        actual_seller_change = seller.wallet_balance - initial_seller_balance
        
        print("🔍 Verification:")
        print(f"   Expected buyer change: ${expected_buyer_change}")
        print(f"   Actual buyer change: ${actual_buyer_change}")
        print(f"   ✅ Buyer OK" if abs(actual_buyer_change - expected_buyer_change) < 0.01 else "❌ Buyer WRONG")
        print()
        print(f"   Expected seller change: ${expected_seller_change}")
        print(f"   Actual seller change: ${actual_seller_change}")
        print(f"   ✅ Seller OK" if abs(actual_seller_change - expected_seller_change) < 0.01 else "❌ Seller WRONG")
        
        if (abs(actual_buyer_change - expected_buyer_change) < 0.01 and 
            abs(actual_seller_change - expected_seller_change) < 0.01):
            print("\n🎉 Payment logic is FIXED! ✅")
        else:
            print("\n❌ Payment logic still has issues!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_payment_transactions()
