#!/bin/bash

# Script to restart Django server while keeping other services running
# This is useful when you need to update Django settings without restarting everything

echo "🔄 Restarting Django server..."

# Find and kill Django process
DJANGO_PID=$(ps aux | grep "python.*manage.py runserver\|python3.*manage.py runserver" | grep -v grep | awk '{print $2}')

if [ ! -z "$DJANGO_PID" ]; then
    echo "🛑 Stopping Django server (PID: $DJANGO_PID)..."
    kill $DJANGO_PID
    sleep 2
else
    echo "ℹ️  Django server not found running"
fi

# Start Django server
echo "🚀 Starting Django server..."
python3 manage.py runserver 0.0.0.0:8000 > django_server.log 2>&1 &
DJANGO_NEW_PID=$!

echo "✅ Django server started (PID: $DJANGO_NEW_PID)"
echo "📋 Django logs: tail -f django_server.log"

# Wait a moment and check if it's running
sleep 3
if ps -p $DJAN<PERSON><PERSON>_NEW_PID > /dev/null; then
    echo "✅ Django server is running successfully"
    echo "🌐 Local URL: http://localhost:8000"
    echo "🔗 Ngrok URL: https://9b5a-197-14-121-2.ngrok-free.app"
else
    echo "❌ Django server failed to start"
    echo "📋 Check logs: cat django_server.log"
    exit 1
fi
