#!/usr/bin/env python3
"""
Test the complete image upload flow for broker reports
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceExecution
from accounts.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
import json
from io import BytesIO
from PIL import Image


def create_test_image():
    """Create a simple test image"""
    # Create a simple 100x100 red image
    img = Image.new('RGB', (100, 100), color='red')
    img_io = BytesIO()
    img.save(img_io, format='PNG')
    img_io.seek(0)
    return img_io.getvalue()


def test_image_upload_flow():
    """Test the complete image upload flow"""
    print("🧪 Testing Image Upload Flow")
    print("=" * 50)
    
    # Find a service execution to test with
    execution = ServiceExecution.objects.filter(status='completed').first()
    if not execution:
        print("❌ No completed service execution found")
        return
    
    print(f"📋 Service Execution: {execution.id}")
    print(f"   Status: {execution.status}")
    print(f"   Broker: {execution.broker.username}")
    print(f"   Current Report Text: '{execution.report_text}'")
    print(f"   Current Report Images: {execution.report_images}")
    
    # Create API client and authenticate as broker
    api_client = APIClient()
    refresh = RefreshToken.for_user(execution.broker)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as broker: {execution.broker.username}")
    
    # Step 1: Upload image
    print(f"\n📤 Step 1: Upload Image")
    image_data = create_test_image()
    image_file = SimpleUploadedFile("test_report.png", image_data, content_type="image/png")
    
    upload_url = f'/api/broker/executions/{execution.id}/upload-image/'
    print(f"   POST {upload_url}")
    
    upload_response = api_client.post(upload_url, {'report_image': image_file}, format='multipart')
    
    print(f"   Response Status: {upload_response.status_code}")
    
    if upload_response.status_code == 201:
        print("   ✅ Image upload successful!")
        upload_data = upload_response.data
        print(f"   Image URL: {upload_data.get('image_url')}")
        image_url = upload_data.get('image_url')
        
        # Step 2: Update execution with report text and image URL
        print(f"\n📤 Step 2: Update Execution with Report")
        update_url = f'/api/broker/executions/{execution.id}/update/'
        update_data = {
            'status': 'completed',
            'report_text': 'Test report with image upload',
            'report_images': [image_url]  # Add the image URL to the array
        }
        
        print(f"   PATCH {update_url}")
        print(f"   Data: {update_data}")
        
        update_response = api_client.patch(update_url, data=update_data, content_type='application/json')
        
        print(f"   Response Status: {update_response.status_code}")
        
        if update_response.status_code == 200:
            print("   ✅ Execution update successful!")
            
            # Check the response data
            response_data = update_response.data
            print(f"   Response Report Text: '{response_data.get('report_text')}'")
            print(f"   Response Report Images: {response_data.get('report_images')}")
            
            # Check database
            execution.refresh_from_db()
            print(f"\n📋 Database After Update:")
            print(f"   Report Text: '{execution.report_text}'")
            print(f"   Report Images: {execution.report_images}")
            
            if execution.report_images and len(execution.report_images) > 0:
                print("   ✅ Image URL saved to database!")
                
                # Check if file exists
                image_path = execution.report_images[0].replace('/media/', '')
                full_path = os.path.join('media', image_path)
                if os.path.exists(full_path):
                    print(f"   ✅ Image file exists: {full_path}")
                else:
                    print(f"   ❌ Image file not found: {full_path}")
            else:
                print("   ❌ No image URL in database!")
        else:
            print("   ❌ Execution update failed!")
            print(f"   Error: {update_response.data}")
    else:
        print("   ❌ Image upload failed!")
        print(f"   Error: {upload_response.data}")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_image_upload_flow()
