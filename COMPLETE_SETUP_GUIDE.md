# 🐟 Fish Auction App - Complete Setup Guide

A comprehensive Flutter application for fish auctions with real-time bidding, payment processing, and delivery tracking. This guide covers the complete setup process including the Django backend with WebSocket support.

## 🎯 Overview

The Fish Auction platform consists of:
- **Flutter Mobile/Web App** - User interface for buyers and sellers
- **Django Backend** - REST API with WebSocket support for real-time features
- **Redis** - Message broker for WebSocket connections and caching
- **SQLite/PostgreSQL** - Database for storing auction data
- **Celery** - Background task processing for automated auctions

## 🚀 Quick Start

### Prerequisites

#### Backend Requirements
- Python 3.8+ 
- Redis Server
- Git

#### Frontend Requirements  
- Flutter SDK 3.0+
- Dart SDK 3.0+
- Android Studio / VS Code
- Chrome browser (for web testing)

## 📋 Step-by-Step Setup

### 1. Backend Setup (Django + WebSockets)

#### Step 1: Clone and Setup Backend
```bash
# Navigate to the backend directory (root of the project)
cd /path/to/fish-auction-project

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### Step 2: Install and Start Redis (Required for WebSockets)
```bash
# On macOS (using Homebrew):
brew install redis
brew services start redis

# On Ubuntu/Debian:
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server

# On Windows (using Chocolatey):
choco install redis-64

# Or use Docker (cross-platform):
docker run -d -p 6379:6379 --name redis redis:alpine

# Test Redis connection:
redis-cli ping
# Should return: PONG
```

#### Step 3: Configure Environment Variables
Edit the `.env` file in the root directory:

```bash
# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-test-key-change-in-production-123456789
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database
DATABASE_URL=sqlite:///db.sqlite3

# Redis (Required for WebSockets)
REDIS_URL=redis://localhost:6379/0

# JWT Settings
JWT_SECRET_KEY=your-jwt-secret-key-change-this
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=1440

# Stripe Payment (Replace with your actual keys)
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_actual_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here

# Optional Services
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
```

#### Step 4: Setup Database
```bash
# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser

# Load test data (optional)
python create_test_data.py
```

#### Step 5: Start Backend Services

**Terminal 1 - Django Server with WebSocket Support:**
```bash
# Use ASGI server for WebSocket support (not regular runserver)
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application

# Alternative: Use uvicorn
# uvicorn fish_auction.asgi:application --host 0.0.0.0 --port 8000
```

**Terminal 2 - Celery Worker (for background tasks):**
```bash
celery -A fish_auction worker --loglevel=info
```

**Terminal 3 - Celery Beat (for scheduled tasks):**
```bash
celery -A fish_auction beat --loglevel=info
```

### 2. Frontend Setup (Flutter App)

#### Step 1: Navigate to Flutter App
```bash
cd fish_auction_app
```

#### Step 2: Install Dependencies
```bash
# Get Flutter dependencies
flutter pub get

# Verify Flutter installation
flutter doctor
```

#### Step 3: Configure API Endpoints
Edit `lib/constants/app_constants.dart`:
```dart
class AppConstants {
  // For local development
  static const String baseUrl = 'http://localhost:8000/api';
  static const String wsUrl = 'ws://localhost:8000/ws';
  
  // For mobile testing, use your computer's IP address:
  // Find your IP: ipconfig (Windows) or ifconfig (Mac/Linux)
  // static const String baseUrl = 'http://*************:8000/api';
  // static const String wsUrl = 'ws://*************:8000/ws';
}
```

#### Step 4: Run Flutter App
```bash
# For web development (recommended for testing):
flutter run -d web-server --web-port 8080

# For mobile emulator:
flutter run

# For specific device:
flutter devices  # List available devices
flutter run -d <device-id>
```

## 🌐 WebSocket Real-time Features

### Backend WebSocket Endpoints:
- `ws://localhost:8000/ws/auction/<auction_id>/` - Real-time auction bidding
- `ws://localhost:8000/ws/notifications/<user_id>/` - User notifications
- `ws://localhost:8000/ws/delivery/<delivery_id>/` - Delivery tracking

### Supported Real-time Events:
- ✅ **Live Bidding** - Real-time bid updates visible to all users
- ✅ **Auction Status Changes** - Start/end notifications  
- ✅ **Auto-bid Triggers** - Automated bidding responses
- ✅ **User Notifications** - Instant alerts and messages
- ✅ **Delivery Tracking** - GPS location updates
- ✅ **Bidding History** - Live chat-style bid updates

### Testing WebSocket Connection:
```bash
# Test WebSocket connection using wscat (install: npm install -g wscat)
wscat -c ws://localhost:8000/ws/auction/1/

# Or use browser console:
const ws = new WebSocket('ws://localhost:8000/ws/auction/1/');
ws.onmessage = (event) => console.log(JSON.parse(event.data));
```

## 💳 Stripe Payment Setup (FREE)

### Step 1: Create Free Stripe Account
1. Go to https://dashboard.stripe.com/register
2. Sign up with your email (completely free for testing)
3. Verify your email address

### Step 2: Get Test API Keys
1. In Stripe Dashboard, go to **Developers** → **API Keys**
2. Copy the **Test** keys (not Live keys):
   - **Publishable key** (starts with `pk_test_`)
   - **Secret key** (starts with `sk_test_`)

### Step 3: Setup FREE Webhooks (Multiple Options)

#### Option 1: Using ngrok (Recommended - FREE)
ngrok provides free HTTPS tunneling to your local development server:

```bash
# 1. Install ngrok (completely free)
# Download from: https://ngrok.com/download
# Or using package managers:
# npm install -g ngrok
# brew install ngrok (macOS)
# choco install ngrok (Windows)

# 2. Start your Django server first
python manage.py runserver 8000

# 3. In another terminal, expose your local server
ngrok http 8000

# 4. You'll see output like:
# Forwarding  https://abc123.ngrok.io -> http://localhost:8000
# Copy the HTTPS URL (e.g., https://abc123.ngrok.io)

# 5. Your webhook URL will be:
# https://abc123.ngrok.io/api/payments/stripe/webhook/
```

**ngrok Free Plan Includes:**
- ✅ HTTPS tunneling
- ✅ 1 online ngrok process
- ✅ 4 tunnels/ngrok process
- ✅ 40 connections/minute
- ✅ Perfect for development and testing

#### Option 2: Using Stripe CLI (Alternative - FREE)
```bash
# 1. Install Stripe CLI: https://stripe.com/docs/stripe-cli
# 2. Login to your Stripe account
stripe login

# 3. Forward webhooks to your local server
stripe listen --forward-to localhost:8000/api/payments/stripe/webhook/

# 4. Stripe CLI will show you the webhook signing secret
# Copy it to your .env file
```

#### Option 3: Using localtunnel (FREE Alternative)
```bash
# 1. Install localtunnel
npm install -g localtunnel

# 2. Start your Django server
python manage.py runserver 8000

# 3. Create tunnel
lt --port 8000 --subdomain your-app-name

# 4. Your webhook URL:
# https://your-app-name.loca.lt/api/payments/stripe/webhook/
```

#### Option 4: Using serveo.net (FREE, No Installation)
```bash
# No installation required - just SSH
ssh -R 80:localhost:8000 serveo.net

# You'll get a URL like: https://random.serveo.net
# Webhook URL: https://random.serveo.net/api/payments/stripe/webhook/
```

### Step 4: Create Webhook Endpoint in Stripe Dashboard
1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Enter your webhook URL:
   - **ngrok**: `https://your-ngrok-url.ngrok.io/api/payments/stripe/webhook/`
   - **Stripe CLI**: Use the URL provided by Stripe CLI
4. Select events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `invoice.payment_succeeded`
   - `customer.subscription.updated`
5. Click **Add endpoint**
6. Copy the **Signing secret** (starts with `whsec_`)

### Step 5: Update Environment Variables
```bash
# Add to your .env file:
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_key_here
STRIPE_SECRET_KEY=sk_test_your_actual_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_signing_secret_here
```

### Step 6: Test Payment Flow
Use these test card numbers (completely free):
- **Success**: 4242 4242 4242 4242
- **Decline**: 4000 0000 0000 0002
- **Requires Authentication**: 4000 0025 0000 3155
- **Insufficient Funds**: 4000 0000 0000 9995
- Any future expiry date and any 3-digit CVC

### Step 7: Test Webhook Integration
```bash
# 1. Start your backend server
python manage.py runserver 8000

# 2. Start ngrok (or your chosen tunnel)
ngrok http 8000

# 3. In Stripe Dashboard, go to Webhooks
# 4. Click "Send test webhook"
# 5. Select "payment_intent.succeeded"
# 6. Check your Django server logs for webhook receipt

# 7. Test with real payment flow:
# - Make a test payment in your app
# - Check Stripe Dashboard → Events
# - Verify webhook was delivered successfully
```

## 🔧 Webhook Handler Implementation

Your Django backend already includes webhook handling. Here's how it works:

### Backend Webhook Endpoint
```python
# payments/views.py
@csrf_exempt
def stripe_webhook(request):
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    endpoint_secret = settings.STRIPE_WEBHOOK_SECRET

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError:
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError:
        return HttpResponse(status=400)

    # Handle the event
    if event['type'] == 'payment_intent.succeeded':
        payment_intent = event['data']['object']
        # Update payment status in database
        handle_successful_payment(payment_intent)

    return HttpResponse(status=200)
```

### Frontend Webhook Response
The Flutter app automatically receives updates through:
1. **WebSocket connections** for real-time updates
2. **API polling** for payment status changes
3. **Push notifications** for important events

## 🧪 Testing the Complete Setup

### 1. Backend API Testing
```bash
# Test basic API
curl http://localhost:8000/api/auctions/

# Test WebSocket (using wscat)
wscat -c ws://localhost:8000/ws/auction/1/
```

### 2. Frontend Testing
1. Open http://localhost:8080 in your browser
2. Register a new account
3. Create a test auction
4. Test real-time bidding
5. Test payment flow with test cards

### 3. Real-time Features Testing
1. Open the app in two browser tabs
2. Login with different accounts
3. Start bidding on the same auction
4. Verify real-time updates appear in both tabs

## 🔧 Troubleshooting

### Common Issues:

#### Redis Connection Error
```bash
# Check if Redis is running
redis-cli ping

# Start Redis if not running
# macOS: brew services start redis
# Linux: sudo systemctl start redis-server
# Windows: Start Redis service
```

#### WebSocket Connection Failed
```bash
# Check Django Channels installation
pip install channels channels-redis

# Verify ASGI configuration in fish_auction/asgi.py
# Ensure Redis is running and accessible
```

#### Stripe Webhook Not Working
```bash
# Check webhook URL is accessible
curl -X POST https://your-ngrok-url.ngrok.io/api/payments/stripe/webhook/

# Verify webhook secret in .env file
# Check Stripe dashboard for webhook delivery attempts
```

#### Flutter App Can't Connect to Backend
```bash
# For mobile testing, use your computer's IP address
# Find IP: ipconfig (Windows) or ifconfig (Mac/Linux)
# Update app_constants.dart with your IP address
```

## 🎯 Next Steps

1. **Customize the App**: Modify colors, logos, and branding
2. **Add More Features**: Implement additional auction types
3. **Deploy to Production**: Use proper hosting for backend and app stores for mobile
4. **Enable Live Payments**: Switch to Stripe live keys for real transactions
5. **Add Analytics**: Implement user behavior tracking

## 📱 App Features

### For Buyers:
- Browse live and scheduled auctions
- Place manual and automatic bids
- Real-time bidding updates
- Wallet management with Stripe
- Delivery tracking
- Auction watchlist
- Multi-language support (English/Arabic)

### For Sellers:
- Create and manage auctions
- Upload fish images and details
- Monitor real-time bidding
- Receive payments via Stripe
- Track delivery status
- Auction analytics

### Admin Features:
- User management and KYC verification
- Auction moderation
- Payment monitoring
- System analytics
- Support ticket management

## 🔒 Security Features

- JWT authentication
- Secure payment processing
- KYC document verification
- Real-time fraud detection
- Encrypted data transmission
- Secure WebSocket connections

## 🤖 Automated Setup Scripts

For easier setup, use the provided automation scripts:

### Option 1: One-Click Setup (Recommended)
```bash
# For macOS/Linux:
./start_with_webhooks.sh

# For Windows:
start_with_webhooks.bat
```

These scripts automatically:
- ✅ Check all requirements
- ✅ Start Redis server
- ✅ Start Django backend
- ✅ Start Celery workers
- ✅ Start ngrok tunnel
- ✅ Display webhook setup instructions
- ✅ Provide cleanup on exit

### Option 2: Python Webhook Setup
```bash
python setup_webhooks.py
```

This script provides:
- ✅ Automated requirement checking
- ✅ Server startup with health checks
- ✅ ngrok tunnel management
- ✅ Webhook endpoint testing
- ✅ Step-by-step Stripe setup instructions

## 📱 Flutter App Quick Start

Once your backend is running with webhooks:

```bash
# Navigate to Flutter app
cd fish_auction_app

# Install dependencies
flutter pub get

# Run on web (recommended for testing)
flutter run -d web-server --web-port 8080

# Or run on mobile
flutter run
```

## 🎯 Complete Development Workflow

1. **Start Backend Services**:
   ```bash
   ./start_with_webhooks.sh  # Starts everything with webhooks
   ```

2. **Configure Stripe Webhooks**:
   - Follow the displayed instructions
   - Add webhook secret to `.env` file

3. **Start Flutter App**:
   ```bash
   cd fish_auction_app
   flutter run -d web-server --web-port 8080
   ```

4. **Test Real-time Features**:
   - Open app in multiple browser tabs
   - Test live bidding between different users
   - Verify WebSocket connections work

5. **Test Payment Flow**:
   - Use Stripe test cards
   - Verify webhook delivery in Stripe Dashboard
   - Check payment status updates in real-time

## 🔗 Useful URLs During Development

- **Flutter App**: http://localhost:8080
- **Django Backend**: http://localhost:8000
- **Django Admin**: http://localhost:8000/admin
- **API Documentation**: http://localhost:8000/api/
- **ngrok Dashboard**: http://localhost:4040
- **Stripe Dashboard**: https://dashboard.stripe.com/

## 💡 Pro Tips

1. **Use ngrok's free plan** - Perfect for webhook development
2. **Keep webhook logs** - Monitor `django.log` for webhook events
3. **Test with multiple browsers** - Verify real-time features
4. **Use Stripe test mode** - Never use live keys in development
5. **Monitor WebSocket connections** - Check browser dev tools

**Your Fish Auction app is now ready for development and testing!** 🎉
