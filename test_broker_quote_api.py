#!/usr/bin/env python3
"""
Test the broker quote API endpoint directly to see if notifications are triggered
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import BrokerService, ServiceRequest, BrokerQuote
from auctions.models import Auction
from notifications.models import Notification
import json

User = get_user_model()


def test_broker_quote_api():
    """Test the broker quote creation API endpoint"""
    print("🧪 Testing Broker Quote API Endpoint")
    print("=" * 50)
    
    # Get test users
    client_user = User.objects.filter(user_type='buyer').first()
    broker_user = User.objects.filter(user_type='broker').first()
    
    if not client_user or not broker_user:
        print("❌ Need buyer and broker users")
        return
    
    print(f"👤 Client: {client_user.username}")
    print(f"🤝 Broker: {broker_user.username}")
    
    # Get existing service request
    service_request = ServiceRequest.objects.filter(client=client_user).first()
    
    if not service_request:
        print("❌ No service request found for client")
        return
    
    print(f"📋 Service Request: {service_request.id}")
    print(f"   Service: {service_request.service.name_ar}")
    print(f"   Status: {service_request.status}")
    
    # Create API client and authenticate as broker
    api_client = APIClient()
    
    # Create JWT token for broker
    refresh = RefreshToken.for_user(broker_user)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as broker: {broker_user.username}")
    
    # Count notifications before
    notifications_before = Notification.objects.count()
    broker_notifications_before = Notification.objects.filter(
        template__notification_type='broker_offer_received'
    ).count()
    
    print(f"📊 Notifications before: {notifications_before}")
    print(f"🤝 Broker offer notifications before: {broker_notifications_before}")
    
    # Prepare quote data
    quote_data = {
        'service_request': str(service_request.id),
        'amount': 75.50,
        'estimated_duration': '3 hours',
        'notes': 'I will provide detailed inspection with photos and report'
    }
    
    print(f"\n📤 Sending POST request to create quote...")
    print(f"   Data: {quote_data}")
    
    # Make API request
    url = '/api/broker/quotes/create/'
    response = api_client.post(url, data=quote_data, format='json')
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    if hasattr(response, 'data'):
        print(f"   Data: {response.data}")
    else:
        print(f"   Content: {response.content.decode()}")
    
    if response.status_code == 201:
        print("✅ Quote created successfully!")
        
        # Check notifications after
        notifications_after = Notification.objects.count()
        broker_notifications_after = Notification.objects.filter(
            template__notification_type='broker_offer_received'
        ).count()
        
        print(f"\n📊 Notifications after: {notifications_after}")
        print(f"🤝 Broker offer notifications after: {broker_notifications_after}")
        
        if broker_notifications_after > broker_notifications_before:
            print("✅ Broker offer notification was created!")
            
            # Show the notification
            latest_notification = Notification.objects.filter(
                template__notification_type='broker_offer_received'
            ).order_by('-created_at').first()
            
            if latest_notification:
                print(f"📨 Latest notification:")
                print(f"   Recipient: {latest_notification.recipient.username}")
                print(f"   Title: {latest_notification.title}")
                print(f"   Channel: {latest_notification.channel}")
                print(f"   Status: {latest_notification.status}")
                print(f"   Message: {latest_notification.message[:100]}...")
        else:
            print("❌ No broker offer notification was created!")
            
    else:
        print(f"❌ Quote creation failed!")
        print(f"   Error: {response.data}")
    
    # Check if quote was created in database
    quote = BrokerQuote.objects.filter(
        service_request=service_request,
        broker=broker_user
    ).order_by('-created_at').first()
    
    if quote:
        print(f"\n📋 Quote in database:")
        print(f"   ID: {quote.id}")
        print(f"   Amount: {quote.amount}")
        print(f"   Status: {quote.status}")
        print(f"   Created: {quote.created_at}")
    else:
        print(f"\n❌ No quote found in database")


if __name__ == '__main__':
    test_broker_quote_api()
