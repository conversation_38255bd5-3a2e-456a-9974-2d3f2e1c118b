@echo off
REM Fish Auction App - Quick Start Script with Webhook Support (Windows)
REM This script starts all necessary services for development

echo 🐟 Fish Auction App - Quick Start with Webhooks (Windows)
echo ================================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed
    echo Please install Python from https://python.org
    pause
    exit /b 1
)
echo ✅ Python is available

REM Check if Redis is running (assuming Redis is installed)
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Redis is not running
    echo Please start Redis server or install it from:
    echo https://github.com/microsoftarchive/redis/releases
    echo Or use Docker: docker run -d -p 6379:6379 redis:alpine
    pause
    exit /b 1
)
echo ✅ Redis is running

REM Check if ngrok is installed
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ❌ ngrok is not installed
    echo Please install ngrok from: https://ngrok.com/download
    echo Or use: npm install -g ngrok
    pause
    exit /b 1
)
echo ✅ ngrok is available

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment activated
)

REM Install Python dependencies
echo 📦 Installing Python dependencies...
pip install -r requirements.txt >nul 2>&1

REM Run Django migrations
echo 🗄️ Running database migrations...
python manage.py migrate >nul 2>&1
echo ✅ Database is ready

REM Kill any existing processes on port 8000
echo 🔄 Checking for existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    taskkill /f /pid %%a >nul 2>&1
)

REM Start Django server in background
echo 🚀 Starting Django server...
start /b python manage.py runserver 8000 > django.log 2>&1

REM Wait for Django server to start
timeout /t 3 /nobreak >nul

REM Check if Django server is running
netstat -an | findstr :8000 >nul
if errorlevel 1 (
    echo ❌ Failed to start Django server
    type django.log
    pause
    exit /b 1
)
echo ✅ Django server running on http://localhost:8000

REM Start Celery worker in background
echo 🔄 Starting Celery worker...
start /b celery -A fish_auction worker --loglevel=info > celery_worker.log 2>&1

REM Start Celery beat in background
echo ⏰ Starting Celery beat...
start /b celery -A fish_auction beat --loglevel=info > celery_beat.log 2>&1

REM Start ngrok tunnel
echo 🌐 Starting ngrok tunnel...
start /b ngrok http 8000 > ngrok.log 2>&1

REM Wait for ngrok to start
timeout /t 5 /nobreak >nul

REM Try to get ngrok public URL (simplified for Windows)
echo 🔍 Getting ngrok public URL...
curl -s http://localhost:4040/api/tunnels > ngrok_response.json 2>nul

REM Display setup instructions
echo.
echo 🎯 STRIPE WEBHOOK SETUP INSTRUCTIONS
echo ================================================
echo 📍 Your webhook URL will be: https://[random].ngrok.io/api/payments/stripe/webhook/
echo.
echo 📋 Steps to complete setup:
echo 1. Go to ngrok dashboard: http://localhost:4040
echo 2. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
echo 3. Go to Stripe Dashboard: https://dashboard.stripe.com/
echo 4. Navigate to: Developers → Webhooks
echo 5. Click 'Add endpoint'
echo 6. Enter endpoint URL: [your-ngrok-url]/api/payments/stripe/webhook/
echo 7. Select these events:
echo    - payment_intent.succeeded
echo    - payment_intent.payment_failed
echo    - invoice.payment_succeeded
echo 8. Click 'Add endpoint'
echo 9. Copy the 'Signing secret' (starts with whsec_)
echo 10. Add it to your .env file:
echo     STRIPE_WEBHOOK_SECRET=whsec_your_signing_secret_here
echo.
echo 🧪 Test your webhook:
echo 1. In Stripe Dashboard, go to your webhook
echo 2. Click 'Send test webhook'
echo 3. Select 'payment_intent.succeeded'
echo 4. Check Django logs: type django.log
echo.
echo 🎉 All services are running!
echo ================================================
echo 📱 Django Backend: http://localhost:8000
echo 🌐 ngrok Dashboard: http://localhost:4040
echo.
echo 📁 Log files:
echo   - Django: django.log
echo   - Celery Worker: celery_worker.log
echo   - Celery Beat: celery_beat.log
echo   - ngrok: ngrok.log
echo.
echo 🛑 Press any key to stop all services...
pause >nul

REM Cleanup
echo.
echo 🛑 Stopping all services...

REM Kill processes by name (Windows specific)
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im celery.exe >nul 2>&1
taskkill /f /im ngrok.exe >nul 2>&1

REM Kill processes on specific ports
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :4040') do (
    taskkill /f /pid %%a >nul 2>&1
)

REM Clean up temporary files
del ngrok_response.json >nul 2>&1

echo ✅ Cleanup complete
pause
