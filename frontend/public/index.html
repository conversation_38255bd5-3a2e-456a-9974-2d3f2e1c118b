<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Fish Auction & Services Platform"
    />
    <title>Fish Auction Platform</title>
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }
      
      .card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
      }
      
      .btn {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      
      .btn:hover {
        background-color: #0056b3;
      }
      
      .btn-success {
        background-color: #28a745;
      }
      
      .btn-success:hover {
        background-color: #1e7e34;
      }
      
      .btn-danger {
        background-color: #dc3545;
      }
      
      .btn-danger:hover {
        background-color: #c82333;
      }
      
      .form-group {
        margin-bottom: 15px;
      }
      
      .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      
      .navbar {
        background-color: #343a40;
        color: white;
        padding: 1rem;
        margin-bottom: 20px;
      }
      
      .navbar h1 {
        margin: 0;
        display: inline-block;
      }
      
      .navbar .user-info {
        float: right;
      }
      
      .auction-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
      }
      
      .auction-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
      }
      
      .auction-card img {
        width: 100%;
        height: 200px;
        object-fit: cover;
      }
      
      .auction-card-body {
        padding: 15px;
      }
      
      .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
      }
      
      .status-live {
        background-color: #28a745;
        color: white;
      }
      
      .status-ended {
        background-color: #6c757d;
        color: white;
      }
      
      .status-scheduled {
        background-color: #ffc107;
        color: black;
      }
      
      .loading {
        text-align: center;
        padding: 40px;
      }
      
      .error {
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 20px;
      }
      
      .success {
        color: #155724;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
