import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          });
          localStorage.setItem('access_token', response.data.access);
          // Retry original request
          error.config.headers.Authorization = `Bearer ${response.data.access}`;
          return api.request(error.config);
        } catch (refreshError) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login/', credentials),
  register: (userData) => api.post('/auth/register/', userData),
  getProfile: () => api.get('/auth/profile/'),
  updateProfile: (data) => api.put('/auth/profile/', data),
};

// Auctions API
export const auctionsAPI = {
  getAuctions: (params) => api.get('/auctions/', { params }),
  getAuction: (id) => api.get(`/auctions/${id}/`),
  createAuction: (data) => api.post('/auctions/create/', data),
  placeBid: (auctionId, amount) => api.post(`/auctions/${auctionId}/bid/`, { amount }),
  setupAutoBid: (auctionId, data) => api.post(`/auctions/${auctionId}/auto-bid/`, data),
  getCategories: () => api.get('/auctions/categories/'),
  getMyAuctions: () => api.get('/auctions/my-auctions/'),
  getMyBids: () => api.get('/auctions/my-bids/'),
  addToWatchlist: (auctionId) => api.post('/auctions/watchlist/', { auction_id: auctionId }),
  removeFromWatchlist: (auctionId) => api.delete(`/auctions/${auctionId}/watchlist/remove/`),
  getWatchlist: () => api.get('/auctions/watchlist/'),
};

// Payments API
export const paymentsAPI = {
  getWalletBalance: () => api.get('/payments/wallet/balance/'),
  getWalletTransactions: () => api.get('/payments/wallet/transactions/'),
  topUpWallet: (data) => api.post('/payments/wallet/topup/', data),
  processPayment: (auctionId, data) => api.post(`/payments/process/${auctionId}/`, data),
  getPayments: () => api.get('/payments/'),
};

export default api;
