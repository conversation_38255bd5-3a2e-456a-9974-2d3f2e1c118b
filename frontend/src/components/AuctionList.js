import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { auctionsAPI } from '../api';

const AuctionList = ({ user }) => {
  const [auctions, setAuctions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: '',
    fish_category: '',
    search: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchAuctions();
    fetchCategories();
  }, [filters, currentPage]);

  const fetchAuctions = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        ...filters
      };
      
      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key];
        }
      });
      
      const response = await auctionsAPI.getAuctions(params);
      setAuctions(response.data.results || []);
      setTotalPages(Math.ceil(response.data.count / 20));
    } catch (error) {
      console.error('Failed to fetch auctions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await auctionsAPI.getCategories();
      setCategories(response.data || []);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const handleFilterChange = (e) => {
    setFilters({
      ...filters,
      [e.target.name]: e.target.value
    });
    setCurrentPage(1);
  };

  const handleAddToWatchlist = async (auctionId) => {
    try {
      await auctionsAPI.addToWatchlist(auctionId);
      // Refresh auctions to update watchlist status
      fetchAuctions();
    } catch (error) {
      console.error('Failed to add to watchlist:', error);
    }
  };

  const handleRemoveFromWatchlist = async (auctionId) => {
    try {
      await auctionsAPI.removeFromWatchlist(auctionId);
      // Refresh auctions to update watchlist status
      fetchAuctions();
    } catch (error) {
      console.error('Failed to remove from watchlist:', error);
    }
  };

  const formatTimeRemaining = (endTime) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end - now;
    
    if (diff <= 0) return 'Ended';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    }
    
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="container">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>Fish Auctions</h2>
        {(user.user_type === 'seller' || user.user_type === 'admin') && (
          <Link to="/create-auction" className="btn btn-success">
            Create New Auction
          </Link>
        )}
      </div>
      
      {/* Filters */}
      <div className="card">
        <h3>Filters</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div className="form-group">
            <input
              type="text"
              name="search"
              placeholder="Search auctions..."
              value={filters.search}
              onChange={handleFilterChange}
              className="form-control"
            />
          </div>
          
          <div className="form-group">
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="form-control"
            >
              <option value="">All Statuses</option>
              <option value="live">Live</option>
              <option value="scheduled">Scheduled</option>
              <option value="ended">Ended</option>
            </select>
          </div>
          
          <div className="form-group">
            <select
              name="fish_category"
              value={filters.fish_category}
              onChange={handleFilterChange}
              className="form-control"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      {/* Auctions Grid */}
      {loading ? (
        <div className="loading">Loading auctions...</div>
      ) : (
        <>
          <div className="auction-grid">
            {auctions.map(auction => (
              <div key={auction.id} className="auction-card">
                {auction.main_image && (
                  <img 
                    src={auction.main_image} 
                    alt={auction.title}
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpc2ggSW1hZ2U8L3RleHQ+PC9zdmc+';
                    }}
                  />
                )}
                <div className="auction-card-body">
                  <h4>{auction.title}</h4>
                  <p><strong>Fish Type:</strong> {auction.fish_type}</p>
                  <p><strong>Weight:</strong> {auction.weight} kg</p>
                  <p><strong>Current Price:</strong> ${auction.current_price}</p>
                  <p><strong>Bids:</strong> {auction.total_bids}</p>
                  <p><strong>Status:</strong> 
                    <span className={`status-badge status-${auction.status}`}>
                      {auction.status}
                    </span>
                  </p>
                  {auction.status === 'live' && (
                    <p><strong>Time Remaining:</strong> {formatTimeRemaining(auction.end_time)}</p>
                  )}
                  
                  <div style={{ marginTop: '15px' }}>
                    <Link to={`/auctions/${auction.id}`} className="btn">
                      View Details
                    </Link>
                    
                    {(user.user_type === 'buyer' || user.user_type === 'admin') && auction.seller.id !== user.id && (
                      auction.is_watched ? (
                        <button 
                          onClick={() => handleRemoveFromWatchlist(auction.id)}
                          className="btn btn-danger"
                          style={{ marginLeft: '10px' }}
                        >
                          ❤️ Remove
                        </button>
                      ) : (
                        <button 
                          onClick={() => handleAddToWatchlist(auction.id)}
                          className="btn"
                          style={{ marginLeft: '10px' }}
                        >
                          🤍 Watch
                        </button>
                      )
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {auctions.length === 0 && (
            <div className="card" style={{ textAlign: 'center' }}>
              <h3>No auctions found</h3>
              <p>Try adjusting your filters or check back later.</p>
            </div>
          )}
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div style={{ textAlign: 'center', marginTop: '20px' }}>
              <button 
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="btn"
              >
                Previous
              </button>
              <span style={{ margin: '0 15px' }}>
                Page {currentPage} of {totalPages}
              </span>
              <button 
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="btn"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AuctionList;
