import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { auctionsAPI } from '../api';

const CreateAuction = ({ user }) => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState([]);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    fish_category: '',
    fish_type: '',
    weight: '',
    quantity: 1,
    catch_date: '',
    catch_location: '',
    auction_type: 'live',
    starting_price: '',
    reserve_price: '',
    buy_now_price: '',
    bid_increment: '5.00',
    start_time: '',
    end_time: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchCategories();
    
    // Set default dates
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfter = new Date(tomorrow);
    dayAfter.setDate(dayAfter.getDate() + 1);
    
    setFormData(prev => ({
      ...prev,
      catch_date: now.toISOString().split('T')[0],
      start_time: tomorrow.toISOString().slice(0, 16),
      end_time: dayAfter.toISOString().slice(0, 16)
    }));
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await auctionsAPI.getCategories();
      setCategories(response.data || []);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await auctionsAPI.createAuction(formData);
      navigate(`/auctions/${response.data.id}`);
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to create auction');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <h2>Create New Auction</h2>
      
      {error && <div className="error">{error}</div>}
      
      <div className="card">
        <form onSubmit={handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            {/* Left Column */}
            <div>
              <h3>Basic Information</h3>
              
              <div className="form-group">
                <label>Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="form-control"
                  required
                  placeholder="e.g., Fresh Bluefin Tuna - Premium Quality"
                />
              </div>
              
              <div className="form-group">
                <label>Description *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className="form-control"
                  rows="4"
                  required
                  placeholder="Describe the fish quality, condition, and any special features..."
                />
              </div>
              
              <div className="form-group">
                <label>Fish Category *</label>
                <select
                  name="fish_category"
                  value={formData.fish_category}
                  onChange={handleChange}
                  className="form-control"
                  required
                >
                  <option value="">Select Category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="form-group">
                <label>Fish Type *</label>
                <input
                  type="text"
                  name="fish_type"
                  value={formData.fish_type}
                  onChange={handleChange}
                  className="form-control"
                  required
                  placeholder="e.g., Bluefin Tuna, Atlantic Salmon"
                />
              </div>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div className="form-group">
                  <label>Weight (kg) *</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0.1"
                    name="weight"
                    value={formData.weight}
                    onChange={handleChange}
                    className="form-control"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Quantity *</label>
                  <input
                    type="number"
                    min="1"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    className="form-control"
                    required
                  />
                </div>
              </div>
              
              <div className="form-group">
                <label>Catch Date *</label>
                <input
                  type="date"
                  name="catch_date"
                  value={formData.catch_date}
                  onChange={handleChange}
                  className="form-control"
                  required
                />
              </div>
              
              <div className="form-group">
                <label>Catch Location *</label>
                <input
                  type="text"
                  name="catch_location"
                  value={formData.catch_location}
                  onChange={handleChange}
                  className="form-control"
                  required
                  placeholder="e.g., Pacific Ocean, North Atlantic"
                />
              </div>
            </div>
            
            {/* Right Column */}
            <div>
              <h3>Auction Settings</h3>
              
              <div className="form-group">
                <label>Auction Type *</label>
                <select
                  name="auction_type"
                  value={formData.auction_type}
                  onChange={handleChange}
                  className="form-control"
                  required
                >
                  <option value="live">Live Auction</option>
                  <option value="timed">Timed Auction</option>
                  <option value="buy_now">Buy Now</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>Starting Price ($) *</label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  name="starting_price"
                  value={formData.starting_price}
                  onChange={handleChange}
                  className="form-control"
                  required
                />
              </div>
              
              <div className="form-group">
                <label>Reserve Price ($)</label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  name="reserve_price"
                  value={formData.reserve_price}
                  onChange={handleChange}
                  className="form-control"
                  placeholder="Minimum price to sell (optional)"
                />
              </div>
              
              <div className="form-group">
                <label>Buy Now Price ($)</label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  name="buy_now_price"
                  value={formData.buy_now_price}
                  onChange={handleChange}
                  className="form-control"
                  placeholder="Instant purchase price (optional)"
                />
              </div>
              
              <div className="form-group">
                <label>Bid Increment ($) *</label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  name="bid_increment"
                  value={formData.bid_increment}
                  onChange={handleChange}
                  className="form-control"
                  required
                />
              </div>
              
              <div className="form-group">
                <label>Start Time *</label>
                <input
                  type="datetime-local"
                  name="start_time"
                  value={formData.start_time}
                  onChange={handleChange}
                  className="form-control"
                  required
                />
              </div>
              
              <div className="form-group">
                <label>End Time *</label>
                <input
                  type="datetime-local"
                  name="end_time"
                  value={formData.end_time}
                  onChange={handleChange}
                  className="form-control"
                  required
                />
              </div>
            </div>
          </div>
          
          <div style={{ marginTop: '30px', textAlign: 'center' }}>
            <button 
              type="button" 
              onClick={() => navigate('/auctions')}
              className="btn"
              style={{ marginRight: '15px' }}
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-success" disabled={loading}>
              {loading ? 'Creating Auction...' : 'Create Auction'}
            </button>
          </div>
        </form>
      </div>
      
      <div className="card">
        <h3>📝 Tips for Creating Successful Auctions</h3>
        <ul>
          <li><strong>High-quality photos:</strong> Upload clear, well-lit images of your fish</li>
          <li><strong>Detailed description:</strong> Include freshness, size, and any special qualities</li>
          <li><strong>Competitive pricing:</strong> Research similar auctions to set appropriate starting prices</li>
          <li><strong>Timing:</strong> Schedule auctions when your target buyers are most active</li>
          <li><strong>Reserve price:</strong> Set a reserve to ensure you don't sell below your minimum acceptable price</li>
        </ul>
      </div>
    </div>
  );
};

export default CreateAuction;
