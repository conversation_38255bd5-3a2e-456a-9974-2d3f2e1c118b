import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { auctionsAPI, paymentsAPI } from '../api';

const AuctionDetail = ({ user }) => {
  const { id } = useParams();
  const [auction, setAuction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [bidAmount, setBidAmount] = useState('');
  const [autoBidData, setAutoBidData] = useState({
    max_amount: '',
    increment: '5.00'
  });
  const [bidding, setBidding] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchAuction();
  }, [id]);

  const fetchAuction = async () => {
    try {
      const response = await auctionsAPI.getAuction(id);
      setAuction(response.data);
      setBidAmount((parseFloat(response.data.current_price) + parseFloat(response.data.bid_increment)).toFixed(2));
    } catch (error) {
      setError('Failed to load auction details');
    } finally {
      setLoading(false);
    }
  };

  const handlePlaceBid = async (e) => {
    e.preventDefault();
    setBidding(true);
    setError('');
    setMessage('');

    try {
      await auctionsAPI.placeBid(id, bidAmount);
      setMessage('Bid placed successfully!');
      fetchAuction(); // Refresh auction data
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to place bid');
    } finally {
      setBidding(false);
    }
  };

  const handleSetupAutoBid = async (e) => {
    e.preventDefault();
    setBidding(true);
    setError('');
    setMessage('');

    try {
      await auctionsAPI.setupAutoBid(id, autoBidData);
      setMessage('Auto-bid configured successfully!');
      fetchAuction(); // Refresh auction data
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to setup auto-bid');
    } finally {
      setBidding(false);
    }
  };

  const handleProcessPayment = async () => {
    try {
      await paymentsAPI.processPayment(id, { payment_method: 'wallet' });
      setMessage('Payment processed successfully!');
      fetchAuction();
    } catch (error) {
      setError(error.response?.data?.error || 'Payment failed');
    }
  };

  const formatTimeRemaining = (endTime) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end - now;
    
    if (diff <= 0) return 'Ended';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    }
    
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return <div className="loading">Loading auction details...</div>;
  }

  if (!auction) {
    return (
      <div className="container">
        <div className="error">Auction not found</div>
        <Link to="/auctions" className="btn">Back to Auctions</Link>
      </div>
    );
  }

  const canBid = user.user_type === 'buyer' && 
                 auction.status === 'live' && 
                 auction.seller.id !== user.id;

  const isWinner = auction.winner && auction.winner.id === user.id;
  const needsPayment = isWinner && auction.status === 'ended';

  return (
    <div className="container">
      <Link to="/auctions" className="btn" style={{ marginBottom: '20px' }}>
        ← Back to Auctions
      </Link>

      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}

      <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '30px' }}>
        {/* Main Content */}
        <div>
          <div className="card">
            <h1>{auction.title}</h1>
            
            {auction.main_image && (
              <img 
                src={auction.main_image} 
                alt={auction.title}
                style={{ width: '100%', maxHeight: '400px', objectFit: 'cover', borderRadius: '8px' }}
                onError={(e) => {
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpc2ggSW1hZ2U8L3RleHQ+PC9zdmc+';
                }}
              />
            )}
            
            <div style={{ marginTop: '20px' }}>
              <p><strong>Description:</strong> {auction.description}</p>
              <p><strong>Fish Type:</strong> {auction.fish_type}</p>
              <p><strong>Weight:</strong> {auction.weight} kg</p>
              <p><strong>Quantity:</strong> {auction.quantity}</p>
              <p><strong>Catch Date:</strong> {new Date(auction.catch_date).toLocaleDateString()}</p>
              <p><strong>Catch Location:</strong> {auction.catch_location}</p>
              <p><strong>Seller:</strong> {auction.seller.first_name} {auction.seller.last_name} (@{auction.seller.username})</p>
            </div>
          </div>

          {/* Bidding History */}
          <div className="card">
            <h3>Bidding History ({auction.total_bids} bids)</h3>
            {auction.bids && auction.bids.length > 0 ? (
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                {auction.bids.map((bid, index) => (
                  <div key={bid.id} style={{ 
                    padding: '10px', 
                    borderBottom: '1px solid #eee',
                    backgroundColor: index === 0 ? '#f8f9fa' : 'white'
                  }}>
                    <strong>${bid.amount}</strong> by {bid.bidder.username}
                    <span style={{ float: 'right', color: '#666' }}>
                      {new Date(bid.timestamp).toLocaleString()}
                    </span>
                    {bid.bid_type === 'auto' && (
                      <span style={{ marginLeft: '10px', fontSize: '12px', color: '#007bff' }}>
                        (Auto-bid)
                      </span>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p>No bids yet. Be the first to bid!</p>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div>
          {/* Auction Status */}
          <div className="card">
            <h3>Auction Status</h3>
            <p><strong>Status:</strong> 
              <span className={`status-badge status-${auction.status}`}>
                {auction.status}
              </span>
            </p>
            <p><strong>Current Price:</strong> <span style={{ fontSize: '24px', color: '#28a745' }}>${auction.current_price}</span></p>
            <p><strong>Starting Price:</strong> ${auction.starting_price}</p>
            {auction.reserve_price && (
              <p><strong>Reserve Price:</strong> ${auction.reserve_price}</p>
            )}
            {auction.buy_now_price && (
              <p><strong>Buy Now Price:</strong> ${auction.buy_now_price}</p>
            )}
            <p><strong>Bid Increment:</strong> ${auction.bid_increment}</p>
            
            {auction.status === 'live' && (
              <p><strong>Time Remaining:</strong> {formatTimeRemaining(auction.end_time)}</p>
            )}
            
            {auction.status === 'ended' && auction.winner && (
              <p><strong>Winner:</strong> {auction.winner.username}</p>
            )}
          </div>

          {/* Payment Section */}
          {needsPayment && (
            <div className="card">
              <h3>🎉 Congratulations!</h3>
              <p>You won this auction! Please complete payment within the deadline.</p>
              <p><strong>Payment Deadline:</strong> {new Date(auction.payment_deadline).toLocaleString()}</p>
              <button onClick={handleProcessPayment} className="btn btn-success">
                Pay with Wallet (${auction.current_price})
              </button>
            </div>
          )}

          {/* Bidding Section */}
          {canBid && (
            <>
              <div className="card">
                <h3>Place Bid</h3>
                <form onSubmit={handlePlaceBid}>
                  <div className="form-group">
                    <label>Bid Amount ($)</label>
                    <input
                      type="number"
                      step="0.01"
                      min={parseFloat(auction.current_price) + parseFloat(auction.bid_increment)}
                      value={bidAmount}
                      onChange={(e) => setBidAmount(e.target.value)}
                      className="form-control"
                      required
                    />
                    <small>Minimum bid: ${(parseFloat(auction.current_price) + parseFloat(auction.bid_increment)).toFixed(2)}</small>
                  </div>
                  <button type="submit" className="btn btn-success" disabled={bidding}>
                    {bidding ? 'Placing Bid...' : 'Place Bid'}
                  </button>
                </form>
              </div>

              <div className="card">
                <h3>Auto-Bid Setup</h3>
                <form onSubmit={handleSetupAutoBid}>
                  <div className="form-group">
                    <label>Maximum Amount ($)</label>
                    <input
                      type="number"
                      step="0.01"
                      min={parseFloat(auction.current_price) + parseFloat(auction.bid_increment)}
                      value={autoBidData.max_amount}
                      onChange={(e) => setAutoBidData({...autoBidData, max_amount: e.target.value})}
                      className="form-control"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Bid Increment ($)</label>
                    <input
                      type="number"
                      step="0.01"
                      min="1.00"
                      value={autoBidData.increment}
                      onChange={(e) => setAutoBidData({...autoBidData, increment: e.target.value})}
                      className="form-control"
                      required
                    />
                  </div>
                  <button type="submit" className="btn" disabled={bidding}>
                    {bidding ? 'Setting up...' : 'Setup Auto-Bid'}
                  </button>
                </form>
                
                {auction.user_auto_bid && (
                  <div style={{ marginTop: '15px', padding: '10px', backgroundColor: '#e7f3ff', borderRadius: '4px' }}>
                    <strong>Active Auto-Bid:</strong><br />
                    Max: ${auction.user_auto_bid.max_amount}<br />
                    Increment: ${auction.user_auto_bid.increment}
                  </div>
                )}
              </div>
            </>
          )}

          {/* Seller Actions */}
          {auction.seller.id === user.id && (
            <div className="card">
              <h3>Seller Actions</h3>
              <p>This is your auction.</p>
              {auction.status === 'ended' && auction.winner && (
                <p><strong>Winner:</strong> {auction.winner.username}</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuctionDetail;
