import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { auctionsAPI, paymentsAPI } from '../api';

const Dashboard = ({ user }) => {
  const [stats, setStats] = useState({
    walletBalance: 0,
    activeAuctions: 0,
    myAuctions: 0,
    myBids: 0,
    watchlistCount: 0
  });
  const [recentAuctions, setRecentAuctions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch wallet balance
        const walletResponse = await paymentsAPI.getWalletBalance();
        
        // Fetch recent auctions
        const auctionsResponse = await auctionsAPI.getAuctions({ page_size: 6 });
        
        // Fetch user-specific data
        let myAuctions = 0;
        let myBids = 0;
        let watchlistCount = 0;
        
        if (user.user_type === 'seller' || user.user_type === 'admin') {
          const myAuctionsResponse = await auctionsAPI.getMyAuctions();
          myAuctions = myAuctionsResponse.data.count || 0;
        }
        
        if (user.user_type === 'buyer' || user.user_type === 'admin') {
          const myBidsResponse = await auctionsAPI.getMyBids();
          const watchlistResponse = await auctionsAPI.getWatchlist();
          myBids = myBidsResponse.data.count || 0;
          watchlistCount = watchlistResponse.data.count || 0;
        }
        
        setStats({
          walletBalance: parseFloat(walletResponse.data.balance),
          activeAuctions: auctionsResponse.data.count || 0,
          myAuctions,
          myBids,
          watchlistCount
        });
        
        setRecentAuctions(auctionsResponse.data.results || []);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  if (loading) {
    return <div className="loading">Loading dashboard...</div>;
  }

  return (
    <div className="container">
      <h2>Welcome to Fish Auction Platform, {user.first_name || user.username}!</h2>
      
      {/* Stats Cards */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px', marginBottom: '30px' }}>
        <div className="card" style={{ textAlign: 'center' }}>
          <h3>${stats.walletBalance.toFixed(2)}</h3>
          <p>Wallet Balance</p>
          <Link to="/wallet" className="btn">Manage Wallet</Link>
        </div>
        
        <div className="card" style={{ textAlign: 'center' }}>
          <h3>{stats.activeAuctions}</h3>
          <p>Active Auctions</p>
          <Link to="/auctions" className="btn">View All</Link>
        </div>
        
        {(user.user_type === 'seller' || user.user_type === 'admin') && (
          <div className="card" style={{ textAlign: 'center' }}>
            <h3>{stats.myAuctions}</h3>
            <p>My Auctions</p>
            <Link to="/create-auction" className="btn btn-success">Create New</Link>
          </div>
        )}
        
        {(user.user_type === 'buyer' || user.user_type === 'admin') && (
          <>
            <div className="card" style={{ textAlign: 'center' }}>
              <h3>{stats.myBids}</h3>
              <p>My Bids</p>
              <Link to="/auctions" className="btn">View Bids</Link>
            </div>
            
            <div className="card" style={{ textAlign: 'center' }}>
              <h3>{stats.watchlistCount}</h3>
              <p>Watchlist</p>
              <Link to="/auctions" className="btn">View Watchlist</Link>
            </div>
          </>
        )}
      </div>
      
      {/* Recent Auctions */}
      <div className="card">
        <h3>Recent Auctions</h3>
        {recentAuctions.length > 0 ? (
          <div className="auction-grid">
            {recentAuctions.map(auction => (
              <div key={auction.id} className="auction-card">
                {auction.main_image && (
                  <img 
                    src={auction.main_image} 
                    alt={auction.title}
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpc2ggSW1hZ2U8L3RleHQ+PC9zdmc+';
                    }}
                  />
                )}
                <div className="auction-card-body">
                  <h4>{auction.title}</h4>
                  <p><strong>Fish Type:</strong> {auction.fish_type}</p>
                  <p><strong>Weight:</strong> {auction.weight} kg</p>
                  <p><strong>Current Price:</strong> ${auction.current_price}</p>
                  <p><strong>Status:</strong> 
                    <span className={`status-badge status-${auction.status}`}>
                      {auction.status}
                    </span>
                  </p>
                  <Link to={`/auctions/${auction.id}`} className="btn">
                    View Details
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p>No auctions available at the moment.</p>
        )}
        
        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <Link to="/auctions" className="btn">View All Auctions</Link>
        </div>
      </div>
      
      {/* Quick Actions */}
      <div className="card">
        <h3>Quick Actions</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <Link to="/auctions" className="btn">Browse Auctions</Link>
          <Link to="/wallet" className="btn">Manage Wallet</Link>
          <Link to="/profile" className="btn">Edit Profile</Link>
          {(user.user_type === 'seller' || user.user_type === 'admin') && (
            <Link to="/create-auction" className="btn btn-success">Create Auction</Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
