import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { authAPI } from '../api';

const Login = ({ onLogin }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await authAPI.login(formData);
      onLogin(response.data.user, response.data.tokens);
    } catch (error) {
      setError(error.response?.data?.detail || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const quickLogin = async (username) => {
    setLoading(true);
    setError('');
    
    try {
      const response = await authAPI.login({
        username: username,
        password: 'testpass123'
      });
      onLogin(response.data.user, response.data.tokens);
    } catch (error) {
      setError('Quick login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="card" style={{ maxWidth: '400px', margin: '50px auto' }}>
        <h2>🐟 Fish Auction Platform</h2>
        <h3>Login</h3>
        
        {error && <div className="error">{error}</div>}
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <input
              type="text"
              name="username"
              placeholder="Username"
              value={formData.username}
              onChange={handleChange}
              className="form-control"
              required
            />
          </div>
          
          <div className="form-group">
            <input
              type="password"
              name="password"
              placeholder="Password"
              value={formData.password}
              onChange={handleChange}
              className="form-control"
              required
            />
          </div>
          
          <button type="submit" className="btn" disabled={loading}>
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
        
        <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
          <h4>Quick Login (Test Users):</h4>
          <button 
            onClick={() => quickLogin('buyer1')} 
            className="btn"
            disabled={loading}
          >
            Login as Buyer1 ($500)
          </button>
          <button 
            onClick={() => quickLogin('buyer2')} 
            className="btn"
            disabled={loading}
          >
            Login as Buyer2 ($750)
          </button>
          <button 
            onClick={() => quickLogin('seller1')} 
            className="btn btn-success"
            disabled={loading}
          >
            Login as Seller1 ($1000)
          </button>
          <button 
            onClick={() => quickLogin('seller2')} 
            className="btn btn-success"
            disabled={loading}
          >
            Login as Seller2 ($1500)
          </button>
          <button 
            onClick={() => quickLogin('broker1')} 
            className="btn"
            style={{ backgroundColor: '#6f42c1', color: 'white' }}
            disabled={loading}
          >
            Login as Broker1 ($2000)
          </button>
        </div>
        
        <p style={{ marginTop: '20px', textAlign: 'center' }}>
          Don't have an account? <Link to="/register">Register here</Link>
        </p>
      </div>
    </div>
  );
};

export default Login;
