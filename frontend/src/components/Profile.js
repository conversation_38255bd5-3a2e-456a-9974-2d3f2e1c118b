import React, { useState } from 'react';
import { authAPI } from '../api';

const Profile = ({ user, setUser }) => {
  const [formData, setFormData] = useState({
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    email: user.email || '',
    phone_number: user.phone_number || '',
    address: user.address || '',
    city: user.city || '',
    state: user.state || '',
    country: user.country || '',
    postal_code: user.postal_code || '',
    enable_notifications: user.enable_notifications,
    enable_whatsapp_notifications: user.enable_whatsapp_notifications,
    enable_email_notifications: user.enable_email_notifications,
    preferred_language: user.preferred_language || 'en'
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setFormData({
      ...formData,
      [e.target.name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await authAPI.updateProfile(formData);
      setUser(response.data);
      setMessage('Profile updated successfully!');
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const getUserTypeColor = (userType) => {
    switch (userType) {
      case 'seller': return '#28a745';
      case 'buyer': return '#007bff';
      case 'broker': return '#6f42c1';
      case 'service_provider': return '#fd7e14';
      case 'admin': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getUserTypeIcon = (userType) => {
    switch (userType) {
      case 'seller': return '🐟';
      case 'buyer': return '🛒';
      case 'broker': return '🤝';
      case 'service_provider': return '🔧';
      case 'admin': return '👑';
      default: return '👤';
    }
  };

  return (
    <div className="container">
      <h2>👤 My Profile</h2>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '30px' }}>
        {/* Profile Summary */}
        <div className="card" style={{ height: 'fit-content' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              width: '100px', 
              height: '100px', 
              borderRadius: '50%', 
              backgroundColor: getUserTypeColor(user.user_type),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '40px',
              margin: '0 auto 20px'
            }}>
              {getUserTypeIcon(user.user_type)}
            </div>
            
            <h3>{user.first_name} {user.last_name}</h3>
            <p style={{ color: '#6c757d' }}>@{user.username}</p>
            
            <div style={{ 
              display: 'inline-block',
              padding: '5px 15px',
              borderRadius: '20px',
              backgroundColor: getUserTypeColor(user.user_type),
              color: 'white',
              fontSize: '14px',
              fontWeight: 'bold',
              textTransform: 'uppercase',
              marginBottom: '20px'
            }}>
              {user.user_type.replace('_', ' ')}
            </div>
            
            <div style={{ textAlign: 'left' }}>
              <h4>Account Status</h4>
              <p><strong>Verified:</strong> {user.is_verified ? '✅ Yes' : '❌ No'}</p>
              <p><strong>KYC Status:</strong> 
                <span style={{ 
                  marginLeft: '5px',
                  padding: '2px 6px',
                  borderRadius: '3px',
                  fontSize: '12px',
                  backgroundColor: user.kyc_status === 'approved' ? '#d4edda' : '#f8d7da',
                  color: user.kyc_status === 'approved' ? '#155724' : '#721c24'
                }}>
                  {user.kyc_status.replace('_', ' ').toUpperCase()}
                </span>
              </p>
              <p><strong>Member Since:</strong> {new Date(user.date_joined).toLocaleDateString()}</p>
              <p><strong>Wallet Balance:</strong> ${parseFloat(user.wallet_balance || 0).toFixed(2)}</p>
            </div>
          </div>
        </div>
        
        {/* Profile Form */}
        <div className="card">
          <h3>Edit Profile</h3>
          
          <form onSubmit={handleSubmit}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
              <div className="form-group">
                <label>First Name</label>
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  className="form-control"
                />
              </div>
              
              <div className="form-group">
                <label>Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  className="form-control"
                />
              </div>
            </div>
            
            <div className="form-group">
              <label>Email</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="form-control"
                disabled
              />
              <small>Email cannot be changed. Contact support if needed.</small>
            </div>
            
            <div className="form-group">
              <label>Phone Number</label>
              <input
                type="tel"
                name="phone_number"
                value={formData.phone_number}
                onChange={handleChange}
                className="form-control"
                placeholder="+1234567890"
              />
            </div>
            
            <div className="form-group">
              <label>Address</label>
              <textarea
                name="address"
                value={formData.address}
                onChange={handleChange}
                className="form-control"
                rows="3"
                placeholder="Street address"
              />
            </div>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
              <div className="form-group">
                <label>City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  className="form-control"
                />
              </div>
              
              <div className="form-group">
                <label>State/Province</label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleChange}
                  className="form-control"
                />
              </div>
              
              <div className="form-group">
                <label>Postal Code</label>
                <input
                  type="text"
                  name="postal_code"
                  value={formData.postal_code}
                  onChange={handleChange}
                  className="form-control"
                />
              </div>
            </div>
            
            <div className="form-group">
              <label>Country</label>
              <input
                type="text"
                name="country"
                value={formData.country}
                onChange={handleChange}
                className="form-control"
              />
            </div>
            
            <div className="form-group">
              <label>Preferred Language</label>
              <select
                name="preferred_language"
                value={formData.preferred_language}
                onChange={handleChange}
                className="form-control"
              >
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="it">Italian</option>
                <option value="pt">Portuguese</option>
              </select>
            </div>
            
            <h4 style={{ marginTop: '30px', marginBottom: '15px' }}>📱 Notification Preferences</h4>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center' }}>
                  <input
                    type="checkbox"
                    name="enable_notifications"
                    checked={formData.enable_notifications}
                    onChange={handleChange}
                    style={{ marginRight: '8px' }}
                  />
                  All Notifications
                </label>
              </div>
              
              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center' }}>
                  <input
                    type="checkbox"
                    name="enable_email_notifications"
                    checked={formData.enable_email_notifications}
                    onChange={handleChange}
                    style={{ marginRight: '8px' }}
                  />
                  📧 Email Notifications
                </label>
              </div>
              
              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center' }}>
                  <input
                    type="checkbox"
                    name="enable_whatsapp_notifications"
                    checked={formData.enable_whatsapp_notifications}
                    onChange={handleChange}
                    style={{ marginRight: '8px' }}
                  />
                  📱 WhatsApp Notifications
                </label>
              </div>
            </div>
            
            <div style={{ marginTop: '30px', textAlign: 'center' }}>
              <button type="submit" className="btn btn-success" disabled={loading}>
                {loading ? 'Updating Profile...' : 'Update Profile'}
              </button>
            </div>
          </form>
        </div>
      </div>
      
      {/* Account Actions */}
      <div className="card">
        <h3>🔧 Account Actions</h3>
        <div style={{ display: 'flex', gap: '15px', flexWrap: 'wrap' }}>
          <button className="btn">Change Password</button>
          <button className="btn">Download Data</button>
          <button className="btn">Privacy Settings</button>
          {user.kyc_status === 'not_submitted' && (
            <button className="btn btn-success">Submit KYC Documents</button>
          )}
          <button className="btn btn-danger">Delete Account</button>
        </div>
        
        <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
          <h4>🛡️ Privacy & Security</h4>
          <p>Your personal information is protected with industry-standard encryption. We never share your data with third parties without your consent.</p>
          <p><strong>Last Login:</strong> {new Date().toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

export default Profile;
