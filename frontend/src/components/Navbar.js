import React from 'react';
import { Link } from 'react-router-dom';

const Navbar = ({ user, onLogout }) => {
  return (
    <nav className="navbar">
      <div className="container">
        <Link to="/dashboard" style={{ textDecoration: 'none', color: 'white' }}>
          <h1>🐟 Fish Auction Platform</h1>
        </Link>
        
        <div style={{ float: 'right' }}>
          <Link to="/dashboard" style={{ color: 'white', marginRight: '20px', textDecoration: 'none' }}>
            Dashboard
          </Link>
          <Link to="/auctions" style={{ color: 'white', marginRight: '20px', textDecoration: 'none' }}>
            Auctions
          </Link>
          {(user.user_type === 'seller' || user.user_type === 'admin') && (
            <Link to="/create-auction" style={{ color: 'white', marginRight: '20px', textDecoration: 'none' }}>
              Create Auction
            </Link>
          )}
          <Link to="/wallet" style={{ color: 'white', marginRight: '20px', textDecoration: 'none' }}>
            Wallet
          </Link>
          <Link to="/profile" style={{ color: 'white', marginRight: '20px', textDecoration: 'none' }}>
            Profile
          </Link>
          
          <span style={{ marginRight: '20px' }}>
            Welcome, {user.first_name || user.username}! 
            <span style={{ 
              background: user.user_type === 'seller' ? '#28a745' : '#007bff',
              padding: '2px 6px',
              borderRadius: '3px',
              fontSize: '12px',
              marginLeft: '5px'
            }}>
              {user.user_type.toUpperCase()}
            </span>
          </span>
          
          <button 
            onClick={onLogout}
            style={{
              background: '#dc3545',
              color: 'white',
              border: 'none',
              padding: '5px 10px',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Logout
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
