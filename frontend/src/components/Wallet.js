import React, { useState, useEffect } from 'react';
import { paymentsAPI } from '../api';

const Wallet = ({ user }) => {
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [topUpAmount, setTopUpAmount] = useState('');
  const [topUpLoading, setTopUpLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchWalletData();
  }, []);

  const fetchWalletData = async () => {
    try {
      const [balanceResponse, transactionsResponse] = await Promise.all([
        paymentsAPI.getWalletBalance(),
        paymentsAPI.getWalletTransactions()
      ]);
      
      setBalance(parseFloat(balanceResponse.data.balance));
      setTransactions(transactionsResponse.data.results || []);
    } catch (error) {
      setError('Failed to load wallet data');
    } finally {
      setLoading(false);
    }
  };

  const handleTopUp = async (e) => {
    e.preventDefault();
    setTopUpLoading(true);
    setError('');
    setMessage('');

    try {
      // For demo purposes, we'll simulate a successful top-up
      // In a real app, this would integrate with Stripe
      const amount = parseFloat(topUpAmount);
      
      if (amount < 1) {
        throw new Error('Minimum top-up amount is $1');
      }
      
      if (amount > 10000) {
        throw new Error('Maximum top-up amount is $10,000');
      }

      // Simulate successful payment
      setBalance(prev => prev + amount);
      setMessage(`Successfully added $${amount.toFixed(2)} to your wallet!`);
      setTopUpAmount('');
      
      // Refresh transactions
      fetchWalletData();
      
    } catch (error) {
      setError(error.message || 'Failed to top up wallet');
    } finally {
      setTopUpLoading(false);
    }
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'deposit': return '💰';
      case 'payment': return '💸';
      case 'refund': return '↩️';
      case 'reserve': return '🔒';
      case 'release': return '🔓';
      default: return '💳';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'deposit':
      case 'refund':
      case 'release':
        return '#28a745';
      case 'payment':
      case 'reserve':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  if (loading) {
    return <div className="loading">Loading wallet...</div>;
  }

  return (
    <div className="container">
      <h2>💰 My Wallet</h2>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      {/* Wallet Balance */}
      <div className="card" style={{ textAlign: 'center', marginBottom: '30px' }}>
        <h1 style={{ color: '#28a745', margin: '0' }}>${balance.toFixed(2)}</h1>
        <p style={{ fontSize: '18px', color: '#6c757d', margin: '10px 0 0 0' }}>Available Balance</p>
      </div>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '30px' }}>
        {/* Top Up Section */}
        <div className="card">
          <h3>💳 Top Up Wallet</h3>
          <form onSubmit={handleTopUp}>
            <div className="form-group">
              <label>Amount ($)</label>
              <input
                type="number"
                step="0.01"
                min="1"
                max="10000"
                value={topUpAmount}
                onChange={(e) => setTopUpAmount(e.target.value)}
                className="form-control"
                placeholder="Enter amount to add"
                required
              />
              <small>Minimum: $1.00 | Maximum: $10,000.00</small>
            </div>
            
            <button type="submit" className="btn btn-success" disabled={topUpLoading}>
              {topUpLoading ? 'Processing...' : 'Add Funds'}
            </button>
          </form>
          
          <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
            <h4>💡 Quick Top-Up Options</h4>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              {[25, 50, 100, 250, 500].map(amount => (
                <button
                  key={amount}
                  onClick={() => setTopUpAmount(amount.toString())}
                  className="btn"
                  style={{ fontSize: '14px', padding: '5px 10px' }}
                >
                  ${amount}
                </button>
              ))}
            </div>
          </div>
          
          <div style={{ marginTop: '20px', fontSize: '14px', color: '#6c757d' }}>
            <p><strong>💳 Payment Methods:</strong></p>
            <ul>
              <li>Credit/Debit Cards (Visa, MasterCard, Amex)</li>
              <li>Bank Transfer</li>
              <li>Digital Wallets (PayPal, Apple Pay, Google Pay)</li>
            </ul>
            <p><em>Note: This is a demo. In production, this would integrate with Stripe for secure payments.</em></p>
          </div>
        </div>
        
        {/* Wallet Stats */}
        <div className="card">
          <h3>📊 Wallet Statistics</h3>
          
          <div style={{ marginBottom: '20px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
              <span>Total Deposits:</span>
              <span style={{ color: '#28a745', fontWeight: 'bold' }}>
                ${transactions
                  .filter(t => t.transaction_type === 'deposit')
                  .reduce((sum, t) => sum + parseFloat(t.amount), 0)
                  .toFixed(2)}
              </span>
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
              <span>Total Spent:</span>
              <span style={{ color: '#dc3545', fontWeight: 'bold' }}>
                ${transactions
                  .filter(t => t.transaction_type === 'payment')
                  .reduce((sum, t) => sum + parseFloat(t.amount), 0)
                  .toFixed(2)}
              </span>
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
              <span>Total Transactions:</span>
              <span style={{ fontWeight: 'bold' }}>{transactions.length}</span>
            </div>
          </div>
          
          <div style={{ padding: '15px', backgroundColor: '#e7f3ff', borderRadius: '5px' }}>
            <h4>🛡️ Security Features</h4>
            <ul style={{ margin: '10px 0', paddingLeft: '20px' }}>
              <li>256-bit SSL encryption</li>
              <li>Two-factor authentication</li>
              <li>Real-time fraud monitoring</li>
              <li>Instant transaction notifications</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Transaction History */}
      <div className="card">
        <h3>📋 Transaction History</h3>
        
        {transactions.length > 0 ? (
          <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
            {transactions.map(transaction => (
              <div 
                key={transaction.id} 
                style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '15px', 
                  borderBottom: '1px solid #eee',
                  backgroundColor: transaction.transaction_type === 'deposit' ? '#f8fff8' : 
                                 transaction.transaction_type === 'payment' ? '#fff8f8' : 'white'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ fontSize: '24px', marginRight: '15px' }}>
                    {getTransactionIcon(transaction.transaction_type)}
                  </span>
                  <div>
                    <div style={{ fontWeight: 'bold', textTransform: 'capitalize' }}>
                      {transaction.transaction_type.replace('_', ' ')}
                    </div>
                    <div style={{ fontSize: '14px', color: '#6c757d' }}>
                      {transaction.description}
                    </div>
                    <div style={{ fontSize: '12px', color: '#999' }}>
                      {new Date(transaction.created_at).toLocaleString()}
                    </div>
                  </div>
                </div>
                
                <div style={{ textAlign: 'right' }}>
                  <div 
                    style={{ 
                      fontSize: '18px', 
                      fontWeight: 'bold',
                      color: getTransactionColor(transaction.transaction_type)
                    }}
                  >
                    {transaction.transaction_type === 'deposit' || 
                     transaction.transaction_type === 'refund' || 
                     transaction.transaction_type === 'release' ? '+' : '-'}
                    ${parseFloat(transaction.amount).toFixed(2)}
                  </div>
                  <div style={{ fontSize: '12px', color: '#6c757d', textTransform: 'uppercase' }}>
                    {transaction.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px', color: '#6c757d' }}>
            <h4>No transactions yet</h4>
            <p>Your transaction history will appear here once you start using your wallet.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Wallet;
