#!/usr/bin/env python3
"""
Test the complete auto-bid flow
"""
import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from decimal import Decimal
from auctions.tasks import process_auto_bids_for_auction

def main():
    print("🧪 Testing Auto-Bid Flow...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    print(f"🎯 Testing with auction: {live_auction.title} (ID: {live_auction.id})")
    print(f"   Current price: ${live_auction.current_price}")
    
    # Get some buyers
    buyers = User.objects.filter(user_type='buyer')[:3]
    if len(buyers) < 2:
        print("❌ Need at least 2 buyers for testing!")
        return
    
    buyer1, buyer2 = buyers[0], buyers[1]
    print(f"👥 Using buyers: {buyer1.username} and {buyer2.username}")
    
    # Clear any existing auto-bids for this auction
    AutoBid.objects.filter(auction=live_auction).delete()
    print("🧹 Cleared existing auto-bids")
    
    # Set up auto-bids for both buyers
    auto_bid1 = AutoBid.objects.create(
        auction=live_auction,
        bidder=buyer1,
        max_amount=Decimal('25.00'),
        increment=live_auction.bid_increment,
        is_active=True
    )
    
    auto_bid2 = AutoBid.objects.create(
        auction=live_auction,
        bidder=buyer2,
        max_amount=Decimal('30.00'),
        increment=live_auction.bid_increment,
        is_active=True
    )
    
    print(f"🤖 Set auto-bid for {buyer1.username}: max ${auto_bid1.max_amount}")
    print(f"🤖 Set auto-bid for {buyer2.username}: max ${auto_bid2.max_amount}")
    
    # Simulate a manual bid to trigger auto-bids
    print(f"\n💰 Placing manual bid to trigger auto-bids...")
    
    # Create a manual bid
    manual_bid = Bid.objects.create(
        auction=live_auction,
        bidder=buyer1,
        amount=live_auction.current_price + live_auction.bid_increment,
        bid_type='manual'
    )
    
    # Update auction price
    live_auction.current_price = manual_bid.amount
    live_auction.total_bids += 1
    live_auction.save()
    
    print(f"✅ Manual bid placed: ${manual_bid.amount} by {buyer1.username}")
    print(f"   Updated auction price: ${live_auction.current_price}")
    
    # Trigger auto-bid processing
    print(f"\n🔄 Triggering auto-bid processing...")
    result = process_auto_bids_for_auction(live_auction.id, manual_bid.id)
    print(f"✅ Auto-bid processing completed. Placed {result} auto-bids")
    
    # Check the results
    print(f"\n📊 Final Results:")
    live_auction.refresh_from_db()
    print(f"   Final price: ${live_auction.current_price}")
    print(f"   Total bids: {live_auction.total_bids}")
    
    # Show all bids
    all_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')
    print(f"\n💰 All bids for this auction:")
    for i, bid in enumerate(all_bids, 1):
        bid_type = "🤖 Auto" if bid.bid_type == 'auto' else "👤 Manual"
        print(f"   {i}. {bid.bidder.username}: ${bid.amount} ({bid_type}) - {bid.timestamp.strftime('%H:%M:%S')}")
    
    # Check auto-bid status
    print(f"\n🤖 Auto-bid status:")
    for auto_bid in [auto_bid1, auto_bid2]:
        auto_bid.refresh_from_db()
        status = "✅ Active" if auto_bid.is_active else "❌ Inactive"
        print(f"   - {auto_bid.bidder.username}: max ${auto_bid.max_amount}, current ${auto_bid.current_bid_amount} ({status})")

if __name__ == "__main__":
    main()
