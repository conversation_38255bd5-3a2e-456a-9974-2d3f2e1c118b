#!/usr/bin/env python3
"""
Test the fixed auto-bid logic
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from decimal import Decimal
from auctions.tasks import process_auto_bids_for_auction

def test_fixed_logic():
    print("🧪 Testing Fixed Auto-Bid Logic...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    print(f"🎯 Auction: {live_auction.title} (ID: {live_auction.id})")
    print(f"   Current price: ${live_auction.current_price}")
    
    # Clear duplicate bids from rabie3
    print("\n🧹 Cleaning up duplicate bids...")
    duplicate_bids = Bid.objects.filter(
        auction=live_auction,
        bidder__username__icontains='rabie',
        amount=55.00
    )
    print(f"   Found {duplicate_bids.count()} duplicate $55 bids from rabie")
    
    # Keep only the first one
    if duplicate_bids.count() > 1:
        bids_to_delete = duplicate_bids[1:]  # Keep first, delete rest
        for bid in bids_to_delete:
            print(f"   Deleting duplicate bid: {bid.bidder.username} ${bid.amount}")
            bid.delete()
    
    # Reset auction price to the highest remaining bid
    highest_bid = Bid.objects.filter(auction=live_auction).order_by('-amount').first()
    if highest_bid:
        live_auction.current_price = highest_bid.amount
        live_auction.total_bids = Bid.objects.filter(auction=live_auction).count()
        live_auction.save()
        print(f"   Reset auction price to: ${live_auction.current_price}")
    
    # Show current state
    print(f"\n📊 Current State:")
    print(f"   Price: ${live_auction.current_price}")
    print(f"   Total bids: {live_auction.total_bids}")
    
    # Show last few bids
    recent_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')[:5]
    print(f"\n💰 Last 5 bids:")
    for i, bid in enumerate(recent_bids, 1):
        bid_type = "🤖 Auto" if bid.bid_type == 'auto' else "👤 Manual"
        print(f"   {i}. {bid.bidder.username}: ${bid.amount} ({bid_type})")
    
    # Show active auto-bids
    active_auto_bids = AutoBid.objects.filter(
        auction=live_auction,
        is_active=True,
        max_amount__gt=live_auction.current_price
    ).order_by('-max_amount')
    
    print(f"\n🤖 Active auto-bids:")
    for ab in active_auto_bids:
        print(f"   - {ab.bidder.username}: max ${ab.max_amount}")
    
    # Test the fixed logic
    print(f"\n🔧 Testing fixed auto-bid logic...")
    last_bid = Bid.objects.filter(auction=live_auction).order_by('-timestamp').first()
    if last_bid:
        print(f"   Last bidder: {last_bid.bidder.username}")
        
        # Show which auto-bids would be excluded
        excluded = active_auto_bids.filter(bidder=last_bid.bidder)
        eligible = active_auto_bids.exclude(bidder=last_bid.bidder)
        
        print(f"   Excluded auto-bids: {excluded.count()}")
        for ab in excluded:
            print(f"     - {ab.bidder.username}: max ${ab.max_amount} (EXCLUDED)")
        
        print(f"   Eligible auto-bids: {eligible.count()}")
        for ab in eligible:
            print(f"     - {ab.bidder.username}: max ${ab.max_amount} (ELIGIBLE)")
        
        if eligible.exists():
            print(f"\n🚀 Triggering auto-bid processing...")
            result = process_auto_bids_for_auction(live_auction.id, last_bid.id)
            print(f"✅ Placed {result} auto-bids")
            
            # Show updated state
            live_auction.refresh_from_db()
            print(f"\n📊 Updated State:")
            print(f"   New price: ${live_auction.current_price}")
            print(f"   Total bids: {live_auction.total_bids}")
            
            # Show new bids
            new_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')[:3]
            print(f"\n💰 Latest 3 bids:")
            for i, bid in enumerate(new_bids, 1):
                bid_type = "🤖 Auto" if bid.bid_type == 'auto' else "👤 Manual"
                print(f"   {i}. {bid.bidder.username}: ${bid.amount} ({bid_type})")
        else:
            print("   No eligible auto-bids to process")

if __name__ == "__main__":
    test_fixed_logic()
