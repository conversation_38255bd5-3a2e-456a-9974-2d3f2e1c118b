#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from auctions.models import Auction, FishCategory
from notifications.models import NotificationTemplate, Notification
from notifications.services import NotificationService
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from datetime import timedelta

User = get_user_model()

def test_notification_structure():
    print("🔍 Testing Notification System Structure")
    print("=" * 50)
    
    try:
        # Get test users
        buyer = User.objects.get(username='whatsapp_test_buyer')
        seller = User.objects.get(username='whatsapp_test_seller')
        
        print(f"✅ Buyer: {buyer.username} (Phone: {buyer.phone_number})")
        print(f"✅ Seller: {seller.username} (Phone: {seller.phone_number})")
        
        # Get auction
        auction = Auction.objects.filter(title__contains="Test Tuna for WhatsApp").first()
        if not auction:
            print("❌ No test auction found")
            return False
        
        print(f"✅ Auction: {auction.title}")
        print(f"   Winner: {auction.winner.username}")
        print(f"   Final price: ${auction.current_price}")
        
        # Test notification service directly
        notification_service = NotificationService()
        
        # Test auction won notification (without actually sending WhatsApp)
        print(f"\n🎉 Testing auction won notification structure...")
        
        try:
            # Get template
            template = NotificationTemplate.objects.filter(notification_type='auction_won').first()
            if not template:
                print("❌ No auction_won template found")
                return False
            
            print(f"✅ Template found: {template.name}")
            print(f"   WhatsApp message template: {template.whatsapp_message[:100]}...")
            
            # Test template rendering
            context = {
                'user_name': buyer.get_full_name() or buyer.username,
                'auction': auction,
            }
            
            # Render WhatsApp message
            rendered_message = notification_service._render_template(template.whatsapp_message, context)
            print(f"✅ Rendered WhatsApp message:")
            print(f"   {rendered_message[:200]}...")
            
            # Test notification creation (in-app only to avoid WhatsApp errors)
            notifications = notification_service.send_notification(
                buyer, 
                'auction_won', 
                context, 
                channels=['in_app']  # Only in-app to test structure
            )
            
            print(f"✅ Created {len(notifications)} in-app notifications")
            
            for notification in notifications:
                print(f"   - {notification.channel}: {notification.status}")
                print(f"     Title: {notification.title}")
                print(f"     Message: {notification.message[:100]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Error testing notification structure: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auction_ending_notifications():
    """Test auction ending notifications that should trigger WhatsApp"""
    print(f"\n🏁 Testing auction ending notification integration...")
    
    try:
        # Import the notification function
        from notifications.tasks import end_expired_auctions
        
        # Get an auction that should trigger notifications
        auction = Auction.objects.filter(title__contains="Test Tuna for WhatsApp").first()
        if not auction:
            print("❌ No test auction found")
            return False
        
        # Check if the auction ending task would process this auction
        print(f"✅ Auction status: {auction.status}")
        print(f"   End time: {auction.end_time}")
        print(f"   Winner: {auction.winner.username if auction.winner else 'None'}")
        print(f"   Payment deadline: {auction.payment_deadline}")
        
        # Test the notification templates that would be used
        templates = NotificationTemplate.objects.filter(
            notification_type__in=['auction_won', 'auction_ended']
        )
        
        print(f"✅ Found {templates.count()} relevant templates:")
        for template in templates:
            print(f"   - {template.notification_type}: {template.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing auction ending notifications: {e}")
        return False

def test_payment_timeout_notifications():
    """Test payment timeout notifications"""
    print(f"\n⏰ Testing payment timeout notification integration...")
    
    try:
        # Import the payment timeout handler
        from notifications.tasks import handle_payment_timeouts
        
        # Get auction with payment deadline
        auction = Auction.objects.filter(
            winner__isnull=False,
            payment_deadline__isnull=False
        ).first()
        
        if auction:
            print(f"✅ Found auction with payment deadline: {auction.title}")
            print(f"   Winner: {auction.winner.username}")
            print(f"   Payment deadline: {auction.payment_deadline}")
            print(f"   Payment received: {auction.payment_received}")
            
            # Check if this would trigger notifications
            now = timezone.now()
            is_overdue = auction.payment_deadline < now and not auction.payment_received
            print(f"   Is overdue: {is_overdue}")
            
            return True
        else:
            print("❌ No auction with payment deadline found")
            return False
        
    except Exception as e:
        print(f"❌ Error testing payment timeout notifications: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Notification System Structure Tests")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Basic notification structure
    result1 = test_notification_structure()
    test_results.append(("Notification Structure", result1))
    
    # Test 2: Auction ending notifications
    result2 = test_auction_ending_notifications()
    test_results.append(("Auction Ending Integration", result2))
    
    # Test 3: Payment timeout notifications
    result3 = test_payment_timeout_notifications()
    test_results.append(("Payment Timeout Integration", result3))
    
    # Results summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All notification structure tests PASSED!")
        print("\n💡 The WhatsApp notification system is ready!")
        print("   Just add UltraMsg credentials to enable WhatsApp sending.")
    else:
        print("⚠️ Some notification structure tests FAILED!")
