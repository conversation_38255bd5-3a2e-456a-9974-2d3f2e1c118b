#!/usr/bin/env python3
"""
Debug the auto-bid logic issue
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid, AutoBid
from accounts.models import User
from django.db import transaction

def debug_auto_bid_logic():
    """Debug why auto-bid logic is not working"""
    print("🔍 Debugging Auto-bid Logic")
    print("=" * 30)
    
    # Find auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live auction found")
        return
    
    print(f"✅ Auction: {auction.title} (ID: {auction.id})")
    print(f"📊 Current price: ${auction.current_price}")
    print(f"💰 Bid increment: ${auction.bid_increment}")
    
    # Get test users
    user_a = User.objects.filter(username='testuser_a').first()
    user_b = User.objects.filter(username='testuser_b').first()
    
    if not user_a or not user_b:
        print("❌ Test users not found")
        return
    
    print(f"✅ Users: {user_a.username}, {user_b.username}")
    
    # Check existing auto-bids
    auto_bids = AutoBid.objects.filter(auction=auction, bidder__in=[user_a, user_b])
    print(f"\n📋 Existing Auto-bids:")
    for auto_bid in auto_bids:
        print(f"   {auto_bid.bidder.username}: max ${auto_bid.max_amount}, active: {auto_bid.is_active}")
    
    # Get last bid
    last_bid = Bid.objects.filter(auction=auction).order_by('-timestamp').first()
    last_bidder = last_bid.bidder if last_bid else None
    
    print(f"\n📋 Last Bid:")
    if last_bid:
        print(f"   ${last_bid.amount} by {last_bid.bidder.username} ({last_bid.bid_type})")
    else:
        print("   No bids found")
    
    # Calculate next bid amount
    next_bid_amount = auction.current_price + auction.bid_increment
    print(f"\n🧮 Calculations:")
    print(f"   Current price: ${auction.current_price}")
    print(f"   Bid increment: ${auction.bid_increment}")
    print(f"   Next bid amount: ${next_bid_amount}")
    
    # Check which auto-bids would qualify with OLD logic
    print(f"\n🔍 OLD Logic (max_amount__gt=current_price):")
    old_logic_auto_bids = AutoBid.objects.filter(
        auction=auction,
        is_active=True,
        max_amount__gt=auction.current_price
    )
    if last_bidder:
        old_logic_auto_bids = old_logic_auto_bids.exclude(bidder=last_bidder)
    
    print(f"   Qualifying auto-bids: {old_logic_auto_bids.count()}")
    for auto_bid in old_logic_auto_bids:
        print(f"   - {auto_bid.bidder.username}: max ${auto_bid.max_amount} > ${auction.current_price}")
    
    # Check which auto-bids would qualify with NEW logic
    print(f"\n🔍 NEW Logic (max_amount__gte=next_bid_amount):")
    new_logic_auto_bids = AutoBid.objects.filter(
        auction=auction,
        is_active=True,
        max_amount__gte=next_bid_amount
    )
    if last_bidder:
        new_logic_auto_bids = new_logic_auto_bids.exclude(bidder=last_bidder)
    
    print(f"   Qualifying auto-bids: {new_logic_auto_bids.count()}")
    for auto_bid in new_logic_auto_bids:
        print(f"   - {auto_bid.bidder.username}: max ${auto_bid.max_amount} >= ${next_bid_amount}")
    
    # Test specific scenario
    print(f"\n🧪 Test Scenario:")
    print(f"   User A max: $60, User B max: $80")
    print(f"   Current price: $59, Next bid: $60")
    print(f"   Expected: Both users should qualify (60 >= 60, 80 >= 60)")
    
    # Check if users would be excluded due to being last bidder
    if last_bidder:
        print(f"\n⚠️ Last bidder exclusion:")
        print(f"   Last bidder: {last_bidder.username}")
        if last_bidder == user_a:
            print(f"   User A would be excluded (last bidder)")
        if last_bidder == user_b:
            print(f"   User B would be excluded (last bidder)")

def test_manual_auto_bid_trigger():
    """Test manually triggering auto-bid logic"""
    print("\n🧪 Manual Auto-bid Trigger Test")
    print("=" * 35)
    
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        return
    
    user_a = User.objects.filter(username='testuser_a').first()
    user_b = User.objects.filter(username='testuser_b').first()
    
    if not user_a or not user_b:
        return
    
    try:
        with transaction.atomic():
            # Clear and recreate auto-bids
            AutoBid.objects.filter(auction=auction, bidder__in=[user_a, user_b]).delete()
            
            # Set specific test conditions
            auction.current_price = Decimal('58.00')
            auction.save()
            
            # Create auto-bids
            auto_bid_a = AutoBid.objects.create(
                auction=auction,
                bidder=user_a,
                max_amount=Decimal('60.00'),
                increment=auction.bid_increment,
                is_active=True
            )
            
            auto_bid_b = AutoBid.objects.create(
                auction=auction,
                bidder=user_b,
                max_amount=Decimal('80.00'),
                increment=auction.bid_increment,
                is_active=True
            )
            
            print(f"✅ Setup complete:")
            print(f"   Current price: ${auction.current_price}")
            print(f"   User A max: ${auto_bid_a.max_amount}")
            print(f"   User B max: ${auto_bid_b.max_amount}")
            
            # Simulate the logic manually
            next_bid_amount = auction.current_price + auction.bid_increment
            print(f"   Next bid amount: ${next_bid_amount}")
            
            # Check which auto-bids qualify
            qualifying_auto_bids = AutoBid.objects.filter(
                auction=auction,
                is_active=True,
                max_amount__gte=next_bid_amount
            ).order_by('-max_amount')
            
            print(f"\n🔍 Qualifying auto-bids:")
            for auto_bid in qualifying_auto_bids:
                can_bid = auto_bid.max_amount >= next_bid_amount
                print(f"   {auto_bid.bidder.username}: max ${auto_bid.max_amount}, can bid ${next_bid_amount}: {can_bid}")
            
            if qualifying_auto_bids.exists():
                highest_auto_bid = qualifying_auto_bids.first()
                print(f"\n🎯 Highest qualifying auto-bid:")
                print(f"   {highest_auto_bid.bidder.username} with max ${highest_auto_bid.max_amount}")
                print(f"   Should place bid at ${next_bid_amount}")
            else:
                print(f"\n❌ No qualifying auto-bids found")
                
    except Exception as e:
        print(f"❌ Error in manual test: {e}")

if __name__ == "__main__":
    debug_auto_bid_logic()
    test_manual_auto_bid_trigger()
