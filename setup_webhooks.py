#!/usr/bin/env python3
"""
Automated Webhook Setup Script for Fish Auction App
This script helps you set up Stripe webhooks automatically using ngrok.
"""

import os
import sys
import subprocess
import requests
import json
import time
from urllib.parse import urljoin

def check_requirements():
    """Check if required tools are installed"""
    print("🔍 Checking requirements...")
    
    # Check if ngrok is installed
    try:
        subprocess.run(['ngrok', 'version'], capture_output=True, check=True)
        print("✅ ngrok is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ ngrok is not installed")
        print("📥 Install ngrok from: https://ngrok.com/download")
        print("   Or use: npm install -g ngrok")
        return False
    
    # Check if Django server can start
    try:
        result = subprocess.run(['python', 'manage.py', 'check'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Django project is ready")
        else:
            print("❌ Django project has issues:")
            print(result.stderr)
            return False
    except FileNotFoundError:
        print("❌ Python or Django not found")
        return False
    
    return True

def start_django_server():
    """Start Django development server"""
    print("🚀 Starting Django server...")
    
    try:
        # Start Django server in background
        process = subprocess.Popen(
            ['python', 'manage.py', 'runserver', '8000'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if server is running
        try:
            response = requests.get('http://localhost:8000/api/', timeout=5)
            print("✅ Django server is running on http://localhost:8000")
            return process
        except requests.exceptions.RequestException:
            print("❌ Django server failed to start")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Error starting Django server: {e}")
        return None

def start_ngrok():
    """Start ngrok tunnel"""
    print("🌐 Starting ngrok tunnel...")
    
    try:
        # Start ngrok in background
        process = subprocess.Popen(
            ['ngrok', 'http', '8000', '--log=stdout'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for ngrok to start
        time.sleep(5)
        
        # Get ngrok public URL
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=10)
            tunnels = response.json()
            
            for tunnel in tunnels['tunnels']:
                if tunnel['proto'] == 'https':
                    public_url = tunnel['public_url']
                    print(f"✅ ngrok tunnel active: {public_url}")
                    return process, public_url
                    
            print("❌ No HTTPS tunnel found")
            process.terminate()
            return None, None
            
        except requests.exceptions.RequestException:
            print("❌ Could not get ngrok tunnel info")
            process.terminate()
            return None, None
            
    except Exception as e:
        print(f"❌ Error starting ngrok: {e}")
        return None, None

def display_webhook_setup_instructions(public_url):
    """Display instructions for setting up webhooks in Stripe Dashboard"""
    webhook_url = urljoin(public_url, '/api/payments/stripe/webhook/')
    
    print("\n" + "="*60)
    print("🎯 STRIPE WEBHOOK SETUP INSTRUCTIONS")
    print("="*60)
    print(f"📍 Your webhook URL: {webhook_url}")
    print("\n📋 Steps to complete setup:")
    print("1. Go to Stripe Dashboard: https://dashboard.stripe.com/")
    print("2. Navigate to: Developers → Webhooks")
    print("3. Click 'Add endpoint'")
    print(f"4. Enter endpoint URL: {webhook_url}")
    print("5. Select these events:")
    print("   - payment_intent.succeeded")
    print("   - payment_intent.payment_failed")
    print("   - invoice.payment_succeeded")
    print("   - customer.subscription.updated")
    print("6. Click 'Add endpoint'")
    print("7. Copy the 'Signing secret' (starts with whsec_)")
    print("8. Add it to your .env file:")
    print("   STRIPE_WEBHOOK_SECRET=whsec_your_signing_secret_here")
    print("\n🧪 Test your webhook:")
    print("1. In Stripe Dashboard, go to your webhook")
    print("2. Click 'Send test webhook'")
    print("3. Select 'payment_intent.succeeded'")
    print("4. Check your Django server logs for webhook receipt")
    print("="*60)

def test_webhook_endpoint(public_url):
    """Test if webhook endpoint is accessible"""
    webhook_url = urljoin(public_url, '/api/payments/stripe/webhook/')
    
    print(f"\n🧪 Testing webhook endpoint: {webhook_url}")
    
    try:
        # Test with a simple POST request
        response = requests.post(webhook_url, 
                               json={'test': 'webhook'}, 
                               timeout=10)
        
        if response.status_code in [200, 400, 405]:  # 400/405 expected without proper Stripe signature
            print("✅ Webhook endpoint is accessible")
            return True
        else:
            print(f"⚠️ Webhook endpoint returned status: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Webhook endpoint test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🐟 Fish Auction App - Webhook Setup Script")
    print("="*50)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please install missing dependencies.")
        sys.exit(1)
    
    # Start Django server
    django_process = start_django_server()
    if not django_process:
        print("\n❌ Failed to start Django server")
        sys.exit(1)
    
    # Start ngrok
    ngrok_process, public_url = start_ngrok()
    if not ngrok_process or not public_url:
        print("\n❌ Failed to start ngrok tunnel")
        django_process.terminate()
        sys.exit(1)
    
    # Test webhook endpoint
    test_webhook_endpoint(public_url)
    
    # Display setup instructions
    display_webhook_setup_instructions(public_url)
    
    print("\n⏳ Servers are running. Press Ctrl+C to stop...")
    
    try:
        # Keep servers running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        
        # Cleanup
        if ngrok_process:
            ngrok_process.terminate()
        if django_process:
            django_process.terminate()
        
        print("✅ Cleanup complete")

if __name__ == "__main__":
    main()
