#!/usr/bin/env python3
"""
Script to trigger test notifications for all the conditions you mentioned
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.utils import timezone
from datetime import timedelta
from accounts.models import User
from auctions.models import Auction, Bid
from notifications.services import NotificationService
from broker_services.models import ServiceExecution, ServiceRequest


def create_test_auction_ending_soon():
    """Create an auction ending in 30 minutes to trigger ending reminders"""
    print("🎣 Creating auction ending soon...")
    
    try:
        seller = User.objects.filter(user_type='seller').first()
        if not seller:
            print("   ❌ No seller found")
            return
        
        # Create auction ending in 30 minutes
        auction = Auction.objects.create(
            title="Test Auction - Ending Soon",
            description="Test auction for notification testing",
            seller=seller,
            starting_price=50.00,
            current_price=50.00,
            status='live',
            auction_type='live',
            end_time=timezone.now() + timedelta(minutes=30)  # Ends in 30 minutes
        )
        
        # Add a test bid
        bidder = User.objects.filter(user_type='buyer').first()
        if bidder:
            Bid.objects.create(
                auction=auction,
                bidder=bidder,
                amount=75.00
            )
            auction.current_price = 75.00
            auction.save()
        
        print(f"   ✅ Created auction: {auction.title} (ends in 30 min)")
        return auction
        
    except Exception as e:
        print(f"   ❌ Error creating auction: {e}")


def create_test_auction_won():
    """Create an auction that just ended with a winner"""
    print("🏆 Creating auction with winner...")
    
    try:
        seller = User.objects.filter(user_type='seller').first()
        bidder = User.objects.filter(user_type='buyer').first()
        
        if not seller or not bidder:
            print("   ❌ Need both seller and buyer users")
            return
        
        # Create auction that just ended
        auction = Auction.objects.create(
            title="Test Auction - Just Won",
            description="Test auction for winner notification",
            seller=seller,
            starting_price=100.00,
            current_price=150.00,
            status='ended',
            auction_type='live',
            end_time=timezone.now() - timedelta(minutes=1),  # Just ended
            winner=bidder,
            payment_deadline=timezone.now() + timedelta(minutes=20)
        )
        
        # Add winning bid
        bid = Bid.objects.create(
            auction=auction,
            bidder=bidder,
            amount=150.00,
            is_winning_bid=True
        )
        
        print(f"   ✅ Created won auction: {auction.title}")
        
        # Send winner notification
        service = NotificationService()
        result = service.send_notification(
            bidder,
            'auction_won',
            {
                'auction': auction,
                'winning_bid': bid,
                'payment_deadline': auction.payment_deadline
            },
            channels=['whatsapp', 'in_app']
        )
        
        print(f"   📱 Sent winner notification: {len(result)} notifications")
        return auction
        
    except Exception as e:
        print(f"   ❌ Error creating won auction: {e}")


def test_broker_service_completion():
    """Test broker service completion notification"""
    print("🤝 Testing broker service completion...")
    
    try:
        # Find a service execution to complete
        execution = ServiceExecution.objects.filter(status='started').first()
        
        if not execution:
            print("   ❌ No active service execution found")
            return
        
        # Complete the service
        execution.status = 'completed'
        execution.completed_at = timezone.now()
        execution.report_text = "Service completed successfully. All tasks done as requested."
        execution.save()
        
        print(f"   ✅ Completed service execution: {execution.id}")
        
        # Send completion notification
        service = NotificationService()
        result = service.send_notification(
            execution.service_request.client,
            'broker_service_status_update',
            {
                'service_execution': execution,
                'old_status': 'started',
                'new_status': 'completed',
            },
            channels=['whatsapp', 'in_app']
        )
        
        print(f"   📱 Sent completion notification: {len(result)} notifications")
        
    except Exception as e:
        print(f"   ❌ Error testing broker service: {e}")


def test_payment_reminder():
    """Test payment reminder notification"""
    print("💰 Testing payment reminder...")
    
    try:
        # Find auction with winner but no payment
        auction = Auction.objects.filter(
            status='ended',
            winner__isnull=False,
            payment_deadline__gt=timezone.now()
        ).first()
        
        if not auction:
            print("   ❌ No auction with pending payment found")
            return
        
        # Send payment reminder
        service = NotificationService()
        result = service.send_notification(
            auction.winner,
            'payment_reminder',
            {
                'auction': auction,
                'time_remaining': auction.payment_deadline - timezone.now()
            },
            channels=['whatsapp', 'in_app']
        )
        
        print(f"   📱 Sent payment reminder: {len(result)} notifications")
        
    except Exception as e:
        print(f"   ❌ Error testing payment reminder: {e}")


def run_all_tests():
    """Run all notification tests"""
    print("🚀 Running All Notification Tests")
    print("=" * 50)
    
    # Test different notification scenarios
    create_test_auction_ending_soon()
    create_test_auction_won()
    test_broker_service_completion()
    test_payment_reminder()
    
    print("\n📊 Final Status Check:")
    
    # Check recent notifications
    from notifications.models import Notification
    recent = Notification.objects.order_by('-created_at')[:5]
    
    for notif in recent:
        status_icon = "✅" if notif.status == 'sent' else "❌" if notif.status == 'failed' else "⏳"
        print(f"   {status_icon} {notif.recipient.username} - {notif.template.notification_type} - {notif.status}")
    
    print("\n🎉 All notification tests completed!")
    print("📱 Check your WhatsApp for messages!")


if __name__ == '__main__':
    run_all_tests()
