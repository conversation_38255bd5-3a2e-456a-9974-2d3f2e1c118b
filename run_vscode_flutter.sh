#!/bin/bash

# Complete VS Code Flutter Setup and Runner
# Sets up environment and runs Flutter app with iOS device selection

echo "🚀 VS Code Flutter Complete Setup"
echo "================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Setup environment
echo -e "${BLUE}🔧 Setting up environment...${NC}"
export PATH="/Users/<USER>/flutter/bin:$PATH"
export PATH="/opt/homebrew/bin:$PATH"
eval "$(/opt/homebrew/bin/brew shellenv)"

# Verify Flutter
echo -e "${BLUE}🔍 Verifying Flutter installation...${NC}"
flutter --version
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Flutter not working${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Flutter is ready${NC}"

# Check VS Code settings
echo -e "${BLUE}📝 Checking VS Code settings...${NC}"
if [ -f ".vscode/settings.json" ]; then
    echo -e "${GREEN}✅ VS Code workspace settings found${NC}"
    echo -e "${BLUE}Current Flutter SDK path in VS Code:${NC}"
    grep -o '"dart.flutterSdkPath": "[^"]*"' .vscode/settings.json || echo "Not set"
else
    echo -e "${YELLOW}⚠️ No VS Code workspace settings found${NC}"
fi

# Navigate to Flutter app
if [ ! -d "fish_auction_app" ]; then
    echo -e "${RED}❌ fish_auction_app directory not found${NC}"
    exit 1
fi

cd fish_auction_app

# Get dependencies
echo -e "${BLUE}📦 Getting Flutter dependencies...${NC}"
flutter pub get

# Check devices
echo -e "${BLUE}📱 Checking available devices...${NC}"
flutter devices

echo ""
echo -e "${GREEN}🎯 VS Code Flutter Setup Complete!${NC}"
echo ""
echo -e "${BLUE}📋 How to run in VS Code:${NC}"
echo ""
echo -e "${YELLOW}Method 1: Using VS Code Flutter Extension${NC}"
echo "1. Open VS Code in the project root directory"
echo "2. Open fish_auction_app/lib/main.dart"
echo "3. Press F5 or click Run → Start Debugging"
echo "4. Select device when prompted"
echo ""
echo -e "${YELLOW}Method 2: Using VS Code Terminal${NC}"
echo "1. Open VS Code integrated terminal (Terminal → New Terminal)"
echo "2. Run these commands:"
echo "   export PATH=\"/Users/<USER>/flutter/bin:\$PATH\""
echo "   cd fish_auction_app"
echo "   flutter run -d ios --verbose"
echo ""
echo -e "${YELLOW}Method 3: Using Command Palette${NC}"
echo "1. Press Cmd+Shift+P"
echo "2. Type 'Flutter: Select Device'"
echo "3. Choose your iOS device/simulator"
echo "4. Press Cmd+Shift+P again"
echo "5. Type 'Flutter: Run Flutter App'"
echo ""
echo -e "${BLUE}🔧 VS Code Flutter Extension Commands:${NC}"
echo "• Cmd+Shift+P → 'Flutter: Launch Emulator'"
echo "• Cmd+Shift+P → 'Flutter: Select Device'"
echo "• Cmd+Shift+P → 'Flutter: Hot Reload'"
echo "• Cmd+Shift+P → 'Flutter: Hot Restart'"
echo "• Cmd+Shift+P → 'Flutter: Open DevTools'"
echo ""
echo -e "${GREEN}🎉 Ready to run Flutter app in VS Code!${NC}"

# Ask if user wants to start backend
echo ""
echo -e "${BLUE}🤔 Do you want to start the backend server now? (y/n)${NC}"
read -n 1 START_BACKEND
echo ""

if [[ $START_BACKEND =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚀 Starting backend server...${NC}"
    cd ..
    ./quick_start_macos.sh &
    BACKEND_PID=$!
    
    echo -e "${GREEN}✅ Backend starting in background (PID: $BACKEND_PID)${NC}"
    echo -e "${BLUE}💡 Backend will be available at http://localhost:8000${NC}"
    
    # Wait a moment for backend to start
    sleep 5
    
    echo -e "${BLUE}🔍 Checking backend status...${NC}"
    if curl -s http://localhost:8000/api/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is running successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ Backend is starting... (may take a few more seconds)${NC}"
    fi
fi

echo ""
echo -e "${GREEN}🎯 Everything is ready!${NC}"
echo -e "${BLUE}💡 Now open VS Code and run your Flutter app${NC}"
