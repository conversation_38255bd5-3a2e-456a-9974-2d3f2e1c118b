#!/usr/bin/env python3
"""
Comprehensive test script for all Fish Auction Platform features
"""

import requests
import json
import time
from datetime import datetime

class FishAuctionTester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.session = requests.Session()
        
        # Use the generated tokens
        self.tokens = {
            'buyer': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.BOdrN7hdvqujY-YNRzg5Sr_a0QkGHm2ZmzESwP7uQas',
            'seller': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.xmqIUtlERd8KMl5uSwIe_Sid3xbviG_Ngyh5AYwNHXE'
        }
    
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def test_delivery_system(self):
        """Test delivery tracking system"""
        self.log("🚚 Testing Delivery System...")
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Test delivery providers
        response = self.session.get(f"{self.base_url}/delivery/providers/")
        if response.status_code == 200:
            providers = response.json()
            self.log(f"✅ Delivery providers endpoint working: {len(providers.get('results', []))} providers")
        else:
            self.log(f"❌ Delivery providers failed: {response.status_code}")
        
        # Test user deliveries
        response = self.session.get(f"{self.base_url}/delivery/", headers=headers)
        if response.status_code == 200:
            deliveries = response.json()
            self.log(f"✅ User deliveries endpoint working: {len(deliveries.get('results', []))} deliveries")
        else:
            self.log(f"❌ User deliveries failed: {response.status_code}")
        
        # Test vessels
        response = self.session.get(f"{self.base_url}/delivery/vessels/", headers=headers)
        if response.status_code == 200:
            vessels = response.json()
            self.log(f"✅ Vessels endpoint working: {len(vessels.get('results', []))} vessels")
        else:
            self.log(f"❌ Vessels failed: {response.status_code}")
    
    def test_notification_system(self):
        """Test notification system"""
        self.log("🔔 Testing Notification System...")
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Test notifications list
        response = self.session.get(f"{self.base_url}/notifications/", headers=headers)
        if response.status_code == 200:
            notifications = response.json()
            self.log(f"✅ Notifications endpoint working: {len(notifications.get('results', []))} notifications")
        else:
            self.log(f"❌ Notifications failed: {response.status_code}")
        
        # Test notification preferences
        response = self.session.get(f"{self.base_url}/notifications/preferences/", headers=headers)
        if response.status_code == 200:
            preferences = response.json()
            self.log(f"✅ Notification preferences working")
        else:
            self.log(f"❌ Notification preferences failed: {response.status_code}")
        
        # Test sending test notification
        response = self.session.post(f"{self.base_url}/notifications/test/", headers=headers)
        if response.status_code == 200:
            self.log(f"✅ Test notification sent successfully")
        else:
            self.log(f"❌ Test notification failed: {response.status_code}")
    
    def test_ai_support_bot(self):
        """Test AI support bot"""
        self.log("🤖 Testing AI Support Bot...")
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Test chat with bot
        chat_data = {
            "message": "Hello, I need help with bidding"
        }
        response = self.session.post(f"{self.base_url}/support/chat/", json=chat_data, headers=headers)
        if response.status_code == 200:
            chat_response = response.json()
            bot_message = chat_response.get('response', '')
            self.log(f"✅ AI Bot responded: {bot_message[:100]}...")
        else:
            self.log(f"❌ AI Bot failed: {response.status_code}")
        
        # Test chat sessions
        response = self.session.get(f"{self.base_url}/support/sessions/", headers=headers)
        if response.status_code == 200:
            sessions = response.json()
            self.log(f"✅ Chat sessions working: {len(sessions.get('results', []))} sessions")
        else:
            self.log(f"❌ Chat sessions failed: {response.status_code}")
    
    def test_enhanced_kyc(self):
        """Test enhanced KYC system"""
        self.log("🆔 Testing Enhanced KYC System...")
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Test user profile with KYC status
        response = self.session.get(f"{self.base_url}/auth/profile/", headers=headers)
        if response.status_code == 200:
            profile = response.json()
            kyc_status = profile.get('kyc_status', 'unknown')
            self.log(f"✅ KYC status retrieved: {kyc_status}")
        else:
            self.log(f"❌ Profile retrieval failed: {response.status_code}")
        
        # Test KYC pending list (should fail for non-admin)
        response = self.session.get(f"{self.base_url}/auth/kyc/pending/", headers=headers)
        if response.status_code == 403:
            self.log(f"✅ KYC pending list properly restricted to admins")
        else:
            self.log(f"⚠️ KYC pending list: {response.status_code}")
    
    def test_auction_features(self):
        """Test auction-related features"""
        self.log("🐟 Testing Auction Features...")
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Test auctions list
        response = self.session.get(f"{self.base_url}/auctions/")
        if response.status_code == 200:
            auctions = response.json()
            total_auctions = len(auctions.get('results', []))
            self.log(f"✅ Auctions list working: {total_auctions} auctions")
        else:
            self.log(f"❌ Auctions list failed: {response.status_code}")
        
        # Test auction categories
        response = self.session.get(f"{self.base_url}/auctions/categories/")
        if response.status_code == 200:
            categories = response.json()
            self.log(f"✅ Auction categories working: {len(categories.get('results', []))} categories")
        else:
            self.log(f"❌ Auction categories failed: {response.status_code}")
        
        # Test watchlist
        response = self.session.get(f"{self.base_url}/auctions/watchlist/", headers=headers)
        if response.status_code == 200:
            watchlist = response.json()
            self.log(f"✅ Watchlist working: {len(watchlist.get('results', []))} items")
        else:
            self.log(f"❌ Watchlist failed: {response.status_code}")
    
    def test_payment_system(self):
        """Test payment and wallet features"""
        self.log("💰 Testing Payment System...")
        
        headers = {"Authorization": f"Bearer {self.tokens['buyer']}"}
        
        # Test wallet balance
        response = self.session.get(f"{self.base_url}/payments/wallet/balance/", headers=headers)
        if response.status_code == 200:
            balance = response.json()
            self.log(f"✅ Wallet balance working: ${balance.get('balance', 0)}")
        else:
            self.log(f"❌ Wallet balance failed: {response.status_code}")
        
        # Test wallet transactions
        response = self.session.get(f"{self.base_url}/payments/wallet/transactions/", headers=headers)
        if response.status_code == 200:
            transactions = response.json()
            self.log(f"✅ Wallet transactions working: {len(transactions.get('results', []))} transactions")
        else:
            self.log(f"❌ Wallet transactions failed: {response.status_code}")
    
    def test_marketplace(self):
        """Test marketplace features"""
        self.log("🏪 Testing Marketplace...")
        
        # Test marketplace categories
        response = self.session.get(f"{self.base_url}/marketplace/categories/")
        if response.status_code == 200:
            categories = response.json()
            self.log(f"✅ Marketplace categories working: {len(categories.get('results', []))} categories")
        else:
            self.log(f"❌ Marketplace categories failed: {response.status_code}")
        
        # Test marketplace listings
        response = self.session.get(f"{self.base_url}/marketplace/listings/")
        if response.status_code == 200:
            listings = response.json()
            self.log(f"✅ Marketplace listings working: {len(listings.get('results', []))} listings")
        else:
            self.log(f"❌ Marketplace listings failed: {response.status_code}")
    
    def run_comprehensive_test(self):
        """Run all feature tests"""
        self.log("🚀 Starting Comprehensive Feature Testing")
        self.log("=" * 80)
        
        try:
            self.test_auction_features()
            time.sleep(1)
            
            self.test_payment_system()
            time.sleep(1)
            
            self.test_delivery_system()
            time.sleep(1)
            
            self.test_notification_system()
            time.sleep(1)
            
            self.test_ai_support_bot()
            time.sleep(1)
            
            self.test_enhanced_kyc()
            time.sleep(1)
            
            self.test_marketplace()
            
            self.log("=" * 80)
            self.log("🎉 Comprehensive Feature Testing Complete!")
            
        except Exception as e:
            self.log(f"❌ Testing failed: {str(e)}")

if __name__ == "__main__":
    tester = FishAuctionTester()
    tester.run_comprehensive_test()
