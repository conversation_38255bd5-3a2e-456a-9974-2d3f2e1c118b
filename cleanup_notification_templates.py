#!/usr/bin/env python3

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from notifications.models import NotificationTemplate

def cleanup_duplicate_templates():
    print("🧹 Cleaning up duplicate notification templates")
    print("=" * 50)
    
    # Get all notification types
    notification_types = NotificationTemplate.objects.values_list('notification_type', flat=True).distinct()
    
    for notification_type in notification_types:
        templates = NotificationTemplate.objects.filter(notification_type=notification_type)
        
        if templates.count() > 1:
            print(f"\n📋 Found {templates.count()} templates for '{notification_type}':")
            
            # Keep the most complete template (one with WhatsApp message)
            best_template = None
            templates_to_delete = []
            
            for template in templates:
                print(f"   - {template.name} (WhatsApp: {'✅' if template.whatsapp_message else '❌'})")
                
                if template.whatsapp_message and not best_template:
                    best_template = template
                elif template.whatsapp_message and best_template:
                    # If both have WhatsApp messages, keep the newer one
                    if template.created_at > best_template.created_at:
                        templates_to_delete.append(best_template)
                        best_template = template
                    else:
                        templates_to_delete.append(template)
                elif not template.whatsapp_message:
                    templates_to_delete.append(template)
            
            # If no template has WhatsApp message, keep the first one
            if not best_template:
                best_template = templates.first()
                templates_to_delete = list(templates.exclude(id=best_template.id))
            
            print(f"   ✅ Keeping: {best_template.name}")
            
            # Delete duplicates
            for template in templates_to_delete:
                print(f"   🗑️ Deleting: {template.name}")
                template.delete()
        else:
            print(f"✅ {notification_type}: 1 template (OK)")
    
    print(f"\n✅ Cleanup completed!")

if __name__ == "__main__":
    cleanup_duplicate_templates()
