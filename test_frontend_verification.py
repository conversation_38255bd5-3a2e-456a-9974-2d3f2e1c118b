#!/usr/bin/env python3

import os
import sys
import django
import requests
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import DocumentVerificationRequest

User = get_user_model()

def test_frontend_verification_endpoints():
    print("🔍 Testing Frontend Verification Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000/api"
    session = requests.Session()
    
    try:
        # Create test seller if doesn't exist
        try:
            seller = User.objects.get(username='frontend_test_seller')
            print(f"✅ Using existing seller: {seller.username}")
        except User.DoesNotExist:
            seller = User.objects.create_user(
                username='frontend_test_seller',
                email='<EMAIL>',
                password='testpass123',
                phone_number='+**********',
                user_type='seller',
                wallet_balance=Decimal('100.00')
            )
            print(f"✅ Created seller: {seller.username}")
        
        # Reset verification status
        seller.is_verified = False
        seller.government_id_status = 'not_submitted'
        seller.hunting_approval_status = 'not_submitted'
        seller.save()
        
        # Authenticate seller
        response = session.post(f"{base_url}/auth/login/", json={
            'username': 'frontend_test_seller',
            'password': 'testpass123'
        })
        
        if response.status_code == 200:
            data = response.json()
            token = data['tokens']['access']
            session.headers.update({'Authorization': f'Bearer {token}'})
            print("✅ Seller authentication successful")
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return False
        
        # Test 1: Submit Government ID document
        print(f"\n📄 Test 1: Submit Government ID document")
        
        gov_id_data = {
            'document_type': 'government_id',
        }
        
        files = {'document_file': ('government_id.jpg', b"fake government id content", 'image/jpeg')}
        
        response = session.post(
            f"{base_url}/auth/documents/submit/",
            data=gov_id_data,
            files=files
        )
        
        print(f"Government ID submission: {response.status_code}")
        if response.status_code == 201:
            print("✅ Government ID submitted successfully")
            gov_id_success = True
        else:
            print(f"❌ Government ID submission failed: {response.text}")
            gov_id_success = False
        
        # Test 2: Submit Hunting Approval document
        print(f"\n📄 Test 2: Submit Hunting Approval document")
        
        hunting_data = {
            'document_type': 'hunting_approval',
        }
        
        files = {'document_file': ('hunting_approval.pdf', b"fake hunting approval content", 'application/pdf')}
        
        response = session.post(
            f"{base_url}/auth/documents/submit/",
            data=hunting_data,
            files=files
        )
        
        print(f"Hunting approval submission: {response.status_code}")
        if response.status_code == 201:
            print("✅ Hunting approval submitted successfully")
            hunting_success = True
        else:
            print(f"❌ Hunting approval submission failed: {response.text}")
            hunting_success = False
        
        # Test 3: Get user's document requests
        print(f"\n📋 Test 3: Get user's document requests")
        
        response = session.get(f"{base_url}/auth/documents/my-requests/")
        
        print(f"Document requests response: {response.status_code}")
        if response.status_code == 200:
            data = response.json()

            # Handle both list and paginated response formats
            if isinstance(data, dict) and 'results' in data:
                requests_list = data['results']
            else:
                requests_list = data

            print(f"✅ Found {len(requests_list)} document requests")

            for request in requests_list:
                if isinstance(request, dict):
                    print(f"   - {request.get('document_type', 'unknown')}: {request.get('status', 'unknown')}")
                else:
                    print(f"   - Request: {request}")

            requests_success = True
        else:
            print(f"❌ Failed to get document requests: {response.text}")
            requests_success = False
        
        # Test 4: Check user profile verification status
        print(f"\n👤 Test 4: Check user profile verification status")
        
        response = session.get(f"{base_url}/auth/profile/")
        
        print(f"Profile response: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Profile retrieved successfully")
            print(f"   Is verified: {data.get('is_verified', False)}")
            print(f"   Government ID status: {data.get('government_id_status', 'not_submitted')}")
            print(f"   Hunting approval status: {data.get('hunting_approval_status', 'not_submitted')}")
            
            profile_success = True
        else:
            print(f"❌ Failed to get profile: {response.text}")
            profile_success = False
        
        # Results
        all_tests = [
            ("Government ID Submission", gov_id_success),
            ("Hunting Approval Submission", hunting_success),
            ("Document Requests Retrieval", requests_success),
            ("Profile Verification Status", profile_success),
        ]
        
        print(f"\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        for test_name, result in all_tests:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(all_tests)} tests passed")
        
        if passed == len(all_tests):
            print("🎉 All frontend verification endpoints are working!")
            print("\n💡 The Flutter app should now be able to:")
            print("   - Submit Government ID documents ✅")
            print("   - Submit Hunting Approval documents ✅") 
            print("   - Retrieve user's document requests ✅")
            print("   - Check verification status in profile ✅")
            print("\n🚀 Ready for Flutter app testing!")
        else:
            print("⚠️ Some frontend verification tests FAILED!")
        
        return passed == len(all_tests)
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_frontend_verification_endpoints()
