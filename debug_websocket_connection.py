#!/usr/bin/env python3
"""
Debug WebSocket connection and test real-time updates
"""

import os
import sys
import django
import asyncio
import websockets
import json
import time
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

async def test_websocket_connection():
    """Test WebSocket connection directly"""
    print("🧪 Testing WebSocket Connection")
    print("=" * 35)
    
    # Find the Jamin fish auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live Jamin fish auction found")
        return
    
    print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
    
    # Test WebSocket URLs
    local_ws_url = f"ws://localhost:8000/ws/auction/{auction.id}/"
    
    # Get current ngrok URL
    import requests
    try:
        ngrok_response = requests.get("http://localhost:4040/api/tunnels")
        ngrok_data = ngrok_response.json()
        ngrok_url = None
        for tunnel in ngrok_data.get('tunnels', []):
            if tunnel.get('proto') == 'https':
                ngrok_url = tunnel['public_url'].replace('https://', 'wss://') + f"/ws/auction/{auction.id}/"
                break
    except:
        ngrok_url = None
    
    print(f"🔗 Local WebSocket URL: {local_ws_url}")
    if ngrok_url:
        print(f"🌐 Ngrok WebSocket URL: {ngrok_url}")
    else:
        print("❌ Could not get ngrok WebSocket URL")
    
    # Test local WebSocket connection
    print("\n🧪 Testing Local WebSocket Connection:")
    try:
        async with websockets.connect(local_ws_url) as websocket:
            print("✅ Local WebSocket connection successful!")
            
            # Send a test message
            test_message = {
                "type": "join_auction",
                "auction_id": auction.id
            }
            await websocket.send(json.dumps(test_message))
            print("📤 Sent join_auction message")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Received: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received (timeout)")
                
    except Exception as e:
        print(f"❌ Local WebSocket connection failed: {e}")
    
    # Test ngrok WebSocket connection if available
    if ngrok_url:
        print("\n🧪 Testing Ngrok WebSocket Connection:")
        try:
            async with websockets.connect(ngrok_url) as websocket:
                print("✅ Ngrok WebSocket connection successful!")
                
                # Send a test message
                test_message = {
                    "type": "join_auction",
                    "auction_id": auction.id
                }
                await websocket.send(json.dumps(test_message))
                print("📤 Sent join_auction message")
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"📨 Received: {response}")
                except asyncio.TimeoutError:
                    print("⏰ No response received (timeout)")
                    
        except Exception as e:
            print(f"❌ Ngrok WebSocket connection failed: {e}")
            print("💡 This might be why Flutter app isn't receiving updates!")

def test_bid_websocket_flow():
    """Test the complete bid WebSocket flow"""
    print("\n🧪 Testing Bid WebSocket Flow")
    print("=" * 30)
    
    # Find auction and buyer
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    buyer = User.objects.filter(user_type='buyer').first()
    
    if not auction or not buyer:
        print("❌ Missing auction or buyer for testing")
        return
    
    print(f"✅ Auction: {auction.title}")
    print(f"✅ Buyer: {buyer.username}")
    print(f"📊 Current price: ${auction.current_price}")
    
    # Create a bid
    new_amount = auction.current_price + auction.bid_increment
    
    try:
        bid = Bid.objects.create(
            auction=auction,
            bidder=buyer,
            amount=new_amount,
            bid_type='manual'
        )
        
        # Update auction
        auction.current_price = new_amount
        auction.total_bids += 1
        auction.save()
        
        print(f"✅ Created bid: ${bid.amount}")
        
        # Send WebSocket update (same as Django would do)
        channel_layer = get_channel_layer()
        bid_data = {
            'auction_id': auction.id,
            'bid_id': bid.id,
            'amount': str(bid.amount),
            'bidder': bid.bidder.username,
            'timestamp': bid.timestamp.isoformat(),
            'current_price': str(auction.current_price),
            'total_bids': auction.total_bids,
            'bid_type': 'manual'
        }
        
        async_to_sync(channel_layer.group_send)(
            f'auction_{auction.id}',
            {
                'type': 'bid_update',
                'bid_data': bid_data
            }
        )
        
        print("✅ WebSocket update sent!")
        print(f"📋 Message structure:")
        print(f"   Group: auction_{auction.id}")
        print(f"   Type: bid_update")
        print(f"   Data: {json.dumps(bid_data, indent=4)}")
        
        print("\n📱 Flutter App Should Receive:")
        flutter_message = {
            "type": "bid_update",
            "data": bid_data
        }
        print(json.dumps(flutter_message, indent=2))
        
    except Exception as e:
        print(f"❌ Error in bid flow: {e}")

def provide_solutions():
    """Provide solutions for WebSocket issues"""
    print("\n🔧 WebSocket Solutions")
    print("=" * 20)
    
    print("1. 🌐 Ngrok WebSocket Issues:")
    print("   - ngrok free tier has WebSocket limitations")
    print("   - Try testing locally first: ws://localhost:8000")
    print("   - Consider ngrok Pro for better WebSocket support")
    print()
    
    print("2. 📱 Flutter App Debugging:")
    print("   - Check browser console for WebSocket errors")
    print("   - Verify WebSocket URL in Flutter constants")
    print("   - Test with local WebSocket URL first")
    print()
    
    print("3. 🔍 Immediate Debug Steps:")
    print("   a. Test locally without ngrok:")
    print("      - Change Flutter wsUrl to 'ws://localhost:8000/ws'")
    print("      - Run Flutter app on same machine as Django")
    print("   b. Check WebSocket logs in Django console")
    print("   c. Monitor network tab in browser for WebSocket traffic")
    print()
    
    print("4. 🚀 Alternative Solution:")
    print("   - Use Server-Sent Events (SSE) instead of WebSocket")
    print("   - More reliable through ngrok")
    print("   - Easier to debug")

if __name__ == "__main__":
    print("🔍 WebSocket Connection Debugger")
    print("=" * 40)
    
    # Test WebSocket connections
    asyncio.run(test_websocket_connection())
    
    # Test bid flow
    test_bid_websocket_flow()
    
    # Provide solutions
    provide_solutions()
