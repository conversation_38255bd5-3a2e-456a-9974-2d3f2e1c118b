2025-06-23 17:50:05,918 INFO     Starting server at tcp:port=8000:interface=0.0.0.0
2025-06-23 17:50:05,919 INFO     HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-06-23 17:50:05,919 INFO     Configuring endpoint tcp:port=8000:interface=0.0.0.0
2025-06-23 17:50:05,919 INFO     Listening on TCP address 0.0.0.0:8000
2025-06-23 17:51:09,934 WARNING  UltraMsg credentials not configured. WhatsApp notifications will be disabled.
Not Found: /api/payments/stripe/config/
2025-06-23 17:51:09,948 WARNING  Not Found: /api/payments/stripe/config/
Unauthorized: /api/auth/profile/
2025-06-23 17:51:11,393 WARNING  Unauthorized: /api/auth/profile/
Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
2025-06-23 17:51:13,006 ERROR    Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
Unauthorized: /api/auth/profile/
2025-06-23 17:51:14,242 WARNING  Unauthorized: /api/auth/profile/
Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
2025-06-23 17:51:15,917 ERROR    Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
Not Found: /api/payments/stripe/config/
2025-06-23 17:53:14,601 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-23 18:15:07,252 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-23 18:24:42,354 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-23 18:28:42,281 WARNING  Not Found: /api/payments/stripe/config/
127.0.0.1:51420 - - [23/Jun/2025:17:51:09] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:51420 - - [23/Jun/2025:17:51:11] "GET /api/auth/profile/" 401 172
127.0.0.1:51447 - - [23/Jun/2025:17:51:13] "POST /api/auth/token/refresh/" 500 110270
127.0.0.1:51420 - - [23/Jun/2025:17:51:14] "GET /api/auth/profile/" 401 172
127.0.0.1:51455 - - [23/Jun/2025:17:51:15] "POST /api/auth/token/refresh/" 500 110270
127.0.0.1:51467 - - [23/Jun/2025:17:51:32] "POST /api/auth/login/" 200 1283
127.0.0.1:51467 - - [23/Jun/2025:17:51:33] "GET /api/auctions/categories/" 200 848
127.0.0.1:51475 - - [23/Jun/2025:17:51:34] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:51477 - - [23/Jun/2025:17:51:34] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:51487 - - [23/Jun/2025:17:51:37] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg" 200 313572
127.0.0.1:51477 - - [23/Jun/2025:17:51:37] "GET /api/auctions/20/" 200 101220
127.0.0.1:51492 - - [23/Jun/2025:17:51:39] "WSCONNECTING /ws/auction/20/" - -
🔌 WebSocket connection attempt for auction 20
127.0.0.1:51492 - - [23/Jun/2025:17:51:39] "WSCONNECT /ws/auction/20/" - -
✅ WebSocket connection accepted for auction 20
📤 Sent auction status for auction 20
127.0.0.1:51477 - - [23/Jun/2025:17:51:40] "GET /api/auctions/20/bids/" 200 17919
127.0.0.1:51511 - - [23/Jun/2025:17:51:47] "GET /api/auctions/20/bids/" 200 17919
127.0.0.1:51674 - - [23/Jun/2025:17:53:14] "GET /api/payments/stripe/config/" 404 5588
127.0.0.1:51713 - - [23/Jun/2025:17:54:08] "POST /api/auth/login/" 200 1282
127.0.0.1:51713 - - [23/Jun/2025:17:54:08] "GET /api/auctions/categories/" 200 848
127.0.0.1:51716 - - [23/Jun/2025:17:54:08] "GET /api/auctions/?page=1&page_size=20" 200 31240
127.0.0.1:51717 - - [23/Jun/2025:17:54:08] "GET /api/auctions/?page=1&page_size=20" 200 31240
127.0.0.1:51719 - - [23/Jun/2025:17:54:08] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:51718 - - [23/Jun/2025:17:54:08] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:51720 - - [23/Jun/2025:17:54:08] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg" 200 313572
127.0.0.1:51717 - - [23/Jun/2025:17:54:10] "GET /api/auctions/?page=1&page_size=20" 200 31240
127.0.0.1:51717 - - [23/Jun/2025:17:54:10] "GET /api/auctions/my-auctions/" 200 18917
127.0.0.1:51742 - - [23/Jun/2025:17:54:18] "WSCONNECTING /ws/auction/20/" - -
🔌 WebSocket connection attempt for auction 20
127.0.0.1:51742 - - [23/Jun/2025:17:54:19] "WSCONNECT /ws/auction/20/" - -
✅ WebSocket connection accepted for auction 20
📤 Sent auction status for auction 20
127.0.0.1:51743 - - [23/Jun/2025:17:54:19] "GET /api/auctions/20/" 200 101110
127.0.0.1:51743 - - [23/Jun/2025:17:54:19] "GET /api/auctions/20/bids/" 200 17900
127.0.0.1:51742 - - [23/Jun/2025:17:54:20] "WSDISCONNECT /ws/auction/20/" - -
127.0.0.1:51807 - - [23/Jun/2025:17:55:15] "GET /api/auctions/?page=1&page_size=20" 200 31240
127.0.0.1:51808 - - [23/Jun/2025:17:55:15] "GET /api/auctions/?page=1&page_size=20" 200 31240
127.0.0.1:51824 - - [23/Jun/2025:17:55:30] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2.jpg" 200 313572
127.0.0.1:51825 - - [23/Jun/2025:17:55:31] "WSCONNECTING /ws/auction/20/" - -
🔌 WebSocket connection attempt for auction 20
127.0.0.1:51825 - - [23/Jun/2025:17:55:31] "WSCONNECT /ws/auction/20/" - -
✅ WebSocket connection accepted for auction 20
📤 Sent auction status for auction 20
127.0.0.1:51826 - - [23/Jun/2025:17:55:32] "GET /api/auctions/20/" 200 101110
127.0.0.1:51826 - - [23/Jun/2025:17:55:32] "GET /api/auctions/20/bids/" 200 17900
127.0.0.1:51833 - - [23/Jun/2025:17:55:37] "GET /api/auctions/20/bids/" 200 17900
127.0.0.1:51881 - - [23/Jun/2025:17:56:04] "GET /api/auctions/20/bids/" 200 17900
127.0.0.1:51825 - - [23/Jun/2025:17:56:07] "WSDISCONNECT /ws/auction/20/" - -
127.0.0.1:51492 - - [23/Jun/2025:18:06:16] "WSDISCONNECT /ws/auction/20/" - -
127.0.0.1:53343 - - [23/Jun/2025:18:14:00] "GET /api/auctions/my-auctions/" 200 18917
127.0.0.1:53342 - - [23/Jun/2025:18:14:00] "GET /api/auctions/?page=1&page_size=20" 200 31240
127.0.0.1:53360 - - [23/Jun/2025:18:14:33] "GET /api/auctions/my-auctions/" 200 18917
127.0.0.1:53386 - - [23/Jun/2025:18:15:07] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:53400 - - [23/Jun/2025:18:15:16] "POST /api/auth/login/" 200 1282
127.0.0.1:53400 - - [23/Jun/2025:18:15:17] "GET /api/auctions/categories/" 200 848
127.0.0.1:53407 - - [23/Jun/2025:18:15:17] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:53408 - - [23/Jun/2025:18:15:17] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:53400 - - [23/Jun/2025:18:15:19] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:53414 - - [23/Jun/2025:18:15:20] "GET /api/auctions/my-auctions/" 200 19088
127.0.0.1:53938 - - [23/Jun/2025:18:21:49] "WSCONNECTING /ws/auction/20/" - -
🔌 WebSocket connection attempt for auction 20
127.0.0.1:53938 - - [23/Jun/2025:18:21:49] "WSCONNECT /ws/auction/20/" - -
✅ WebSocket connection accepted for auction 20
📤 Sent auction status for auction 20
127.0.0.1:54158 - - [23/Jun/2025:18:23:44] "OPTIONS /api/payments/stripe/config/" 200 -
127.0.0.1:53938 - - [23/Jun/2025:18:24:41] "WSDISCONNECT /ws/auction/20/" - -
127.0.0.1:54249 - - [23/Jun/2025:18:24:42] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:54249 - - [23/Jun/2025:18:24:43] "GET /api/auth/profile/" 200 778
127.0.0.1:54249 - - [23/Jun/2025:18:24:43] "GET /api/auctions/categories/" 200 848
127.0.0.1:54261 - - [23/Jun/2025:18:24:44] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54263 - - [23/Jun/2025:18:24:44] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54393 - - [23/Jun/2025:18:25:38] "GET /api/auth/profile/" 200 777
127.0.0.1:54393 - - [23/Jun/2025:18:25:38] "GET /api/auctions/categories/" 200 848
127.0.0.1:54408 - - [23/Jun/2025:18:25:39] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54407 - - [23/Jun/2025:18:25:39] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54425 - - [23/Jun/2025:18:25:40] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:54429 - - [23/Jun/2025:18:25:41] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:54428 - - [23/Jun/2025:18:25:41] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg" 200 313572
127.0.0.1:54555 - - [23/Jun/2025:18:28:30] "GET /api/auctions/my-auctions/" 200 19088
127.0.0.1:54553 - - [23/Jun/2025:18:28:30] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54574 - - [23/Jun/2025:18:28:42] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:54574 - - [23/Jun/2025:18:28:42] "GET /api/auth/profile/" 200 777
127.0.0.1:54574 - - [23/Jun/2025:18:28:43] "GET /api/auctions/categories/" 200 848
127.0.0.1:54583 - - [23/Jun/2025:18:28:43] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54585 - - [23/Jun/2025:18:28:43] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:54602 - - [23/Jun/2025:18:28:45] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:54601 - - [23/Jun/2025:18:28:45] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:54604 - - [23/Jun/2025:18:28:45] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg" 200 313572
127.0.0.1:54574 - - [23/Jun/2025:18:28:46] "GET /api/auctions/my-auctions/" 200 19088
127.0.0.1:54585 - - [23/Jun/2025:18:28:46] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55541 - - [23/Jun/2025:18:40:53] "GET /api/auth/profile/" 200 777
127.0.0.1:55541 - - [23/Jun/2025:18:40:54] "GET /api/auctions/categories/" 200 848
Not Found: /api/payments/stripe/config/
2025-06-23 18:41:12,619 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-23 18:51:10,824 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-24 09:03:47,243 WARNING  Not Found: /api/payments/stripe/config/
Unauthorized: /api/auth/profile/
2025-06-24 09:03:48,187 WARNING  Unauthorized: /api/auth/profile/
Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
2025-06-24 09:03:49,161 ERROR    Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/core/handlers/base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/asgiref/sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/fish-main/venv/lib/python3.13/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
Not Found: /api/payments/stripe/config/
2025-06-24 09:03:58,240 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-24 09:10:23,820 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-24 09:10:35,795 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/payments/stripe/config/
2025-06-24 09:11:29,924 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/auctions/1750756750390/
2025-06-24 09:19:10,445 WARNING  Not Found: /api/auctions/1750756750390/
Not Found: /api/payments/stripe/config/
2025-06-24 09:19:21,861 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/auctions/24/auto-bid/
2025-06-24 09:19:55,996 WARNING  Not Found: /api/auctions/24/auto-bid/
Not Found: /api/auctions/24/auto-bid/
2025-06-24 09:20:30,457 WARNING  Not Found: /api/auctions/24/auto-bid/
Not Found: /api/auctions/24/auto-bid/
2025-06-24 09:20:55,260 WARNING  Not Found: /api/auctions/24/auto-bid/
127.0.0.1:55549 - - [23/Jun/2025:18:40:54] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55551 - - [23/Jun/2025:18:40:54] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55549 - - [23/Jun/2025:18:40:56] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55541 - - [23/Jun/2025:18:40:56] "GET /api/auctions/my-auctions/" 200 19088
127.0.0.1:55573 - - [23/Jun/2025:18:40:57] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg" 200 313572
127.0.0.1:55571 - - [23/Jun/2025:18:40:57] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:55572 - - [23/Jun/2025:18:40:57] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:55593 - - [23/Jun/2025:18:41:12] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:55593 - - [23/Jun/2025:18:41:13] "GET /api/auth/profile/" 200 777
127.0.0.1:55593 - - [23/Jun/2025:18:41:15] "GET /api/auctions/categories/" 200 848
127.0.0.1:55611 - - [23/Jun/2025:18:41:16] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55613 - - [23/Jun/2025:18:41:16] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55914 - - [23/Jun/2025:18:48:45] "GET /api/auctions/?page=1&page_size=20" 200 31430
127.0.0.1:55916 - - [23/Jun/2025:18:48:45] "GET /api/auctions/my-auctions/" 200 19088
127.0.0.1:56106 - - [23/Jun/2025:18:51:10] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:60497 - - [24/Jun/2025:09:03:47] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:60497 - - [24/Jun/2025:09:03:48] "GET /api/auth/profile/" 401 172
127.0.0.1:60502 - - [24/Jun/2025:09:03:49] "POST /api/auth/token/refresh/" 500 110270
127.0.0.1:60516 - - [24/Jun/2025:09:03:58] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:60538 - - [24/Jun/2025:09:04:43] "POST /api/auth/login/" 200 1282
127.0.0.1:60538 - - [24/Jun/2025:09:04:44] "GET /api/auctions/categories/" 200 848
127.0.0.1:60545 - - [24/Jun/2025:09:04:44] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60546 - - [24/Jun/2025:09:04:44] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60561 - - [24/Jun/2025:09:04:46] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:60563 - - [24/Jun/2025:09:04:46] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:60574 - - [24/Jun/2025:09:05:04] "POST /api/auth/login/" 200 1283
127.0.0.1:60574 - - [24/Jun/2025:09:05:04] "GET /api/auctions/categories/" 200 848
127.0.0.1:60584 - - [24/Jun/2025:09:05:05] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60583 - - [24/Jun/2025:09:05:05] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60589 - - [24/Jun/2025:09:05:12] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60595 - - [24/Jun/2025:09:05:24] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60598 - - [24/Jun/2025:09:05:24] "GET /api/auctions/my-auctions/" 200 19082
127.0.0.1:60824 - - [24/Jun/2025:09:10:23] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:60842 - - [24/Jun/2025:09:10:35] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:60916 - - [24/Jun/2025:09:11:29] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:60956 - - [24/Jun/2025:09:13:27] "POST /api/auth/login/" 200 1282
127.0.0.1:60956 - - [24/Jun/2025:09:13:27] "GET /api/auctions/categories/" 200 848
127.0.0.1:60966 - - [24/Jun/2025:09:13:27] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60967 - - [24/Jun/2025:09:13:27] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60977 - - [24/Jun/2025:09:13:28] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:60979 - - [24/Jun/2025:09:13:28] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:60967 - - [24/Jun/2025:09:13:29] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:60956 - - [24/Jun/2025:09:13:29] "GET /api/auctions/my-auctions/" 200 19082
127.0.0.1:61006 - - [24/Jun/2025:09:13:57] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:61008 - - [24/Jun/2025:09:13:57] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg" 200 289175
127.0.0.1:61010 - - [24/Jun/2025:09:13:57] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg" 200 313572
127.0.0.1:61015 - - [24/Jun/2025:09:14:09] "POST /api/auth/login/" 200 1282
127.0.0.1:61015 - - [24/Jun/2025:09:14:09] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:61015 - - [24/Jun/2025:09:14:11] "GET /api/auctions/?page=1&page_size=20" 200 31424
127.0.0.1:61019 - - [24/Jun/2025:09:14:11] "GET /api/auctions/my-auctions/" 200 19082
127.0.0.1:61169 - - [24/Jun/2025:09:19:10] "POST /api/auctions/create/" 201 592
127.0.0.1:61169 - - [24/Jun/2025:09:19:10] "GET /api/auctions/1750756750390/" 404 48
127.0.0.1:61169 - - [24/Jun/2025:09:19:10] "GET /api/auctions/my-auctions/" 200 20484
127.0.0.1:61179 - - [24/Jun/2025:09:19:21] "GET /api/payments/stripe/config/" 404 5607
127.0.0.1:61179 - - [24/Jun/2025:09:19:22] "GET /api/auth/profile/" 200 777
127.0.0.1:61179 - - [24/Jun/2025:09:19:22] "GET /api/auctions/categories/" 200 848
127.0.0.1:61186 - - [24/Jun/2025:09:19:23] "GET /api/auctions/?page=1&page_size=20" 200 31502
127.0.0.1:61187 - - [24/Jun/2025:09:19:23] "GET /api/auctions/?page=1&page_size=20" 200 31502
127.0.0.1:61193 - - [24/Jun/2025:09:19:23] "GET /media/auction_images/image_picker_0599D903-9613-4D1C-A095-315D33E899A4-84806-00000475F8273DAE.jpg" 200 226753
127.0.0.1:61187 - - [24/Jun/2025:09:19:26] "GET /api/auctions/?page=1&page_size=20" 200 31502
127.0.0.1:61197 - - [24/Jun/2025:09:19:26] "GET /api/auctions/my-auctions/" 200 20484
127.0.0.1:61204 - - [24/Jun/2025:09:19:47] "POST /api/auth/login/" 200 1283
127.0.0.1:61204 - - [24/Jun/2025:09:19:47] "GET /api/auctions/?page=1&page_size=20" 200 31502
127.0.0.1:61212 - - [24/Jun/2025:09:19:51] "WSCONNECTING /ws/auction/24/" - -
🔌 WebSocket connection attempt for auction 24
127.0.0.1:61215 - - [24/Jun/2025:09:19:51] "GET /api/auctions/24/" 200 1895
127.0.0.1:61212 - - [24/Jun/2025:09:19:51] "WSCONNECT /ws/auction/24/" - -
✅ WebSocket connection accepted for auction 24
📤 Sent auction status for auction 24
127.0.0.1:61215 - - [24/Jun/2025:09:19:51] "GET /api/auctions/24/bids/" 200 52
127.0.0.1:61215 - - [24/Jun/2025:09:19:51] "GET /api/auctions/my-bids/" 200 18022
127.0.0.1:61222 - - [24/Jun/2025:09:19:55] "GET /api/auctions/24/auto-bid/" 404 43
127.0.0.1:61222 - - [24/Jun/2025:09:19:57] "GET /api/auctions/24/bids/" 200 52
127.0.0.1:61244 - - [24/Jun/2025:09:20:30] "GET /api/auctions/24/auto-bid/" 404 43
127.0.0.1:61244 - - [24/Jun/2025:09:20:32] "GET /api/auctions/24/bids/" 200 52
127.0.0.1:61252 - - [24/Jun/2025:09:20:39] "POST /api/auctions/24/bid/" 201 895
127.0.0.1:61252 - - [24/Jun/2025:09:20:39] "GET /api/auctions/24/" 200 3681
127.0.0.1:61252 - - [24/Jun/2025:09:20:40] "GET /api/auctions/24/bids/" 200 947
127.0.0.1:61252 - - [24/Jun/2025:09:20:41] "GET /api/auctions/24/bids/" 200 947
127.0.0.1:61212 - - [24/Jun/2025:09:20:50] "WSDISCONNECT /ws/auction/24/" - -
127.0.0.1:61269 - - [24/Jun/2025:09:20:50] "WSCONNECTING /ws/auction/24/" - -
🔌 WebSocket connection attempt for auction 24
127.0.0.1:61269 - - [24/Jun/2025:09:20:50] "WSCONNECT /ws/auction/24/" - -
✅ WebSocket connection accepted for auction 24
📤 Sent auction status for auction 24
127.0.0.1:61268 - - [24/Jun/2025:09:20:50] "GET /api/auctions/24/" 200 3681
127.0.0.1:61268 - - [24/Jun/2025:09:20:50] "GET /api/auctions/24/bids/" 200 947
127.0.0.1:61268 - - [24/Jun/2025:09:20:51] "GET /api/auctions/my-bids/" 200 18023
127.0.0.1:61286 - - [24/Jun/2025:09:20:54] "GET /api/auctions/24/bids/" 200 947
127.0.0.1:61286 - - [24/Jun/2025:09:20:55] "GET /api/auctions/24/auto-bid/" 404 43
Not Found: /api/auctions/24/auto-bid/
2025-06-24 09:27:14,182 WARNING  Not Found: /api/auctions/24/auto-bid/
Not Found: /api/payments/stripe/config/
2025-06-24 09:27:27,039 WARNING  Not Found: /api/payments/stripe/config/
Not Found: /api/auctions/24/auto-bid/
2025-06-24 09:27:42,556 WARNING  Not Found: /api/auctions/24/auto-bid/
Not Found: /api/auctions/24/auto-bid/
2025-06-24 09:29:47,352 WARNING  Not Found: /api/auctions/24/auto-bid/
