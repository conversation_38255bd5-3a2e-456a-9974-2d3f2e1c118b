2025-07-09 16:20:55,043 DEBUG    Read environment variables from: /Users/<USER>/Downloads/fish-main/.env
2025-07-09 16:20:55,044 DEBUG    get 'SECRET_KEY' casted as 'None' with default '<NoValue>'
2025-07-09 16:20:55,044 DEBUG    get 'DEBUG' casted as 'None' with default '<NoValue>'
2025-07-09 16:20:55,044 DEBUG    get 'DATABASE_URL' casted as 'None' with default '<NoValue>'
2025-07-09 16:20:55,044 DEBUG    get 'JWT_ACCESS_TOKEN_LIFETIME' casted as 'None' with default '60'
2025-07-09 16:20:55,044 DEBUG    get 'JWT_REFRESH_TOKEN_LIFETIME' casted as 'None' with default '1440'
2025-07-09 16:20:55,044 DEBUG    get 'JWT_SECRET_KEY' casted as 'None' with default 'django-insecure-test-key-change-in-production-123456789'
2025-07-09 16:20:55,044 DEBUG    get 'REDIS_URL' casted as 'None' with default 'redis://localhost:6379/0'
2025-07-09 16:20:55,044 DEBUG    get 'REDIS_URL' casted as 'None' with default 'redis://localhost:6379/0'
2025-07-09 16:20:55,044 DEBUG    get 'EMAIL_BACKEND' casted as 'None' with default 'django.core.mail.backends.console.EmailBackend'
2025-07-09 16:20:55,044 DEBUG    get 'EMAIL_HOST' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'EMAIL_PORT' casted as 'None' with default '587'
2025-07-09 16:20:55,044 DEBUG    get 'EMAIL_USE_TLS' casted as 'None' with default 'True'
2025-07-09 16:20:55,044 DEBUG    get 'EMAIL_HOST_USER' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'EMAIL_HOST_PASSWORD' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'STRIPE_PUBLISHABLE_KEY' casted as 'None' with default 'pk_test_51OLNPZHIyomRWz3uKNPoOk1439K6zjFyLcBEGoCuzL6JVGfI76Q70kzrxQC4w1y3VvZRirOnv5MntcjNI2cdCYX90016aQVKcs'
2025-07-09 16:20:55,044 DEBUG    get 'STRIPE_SECRET_KEY' casted as 'None' with default 'sk_test_51OLNPZHIyomRWz3uVKGR91iEwmdYt7N4AOuZtA0Cmsf46weg58JNuvWKcFvlV6nO2QSUACiFqk65fPWkyTuS3KEj00usUtxF04'
2025-07-09 16:20:55,044 DEBUG    get 'STRIPE_WEBHOOK_SECRET' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_PAYPAL_CLIENT_ID' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_PAYPAL_CLIENT_SECRET' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'PAYPAL_MODE' casted as 'None' with default 'sandbox'
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_STRIPE_ACCOUNT_ID' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_PAYONEER_USERNAME' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_PAYONEER_PASSWORD' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_PAYONEER_PARTNER_ID' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_BANK_ACCOUNT_NAME' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_BANK_ACCOUNT_NUMBER' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_BANK_ROUTING_NUMBER' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ADMIN_BANK_NAME' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'TWILIO_ACCOUNT_SID' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'TWILIO_AUTH_TOKEN' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'TWILIO_WHATSAPP_FROM' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'GOOGLE_MAPS_API_KEY' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'OPENAI_API_KEY' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ULTRAMSG_INSTANCE_ID' casted as 'None' with default ''
2025-07-09 16:20:55,044 DEBUG    get 'ULTRAMSG_TOKEN' casted as 'None' with default ''
2025-07-09 17:20:55,131 INFO     Starting server at tcp:port=8001:interface=0.0.0.0
2025-07-09 17:20:55,131 INFO     HTTP/2 support not enabled (install the http2 and tls Twisted extras)
2025-07-09 17:20:55,132 INFO     Configuring endpoint tcp:port=8001:interface=0.0.0.0
2025-07-09 17:20:55,132 INFO     HTTPFactory starting on 8001
2025-07-09 17:20:55,132 INFO     Starting factory <daphne.http_protocol.HTTPFactory object at 0x105ac0c20>
2025-07-09 17:20:55,132 INFO     Listening on TCP address 0.0.0.0:8001
