#!/bin/bash

# Fish Auction App - Simple Quick Start for macOS
# Simplified version that focuses on getting the server running

echo "🐟 Fish Auction App - Simple Quick Start (macOS)"
echo "================================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Detect Python command
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
else
    echo -e "${RED}❌ Python not found. Please install Python 3.8+${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Using Python: $PYTHON_CMD${NC}"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${BLUE}💡 Creating virtual environment...${NC}"
    $PYTHON_CMD -m venv venv
fi

# Activate virtual environment
echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Install dependencies (skip problematic ones)
echo -e "${BLUE}📦 Installing dependencies...${NC}"
pip install -r requirements.txt --ignore-installed || {
    echo -e "${YELLOW}⚠️ Some dependencies failed to install, trying essential ones only...${NC}"
    pip install Django djangorestframework django-cors-headers django-environ redis djangorestframework-simplejwt channels daphne stripe celery django-celery-beat
}

# Fix JWT settings before migrations
echo -e "${BLUE}🔧 Fixing JWT configuration...${NC}"
python -c "
import re

try:
    with open('fish_auction/settings.py', 'r') as f:
        content = f.read()

    # Fix JWT settings to prevent OutstandingToken errors
    jwt_pattern = r'SIMPLE_JWT = \{[^}]*\}'
    new_jwt_config = '''SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=env('JWT_ACCESS_TOKEN_LIFETIME', default=60)),
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=env('JWT_REFRESH_TOKEN_LIFETIME', default=1440)),
    'ROTATE_REFRESH_TOKENS': False,
    'UPDATE_LAST_LOGIN': False,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': env('JWT_SECRET_KEY', default=SECRET_KEY),
}'''
    
    if 'SIMPLE_JWT' in content:
        content = re.sub(jwt_pattern, new_jwt_config, content, flags=re.DOTALL)
    else:
        content += '\n\n# JWT Settings\n' + new_jwt_config + '\n'

    # Remove blacklist app from INSTALLED_APPS if present
    content = re.sub(r\"'rest_framework_simplejwt\.token_blacklist',?\s*\", '', content)

    # Add Stripe test keys
    stripe_pattern = r\"STRIPE_PUBLISHABLE_KEY = env\('STRIPE_PUBLISHABLE_KEY', default='[^']*'\)\"
    stripe_replacement = \"STRIPE_PUBLISHABLE_KEY = env('STRIPE_PUBLISHABLE_KEY', default='pk_test_51OLNPZHIyomRWz3uKNPoOk1439K6zjFyLcBEGoCuzL6JVGfI76Q70kzrxQC4w1y3VvZRirOnv5MntcjNI2cdCYX90016aQVKcs')\"
    content = re.sub(stripe_pattern, stripe_replacement, content)

    stripe_secret_pattern = r\"STRIPE_SECRET_KEY = env\('STRIPE_SECRET_KEY', default='[^']*'\)\"
    stripe_secret_replacement = \"STRIPE_SECRET_KEY = env('STRIPE_SECRET_KEY', default='sk_test_51OLNPZHIyomRWz3uVKGR91iEwmdYt7N4AOuZtA0Cmsf46weg58JNuvWKcFvlV6nO2QSUACiFqk65fPWkyTuS3KEj00usUtxF04')\"
    content = re.sub(stripe_secret_pattern, stripe_secret_replacement, content)

    with open('fish_auction/settings.py', 'w') as f:
        f.write(content)
    print('✅ JWT and Stripe configuration fixed')
except Exception as e:
    print(f'⚠️ Could not fix JWT configuration: {e}')
"

# Create migrations for any model changes
echo -e "${BLUE}🔄 Creating migrations...${NC}"
python manage.py makemigrations

# Run migrations
echo -e "${BLUE}🗄️ Setting up database...${NC}"
python manage.py migrate

# Create notification templates
echo -e "${BLUE}📱 Setting up notification templates...${NC}"
python -c "
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from notifications.models import NotificationTemplate

# Check if templates already exist
if NotificationTemplate.objects.count() == 0:
    # Create basic notification templates
    templates = [
        {
            'name': 'Auction Ending Soon',
            'notification_type': 'auction_ending_soon',
            'email_subject': 'Auction Ending Soon',
            'email_body': 'Your auction is ending soon!',
            'whatsapp_message': 'Auction ending soon!',
            'push_title': 'Auction Ending Soon',
            'push_body': 'Your auction is ending soon!'
        },
        {
            'name': 'Auction Started',
            'notification_type': 'auction_started',
            'email_subject': 'Auction Started',
            'email_body': 'Your auction has started!',
            'whatsapp_message': 'Auction started!',
            'push_title': 'Auction Started',
            'push_body': 'Your auction has started!'
        }
    ]
    
    for template_data in templates:
        NotificationTemplate.objects.create(**template_data)
    
    print(f'✅ Created {len(templates)} notification templates')
else:
    print(f'✅ Found {NotificationTemplate.objects.count()} existing notification templates')
" || {
    echo -e "${YELLOW}⚠️ Could not create notification templates${NC}"
}

# Kill any existing processes
echo -e "${BLUE}🔄 Cleaning up existing processes...${NC}"
pkill -f "runserver" 2>/dev/null || true
pkill -f "daphne.*fish_auction" 2>/dev/null || true
lsof -ti:8000 | xargs kill -9 2>/dev/null || true
lsof -ti:8001 | xargs kill -9 2>/dev/null || true

# Find available port
PORT=8000
if lsof -i :$PORT >/dev/null 2>&1; then
    PORT=8001
    if lsof -i :$PORT >/dev/null 2>&1; then
        PORT=8002
    fi
fi

# Start Django server
echo -e "${BLUE}🚀 Starting Django server...${NC}"
echo -e "${GREEN}✅ Server will start on http://localhost:$PORT${NC}"
echo -e "${YELLOW}💡 Press Ctrl+C to stop the server${NC}"
echo ""

# Start the server
python manage.py runserver 0.0.0.0:$PORT
