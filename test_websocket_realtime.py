#!/usr/bin/env python3
"""
Test WebSocket real-time updates for bidding
"""

import os
import sys
import django
import asyncio
import websockets
import json
import time
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

def test_websocket_connection():
    print("🧪 Testing WebSocket Real-time Updates")
    print("=" * 40)
    
    # Find the Jamin fish auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live Jamin fish auction found")
        return
    
    print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
    print(f"📊 Current price: ${auction.current_price}")
    print(f"📊 Total bids: {auction.total_bids}")
    print()
    
    # Get a test buyer
    buyer = User.objects.filter(user_type='buyer').first()
    if not buyer:
        print("❌ No buyer found for testing")
        return
    
    print(f"👤 Test buyer: {buyer.username}")
    print()
    
    # Test WebSocket URL
    ws_url = f"ws://localhost:8000/ws/auction/{auction.id}/"
    print(f"🔗 WebSocket URL: {ws_url}")
    print()
    
    # Test manual bid creation and WebSocket broadcast
    print("🧪 Testing manual bid and WebSocket broadcast...")
    
    try:
        # Create a manual bid
        new_amount = auction.current_price + auction.bid_increment
        
        bid = Bid.objects.create(
            auction=auction,
            bidder=buyer,
            amount=new_amount,
            bid_type='manual'
        )
        
        # Update auction
        auction.current_price = new_amount
        auction.total_bids += 1
        auction.save()
        
        print(f"✅ Created bid: ${bid.amount} by {buyer.username}")
        
        # Send WebSocket update
        channel_layer = get_channel_layer()
        if channel_layer:
            try:
                async_to_sync(channel_layer.group_send)(
                    f'auction_{auction.id}',
                    {
                        'type': 'bid_update',
                        'bid_data': {
                            'auction_id': auction.id,
                            'bid_id': bid.id,
                            'amount': str(bid.amount),
                            'bidder': bid.bidder.username,
                            'timestamp': bid.timestamp.isoformat(),
                            'current_price': str(auction.current_price),
                            'total_bids': auction.total_bids,
                            'bid_type': 'manual'
                        }
                    }
                )
                print("✅ WebSocket update sent!")
                
            except Exception as e:
                print(f"❌ Error sending WebSocket update: {e}")
        
        print()
        print("📱 Flutter App Debugging:")
        print("1. Check if WebSocket is connected in Flutter app")
        print("2. Look for console logs showing WebSocket messages")
        print("3. Verify the WebSocket URL in Flutter constants")
        print("4. Check if the bid update handler is working")
        print()
        
        # Check current WebSocket URL in Flutter
        try:
            with open('fish_auction_app/lib/constants/app_constants.dart', 'r') as f:
                content = f.read()
                import re
                ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
                if ws_url_match:
                    flutter_ws_url = ws_url_match.group(1)
                    print(f"📱 Flutter WebSocket URL: {flutter_ws_url}")
                    
                    # Check if it matches expected format
                    expected_pattern = f"wss://.*\\.ngrok-free\\.app/ws"
                    if re.match(expected_pattern, flutter_ws_url):
                        print("✅ Flutter WebSocket URL format looks correct")
                    else:
                        print("❌ Flutter WebSocket URL format might be incorrect")
                        print(f"   Expected pattern: {expected_pattern}")
                else:
                    print("❌ Could not find WebSocket URL in Flutter constants")
        except Exception as e:
            print(f"❌ Error reading Flutter constants: {e}")
        
        print()
        print("🔍 Troubleshooting Steps:")
        print("1. Check Flutter console for WebSocket connection logs")
        print("2. Verify ngrok tunnel is working for WebSocket")
        print("3. Check if bid_update messages are being received")
        print("4. Ensure auction detail screen is listening to WebSocket")
        print("5. Check if setState is being called on bid updates")
        
    except Exception as e:
        print(f"❌ Error creating test bid: {e}")

if __name__ == "__main__":
    test_websocket_connection()
