Watching for file changes with StatReloader
/Users/<USER>/Downloads/fish-main/auctions/views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 12:34:36
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
/Users/<USER>/Downloads/fish-main/auctions/urls.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 12:38:59
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
Exception in thread django-main-thread:
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/checks/urls.py", line 136, in check_custom_error_handlers
    handler = resolver.resolve_error_handler(status_code)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/resolvers.py", line 732, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
                       ^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/fish_auction/urls.py", line 31, in <module>
    path('api/auctions/', include('auctions.urls')),
                          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/auctions/urls.py", line 2, in <module>
    from .views import (
ImportError: cannot import name 'AuctionBidsView' from 'auctions.views' (/Users/<USER>/Downloads/fish-main/auctions/views.py)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py", line 982, in run
    self._target(*self._args, **self._kwargs)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/management/commands/runserver.py", line 134, in inner_run
    self.check(**check_kwargs)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/management/base.py", line 492, in check
    all_issues = checks.run_checks(
                 ^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/checks/registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/checks/urls.py", line 138, in check_custom_error_handlers
    path = getattr(resolver.urlconf_module, "handler%s" % status_code)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/fish_auction/urls.py", line 31, in <module>
    path('api/auctions/', include('auctions.urls')),
                          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/auctions/urls.py", line 2, in <module>
    from .views import (
ImportError: cannot import name 'AuctionBidsView' from 'auctions.views' (/Users/<USER>/Downloads/fish-main/auctions/views.py)
/Users/<USER>/Downloads/fish-main/auctions/urls.py changed, reloading.
Performing system checks...

Watching for file changes with StatReloader
Exception in thread django-main-thread:
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/checks/urls.py", line 136, in check_custom_error_handlers
    handler = resolver.resolve_error_handler(status_code)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/resolvers.py", line 732, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
                       ^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/fish_auction/urls.py", line 31, in <module>
    path('api/auctions/', include('auctions.urls')),
                          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/auctions/urls.py", line 2, in <module>
    from .views import (
ImportError: cannot import name 'AuctionBidsView' from 'auctions.views' (/Users/<USER>/Downloads/fish-main/auctions/views.py)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py", line 982, in run
    self._target(*self._args, **self._kwargs)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/utils/autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/management/commands/runserver.py", line 134, in inner_run
    self.check(**check_kwargs)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/management/base.py", line 492, in check
    all_issues = checks.run_checks(
                 ^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/checks/registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/checks/urls.py", line 138, in check_custom_error_handlers
    path = getattr(resolver.urlconf_module, "handler%s" % status_code)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/fish_auction/urls.py", line 31, in <module>
    path('api/auctions/', include('auctions.urls')),
                          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/urls/conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/fish-main/auctions/urls.py", line 2, in <module>
    from .views import (
ImportError: cannot import name 'AuctionBidsView' from 'auctions.views' (/Users/<USER>/Downloads/fish-main/auctions/views.py)
/Users/<USER>/Downloads/fish-main/auctions/views.py changed, reloading.
Performing system checks...

Watching for file changes with StatReloader
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg
Not Found: /auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg
[17/Jun/2025 12:49:53] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 404 4080
[17/Jun/2025 12:49:53] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 404 4080
[17/Jun/2025 12:49:53] "GET /auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4056
[17/Jun/2025 12:49:56] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 27950
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg
[17/Jun/2025 12:49:56] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 404 4080
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg
Not Found: /auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
[17/Jun/2025 12:49:56] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 404 4080
[17/Jun/2025 12:49:56] "GET /auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4056
[17/Jun/2025 12:49:58] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 27950
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg
Not Found: /auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg
[17/Jun/2025 12:49:58] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 404 4080
[17/Jun/2025 12:49:58] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 404 4080
[17/Jun/2025 12:49:58] "GET /auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4056
Not Found: /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2.jpg
[17/Jun/2025 12:49:59] "GET /auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2.jpg HTTP/1.1" 404 4056
/Users/<USER>/Downloads/fish-main/fish_auction/settings.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 12:49:10
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
/Users/<USER>/Downloads/fish-main/fish_auction/urls.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 12:57:10
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
/Users/<USER>/Downloads/fish-main/fish_auction/urls.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 13:02:51
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
Not Found: /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
[17/Jun/2025 13:04:37] "HEAD /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 0
[17/Jun/2025 13:04:49] "GET /api/auth/profile/ HTTP/1.1" 200 778
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/pagination.py:207: UnorderedObjectListWarning: Pagination may yield inconsistent results with an unordered object_list: <class 'auctions.models.FishCategory'> QuerySet.
  paginator = self.django_paginator_class(queryset, page_size)
[17/Jun/2025 13:04:49] "GET /api/auctions/categories/ HTTP/1.1" 200 755
[17/Jun/2025 13:04:49] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:04:49] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
Not Found: /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
[17/Jun/2025 13:04:49] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4534
Not Found: /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
Not Found: /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg
[17/Jun/2025 13:04:52] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4534
Not Found: /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg
[17/Jun/2025 13:04:52] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 404 4566
[17/Jun/2025 13:04:52] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 404 4566
[17/Jun/2025 13:04:52] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
Not Found: /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
[17/Jun/2025 13:04:52] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4534
Not Found: /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg
Not Found: /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg
[17/Jun/2025 13:04:52] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 404 4566
[17/Jun/2025 13:04:52] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 404 4566
[17/Jun/2025 13:04:54] "GET /api/auctions/22/ HTTP/1.1" 200 4455
Not Found: /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
[17/Jun/2025 13:04:54] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4534
[17/Jun/2025 13:04:54] "GET /api/auctions/22/bids/ HTTP/1.1" 200 1840
Not Found: /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg
Not Found: /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg
[17/Jun/2025 13:04:54] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 404 4534
[17/Jun/2025 13:04:54] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 404 4566
Not Found: /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg
[17/Jun/2025 13:04:54] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 404 4566
[17/Jun/2025 13:05:12] "HEAD /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 200 0
/Users/<USER>/Downloads/fish-main/auctions/serializers.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 13:03:07
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
[17/Jun/2025 13:05:44] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_nP4eM9W.jpg HTTP/1.1" 200 313572
[17/Jun/2025 13:05:44] "GET /media/auction_images/image_picker_DD453363-7B87-4622-83AB-6D4F3FB7F562-99615-0000098A014D8CB2_iij12pz.jpg HTTP/1.1" 200 313572
[17/Jun/2025 13:05:44] "GET /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 200 289175
[17/Jun/2025 13:05:51] "GET /api/auctions/22/ HTTP/1.1" 200 4455
[17/Jun/2025 13:05:51] "GET /api/auctions/22/bids/ HTTP/1.1" 200 1840
/Users/<USER>/Downloads/fish-main/auctions/serializers.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 13:05:43
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
/Users/<USER>/Downloads/fish-main/auctions/serializers.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 13:06:37
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
/Users/<USER>/Downloads/fish-main/auctions/serializers.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 13:06:56
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
[17/Jun/2025 13:07:50] "GET /api/auctions/22/ HTTP/1.1" 200 4455
[17/Jun/2025 13:08:05] "HEAD /media/auction_images/image_picker_2008FF06-D056-4F65-B19D-78811779F779-99615-0000098E7634C9B6.jpg HTTP/1.1" 200 0
[17/Jun/2025 13:08:16] "GET /api/auctions/?limit=1 HTTP/1.1" 200 28891
Unauthorized: /api/auctions/22/bids/
[17/Jun/2025 13:09:29] "GET /api/auctions/22/bids/ HTTP/1.1" 401 58
/Users/<USER>/Downloads/fish-main/auctions/views.py changed, reloading.
Performing system checks...

System check identified no issues (0 silenced).
June 17, 2025 - 13:07:30
Django version 5.2.3, using settings 'fish_auction.settings'
Starting development server at http://0.0.0.0:8000/
Quit the server with CONTROL-C.

WARNING: This is a development server. Do not use it in a production setting. Use a production WSGI or ASGI server instead.
For more information on production servers see: https://docs.djangoproject.com/en/5.2/howto/deployment/
Watching for file changes with StatReloader
[17/Jun/2025 13:10:34] "GET /api/auctions/22/bids/ HTTP/1.1" 200 1840
[17/Jun/2025 13:10:56] "GET /api/auth/profile/ HTTP/1.1" 200 778
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/pagination.py:207: UnorderedObjectListWarning: Pagination may yield inconsistent results with an unordered object_list: <class 'auctions.models.FishCategory'> QuerySet.
  paginator = self.django_paginator_class(queryset, page_size)
[17/Jun/2025 13:10:56] "GET /api/auctions/categories/ HTTP/1.1" 200 755
[17/Jun/2025 13:10:56] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:10:56] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:10:59] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/pagination.py:207: UnorderedObjectListWarning: Pagination may yield inconsistent results with an unordered object_list: <class 'auctions.models.AuctionWatchlist'> QuerySet.
  paginator = self.django_paginator_class(queryset, page_size)
[17/Jun/2025 13:10:59] "GET /api/auctions/watchlist/ HTTP/1.1" 200 52
[17/Jun/2025 13:11:00] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:11:00] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:11:04] "GET /api/auctions/22/ HTTP/1.1" 200 4455
[17/Jun/2025 13:11:04] "GET /api/auctions/22/bids/ HTTP/1.1" 200 1840
[17/Jun/2025 13:12:45] "GET /api/auth/profile/ HTTP/1.1" 200 778
[17/Jun/2025 13:12:45] "GET /api/auctions/categories/ HTTP/1.1" 200 755
[17/Jun/2025 13:12:45] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:12:46] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:12:47] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:12:48] "GET /api/auctions/watchlist/ HTTP/1.1" 200 52
[17/Jun/2025 13:12:48] "GET /api/auctions/?page=1&page_size=20 HTTP/1.1" 200 28896
[17/Jun/2025 13:12:50] "GET /api/auctions/22/ HTTP/1.1" 200 4455
[17/Jun/2025 13:12:50] "GET /api/auctions/22/bids/ HTTP/1.1" 200 1840
Unauthorized: /api/auctions/22/bid/
[17/Jun/2025 20:45:02] "POST /api/auctions/22/bid/ HTTP/1.1" 401 172
Internal Server Error: /api/auth/token/refresh/
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework_simplejwt/views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework/serializers.py", line 447, in run_validation
    value = self.validate(value)
            ^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework_simplejwt/serializers.py", line 141, in validate
    refresh.outstand()
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/rest_framework_simplejwt/tokens.py", line 222, in outstand
    return OutstandingToken.objects.get_or_create(
           ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'OutstandingToken' has no attribute 'objects'
[17/Jun/2025 20:45:02] "POST /api/auth/token/refresh/ HTTP/1.1" 500 21253
[17/Jun/2025 20:45:09] "GET /api/auctions/22/ HTTP/1.1" 200 4449
[17/Jun/2025 20:45:09] "GET /api/auctions/22/bids/ HTTP/1.1" 200 1840
Not Found: /ws/auction/22/
[17/Jun/2025 21:07:39] "GET /ws/auction/22/ HTTP/1.1" 404 3994
