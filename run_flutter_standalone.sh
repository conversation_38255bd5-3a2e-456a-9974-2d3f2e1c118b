#!/bin/bash

# Flutter Standalone Runner for VS Code
# Run Flutter app without backend dependency

echo "📱 Flutter Standalone Runner (VS Code)"
echo "======================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Set Flutter path
export PATH="/Users/<USER>/flutter/bin:$PATH"

echo -e "${GREEN}✅ Using Flutter: $(which flutter)${NC}"

# Check if we're in the right directory
if [ ! -d "fish_auction_app" ]; then
    echo -e "${RED}❌ fish_auction_app directory not found${NC}"
    echo -e "${BLUE}💡 Please run this script from the project root directory${NC}"
    exit 1
fi

cd fish_auction_app

echo -e "${BLUE}🔍 Checking Flutter setup...${NC}"
flutter doctor

echo -e "${BLUE}📦 Getting dependencies...${NC}"
flutter pub get

echo -e "${BLUE}📱 Checking for iOS devices...${NC}"
flutter devices

echo ""
echo -e "${BLUE}🎯 VS Code Terminal Flutter Runner${NC}"
echo "=================================="
echo ""
echo -e "${GREEN}Available commands:${NC}"
echo "1. flutter run -d ios                    # Run on iOS device"
echo "2. flutter run -d ios --simulator        # Run on iOS simulator"
echo "3. flutter devices                       # List devices"
echo "4. flutter run --verbose                 # Run with verbose output"
echo "5. flutter run -d [device-id]           # Run on specific device"
echo ""

# Show available devices with numbers
echo -e "${BLUE}📱 Available devices:${NC}"
flutter devices --machine | python3 -c "
import sys, json
try:
    devices = json.load(sys.stdin)
    for i, device in enumerate(devices):
        name = device.get('name', 'Unknown')
        device_id = device.get('id', 'unknown')
        platform = device.get('platformType', 'Unknown')
        
        if 'ios' in platform.lower():
            if 'simulator' in name.lower():
                print(f'{i+1}. 📲 {name} ({device_id}) - iOS Simulator')
            else:
                print(f'{i+1}. 📱 {name} ({device_id}) - iOS Device')
        else:
            print(f'{i+1}. 🖥️  {name} ({device_id}) - {platform}')
except Exception as e:
    print('Could not parse devices')
"

echo ""
echo -e "${BLUE}🚀 Quick Start Options:${NC}"
echo "A. Auto-run on iOS device"
echo "B. Auto-run on iOS simulator"
echo "C. Select device manually"
echo "D. Show Flutter commands only"
echo ""

read -p "Choose option (A/B/C/D): " OPTION

case $OPTION in
    [Aa])
        echo -e "${BLUE}🚀 Running on iOS device...${NC}"
        echo -e "${YELLOW}💡 Make sure your iOS device is connected and trusted${NC}"
        echo ""
        echo -e "${GREEN}Command: flutter run -d ios --verbose${NC}"
        echo ""
        flutter run -d ios --verbose
        ;;
    [Bb])
        echo -e "${BLUE}🚀 Running on iOS simulator...${NC}"
        echo -e "${BLUE}📲 Opening iOS Simulator...${NC}"
        open -a Simulator
        sleep 3
        echo ""
        echo -e "${GREEN}Command: flutter run -d ios --simulator --verbose${NC}"
        echo ""
        flutter run -d ios --simulator --verbose
        ;;
    [Cc])
        echo -e "${BLUE}📱 Device Selection:${NC}"
        flutter devices
        echo ""
        read -p "Enter device ID: " DEVICE_ID
        echo ""
        echo -e "${GREEN}Command: flutter run -d $DEVICE_ID --verbose${NC}"
        echo ""
        flutter run -d "$DEVICE_ID" --verbose
        ;;
    [Dd])
        echo -e "${BLUE}📋 Flutter Commands for VS Code Terminal:${NC}"
        echo ""
        echo -e "${GREEN}# Navigate to Flutter app directory${NC}"
        echo "cd fish_auction_app"
        echo ""
        echo -e "${GREEN}# Get dependencies${NC}"
        echo "flutter pub get"
        echo ""
        echo -e "${GREEN}# List available devices${NC}"
        echo "flutter devices"
        echo ""
        echo -e "${GREEN}# Run on iOS device${NC}"
        echo "flutter run -d ios --verbose"
        echo ""
        echo -e "${GREEN}# Run on iOS simulator${NC}"
        echo "flutter run -d ios --simulator --verbose"
        echo ""
        echo -e "${GREEN}# Run with device selection${NC}"
        echo "flutter run --verbose"
        echo ""
        echo -e "${GREEN}# Hot reload commands (while app is running):${NC}"
        echo "r  - Hot reload"
        echo "R  - Hot restart"
        echo "q  - Quit"
        echo "h  - Help"
        echo ""
        echo -e "${BLUE}💡 Copy and paste these commands in VS Code terminal${NC}"
        ;;
    *)
        echo -e "${RED}❌ Invalid option${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}🎉 Flutter runner complete!${NC}"
echo -e "${BLUE}💡 For VS Code: Use integrated terminal and run flutter commands directly${NC}"
