#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from auctions.models import Auction, Bid, FishCategory
from notifications.models import NotificationTemplate, Notification
from notifications.services import NotificationService, send_auction_notification
from notifications.ultramsg_service import UltraMsgService
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from datetime import timedelta

User = get_user_model()

class WhatsAppNotificationTester:
    def __init__(self):
        self.ultramsg_service = UltraMsgService()
        self.notification_service = NotificationService()
        
    def test_ultramsg_configuration(self):
        """Test if UltraMsg is properly configured"""
        print("🔧 Testing UltraMsg Configuration")
        print("-" * 30)
        
        is_configured = self.ultramsg_service.is_configured()
        print(f"UltraMsg configured: {'✅ Yes' if is_configured else '❌ No'}")
        
        if is_configured:
            print(f"Instance ID: {self.ultramsg_service.instance_id}")
            print(f"Base URL: {self.ultramsg_service.base_url}")
        else:
            print("❌ UltraMsg credentials not found in settings")
            print("   Please configure ULTRAMSG_INSTANCE_ID and ULTRAMSG_TOKEN")
        
        return is_configured
    
    def test_notification_templates(self):
        """Test if notification templates exist"""
        print("\n📋 Testing Notification Templates")
        print("-" * 30)
        
        required_templates = [
            'auction_won',
            'auction_ended',
            'payment_reminder',
            'kyc_approved'
        ]
        
        templates_found = 0
        for template_type in required_templates:
            try:
                templates = NotificationTemplate.objects.filter(notification_type=template_type)
                if templates.exists():
                    template = templates.first()
                    print(f"✅ {template_type}: {template.name}")
                    templates_found += 1
                else:
                    print(f"❌ {template_type}: Template not found")
            except Exception as e:
                print(f"❌ {template_type}: Error - {e}")
        
        print(f"\nTemplates found: {templates_found}/{len(required_templates)}")
        return templates_found == len(required_templates)
    
    def create_test_templates(self):
        """Create test notification templates if they don't exist"""
        print("\n📝 Creating test notification templates...")
        
        templates = [
            {
                'name': 'Test Auction Won WhatsApp',
                'notification_type': 'auction_won',
                'email_subject': '🎉 Congratulations! You won the auction',
                'email_body': 'You won the auction for {{ auction.title }}',
                'whatsapp_message': '''🎉 *Congratulations!* 

You won the auction for *{{ auction.title }}*!

💰 Final Price: ${{ auction.current_price }}
⏰ Payment Deadline: {{ auction.payment_deadline|date:"M d, Y H:i" }}

Please complete your payment within 20 minutes to secure your purchase.

🐟 Fish Auction Team''',
                'push_title': '🎉 Auction Won!',
                'push_body': 'You won {{ auction.title }} for ${{ auction.current_price }}'
            },
            {
                'name': 'Test Auction Ended WhatsApp',
                'notification_type': 'auction_ended',
                'email_subject': '🏁 Your auction has ended',
                'email_body': 'Your auction {{ auction.title }} has ended',
                'whatsapp_message': '''🏁 *Auction Ended*

Your auction *{{ auction.title }}* has ended!

{% if auction.winner %}
🎯 Winner: {{ auction.winner.get_full_name|default:auction.winner.username }}
💰 Final Price: ${{ auction.current_price }}
{% else %}
❌ No bids received
{% endif %}

🐟 Fish Auction Team''',
                'push_title': '🏁 Auction Ended',
                'push_body': 'Your auction {{ auction.title }} has ended'
            }
        ]
        
        created_count = 0
        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                print(f"✅ Created: {template.name}")
            else:
                print(f"📋 Exists: {template.name}")
        
        print(f"Created {created_count} new templates")
        return created_count
    
    def setup_test_users(self):
        """Create test users with phone numbers"""
        print("\n👥 Setting up test users...")
        
        # Create buyer with phone number
        try:
            buyer = User.objects.get(username='whatsapp_test_buyer')
        except User.DoesNotExist:
            buyer = User.objects.create_user(
                username='whatsapp_test_buyer',
                email='<EMAIL>',
                password='testpass123',
                phone_number='+1234567890',  # Test phone number
                wallet_balance=Decimal('200.00')
            )
        
        # Ensure phone number is set
        if not buyer.phone_number:
            buyer.phone_number = '+1234567890'
            buyer.save()
        
        # Create seller with phone number
        try:
            seller = User.objects.get(username='whatsapp_test_seller')
        except User.DoesNotExist:
            seller = User.objects.create_user(
                username='whatsapp_test_seller',
                email='<EMAIL>',
                password='testpass123',
                phone_number='+0987654321',  # Test phone number
                wallet_balance=Decimal('50.00')
            )
        
        # Ensure phone number is set
        if not seller.phone_number:
            seller.phone_number = '+0987654321'
            seller.save()
        
        print(f"✅ Buyer: {buyer.username} (Phone: {buyer.phone_number})")
        print(f"✅ Seller: {seller.username} (Phone: {seller.phone_number})")
        
        return buyer, seller
    
    def create_test_auction(self, seller, buyer):
        """Create a test auction for notification testing"""
        print("\n🐟 Creating test auction...")
        
        # Get or create fish category
        fish_category, _ = FishCategory.objects.get_or_create(
            name="Tuna",
            defaults={'name_ar': 'تونة'}
        )
        
        # Create test image
        test_image = SimpleUploadedFile(
            "test_tuna_whatsapp.jpg",
            b"fake image content",
            content_type="image/jpeg"
        )
        
        # Create auction
        auction = Auction.objects.create(
            title="Test Tuna for WhatsApp Notifications",
            description="Test auction for WhatsApp notification testing",
            fish_category=fish_category,
            fish_type="Tuna",
            quantity=1,
            weight=Decimal('7.0'),
            catch_date=timezone.now().date(),
            catch_location="Test Harbor",
            starting_price=Decimal('35.00'),
            current_price=Decimal('50.00'),
            target_price=Decimal('60.00'),
            seller=seller,
            status='ended',
            start_time=timezone.now() - timedelta(hours=2),
            end_time=timezone.now() - timedelta(minutes=15),
            winner=buyer,
            payment_deadline=timezone.now() + timedelta(minutes=20),
            payment_received=False,
            main_image=test_image
        )
        
        print(f"✅ Created auction: {auction.title} (ID: {auction.id})")
        print(f"   Winner: {auction.winner.username}")
        print(f"   Final price: ${auction.current_price}")
        
        return auction
    
    def test_auction_won_notification(self, auction):
        """Test sending auction won notification"""
        print("\n🎉 Testing auction won notification...")
        
        try:
            # Send notification to winner
            notifications = send_auction_notification(auction, 'auction_won')
            
            print(f"✅ Sent {len(notifications)} notifications")
            
            # Check WhatsApp notifications
            whatsapp_notifications = [n for n in notifications if n.channel == 'whatsapp']
            
            if whatsapp_notifications:
                for notification in whatsapp_notifications:
                    print(f"   📱 WhatsApp to {notification.recipient.phone_number}: {notification.status}")
                    print(f"      Message: {notification.message[:100]}...")
                return True
            else:
                print("❌ No WhatsApp notifications sent")
                return False
                
        except Exception as e:
            print(f"❌ Error sending auction won notification: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_auction_ended_notification(self, auction):
        """Test sending auction ended notification to seller"""
        print("\n🏁 Testing auction ended notification...")
        
        try:
            # Send notification to seller
            notifications = send_auction_notification(auction, 'auction_ended')
            
            print(f"✅ Sent {len(notifications)} notifications")
            
            # Check WhatsApp notifications
            whatsapp_notifications = [n for n in notifications if n.channel == 'whatsapp']
            
            if whatsapp_notifications:
                for notification in whatsapp_notifications:
                    print(f"   📱 WhatsApp to {notification.recipient.phone_number}: {notification.status}")
                    print(f"      Message: {notification.message[:100]}...")
                return True
            else:
                print("❌ No WhatsApp notifications sent")
                return False
                
        except Exception as e:
            print(f"❌ Error sending auction ended notification: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_tests(self):
        """Run all WhatsApp notification tests"""
        print("🚀 Starting WhatsApp Notification Tests")
        print("=" * 50)
        
        test_results = []
        
        # Test 1: UltraMsg configuration
        result1 = self.test_ultramsg_configuration()
        test_results.append(("UltraMsg Configuration", result1))
        
        # Test 2: Notification templates
        result2 = self.test_notification_templates()
        if not result2:
            self.create_test_templates()
            result2 = True  # Consider it passed if we created templates
        test_results.append(("Notification Templates", result2))
        
        # Test 3: Setup test users
        buyer, seller = self.setup_test_users()
        test_results.append(("Test Users Setup", True))
        
        # Test 4: Create test auction
        auction = self.create_test_auction(seller, buyer)
        test_results.append(("Test Auction Creation", True))
        
        # Test 5: Auction won notification
        if result1:  # Only test if UltraMsg is configured
            result5 = self.test_auction_won_notification(auction)
            test_results.append(("Auction Won Notification", result5))
            
            # Test 6: Auction ended notification
            result6 = self.test_auction_ended_notification(auction)
            test_results.append(("Auction Ended Notification", result6))
        else:
            print("\n⚠️ Skipping WhatsApp tests - UltraMsg not configured")
            test_results.append(("Auction Won Notification", False))
            test_results.append(("Auction Ended Notification", False))
        
        # Results summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            print("🎉 All WhatsApp notification tests PASSED!")
        else:
            print("⚠️ Some WhatsApp notification tests FAILED!")
            if not result1:
                print("\n💡 To enable WhatsApp notifications:")
                print("   1. Sign up for UltraMsg service")
                print("   2. Add ULTRAMSG_INSTANCE_ID and ULTRAMSG_TOKEN to settings")

if __name__ == "__main__":
    tester = WhatsAppNotificationTester()
    tester.run_tests()
