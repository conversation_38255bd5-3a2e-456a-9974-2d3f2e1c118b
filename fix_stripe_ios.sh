#!/bin/bash

# Fix Stripe iOS Plugin - Add missing requiresMainQueueSetup method
# This fixes the build error in the Stripe iOS plugin

echo "🔧 Fixing Stripe iOS Plugin..."

# Path to the problematic file
STRIPE_BRIDGE_FILE="$HOME/.pub-cache/hosted/pub.dev/stripe_ios-10.2.0/ios/Classes/Bridge/RCTBridge.m"

if [ -f "$STRIPE_BRIDGE_FILE" ]; then
    echo "✅ Found Stripe bridge file"
    
    # Check if the fix is already applied
    if grep -q "requiresMainQueueSetup" "$STRIPE_BRIDGE_FILE"; then
        echo "✅ Fix already applied"
    else
        echo "🔧 Applying fix..."
        
        # Add the missing method implementation
        cat >> "$STRIPE_BRIDGE_FILE" << 'EOF'

+ (BOOL)requiresMainQueueSetup {
    return YES;
}
EOF
        
        echo "✅ Fix applied successfully"
    fi
else
    echo "❌ Stripe bridge file not found at: $STRIPE_BRIDGE_FILE"
    echo "Trying alternative location..."
    
    # Try to find the file in the current project
    STRIPE_LOCAL_FILE="ios/Pods/stripe_ios/ios/Classes/Bridge/RCTBridge.m"
    if [ -f "$STRIPE_LOCAL_FILE" ]; then
        echo "✅ Found local Stripe bridge file"
        
        if grep -q "requiresMainQueueSetup" "$STRIPE_LOCAL_FILE"; then
            echo "✅ Fix already applied"
        else
            echo "🔧 Applying fix to local file..."
            cat >> "$STRIPE_LOCAL_FILE" << 'EOF'

+ (BOOL)requiresMainQueueSetup {
    return YES;
}
EOF
            echo "✅ Fix applied successfully"
        fi
    else
        echo "❌ Could not find Stripe bridge file"
        echo "Let's try a different approach..."
        
        # Create a simple patch in the iOS project
        mkdir -p ios/Runner/Patches
        cat > ios/Runner/Patches/StripeIOSFix.h << 'EOF'
// Fix for Stripe iOS Plugin
// This header provides the missing requiresMainQueueSetup method

#import <Foundation/Foundation.h>

@interface RCTBridge (StripeIOSFix)
+ (BOOL)requiresMainQueueSetup;
@end
EOF

        cat > ios/Runner/Patches/StripeIOSFix.m << 'EOF'
// Fix for Stripe iOS Plugin
// This implementation provides the missing requiresMainQueueSetup method

#import "StripeIOSFix.h"

@implementation RCTBridge (StripeIOSFix)

+ (BOOL)requiresMainQueueSetup {
    return YES;
}

@end
EOF
        
        echo "✅ Created patch files in ios/Runner/Patches/"
    fi
fi

echo "🎉 Stripe iOS fix completed!"
