#!/usr/bin/env python3
"""
Test WebSocket real-time updates
"""
import os
import sys
import django
import asyncio
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from decimal import Decimal
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

def test_websocket_send():
    print("🧪 Testing WebSocket Send...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    print(f"🎯 Testing with auction: {live_auction.title} (ID: {live_auction.id})")
    
    # Test sending a WebSocket message
    channel_layer = get_channel_layer()
    if not channel_layer:
        print("❌ No channel layer found!")
        return
    
    print("✅ Channel layer found")
    
    # Send a test bid update
    test_bid_data = {
        'auction_id': live_auction.id,
        'bid_id': 999,
        'amount': '50.00',
        'bidder': 'test_user',
        'timestamp': '2025-06-22T11:35:00Z',
        'current_price': '50.00',
        'total_bids': 20,
        'bid_type': 'manual'
    }
    
    try:
        async_to_sync(channel_layer.group_send)(
            f'auction_{live_auction.id}',
            {
                'type': 'bid_update',
                'bid_data': test_bid_data
            }
        )
        print("✅ Test WebSocket message sent successfully!")
        print(f"   Group: auction_{live_auction.id}")
        print(f"   Data: {test_bid_data}")
        
    except Exception as e:
        print(f"❌ Error sending WebSocket message: {e}")

def test_manual_bid():
    print("\n🧪 Testing Manual Bid with WebSocket...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    # Get a buyer
    buyer = User.objects.filter(user_type='buyer').first()
    if not buyer:
        print("❌ No buyer found!")
        return
    
    print(f"🎯 Placing manual bid on auction: {live_auction.title}")
    print(f"   Current price: ${live_auction.current_price}")
    
    # Place a manual bid
    new_amount = live_auction.current_price + live_auction.bid_increment
    
    bid = Bid.objects.create(
        auction=live_auction,
        bidder=buyer,
        amount=new_amount,
        bid_type='manual'
    )
    
    # Update auction
    live_auction.current_price = new_amount
    live_auction.total_bids += 1
    live_auction.save()
    
    print(f"✅ Manual bid placed: ${bid.amount} by {buyer.username}")
    
    # Send WebSocket update
    channel_layer = get_channel_layer()
    if channel_layer:
        try:
            async_to_sync(channel_layer.group_send)(
                f'auction_{live_auction.id}',
                {
                    'type': 'bid_update',
                    'bid_data': {
                        'auction_id': live_auction.id,
                        'bid_id': bid.id,
                        'amount': str(bid.amount),
                        'bidder': bid.bidder.username,
                        'timestamp': bid.timestamp.isoformat(),
                        'current_price': str(live_auction.current_price),
                        'total_bids': live_auction.total_bids,
                        'bid_type': 'manual'
                    }
                }
            )
            print("✅ WebSocket update sent for manual bid!")
            
        except Exception as e:
            print(f"❌ Error sending WebSocket update: {e}")
    
    # Trigger auto-bids
    from auctions.tasks import process_auto_bids_for_auction
    print("🔄 Triggering auto-bid processing...")
    result = process_auto_bids_for_auction(live_auction.id, bid.id)
    print(f"✅ Auto-bid processing completed. Placed {result} auto-bids")

if __name__ == "__main__":
    test_websocket_send()
    test_manual_bid()
