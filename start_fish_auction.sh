#!/bin/bash

# Fish Auction App Startup Script
# This script starts all required services for the Fish Auction app

echo "🐟 Starting Fish Auction App..."

# Get the directory where this script is located
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$DIR"

# Activate virtual environment
echo "📦 Activating virtual environment..."
source venv/bin/activate

# Check if Redis is running
echo "🔍 Checking Redis..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis is not running. Please start Redis first:"
    echo "   brew services start redis"
    echo "   or"
    echo "   redis-server"
    exit 1
fi
echo "✅ Redis is running"

# Function to start a service in the background
start_service() {
    local service_name=$1
    local command=$2
    local log_file=$3
    
    echo "🚀 Starting $service_name..."
    nohup $command > "$log_file" 2>&1 &
    local pid=$!
    echo "$pid" > "${service_name}.pid"
    echo "✅ $service_name started (PID: $pid)"
}

# Start Django server
start_service "django-server" "daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application" "logs/django.log"

# Wait a moment for Django to start
sleep 2

# Start Celery worker
start_service "celery-worker" "celery -A fish_auction worker --loglevel=info" "logs/celery-worker.log"

# Start Celery beat
start_service "celery-beat" "celery -A fish_auction beat --loglevel=info" "logs/celery-beat.log"

echo ""
echo "🎉 Fish Auction App started successfully!"
echo ""
echo "📊 Services running:"
echo "   🌐 Django Server: http://localhost:8000"
echo "   🤖 Celery Worker: Background task processing"
echo "   ⏰ Celery Beat: Periodic task scheduling"
echo ""
echo "📝 Logs:"
echo "   Django: logs/django.log"
echo "   Celery Worker: logs/celery-worker.log"
echo "   Celery Beat: logs/celery-beat.log"
echo ""
echo "🛑 To stop all services, run: ./stop_fish_auction.sh"
