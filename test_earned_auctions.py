#!/usr/bin/env python3

import os
import sys
import django
import requests
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from auctions.models import Auction, Bid, FishCategory
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile

User = get_user_model()

class EarnedAuctionsTester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.session = requests.Session()
        self.test_user = None
        self.seller_user = None
        self.token = None
        
    def setup_test_users(self):
        """Create or get test users"""
        try:
            self.test_user = User.objects.get(username='earned_test_buyer')
            print(f"✅ Using existing buyer: {self.test_user.username}")
        except User.DoesNotExist:
            self.test_user = User.objects.create_user(
                username='earned_test_buyer',
                email='<EMAIL>',
                password='testpass123',
                wallet_balance=Decimal('200.00')
            )
            print(f"✅ Created buyer: {self.test_user.username}")
        
        try:
            self.seller_user = User.objects.get(username='earned_test_seller')
            print(f"✅ Using existing seller: {self.seller_user.username}")
        except User.DoesNotExist:
            self.seller_user = User.objects.create_user(
                username='earned_test_seller',
                email='<EMAIL>',
                password='testpass123',
                wallet_balance=Decimal('50.00')
            )
            print(f"✅ Created seller: {self.seller_user.username}")
    
    def authenticate(self):
        """Authenticate test user"""
        try:
            response = self.session.post(f"{self.base_url}/auth/login/", json={
                'username': 'earned_test_buyer',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                data = response.json()
                self.token = data['tokens']['access']
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def create_test_auction(self):
        """Create a test auction and make the user win it"""
        try:
            # Get or create fish category
            fish_category, _ = FishCategory.objects.get_or_create(
                name="Salmon",
                defaults={'name_ar': 'سلمون'}
            )

            # Create a simple test image
            test_image = SimpleUploadedFile(
                "test_fish.jpg",
                b"fake image content",
                content_type="image/jpeg"
            )

            # Create auction
            auction = Auction.objects.create(
                title="Test Fish for Earned Auctions",
                description="Test auction for earned auctions testing",
                fish_category=fish_category,
                fish_type="Salmon",
                quantity=1,
                weight=Decimal('5.0'),
                catch_date=timezone.now().date() - timedelta(days=1),
                catch_location="Test Harbor",
                starting_price=Decimal('20.00'),
                current_price=Decimal('20.00'),
                target_price=Decimal('50.00'),
                seller=self.seller_user,
                status='ended',  # Set as ended
                start_time=timezone.now() - timedelta(hours=2),
                end_time=timezone.now() - timedelta(minutes=30),
                winner=self.test_user,  # Set buyer as winner
                payment_deadline=timezone.now() + timedelta(minutes=15),  # 15 minutes to pay
                payment_received=False,
                main_image=test_image
            )
            
            # Create a winning bid
            bid = Bid.objects.create(
                auction=auction,
                bidder=self.test_user,
                amount=Decimal('45.00'),
                timestamp=timezone.now() - timedelta(minutes=35)
            )
            
            # Update auction current price
            auction.current_price = bid.amount
            auction.save()
            
            print(f"✅ Created test auction: {auction.title} (ID: {auction.id})")
            print(f"   Winner: {auction.winner.username}")
            print(f"   Final price: ${auction.current_price}")
            print(f"   Payment deadline: {auction.payment_deadline}")
            
            return auction
            
        except Exception as e:
            print(f"❌ Failed to create test auction: {e}")
            return None
    
    def test_earned_auctions_endpoint(self):
        """Test the earned auctions API endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/auctions/earned/")
            
            print(f"Earned auctions response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                auctions = data.get('results', data) if isinstance(data, dict) and 'results' in data else data
                
                print(f"✅ Retrieved {len(auctions)} earned auctions")
                
                for i, auction in enumerate(auctions):
                    print(f"   {i+1}. {auction['title']}")
                    print(f"      Status: {auction['status']}")
                    print(f"      Final price: ${auction['current_price']}")
                    print(f"      Payment received: {auction.get('payment_received', False)}")
                    print(f"      Payment deadline: {auction.get('payment_deadline', 'N/A')}")
                
                return len(auctions) > 0
            else:
                print(f"❌ Failed to get earned auctions: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Earned auctions test error: {e}")
            return False
    
    def test_payment_processing(self, auction_id):
        """Test payment processing for earned auction"""
        try:
            print(f"\n💳 Testing payment processing for auction {auction_id}...")
            
            # Get wallet balance before payment
            balance_response = self.session.get(f"{self.base_url}/payments/wallet/balance/")
            if balance_response.status_code == 200:
                initial_balance = balance_response.json()['balance']
                print(f"   Initial balance: ${initial_balance}")
            
            # Process payment using wallet
            payment_response = self.session.post(f"{self.base_url}/payments/process/{auction_id}/", json={
                'payment_method': 'wallet'
            })
            
            print(f"   Payment response status: {payment_response.status_code}")
            print(f"   Payment response: {payment_response.text}")
            
            if payment_response.status_code == 200:
                payment_data = payment_response.json()
                print(f"✅ Payment processed successfully")
                print(f"   Payment ID: {payment_data.get('payment', {}).get('id', 'N/A')}")
                
                # Check balance after payment
                balance_response = self.session.get(f"{self.base_url}/payments/wallet/balance/")
                if balance_response.status_code == 200:
                    final_balance = balance_response.json()['balance']
                    print(f"   Final balance: ${final_balance}")
                
                return True
            else:
                error_data = payment_response.json() if payment_response.headers.get('content-type', '').startswith('application/json') else {}
                error_msg = error_data.get('error', payment_response.text)
                print(f"❌ Payment failed: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ Payment processing error: {e}")
            return False
    
    def run_tests(self):
        """Run all earned auctions tests"""
        print("🚀 Starting Earned Auctions Tests")
        print("=" * 50)
        
        # Setup
        self.setup_test_users()
        if not self.authenticate():
            return
        
        # Create test auction
        test_auction = self.create_test_auction()
        if not test_auction:
            return
        
        # Test cases
        test_results = []
        
        # Test 1: Get earned auctions
        print("\n📝 Test 1: Get earned auctions")
        result1 = self.test_earned_auctions_endpoint()
        test_results.append(("Get earned auctions", result1))
        
        # Test 2: Process payment
        print("\n📝 Test 2: Process payment for earned auction")
        result2 = self.test_payment_processing(test_auction.id)
        test_results.append(("Process payment", result2))
        
        # Test 3: Check earned auctions after payment
        print("\n📝 Test 3: Check earned auctions after payment")
        result3 = self.test_earned_auctions_endpoint()
        test_results.append(("Check after payment", result3))
        
        # Results summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            print("🎉 All earned auctions tests PASSED!")
        else:
            print("⚠️ Some earned auctions tests FAILED!")

if __name__ == "__main__":
    tester = EarnedAuctionsTester()
    tester.run_tests()
