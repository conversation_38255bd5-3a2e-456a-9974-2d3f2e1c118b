# 🌐 WebSocket Setup Guide - Fish Auction App

Complete guide for setting up Django with WebSocket support for real-time features.

## 🎯 Why WebSockets Matter

The Fish Auction app uses WebSockets for:
- ✅ **Real-time bidding** - Instant bid updates across all users
- ✅ **Live auction status** - Start/end notifications
- ✅ **Auto-bidding triggers** - Automated bidding responses
- ✅ **User notifications** - Instant alerts
- ✅ **Delivery tracking** - GPS location updates
- ✅ **Chat-style bidding** - Live bidding history

## 🔧 ASGI vs WSGI

**WSGI (Default Django)**: Only supports HTTP requests
**ASGI (Required for WebSockets)**: Supports both HTTP and WebSocket connections

### ❌ Wrong Way (No WebSocket Support):
```bash
python3 manage.py runserver 8000  # Only HTTP, no WebSockets
```

### ✅ Correct Way (With WebSocket Support):
```bash
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application  # HTTP + WebSockets
```

## 🚀 Quick WebSocket Setup

### Step 1: Install Dependencies
```bash
# Activate virtual environment
source venv/bin/activate

# Install WebSocket dependencies (already in requirements.txt)
pip install channels==4.0.0 channels-redis==4.1.0 daphne==4.0.0

# Or install all requirements
pip install -r requirements.txt
```

### Step 2: Start Redis (Required for WebSockets)
```bash
# Install Redis
brew install redis  # macOS
sudo apt install redis-server  # Ubuntu

# Start Redis
brew services start redis  # macOS
sudo systemctl start redis-server  # Ubuntu

# Test Redis
redis-cli ping  # Should return PONG
```

### Step 3: Start Django with ASGI
```bash
# Method 1: Using Daphne (Recommended)
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application

# Method 2: Using Uvicorn (Alternative)
uvicorn fish_auction.asgi:application --host 0.0.0.0 --port 8000 --reload

# Method 3: Development only (limited WebSocket support)
python3 manage.py runserver 8000
```

### Step 4: Test WebSocket Connection
```bash
# Install wscat for testing
npm install -g wscat

# Test auction WebSocket
wscat -c ws://localhost:8000/ws/auction/1/

# Test notifications WebSocket
wscat -c ws://localhost:8000/ws/notifications/1/
```

## 🔍 WebSocket Endpoints

The Fish Auction app provides these WebSocket endpoints:

### 1. Auction Room WebSocket
```
ws://localhost:8000/ws/auction/<auction_id>/
```
**Purpose**: Real-time bidding and auction updates
**Events**:
- `bid_placed` - New bid received
- `auction_started` - Auction goes live
- `auction_ended` - Auction finished
- `auto_bid_triggered` - Auto-bid activated

### 2. User Notifications WebSocket
```
ws://localhost:8000/ws/notifications/<user_id>/
```
**Purpose**: Personal notifications for users
**Events**:
- `bid_outbid` - User was outbid
- `auction_won` - User won auction
- `payment_reminder` - Payment due
- `delivery_update` - Delivery status change

### 3. Delivery Tracking WebSocket
```
ws://localhost:8000/ws/delivery/<delivery_id>/
```
**Purpose**: Real-time delivery tracking
**Events**:
- `location_update` - GPS coordinates
- `status_change` - Delivery status update
- `delivery_completed` - Package delivered

## 🧪 Testing WebSocket Features

### Test 1: Real-time Bidding
```bash
# Terminal 1: Start backend with WebSockets
daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application

# Terminal 2: Connect to auction room
wscat -c ws://localhost:8000/ws/auction/1/

# Terminal 3: Connect as another user
wscat -c ws://localhost:8000/ws/auction/1/

# Place a bid through the Flutter app and watch real-time updates
```

### Test 2: Browser Testing
```javascript
// Open browser console on http://localhost:8080
const ws = new WebSocket('ws://localhost:8000/ws/auction/1/');

ws.onopen = () => console.log('WebSocket connected');
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};

// Send a test message
ws.send(JSON.stringify({
    'type': 'join_auction',
    'auction_id': 1
}));
```

### Test 3: Multiple Browser Tabs
1. Open Flutter app in two browser tabs
2. Login with different accounts
3. Navigate to the same auction
4. Place bids from one tab
5. Watch real-time updates in the other tab

## 🔧 ASGI Configuration

The app's ASGI configuration is in `fish_auction/asgi.py`:

```python
import os
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.core.asgi import get_asgi_application
import auctions.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            auctions.routing.websocket_urlpatterns
        )
    ),
})
```

## 🚨 Common WebSocket Issues

### Issue 1: WebSocket Connection Refused
**Cause**: Using WSGI instead of ASGI
**Solution**: Use `daphne` or `uvicorn`, not `runserver`

### Issue 2: Redis Connection Error
**Cause**: Redis not running
**Solution**: 
```bash
brew services start redis  # macOS
sudo systemctl start redis-server  # Ubuntu
```

### Issue 3: WebSocket Closes Immediately
**Cause**: Authentication or routing issues
**Solution**: Check Django logs and WebSocket routing

### Issue 4: No Real-time Updates
**Cause**: Frontend not connecting to WebSocket
**Solution**: Check browser console for WebSocket errors

## 📱 Flutter WebSocket Integration

The Flutter app connects to WebSockets using:

```dart
// lib/services/websocket_service.dart
import 'package:web_socket_channel/web_socket_channel.dart';

class WebSocketService {
  WebSocketChannel? _channel;
  
  void connectToAuction(int auctionId) {
    _channel = WebSocketChannel.connect(
      Uri.parse('ws://localhost:8000/ws/auction/$auctionId/')
    );
    
    _channel!.stream.listen((data) {
      final message = jsonDecode(data);
      // Handle real-time updates
      handleAuctionUpdate(message);
    });
  }
}
```

## 🎯 Production Considerations

### For Production Deployment:
```bash
# Use proper ASGI server with multiple workers
daphne -b 0.0.0.0 -p 8000 --workers 4 fish_auction.asgi:application

# Or use Gunicorn with Uvicorn workers
gunicorn fish_auction.asgi:application -w 4 -k uvicorn.workers.UvicornWorker
```

### Environment Variables:
```bash
# Redis for production
REDIS_URL=redis://your-redis-server:6379/0

# WebSocket settings
CHANNEL_LAYERS_BACKEND=channels_redis.core.RedisChannelLayer
```

## ✅ Verification Checklist

- [ ] Redis is running (`redis-cli ping` returns PONG)
- [ ] Django started with Daphne/Uvicorn (not runserver)
- [ ] WebSocket test connection works (`wscat -c ws://localhost:8000/ws/auction/1/`)
- [ ] Browser console shows WebSocket connection
- [ ] Real-time bidding works between browser tabs
- [ ] No WebSocket errors in Django logs

## 🎉 Success Indicators

When WebSockets are working correctly:
- ✅ Multiple users see bids in real-time
- ✅ Auction status changes appear instantly
- ✅ Auto-bidding triggers immediately
- ✅ Notifications appear without page refresh
- ✅ Browser dev tools show active WebSocket connections

Your Fish Auction app now has full real-time capabilities! 🐟⚡
