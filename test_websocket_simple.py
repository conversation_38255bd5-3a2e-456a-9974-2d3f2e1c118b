#!/usr/bin/env python3
"""
Simple WebSocket connection test
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json
import requests

def test_websocket_setup():
    """Test WebSocket setup and configuration"""
    print("🧪 Testing WebSocket Setup")
    print("=" * 30)
    
    # 1. Check Django WebSocket configuration
    from django.conf import settings
    print(f"✅ ASGI Application: {getattr(settings, 'ASGI_APPLICATION', 'Not set')}")
    
    # 2. Check channel layer
    channel_layer = get_channel_layer()
    print(f"✅ Channel Layer: {type(channel_layer).__name__}")
    
    # 3. Test Redis connection
    try:
        async_to_sync(channel_layer.group_send)(
            'test_group',
            {'type': 'test_message', 'message': 'Hello WebSocket!'}
        )
        print("✅ Redis connection working")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False
    
    return True

def get_websocket_urls():
    """Get WebSocket URLs for testing"""
    print("\n🔗 WebSocket URLs")
    print("=" * 20)
    
    # Find auction
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    if not auction:
        print("❌ No live Jamin fish auction found")
        return None, None
    
    print(f"✅ Found auction: {auction.title} (ID: {auction.id})")
    
    # Local WebSocket URL
    local_ws_url = f"ws://localhost:8000/ws/auction/{auction.id}/"
    print(f"🏠 Local WebSocket URL: {local_ws_url}")
    
    # Get ngrok WebSocket URL
    try:
        ngrok_response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        ngrok_data = ngrok_response.json()
        ngrok_ws_url = None
        
        for tunnel in ngrok_data.get('tunnels', []):
            if tunnel.get('proto') == 'https':
                ngrok_ws_url = tunnel['public_url'].replace('https://', 'wss://') + f"/ws/auction/{auction.id}/"
                break
        
        if ngrok_ws_url:
            print(f"🌐 Ngrok WebSocket URL: {ngrok_ws_url}")
        else:
            print("❌ No HTTPS ngrok tunnel found")
            
    except Exception as e:
        print(f"❌ Could not get ngrok URL: {e}")
        ngrok_ws_url = None
    
    return local_ws_url, ngrok_ws_url

def test_bid_message_flow():
    """Test bid message creation and sending"""
    print("\n🧪 Testing Bid Message Flow")
    print("=" * 30)
    
    # Find auction and buyer
    auction = Auction.objects.filter(title__icontains='jamin', status='live').first()
    buyer = User.objects.filter(user_type='buyer').first()
    
    if not auction or not buyer:
        print("❌ Missing auction or buyer")
        return
    
    print(f"✅ Auction: {auction.title}")
    print(f"✅ Buyer: {buyer.username}")
    print(f"📊 Current price: ${auction.current_price}")
    
    # Create test bid data (don't actually create bid)
    new_amount = auction.current_price + auction.bid_increment
    
    bid_data = {
        'auction_id': auction.id,
        'bid_id': 999,  # Test ID
        'amount': str(new_amount),
        'bidder': buyer.username,
        'timestamp': '2025-06-22T20:00:00Z',
        'current_price': str(new_amount),
        'total_bids': auction.total_bids + 1,
        'bid_type': 'test'
    }
    
    # Send WebSocket message
    channel_layer = get_channel_layer()
    try:
        async_to_sync(channel_layer.group_send)(
            f'auction_{auction.id}',
            {
                'type': 'bid_update',
                'bid_data': bid_data
            }
        )
        print("✅ WebSocket message sent successfully!")
        
        print(f"\n📋 Message Details:")
        print(f"   Group: auction_{auction.id}")
        print(f"   Type: bid_update")
        print(f"   Data: {json.dumps(bid_data, indent=4)}")
        
        print(f"\n📱 Flutter Should Receive:")
        flutter_message = {
            "type": "bid_update",
            "data": bid_data
        }
        print(json.dumps(flutter_message, indent=2))
        
    except Exception as e:
        print(f"❌ Error sending WebSocket message: {e}")

def check_flutter_websocket_config():
    """Check Flutter WebSocket configuration"""
    print("\n📱 Flutter WebSocket Configuration")
    print("=" * 35)
    
    try:
        with open('fish_auction_app/lib/constants/app_constants.dart', 'r') as f:
            content = f.read()
            
        import re
        ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
        
        if ws_url_match:
            flutter_ws_url = ws_url_match.group(1)
            print(f"✅ Flutter WebSocket URL: {flutter_ws_url}")
            
            # Check if it's using ngrok
            if 'ngrok-free.app' in flutter_ws_url:
                print("🌐 Using ngrok WebSocket URL")
                
                # Check if ngrok URL is current
                try:
                    ngrok_response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
                    ngrok_data = ngrok_response.json()
                    current_ngrok = None
                    
                    for tunnel in ngrok_data.get('tunnels', []):
                        if tunnel.get('proto') == 'https':
                            current_ngrok = tunnel['public_url']
                            break
                    
                    if current_ngrok and current_ngrok.replace('https://', '') in flutter_ws_url:
                        print("✅ Flutter WebSocket URL matches current ngrok tunnel")
                    else:
                        print("❌ Flutter WebSocket URL doesn't match current ngrok tunnel")
                        print(f"   Current ngrok: {current_ngrok}")
                        print(f"   Flutter URL: {flutter_ws_url}")
                        
                except Exception as e:
                    print(f"⚠️ Could not verify ngrok URL: {e}")
                    
            elif 'localhost' in flutter_ws_url:
                print("🏠 Using local WebSocket URL")
            else:
                print("⚠️ Unknown WebSocket URL format")
                
        else:
            print("❌ WebSocket URL not found in Flutter constants")
            
    except Exception as e:
        print(f"❌ Error reading Flutter constants: {e}")

def provide_websocket_solutions():
    """Provide WebSocket debugging solutions"""
    print("\n🔧 WebSocket Debugging Solutions")
    print("=" * 35)
    
    print("1. 🧪 Test Locally First:")
    print("   - Change Flutter wsUrl to: 'ws://localhost:8000/ws'")
    print("   - Run Flutter app on same machine as Django")
    print("   - This eliminates ngrok as a variable")
    print()
    
    print("2. 🌐 Ngrok WebSocket Issues:")
    print("   - ngrok free tier has WebSocket limitations")
    print("   - WebSocket connections may be unstable")
    print("   - Consider upgrading to ngrok Pro")
    print()
    
    print("3. 📱 Flutter Debugging:")
    print("   - Open browser Developer Tools (F12)")
    print("   - Go to Network tab")
    print("   - Filter by 'WS' (WebSocket)")
    print("   - Check if WebSocket connection is established")
    print("   - Look for connection errors or message traffic")
    print()
    
    print("4. 🔍 Django Debugging:")
    print("   - Check Django console for WebSocket connection logs")
    print("   - Look for '🔌 WebSocket connection attempt' messages")
    print("   - Verify auction consumer is receiving connections")
    print()
    
    print("5. 🚀 Quick Fix Commands:")
    print("   # Test with local WebSocket:")
    print("   # 1. Update Flutter constants to use localhost")
    print("   # 2. Run: flutter run -d web-server --web-port 8080")
    print("   # 3. Test bidding functionality")

if __name__ == "__main__":
    print("🔍 WebSocket Connection Tester")
    print("=" * 35)
    
    # Test WebSocket setup
    if not test_websocket_setup():
        print("❌ WebSocket setup failed, exiting")
        sys.exit(1)
    
    # Get WebSocket URLs
    local_url, ngrok_url = get_websocket_urls()
    
    # Test message flow
    test_bid_message_flow()
    
    # Check Flutter config
    check_flutter_websocket_config()
    
    # Provide solutions
    provide_websocket_solutions()
    
    print("\n🎯 Next Steps:")
    print("1. Check Flutter browser console for WebSocket errors")
    print("2. Try testing with local WebSocket URL first")
    print("3. Monitor Django console for connection attempts")
    print("4. Test placing a bid and watch for real-time updates")
