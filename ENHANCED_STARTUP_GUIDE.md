# 🚀 Enhanced Fish Auction Startup Guide

## Overview

This guide covers the enhanced startup process with debug output and iOS device support.

## 📋 Prerequisites

### Required Software
- **Python 3.8+** with pip
- **Redis** (for WebSocket support)
- **ngrok** (for public tunneling)
- **Flutter** (for mobile app)
- **Xcode** (for iOS development)

### Quick Installation (macOS)
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install redis ngrok python3

# Install Flutter
# Download from: https://flutter.dev/docs/get-started/install

# Install Xcode from App Store
```

## 🎯 Step-by-Step Startup

### 1. Start Backend with Debug Output

The enhanced quick start script now shows all debug information in real-time:

```bash
./quick_start_macos.sh
```

**What it does:**
- ✅ Checks all dependencies
- ✅ Starts Redis server
- ✅ Runs Django with verbose output (`-v 2`)
- ✅ Starts Celery with debug logging
- ✅ Launches ngrok with stdout logging
- ✅ Updates Flutter app configuration automatically
- ✅ Checks iOS development setup
- ✅ Offers to auto-start Flutter app

**Debug Features:**
- Real-time server output (no log files)
- Verbose Celery worker logs
- ngrok tunnel status
- Detailed error messages
- iOS development environment check

### 2. Run Flutter App on iOS Device

#### Option A: Auto-start (from quick start script)
When prompted, choose `y` to automatically start the Flutter app.

#### Option B: Manual start (recommended)
```bash
./run_flutter_ios.sh
```

**Flutter iOS Runner Features:**
- 📱 Auto-detects connected iOS devices
- 🔍 Checks Flutter and Xcode setup
- 🧹 Cleans previous builds
- 📦 Gets dependencies automatically
- 🎯 Multiple deployment options
- 🐛 Debug mode with verbose output

#### Option C: Direct Flutter commands
```bash
cd fish_auction_app

# Get dependencies
flutter pub get

# Check devices
flutter devices

# Run on iOS device
flutter run -d ios --verbose

# Run on iOS simulator
flutter run -d ios --simulator --verbose
```

## 🔧 Debug Output Explained

### Django Server Debug
```
HTTP GET /api/auctions/ 200 [0.05, 127.0.0.1:52847]
WebSocket CONNECT /ws/auction/1/ [127.0.0.1:52848]
```

### Celery Worker Debug
```
[2024-01-20 10:30:15,123: INFO/MainProcess] Connected to redis://localhost:6379//
[2024-01-20 10:30:15,456: DEBUG/MainProcess] | Worker: Preparing to run task
```

### ngrok Debug
```
t=2024-01-20T10:30:15+0000 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:8000
```

### Flutter Debug
```
Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).
```

## 📱 iOS Device Setup

### First-Time Setup
1. **Connect iOS device** via USB
2. **Trust this computer** on your device
3. **Enable Developer Mode** (iOS 16+):
   - Settings → Privacy & Security → Developer Mode → On
4. **Add Apple ID** to Xcode (if not done):
   - Xcode → Preferences → Accounts → Add Apple ID

### Troubleshooting iOS Issues

#### Device Not Detected
```bash
# Check if device is connected
flutter devices

# Check iOS development setup
flutter doctor --verbose

# Reset device connection
flutter clean
```

#### Code Signing Issues
1. Open `fish_auction_app/ios/Runner.xcworkspace` in Xcode
2. Select your development team
3. Change bundle identifier if needed
4. Build from Xcode first, then use Flutter

#### Simulator Issues
```bash
# Open iOS Simulator
open -a Simulator

# List available simulators
xcrun simctl list devices

# Boot specific simulator
xcrun simctl boot "iPhone 14"
```

## 🌐 Network Configuration

### ngrok URL Updates
The script automatically updates:
- **Flutter app constants** with new ngrok URL
- **Django ALLOWED_HOSTS** for external access
- **WebSocket URLs** for real-time features

### Manual URL Update
If needed, manually update `fish_auction_app/lib/constants/app_constants.dart`:
```dart
static const String baseUrl = 'https://your-ngrok-url.ngrok.io/api';
static const String wsUrl = 'wss://your-ngrok-url.ngrok.io/ws';
```

## 🔍 Monitoring & Debugging

### Real-Time Monitoring
- **Django**: All HTTP requests and responses
- **WebSocket**: Connection status and messages
- **Celery**: Task execution and results
- **ngrok**: Tunnel status and traffic
- **Flutter**: Hot reload and device logs

### Log Locations (if needed)
- Django: Real-time output (no file)
- Celery: Real-time output (no file)
- ngrok: Real-time output (no file)
- Flutter: Device logs in Xcode Console

### Health Checks
```bash
# Check if services are running
lsof -i :8000  # Django
lsof -i :6379  # Redis
lsof -i :4040  # ngrok

# Test API endpoints
curl http://localhost:8000/api/
curl http://localhost:8000/api/auctions/

# Test WebSocket
# Use browser console: new WebSocket('ws://localhost:8000/ws/auction/1/')
```

## 🛑 Stopping Services

### Graceful Shutdown
Press `Ctrl+C` in the terminal running the quick start script.

### Manual Cleanup
```bash
# Kill specific processes
pkill -f "daphne.*fish_auction"
pkill -f "celery.*fish_auction"
pkill -f "ngrok http 8000"
pkill -f "flutter run"

# Stop Redis
brew services stop redis
```

## 🎯 Quick Commands Summary

```bash
# Start everything with debug output
./quick_start_macos.sh

# Run Flutter on iOS (separate terminal)
./run_flutter_ios.sh

# Check status
flutter devices
curl http://localhost:8000/api/

# Stop everything
# Press Ctrl+C in quick start terminal
```

## 🔧 Advanced Options

### Custom Django Settings
```bash
# Run with specific settings
export DJANGO_SETTINGS_MODULE=fish_auction.settings_debug
./quick_start_macos.sh
```

### Flutter Build Modes
```bash
# Debug mode (default)
flutter run -d ios --debug

# Profile mode
flutter run -d ios --profile

# Release mode
flutter run -d ios --release
```

### Custom ngrok Configuration
```bash
# Use custom ngrok config
ngrok http 8000 --config ~/.ngrok2/ngrok.yml
```

This enhanced setup provides comprehensive debugging capabilities and streamlined iOS deployment for your Fish Auction app! 🎉
