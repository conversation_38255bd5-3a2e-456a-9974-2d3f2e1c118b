#!/usr/bin/env python3

import os
import sys
import django
import requests
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import DocumentVerificationRequest
from auctions.models import Auction, FishCategory
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class SellerVerificationTester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.session = requests.Session()
        self.test_seller = None
        self.admin_user = None
        self.seller_token = None
        self.admin_token = None
        
    def setup_test_users(self):
        """Create or get test users"""
        try:
            self.test_seller = User.objects.get(username='verification_test_seller')
            print(f"✅ Using existing seller: {self.test_seller.username}")
        except User.DoesNotExist:
            self.test_seller = User.objects.create_user(
                username='verification_test_seller',
                email='<EMAIL>',
                password='testpass123',
                phone_number='+1234567890',
                user_type='seller',
                wallet_balance=Decimal('100.00')
            )
            print(f"✅ Created seller: {self.test_seller.username}")
        
        # Reset verification status for testing
        self.test_seller.is_verified = False
        self.test_seller.government_id_status = 'not_submitted'
        self.test_seller.hunting_approval_status = 'not_submitted'
        self.test_seller.save()
        
        try:
            self.admin_user = User.objects.get(username='verification_admin')
            print(f"✅ Using existing admin: {self.admin_user.username}")
        except User.DoesNotExist:
            self.admin_user = User.objects.create_user(
                username='verification_admin',
                email='<EMAIL>',
                password='adminpass123',
                is_staff=True,
                is_superuser=True
            )
            print(f"✅ Created admin: {self.admin_user.username}")
    
    def authenticate_seller(self):
        """Authenticate seller"""
        try:
            response = self.session.post(f"{self.base_url}/auth/login/", json={
                'username': 'verification_test_seller',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                data = response.json()
                self.seller_token = data['tokens']['access']
                print("✅ Seller authentication successful")
                return True
            else:
                print(f"❌ Seller authentication failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Seller authentication error: {e}")
            return False
    
    def authenticate_admin(self):
        """Authenticate admin"""
        try:
            admin_session = requests.Session()
            response = admin_session.post(f"{self.base_url}/auth/login/", json={
                'username': 'verification_admin',
                'password': 'adminpass123'
            })
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data['tokens']['access']
                print("✅ Admin authentication successful")
                return admin_session
            else:
                print(f"❌ Admin authentication failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Admin authentication error: {e}")
            return None
    
    def test_document_submission(self):
        """Test document submission by seller"""
        print(f"\n📄 Testing document submission...")
        
        # Set seller token
        self.session.headers.update({'Authorization': f'Bearer {self.seller_token}'})
        
        try:
            # Create fake documents
            gov_id_file = SimpleUploadedFile(
                "government_id.jpg",
                b"fake government id content",
                content_type="image/jpeg"
            )
            
            hunting_approval_file = SimpleUploadedFile(
                "hunting_approval.pdf",
                b"fake hunting approval content",
                content_type="application/pdf"
            )
            
            # Submit government ID
            gov_id_data = {
                'document_type': 'government_id',
            }
            
            files = {'document_file': ('government_id.jpg', b"fake government id content", 'image/jpeg')}
            
            response = self.session.post(
                f"{self.base_url}/auth/documents/submit/",
                data=gov_id_data,
                files=files
            )
            
            print(f"Government ID submission: {response.status_code}")
            if response.status_code == 201:
                print("✅ Government ID submitted successfully")
                gov_id_success = True
            else:
                print(f"❌ Government ID submission failed: {response.text}")
                gov_id_success = False
            
            # Submit hunting approval
            hunting_data = {
                'document_type': 'hunting_approval',
            }
            
            files = {'document_file': ('hunting_approval.pdf', b"fake hunting approval content", 'application/pdf')}
            
            response = self.session.post(
                f"{self.base_url}/auth/documents/submit/",
                data=hunting_data,
                files=files
            )
            
            print(f"Hunting approval submission: {response.status_code}")
            if response.status_code == 201:
                print("✅ Hunting approval submitted successfully")
                hunting_success = True
            else:
                print(f"❌ Hunting approval submission failed: {response.text}")
                hunting_success = False
            
            return gov_id_success and hunting_success
            
        except Exception as e:
            print(f"❌ Document submission error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_admin_verification_interface(self):
        """Test admin verification interface"""
        print(f"\n🔍 Testing admin verification interface...")
        
        admin_session = self.authenticate_admin()
        if not admin_session:
            return False
        
        admin_session.headers.update({'Authorization': f'Bearer {self.admin_token}'})
        
        try:
            # Get pending verifications
            response = admin_session.get(f"{self.base_url}/auth/documents/pending/")
            
            print(f"Pending verifications response: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                pending_requests = data.get('results', data) if isinstance(data, dict) and 'results' in data else data
                
                print(f"✅ Found {len(pending_requests)} pending verification requests")
                
                # Find our seller's requests
                seller_requests = []
                for req in pending_requests:
                    if isinstance(req, dict) and 'user' in req:
                        if isinstance(req['user'], dict) and req['user'].get('username') == 'verification_test_seller':
                            seller_requests.append(req)
                        elif isinstance(req['user'], int) and req['user'] == self.test_seller.id:
                            seller_requests.append(req)
                
                if seller_requests:
                    print(f"✅ Found {len(seller_requests)} requests from test seller")
                    
                    # Approve the first request
                    first_request = seller_requests[0]
                    request_id = first_request['id']
                    
                    approval_response = admin_session.patch(
                        f"{self.base_url}/auth/documents/review/{request_id}/",
                        json={
                            'status': 'approved',
                            'review_notes': 'Test approval - document looks good'
                        }
                    )
                    
                    print(f"Approval response: {approval_response.status_code}")
                    
                    if approval_response.status_code == 200:
                        print("✅ Document approved successfully")
                        return True
                    else:
                        print(f"❌ Document approval failed: {approval_response.text}")
                        return False
                else:
                    print("❌ No requests found from test seller")
                    return False
            else:
                print(f"❌ Failed to get pending verifications: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Admin verification interface error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_seller_verification_status(self):
        """Test seller verification status check"""
        print(f"\n✅ Testing seller verification status...")
        
        try:
            # Refresh seller from database
            self.test_seller.refresh_from_db()
            
            print(f"Seller verification status:")
            print(f"   Is verified: {self.test_seller.is_verified}")
            print(f"   Government ID status: {self.test_seller.government_id_status}")
            print(f"   Hunting approval status: {self.test_seller.hunting_approval_status}")
            
            # Check verification requests
            verification_requests = DocumentVerificationRequest.objects.filter(user=self.test_seller)
            print(f"   Total verification requests: {verification_requests.count()}")
            
            for request in verification_requests:
                print(f"   - {request.document_type}: {request.status}")
            
            return True
            
        except Exception as e:
            print(f"❌ Verification status check error: {e}")
            return False
    
    def test_auction_creation_restriction(self):
        """Test that unverified sellers cannot create auctions"""
        print(f"\n🚫 Testing auction creation restriction for unverified sellers...")
        
        self.session.headers.update({'Authorization': f'Bearer {self.seller_token}'})
        
        try:
            # Get or create fish category
            fish_category, _ = FishCategory.objects.get_or_create(
                name="Bass"
            )
            
            # Try to create auction
            auction_data = {
                'title': 'Test Bass Auction',
                'description': 'Test auction by unverified seller',
                'fish_category': fish_category.id,
                'fish_type': 'Bass',
                'quantity': 1,
                'weight': 3.5,
                'catch_date': timezone.now().date().isoformat(),
                'catch_location': 'Test Lake',
                'starting_price': 25.00,
                'target_price': 40.00,
                'auction_type': 'live',
                'start_time': (timezone.now() + timedelta(hours=1)).isoformat(),
                'end_time': (timezone.now() + timedelta(hours=25)).isoformat(),
            }

            # Create a proper image file for testing
            from PIL import Image
            import io

            # Create a simple test image
            img = Image.new('RGB', (100, 100), color='red')
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='JPEG')
            img_bytes.seek(0)

            files = {'main_image': ('test_bass.jpg', img_bytes.getvalue(), 'image/jpeg')}
            
            response = self.session.post(
                f"{self.base_url}/auctions/create/",
                data=auction_data,
                files=files
            )
            
            print(f"Auction creation response: {response.status_code}")
            
            if response.status_code == 403 or response.status_code == 400:
                response_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                error_message = response_data.get('error', response.text)
                
                if 'verif' in error_message.lower():
                    print("✅ Auction creation properly restricted for unverified seller")
                    print(f"   Error message: {error_message}")
                    return True
                else:
                    print(f"❌ Auction creation failed for different reason: {error_message}")
                    return False
            else:
                print(f"❌ Auction creation should have been restricted but got: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Auction creation restriction test error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_tests(self):
        """Run all seller verification tests"""
        print("🚀 Starting Seller Verification Tests")
        print("=" * 50)
        
        # Setup
        self.setup_test_users()
        
        if not self.authenticate_seller():
            return
        
        test_results = []
        
        # Test 1: Document submission
        print("\n📝 Test 1: Document submission by seller")
        result1 = self.test_document_submission()
        test_results.append(("Document Submission", result1))
        
        # Test 2: Admin verification interface
        print("\n📝 Test 2: Admin verification interface")
        result2 = self.test_admin_verification_interface()
        test_results.append(("Admin Verification Interface", result2))
        
        # Test 3: Verification status check
        print("\n📝 Test 3: Seller verification status")
        result3 = self.test_seller_verification_status()
        test_results.append(("Verification Status Check", result3))
        
        # Test 4: Auction creation restriction
        print("\n📝 Test 4: Auction creation restriction")
        result4 = self.test_auction_creation_restriction()
        test_results.append(("Auction Creation Restriction", result4))
        
        # Results summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            print("🎉 All seller verification tests PASSED!")
        else:
            print("⚠️ Some seller verification tests FAILED!")

if __name__ == "__main__":
    tester = SellerVerificationTester()
    tester.run_tests()
