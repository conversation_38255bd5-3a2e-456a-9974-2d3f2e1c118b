version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fish_auction
      POSTGRES_USER: fish_user
      POSTGRES_PASSWORD: fish_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  web:
    build: .
    command: daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application
    volumes:
      - .:/app
      - media_files:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DATABASE_URL=********************************************/fish_auction
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped

  celery-worker:
    build: .
    command: celery -A fish_auction worker --loglevel=info
    volumes:
      - .:/app
      - media_files:/app/media
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DATABASE_URL=********************************************/fish_auction
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped

  celery-beat:
    build: .
    command: celery -A fish_auction beat --loglevel=info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DATABASE_URL=********************************************/fish_auction
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  media_files:
