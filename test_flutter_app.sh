#!/bin/bash

# Script to test Flutter app configuration and connection
echo "🧪 Testing Flutter App Configuration"
echo "====================================="

# Check if Flutter app directory exists
if [ ! -d "fish_auction_app" ]; then
    echo "❌ Flutter app directory not found"
    exit 1
fi

# Get current ngrok URL
CURRENT_NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data.get('tunnels', []):
        if tunnel.get('proto') == 'https':
            print(tunnel['public_url'])
            break
except:
    pass
")

if [ -z "$CURRENT_NGROK_URL" ]; then
    echo "❌ Could not get current ngrok URL"
    echo "   Make sure ngrok is running: ngrok http 8000"
    exit 1
fi

echo "🌐 Current ngrok URL: $CURRENT_NGROK_URL"

# Check Flutter app configuration
FLUTTER_CONSTANTS="fish_auction_app/lib/constants/app_constants.dart"
if [ ! -f "$FLUTTER_CONSTANTS" ]; then
    echo "❌ Flutter constants file not found: $FLUTTER_CONSTANTS"
    exit 1
fi

# Extract configured URL from Flutter app
FLUTTER_URL=$(grep "baseUrl = " "$FLUTTER_CONSTANTS" | sed "s/.*baseUrl = '\\([^']*\\)'.*/\\1/" | sed 's|/api||')

echo "📱 Flutter configured URL: $FLUTTER_URL"

# Compare URLs
if [ "$CURRENT_NGROK_URL" = "$FLUTTER_URL" ]; then
    echo "✅ Flutter app URL matches current ngrok URL"
else
    echo "❌ URL mismatch detected!"
    echo "   Current ngrok: $CURRENT_NGROK_URL"
    echo "   Flutter config: $FLUTTER_URL"
    echo ""
    echo "🔧 Fixing URL mismatch..."
    
    # Update Flutter constants
    NGROK_HOST=$(echo "$CURRENT_NGROK_URL" | sed 's|https://||' | sed 's|http://||')
    sed -i '' "s|static const String baseUrl = 'https://.*\.ngrok-free\.app/api';|static const String baseUrl = 'https://$NGROK_HOST/api';|g" "$FLUTTER_CONSTANTS"
    sed -i '' "s|static const String wsUrl = 'wss://.*\.ngrok-free\.app/ws';|static const String wsUrl = 'wss://$NGROK_HOST/ws';|g" "$FLUTTER_CONSTANTS"
    
    echo "✅ Flutter app URL updated to: $CURRENT_NGROK_URL"
fi

# Test API connection
echo ""
echo "🧪 Testing API connection..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$CURRENT_NGROK_URL/api/auth/login/" \
    -H "Content-Type: application/json" \
    -H "ngrok-skip-browser-warning: true" \
    -d '{"username":"test","password":"test"}')

if [ "$HTTP_CODE" = "400" ]; then
    echo "✅ API connection working (HTTP 400 expected for invalid credentials)"
elif [ "$HTTP_CODE" = "200" ]; then
    echo "✅ API connection working (HTTP 200)"
else
    echo "❌ API connection failed (HTTP $HTTP_CODE)"
    echo "   Check if Django server is running"
    exit 1
fi

# Test with valid credentials
echo ""
echo "🔑 Testing login with valid credentials..."
LOGIN_RESPONSE=$(curl -s "$CURRENT_NGROK_URL/api/auth/login/" \
    -H "Content-Type: application/json" \
    -H "ngrok-skip-browser-warning: true" \
    -d '{"username":"testuser","password":"testpass123"}')

if echo "$LOGIN_RESPONSE" | grep -q '"tokens"'; then
    echo "✅ Login test successful!"
    USERNAME=$(echo "$LOGIN_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data['user']['username'])
except:
    print('Unknown')
")
    echo "   Logged in as: $USERNAME"
else
    echo "❌ Login test failed"
    echo "   Response: $LOGIN_RESPONSE"
fi

echo ""
echo "🚀 Ready to start Flutter app!"
echo "================================"
echo "Run these commands:"
echo "1. cd fish_auction_app"
echo "2. flutter pub get"
echo "3. flutter run -d web-server --web-port 8080"
echo ""
echo "🔑 Test credentials:"
echo "   Username: testuser"
echo "   Password: testpass123"
echo ""
echo "📱 Or for iOS simulator:"
echo "   flutter run -d ios"
echo ""
echo "🌐 URLs configured:"
echo "   API: $CURRENT_NGROK_URL/api"
echo "   WebSocket: ${CURRENT_NGROK_URL/https/wss}/ws"
