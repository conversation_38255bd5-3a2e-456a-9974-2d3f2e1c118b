#!/usr/bin/env python3
"""
Test script to verify payment and delivery notifications work
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction
from accounts.models import User
from payments.models import Payment
from delivery.models import Delivery
from notifications.models import Notification
from payments.services import PaymentService
from notifications.services import NotificationService

def test_payment_notification():
    """Test payment notification to seller"""
    print("💰 Testing Payment Notification")
    print("=" * 40)
    
    try:
        # Get test data
        auction = Auction.objects.get(title__icontains='samakat kos ibrahim')
        seller = auction.seller
        buyer = User.objects.get(username='rabie4')
        
        print(f"🎣 Auction: {auction.title}")
        print(f"👤 Seller: {seller.username}")
        print(f"👤 Buyer: {buyer.username}")
        
        # Count initial notifications for seller
        initial_count = Notification.objects.filter(recipient=seller).count()
        print(f"📱 Initial seller notifications: {initial_count}")
        
        # Create a test payment
        payment_service = PaymentService()
        payment = payment_service.create_payment(auction, buyer)
        
        print(f"📄 Created payment: ${payment.amount}")
        print(f"   Seller amount: ${payment.seller_amount}")
        
        # Test payment notification directly
        print("\n📨 Testing payment notification...")
        result = payment_service.send_payment_notification(payment)
        
        # Check notifications
        final_count = Notification.objects.filter(recipient=seller).count()
        added_count = final_count - initial_count
        
        print(f"\n📊 Results:")
        print(f"   Initial notifications: {initial_count}")
        print(f"   Final notifications: {final_count}")
        print(f"   Added notifications: {added_count}")
        
        # Show recent notifications
        recent = Notification.objects.filter(recipient=seller).order_by('-created_at')[:3]
        print(f"\n📱 Recent seller notifications:")
        for notif in recent:
            print(f"   {notif.created_at.strftime('%H:%M:%S')}: {notif.title} ({notif.channel}) - {notif.status}")
        
        return added_count > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_notification():
    """Test delivery notification to buyer"""
    print("\n📦 Testing Delivery Notification")
    print("=" * 40)
    
    try:
        # Get test data
        auction = Auction.objects.get(title__icontains='samakat kos ibrahim')
        buyer = User.objects.get(username='rabie4')
        
        # Get or create delivery
        delivery = Delivery.objects.filter(auction=auction).first()
        if not delivery:
            print("📝 Creating test delivery...")
            delivery = Delivery.objects.create(
                auction=auction,
                buyer=buyer,
                seller=auction.seller,
                status='pending'
            )
        
        print(f"📦 Delivery: {delivery.id}")
        print(f"👤 Buyer: {buyer.username}")
        print(f"📊 Current status: {delivery.status}")
        
        # Count initial notifications for buyer
        initial_count = Notification.objects.filter(recipient=buyer).count()
        print(f"📱 Initial buyer notifications: {initial_count}")
        
        # Test delivery notification directly
        print("\n📨 Testing delivery notification...")
        
        from notifications.services import NotificationService
        notification_service = NotificationService()
        
        notifications = notification_service.send_notification(
            user=buyer,
            notification_type='delivery_update',
            context={
                'delivery': delivery,
                'auction': auction,
                'status': 'picked_up',
                'message': 'Package picked up by delivery service',
                'status_display': 'Picked Up',
                'delivery_status': 'Picked Up'
            }
        )
        
        print(f"📦 Notifications sent: {len(notifications) if notifications else 0}")
        if notifications:
            for notif in notifications:
                print(f"   📱 {notif.channel}: {notif.status} - {notif.title}")
        
        # Check notifications
        final_count = Notification.objects.filter(recipient=buyer).count()
        added_count = final_count - initial_count
        
        print(f"\n📊 Results:")
        print(f"   Initial notifications: {initial_count}")
        print(f"   Final notifications: {final_count}")
        print(f"   Added notifications: {added_count}")
        
        return added_count > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🧪 Testing Notification Flows")
    print("=" * 50)
    
    payment_success = test_payment_notification()
    delivery_success = test_delivery_notification()
    
    print(f"\n📊 Final Results:")
    print(f"   Payment notifications: {'✅ Working' if payment_success else '❌ Failed'}")
    print(f"   Delivery notifications: {'✅ Working' if delivery_success else '❌ Failed'}")
    
    if payment_success and delivery_success:
        print("\n🎉 All notification flows working!")
    else:
        print("\n❌ Some notification flows need fixing!")
