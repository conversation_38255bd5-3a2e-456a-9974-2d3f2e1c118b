# 🐟 Fish Auction Platform - Web Frontend

A simple, responsive web frontend for testing the Fish Auction Platform API. Built with vanilla HTML, CSS, and JavaScript - no build tools required!

## 🚀 Quick Start

1. **Make sure the Django backend is running:**
   ```bash
   python manage.py runserver
   ```

2. **Open the frontend:**
   - Simply open `index.html` in your web browser
   - Or serve it with a simple HTTP server:
     ```bash
     # Python 3
     python -m http.server 3000
     
     # Node.js (if available)
     npx serve .
     ```

3. **Start testing!**
   - Use the quick login buttons to test different user types
   - Browse auctions, manage wallet, place bids, and more!

## 🎯 Features

### ✅ **Implemented Features**
- **User Authentication**: Login with test users
- **Dashboard**: Overview of auctions, wallet balance, and quick actions
- **Auction Browsing**: View all auctions with filtering and search
- **Quick Bidding**: Place bids directly from auction list
- **Wallet Management**: View balance, transaction history, and simulate top-ups
- **Profile Management**: View and edit user profile information
- **Responsive Design**: Works on desktop, tablet, and mobile devices

### 🔧 **Test Users Available**
- **Buyer1**: Username: `buyer1`, Password: `testpass123`, Wallet: $500
- **Buyer2**: Username: `buyer2`, Password: `testpass123`, Wallet: $750
- **Seller1**: Username: `seller1`, Password: `testpass123`, Wallet: $1000
- **Seller2**: Username: `seller2`, Password: `testpass123`, Wallet: $1500
- **Broker1**: Username: `broker1`, Password: `testpass123`, Wallet: $2000

## 🎨 User Interface

### **Modern Design Features**
- **Gradient backgrounds** and smooth animations
- **Card-based layout** for better content organization
- **Responsive grid system** that adapts to screen size
- **Status badges** for auction states (Live, Ended, Scheduled)
- **Interactive buttons** with hover effects
- **Loading animations** for better user experience

### **Color Coding**
- **Sellers**: Green theme (🐟)
- **Buyers**: Blue theme (🛒)
- **Brokers**: Purple theme (🤝)
- **Live Auctions**: Green status badge
- **Ended Auctions**: Gray status badge
- **Scheduled Auctions**: Yellow status badge

## 📱 Responsive Design

The frontend is fully responsive and works on:
- **Desktop** (1200px+)
- **Tablet** (768px - 1199px)
- **Mobile** (< 768px)

## 🔧 API Integration

### **Endpoints Used**
- `POST /api/auth/login/` - User authentication
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/` - Update user profile
- `GET /api/auctions/` - List auctions with filtering
- `GET /api/auctions/categories/` - Get fish categories
- `POST /api/auctions/{id}/bid/` - Place bid on auction
- `GET /api/payments/wallet/balance/` - Get wallet balance
- `GET /api/payments/wallet/transactions/` - Get transaction history

### **Authentication**
- Uses JWT tokens stored in localStorage
- Automatic token inclusion in API requests
- Persistent login across browser sessions

## 🎮 How to Test

### **1. Login as Different User Types**
```javascript
// Use quick login buttons or manual login
- Buyer: Can browse auctions, place bids, manage wallet
- Seller: Can view auctions, see dashboard (create auction coming soon)
- Broker: Full access to platform features
```

### **2. Browse Auctions**
```javascript
// Test auction filtering and search
- Search by fish type or title
- Filter by status (Live, Scheduled, Ended)
- Filter by fish category
- View auction details
```

### **3. Place Bids**
```javascript
// Test bidding functionality
- Use "Quick Bid" button for instant bidding
- Bid amount automatically calculated (current price + increment)
- Real-time feedback on bid success/failure
```

### **4. Manage Wallet**
```javascript
// Test wallet operations
- View current balance
- Simulate wallet top-up (demo mode)
- View transaction history
- Quick amount buttons for easy top-up
```

### **5. Profile Management**
```javascript
// Test profile features
- View user information and stats
- Edit profile details
- See account verification status
- Update notification preferences
```

## 🔍 Testing Scenarios

### **Scenario 1: Buyer Journey**
1. Login as `buyer1`
2. Browse available auctions
3. Filter by "Live" status
4. Place a bid on an active auction
5. Check wallet balance and transaction history
6. Update profile information

### **Scenario 2: Seller Journey**
1. Login as `seller1`
2. View dashboard with seller stats
3. Browse all auctions (including own)
4. Check wallet balance from sales
5. View profile with seller-specific information

### **Scenario 3: Multi-User Bidding**
1. Login as `buyer1`, place a bid
2. Logout and login as `buyer2`
3. Place a higher bid on the same auction
4. Switch back to `buyer1` to see updated auction

## 🚧 Limitations (Demo Version)

- **Wallet Top-up**: Simulated only (no real payment processing)
- **Real-time Updates**: Manual refresh required for auction updates
- **Image Upload**: Uses placeholder images
- **Create Auction**: UI placeholder (functionality in backend)
- **WebSocket**: Not implemented in this simple frontend

## 🔮 Future Enhancements

- **Real-time Bidding**: WebSocket integration for live updates
- **Image Upload**: File upload for auction images
- **Create Auction Form**: Complete auction creation workflow
- **Advanced Filtering**: More sophisticated search and filter options
- **Notifications**: In-app notification system
- **Mobile App**: React Native or Flutter mobile version

## 🐛 Troubleshooting

### **Common Issues**

1. **CORS Errors**
   - Make sure Django backend is running with CORS enabled
   - Check that `CORS_ALLOWED_ORIGINS` includes your frontend URL

2. **API Connection Failed**
   - Verify backend is running on `http://localhost:8000`
   - Check browser console for network errors

3. **Login Issues**
   - Ensure sample data is created: `python manage.py create_sample_data`
   - Try the quick login buttons instead of manual entry

4. **Blank Pages**
   - Check browser console for JavaScript errors
   - Ensure all files are in the same directory

## 📞 Support

This is a demo frontend for testing the Fish Auction Platform API. For issues:
1. Check the browser console for errors
2. Verify the Django backend is running
3. Ensure sample data exists in the database
4. Try refreshing the page or clearing localStorage

---

**🎉 Enjoy testing the Fish Auction Platform!** 

This frontend demonstrates the core functionality of the platform and provides a great way to interact with the API endpoints.
