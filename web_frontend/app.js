// Fish Auction Platform - Frontend JavaScript

const API_BASE_URL = 'http://localhost:8000/api';
let currentUser = null;
let authToken = null;

// Utility Functions
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
    setTimeout(() => {
        alertContainer.innerHTML = '';
    }, 5000);
}

function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="loading">Loading...</div>';
}

function formatCurrency(amount) {
    return `$${parseFloat(amount).toFixed(2)}`;
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
}

// API Functions
async function apiCall(endpoint, method = 'GET', data = null) {
    const config = {
        method,
        headers: {
            'Content-Type': 'application/json',
        },
    };

    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
        config.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.detail || result.error || 'API call failed');
        }
        
        return result;
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}

// Authentication Functions
async function login(username, password) {
    try {
        const result = await apiCall('/auth/login/', 'POST', { username, password });
        authToken = result.tokens.access;
        currentUser = result.user;
        localStorage.setItem('authToken', authToken);
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        showDashboard();
        showAlert('Login successful!');
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

async function quickLogin(username) {
    await login(username, 'testpass123');
}

function logout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    showLogin();
    showAlert('Logged out successfully!');
}

// Navigation Functions
function showSection(sectionId) {
    const sections = ['loginSection', 'dashboardSection', 'auctionsSection', 'walletSection', 'profileSection'];
    sections.forEach(id => {
        document.getElementById(id).classList.add('hidden');
    });
    document.getElementById(sectionId).classList.remove('hidden');
}

function showLogin() {
    showSection('loginSection');
    updateNavigation();
}

function showDashboard() {
    showSection('dashboardSection');
    updateNavigation();
    loadDashboard();
}

function showAuctions() {
    showSection('auctionsSection');
    loadAuctions();
}

function showWallet() {
    showSection('walletSection');
    loadWallet();
}

function showProfile() {
    showSection('profileSection');
    loadProfile();
}

function updateNavigation() {
    const navLinks = document.getElementById('navLinks');
    if (currentUser) {
        navLinks.innerHTML = `
            <a href="#" onclick="showDashboard()">Dashboard</a>
            <a href="#" onclick="showAuctions()">Auctions</a>
            <a href="#" onclick="showWallet()">Wallet</a>
            <a href="#" onclick="showProfile()">Profile</a>
            <span style="margin-left: 20px;">Welcome, ${currentUser.first_name || currentUser.username}!</span>
            <span class="user-badge">${currentUser.user_type.toUpperCase()}</span>
            <a href="#" onclick="logout()" style="margin-left: 20px; background: rgba(255,255,255,0.2);">Logout</a>
        `;
    } else {
        navLinks.innerHTML = '<a href="#" onclick="showLogin()">Login</a>';
    }
}

// Dashboard Functions
async function loadDashboard() {
    try {
        document.getElementById('welcomeMessage').textContent = 
            `Welcome back, ${currentUser.first_name || currentUser.username}! 🎉`;

        // Show create auction button for sellers
        if (currentUser.user_type === 'seller' || currentUser.user_type === 'admin') {
            document.getElementById('createAuctionBtn').classList.remove('hidden');
        }

        // Load wallet balance
        const walletData = await apiCall('/payments/wallet/balance/');
        
        // Load auctions count
        const auctionsData = await apiCall('/auctions/');
        
        // Update stats
        const statsGrid = document.getElementById('statsGrid');
        statsGrid.innerHTML = `
            <div class="stat-card">
                <h3>${formatCurrency(walletData.balance)}</h3>
                <p>Wallet Balance</p>
            </div>
            <div class="stat-card">
                <h3>${auctionsData.count || 0}</h3>
                <p>Active Auctions</p>
            </div>
            <div class="stat-card">
                <h3>${currentUser.user_type === 'seller' ? 'Seller' : 'Buyer'}</h3>
                <p>Account Type</p>
            </div>
        `;

        // Load recent auctions
        loadRecentAuctions(auctionsData.results || []);
        
    } catch (error) {
        showAlert('Failed to load dashboard data', 'error');
    }
}

function loadRecentAuctions(auctions) {
    const container = document.getElementById('recentAuctions');
    
    if (auctions.length === 0) {
        container.innerHTML = '<p style="grid-column: 1/-1; text-align: center; color: #6c757d;">No auctions available at the moment.</p>';
        return;
    }

    container.innerHTML = auctions.slice(0, 6).map(auction => `
        <div class="auction-card">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpc2ggSW1hZ2U8L3RleHQ+PC9zdmc+" alt="${auction.title}">
            <div class="auction-card-body">
                <h4>${auction.title}</h4>
                <p><strong>Fish Type:</strong> ${auction.fish_type}</p>
                <p><strong>Weight:</strong> ${auction.weight} kg</p>
                <p><strong>Current Price:</strong> <span class="price-highlight">${formatCurrency(auction.current_price)}</span></p>
                <p><strong>Status:</strong> <span class="status-badge status-${auction.status}">${auction.status}</span></p>
                <button onclick="viewAuctionDetails(${auction.id})" class="btn">View Details</button>
            </div>
        </div>
    `).join('');
}

// Auctions Functions
async function loadAuctions() {
    try {
        showLoading('auctionsList');
        
        // Load categories for filter
        const categoriesData = await apiCall('/auctions/categories/');
        const categoryFilter = document.getElementById('categoryFilter');
        categoryFilter.innerHTML = '<option value="">All Categories</option>' +
            categoriesData.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('');
        
        // Load auctions
        const auctionsData = await apiCall('/auctions/');
        displayAuctions(auctionsData.results || []);
        
    } catch (error) {
        showAlert('Failed to load auctions', 'error');
    }
}

function displayAuctions(auctions) {
    const container = document.getElementById('auctionsList');
    
    if (auctions.length === 0) {
        container.innerHTML = '<p style="grid-column: 1/-1; text-align: center; color: #6c757d;">No auctions found.</p>';
        return;
    }

    container.innerHTML = auctions.map(auction => `
        <div class="auction-card">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpc2ggSW1hZ2U8L3RleHQ+PC9zdmc+" alt="${auction.title}">
            <div class="auction-card-body">
                <h4>${auction.title}</h4>
                <p><strong>Fish Type:</strong> ${auction.fish_type}</p>
                <p><strong>Weight:</strong> ${auction.weight} kg</p>
                <p><strong>Current Price:</strong> <span class="price-highlight">${formatCurrency(auction.current_price)}</span></p>
                <p><strong>Bids:</strong> ${auction.total_bids}</p>
                <p><strong>Status:</strong> <span class="status-badge status-${auction.status}">${auction.status}</span></p>
                <p><strong>Seller:</strong> ${auction.seller.username}</p>
                <div style="margin-top: 15px;">
                    <button onclick="viewAuctionDetails(${auction.id})" class="btn">View Details</button>
                    ${canBidOnAuction(auction) ? `<button onclick="quickBid(${auction.id})" class="btn btn-success">Quick Bid</button>` : ''}
                </div>
            </div>
        </div>
    `).join('');
}

function canBidOnAuction(auction) {
    return currentUser.user_type === 'buyer' && 
           auction.status === 'live' && 
           auction.seller.id !== currentUser.id;
}

async function quickBid(auctionId) {
    try {
        const auction = await apiCall(`/auctions/${auctionId}/`);
        const bidAmount = (parseFloat(auction.current_price) + parseFloat(auction.bid_increment)).toFixed(2);
        
        if (confirm(`Place a bid of ${formatCurrency(bidAmount)} on "${auction.title}"?`)) {
            await apiCall(`/auctions/${auctionId}/bid/`, 'POST', { amount: bidAmount });
            showAlert('Bid placed successfully!');
            loadAuctions(); // Refresh auctions
        }
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

function viewAuctionDetails(auctionId) {
    showAlert(`Auction details for ID ${auctionId} - Feature coming soon!`);
}

async function filterAuctions() {
    try {
        const search = document.getElementById('searchInput').value;
        const status = document.getElementById('statusFilter').value;
        const category = document.getElementById('categoryFilter').value;
        
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (status) params.append('status', status);
        if (category) params.append('fish_category', category);
        
        const auctionsData = await apiCall(`/auctions/?${params.toString()}`);
        displayAuctions(auctionsData.results || []);
        
    } catch (error) {
        showAlert('Failed to filter auctions', 'error');
    }
}

// Wallet Functions
async function loadWallet() {
    try {
        const [balanceData, transactionsData] = await Promise.all([
            apiCall('/payments/wallet/balance/'),
            apiCall('/payments/wallet/transactions/')
        ]);
        
        document.getElementById('walletBalance').textContent = formatCurrency(balanceData.balance);
        
        const transactionHistory = document.getElementById('transactionHistory');
        const transactions = transactionsData.results || [];
        
        if (transactions.length === 0) {
            transactionHistory.innerHTML = '<p style="text-align: center; color: #6c757d;">No transactions yet.</p>';
        } else {
            transactionHistory.innerHTML = transactions.slice(0, 10).map(transaction => `
                <div style="padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                    <div>
                        <strong>${transaction.transaction_type.replace('_', ' ').toUpperCase()}</strong>
                        <br>
                        <small style="color: #6c757d;">${formatDateTime(transaction.created_at)}</small>
                    </div>
                    <div style="text-align: right;">
                        <strong style="color: ${transaction.transaction_type === 'deposit' ? '#28a745' : '#dc3545'};">
                            ${transaction.transaction_type === 'deposit' ? '+' : '-'}${formatCurrency(transaction.amount)}
                        </strong>
                    </div>
                </div>
            `).join('');
        }
        
    } catch (error) {
        showAlert('Failed to load wallet data', 'error');
    }
}

function setTopUpAmount(amount) {
    document.getElementById('topUpAmount').value = amount;
}

// Profile Functions
async function loadProfile() {
    try {
        const userData = await apiCall('/auth/profile/');
        
        document.getElementById('userFullName').textContent = 
            `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || userData.username;
        document.getElementById('userUsername').textContent = `@${userData.username}`;
        document.getElementById('userTypeBadge').textContent = userData.user_type.toUpperCase();
        document.getElementById('memberSince').textContent = formatDate(userData.date_joined);
        document.getElementById('profileWalletBalance').textContent = formatCurrency(userData.wallet_balance);
        document.getElementById('verifiedStatus').textContent = userData.is_verified ? '✅ Yes' : '❌ No';
        
        // Populate form
        document.getElementById('firstName').value = userData.first_name || '';
        document.getElementById('lastName').value = userData.last_name || '';
        document.getElementById('email').value = userData.email || '';
        document.getElementById('phoneNumber').value = userData.phone_number || '';
        
        // Set avatar color based on user type
        const avatar = document.getElementById('userAvatar');
        const colors = {
            seller: '#28a745',
            buyer: '#007bff',
            broker: '#6f42c1',
            service_provider: '#fd7e14',
            admin: '#dc3545'
        };
        avatar.style.background = colors[userData.user_type] || '#6c757d';
        
    } catch (error) {
        showAlert('Failed to load profile data', 'error');
    }
}

// Event Listeners
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    await login(username, password);
});

document.getElementById('topUpForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const amount = document.getElementById('topUpAmount').value;
    
    // Simulate successful top-up (in real app, this would call the API)
    showAlert(`Successfully added ${formatCurrency(amount)} to your wallet! (Demo)`);
    document.getElementById('topUpAmount').value = '';
    
    // Update balance display (simulate)
    const currentBalance = parseFloat(document.getElementById('walletBalance').textContent.replace('$', ''));
    document.getElementById('walletBalance').textContent = formatCurrency(currentBalance + parseFloat(amount));
});

document.getElementById('profileForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = {
        first_name: document.getElementById('firstName').value,
        last_name: document.getElementById('lastName').value,
        phone_number: document.getElementById('phoneNumber').value
    };
    
    try {
        await apiCall('/auth/profile/', 'PUT', formData);
        showAlert('Profile updated successfully!');
        
        // Update current user data
        currentUser = { ...currentUser, ...formData };
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        updateNavigation();
        
    } catch (error) {
        showAlert(error.message, 'error');
    }
});

// Initialize App
document.addEventListener('DOMContentLoaded', () => {
    // Check for stored auth token
    const storedToken = localStorage.getItem('authToken');
    const storedUser = localStorage.getItem('currentUser');
    
    if (storedToken && storedUser) {
        authToken = storedToken;
        currentUser = JSON.parse(storedUser);
        showDashboard();
    } else {
        showLogin();
    }
    
    updateNavigation();
});
