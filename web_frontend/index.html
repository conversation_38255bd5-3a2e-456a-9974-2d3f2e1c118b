<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐟 منصة مزاد الأسماك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 24px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .grid {
            display: grid;
            gap: 24px;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .auction-card {
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s;
        }

        .auction-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        .auction-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .auction-card-body {
            padding: 20px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-live {
            background-color: #56ab2f;
            color: white;
        }

        .status-ended {
            background-color: #6c757d;
            color: white;
        }

        .status-scheduled {
            background-color: #ffc107;
            color: black;
        }

        .alert {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #6c757d;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            text-align: center;
            padding: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }

        .stat-card h3 {
            font-size: 2.5rem;
            margin-bottom: 8px;
        }

        .hidden {
            display: none;
        }

        .user-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .price-highlight {
            font-size: 1.5rem;
            font-weight: bold;
            color: #56ab2f;
        }

        @media (max-width: 768px) {
            .navbar .container {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .container {
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <h1>🐟 Fish Auction Platform</h1>
            <div class="nav-links" id="navLinks">
                <a href="#" onclick="showLogin()">Login</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <!-- Login Section -->
        <div id="loginSection">
            <div class="card" style="max-width: 500px; margin: 50px auto;">
                <h2 style="text-align: center; margin-bottom: 30px;">Welcome to Fish Auction Platform</h2>

                <div id="alertContainer"></div>

                <form id="loginForm">
                    <div class="form-group">
                        <label>Username</label>
                        <input type="text" id="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <input type="password" id="password" class="form-control" required>
                    </div>
                    <button type="submit" class="btn">Login</button>
                </form>

                <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4>🚀 Quick Login (Test Users):</h4>
                    <button onclick="quickLogin('buyer1')" class="btn">Buyer1 ($500)</button>
                    <button onclick="quickLogin('buyer2')" class="btn">Buyer2 ($750)</button>
                    <button onclick="quickLogin('seller1')" class="btn btn-success">Seller1 ($1000)</button>
                    <button onclick="quickLogin('seller2')" class="btn btn-success">Seller2 ($1500)</button>
                    <button onclick="quickLogin('broker1')" class="btn btn-warning">Broker1 ($2000)</button>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboardSection" class="hidden">
            <h2 id="welcomeMessage"></h2>

            <div class="stats-grid" id="statsGrid">
                <!-- Stats will be populated here -->
            </div>

            <div class="card">
                <h3>🎯 Quick Actions</h3>
                <button onclick="showAuctions()" class="btn">Browse Auctions</button>
                <button onclick="showWallet()" class="btn">My Wallet</button>
                <button onclick="showProfile()" class="btn">My Profile</button>
                <button id="createAuctionBtn" onclick="showCreateAuction()" class="btn btn-success hidden">Create
                    Auction</button>
            </div>

            <div class="card">
                <h3>🐟 Recent Auctions</h3>
                <div id="recentAuctions" class="grid grid-3">
                    <!-- Recent auctions will be populated here -->
                </div>
            </div>
        </div>

        <!-- Auctions Section -->
        <div id="auctionsSection" class="hidden">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>🐟 Fish Auctions</h2>
                <button onclick="showDashboard()" class="btn">← Back to Dashboard</button>
            </div>

            <div class="card">
                <h3>🔍 Filters</h3>
                <div class="grid grid-3">
                    <div class="form-group">
                        <label>Search</label>
                        <input type="text" id="searchInput" class="form-control" placeholder="Search auctions...">
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="live">Live</option>
                            <option value="scheduled">Scheduled</option>
                            <option value="ended">Ended</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Category</label>
                        <select id="categoryFilter" class="form-control">
                            <option value="">All Categories</option>
                        </select>
                    </div>
                </div>
                <button onclick="filterAuctions()" class="btn">Apply Filters</button>
            </div>

            <div id="auctionsList" class="grid grid-3">
                <!-- Auctions will be populated here -->
            </div>
        </div>

        <!-- Wallet Section -->
        <div id="walletSection" class="hidden">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>💰 My Wallet</h2>
                <button onclick="showDashboard()" class="btn">← Back to Dashboard</button>
            </div>

            <div class="card" style="text-align: center;">
                <h1 id="walletBalance" class="price-highlight">$0.00</h1>
                <p style="color: #6c757d; font-size: 18px;">Available Balance</p>
            </div>

            <div class="grid grid-2">
                <div class="card">
                    <h3>💳 Top Up Wallet</h3>
                    <form id="topUpForm">
                        <div class="form-group">
                            <label>Amount ($)</label>
                            <input type="number" id="topUpAmount" class="form-control" min="1" max="10000" step="0.01"
                                required>
                        </div>
                        <button type="submit" class="btn btn-success">Add Funds</button>
                    </form>
                    <div style="margin-top: 15px;">
                        <p><strong>Quick amounts:</strong></p>
                        <button onclick="setTopUpAmount(25)" class="btn"
                            style="padding: 5px 10px; font-size: 12px;">$25</button>
                        <button onclick="setTopUpAmount(50)" class="btn"
                            style="padding: 5px 10px; font-size: 12px;">$50</button>
                        <button onclick="setTopUpAmount(100)" class="btn"
                            style="padding: 5px 10px; font-size: 12px;">$100</button>
                        <button onclick="setTopUpAmount(250)" class="btn"
                            style="padding: 5px 10px; font-size: 12px;">$250</button>
                    </div>
                </div>

                <div class="card">
                    <h3>📊 Transaction History</h3>
                    <div id="transactionHistory">
                        <!-- Transactions will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Section -->
        <div id="profileSection" class="hidden">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>👤 My Profile</h2>
                <button onclick="showDashboard()" class="btn">← Back to Dashboard</button>
            </div>

            <div class="grid grid-2">
                <div class="card">
                    <div style="text-align: center;">
                        <div id="userAvatar"
                            style="width: 100px; height: 100px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 40px;">
                            👤
                        </div>
                        <h3 id="userFullName"></h3>
                        <p id="userUsername" style="color: #6c757d;"></p>
                        <div id="userTypeBadge" class="user-badge"></div>
                        <div style="margin-top: 20px; text-align: left;">
                            <p><strong>Member Since:</strong> <span id="memberSince"></span></p>
                            <p><strong>Wallet Balance:</strong> <span id="profileWalletBalance"></span></p>
                            <p><strong>Verified:</strong> <span id="verifiedStatus"></span></p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>Edit Profile</h3>
                    <form id="profileForm">
                        <div class="grid grid-2">
                            <div class="form-group">
                                <label>First Name</label>
                                <input type="text" id="firstName" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>Last Name</label>
                                <input type="text" id="lastName" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" id="email" class="form-control" disabled>
                        </div>
                        <div class="form-group">
                            <label>Phone Number</label>
                            <input type="tel" id="phoneNumber" class="form-control">
                        </div>
                        <button type="submit" class="btn btn-success">Update Profile</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>

</html>