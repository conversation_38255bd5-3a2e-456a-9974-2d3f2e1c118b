#!/bin/bash

# Flutter Permission Fix Script
# Fixes common Flutter permission issues on macOS

echo "🔧 Flutter Permission Fix Tool"
echo "=============================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check current user
CURRENT_USER=$(whoami)
echo -e "${BLUE}Current user: $CURRENT_USER${NC}"

# Find Flutter installation
FLUTTER_PATH=$(which flutter 2>/dev/null)
if [ -z "$FLUTTER_PATH" ]; then
    echo -e "${RED}❌ Flutter not found in PATH${NC}"
    echo -e "${BLUE}💡 Please add Flutter to your PATH or install Flutter${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Flutter found at: $FLUTTER_PATH${NC}"

# Get Flutter root directory
FLUTTER_ROOT=$(dirname $(dirname $FLUTTER_PATH))
echo -e "${BLUE}Flutter root: $FLUTTER_ROOT${NC}"

# Check ownership
FLUTTER_OWNER=$(ls -ld "$FLUTTER_ROOT" | awk '{print $3}')
echo -e "${BLUE}Flutter directory owner: $FLUTTER_OWNER${NC}"

if [ "$FLUTTER_OWNER" != "$CURRENT_USER" ]; then
    echo -e "${YELLOW}⚠️ Flutter is owned by '$FLUTTER_OWNER' but you are '$CURRENT_USER'${NC}"
    echo ""
    echo -e "${BLUE}🎯 Choose a solution:${NC}"
    echo "1. Fix permissions (requires admin password)"
    echo "2. Install Flutter for current user"
    echo "3. Use Flutter with sudo (not recommended)"
    echo "4. Create symlinks to writable location"
    echo "5. Skip and continue (may have issues)"
    
    read -p "Enter choice (1-5): " CHOICE
    
    case $CHOICE in
        1)
            echo -e "${BLUE}🔧 Fixing permissions...${NC}"
            echo -e "${YELLOW}This will change ownership of Flutter to $CURRENT_USER${NC}"
            sudo chown -R $CURRENT_USER:staff "$FLUTTER_ROOT"
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✅ Permissions fixed successfully${NC}"
            else
                echo -e "${RED}❌ Failed to fix permissions${NC}"
                exit 1
            fi
            ;;
        2)
            echo -e "${BLUE}📦 Installing Flutter for current user...${NC}"
            echo -e "${YELLOW}This will download and install Flutter in your home directory${NC}"
            
            # Create Flutter directory in user's home
            FLUTTER_HOME="$HOME/flutter"
            if [ -d "$FLUTTER_HOME" ]; then
                echo -e "${YELLOW}⚠️ Flutter already exists in $FLUTTER_HOME${NC}"
                echo -e "${BLUE}Remove existing installation? (y/n)${NC}"
                read -n 1 REMOVE_EXISTING
                echo ""
                if [[ $REMOVE_EXISTING =~ ^[Yy]$ ]]; then
                    rm -rf "$FLUTTER_HOME"
                else
                    echo -e "${BLUE}Using existing installation${NC}"
                fi
            fi
            
            if [ ! -d "$FLUTTER_HOME" ]; then
                echo -e "${BLUE}📥 Downloading Flutter...${NC}"
                cd "$HOME"
                git clone https://github.com/flutter/flutter.git -b stable
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Flutter downloaded successfully${NC}"
                else
                    echo -e "${RED}❌ Failed to download Flutter${NC}"
                    exit 1
                fi
            fi
            
            # Update PATH
            echo -e "${BLUE}🔧 Updating PATH...${NC}"
            SHELL_RC=""
            if [ -f "$HOME/.zshrc" ]; then
                SHELL_RC="$HOME/.zshrc"
            elif [ -f "$HOME/.bash_profile" ]; then
                SHELL_RC="$HOME/.bash_profile"
            elif [ -f "$HOME/.bashrc" ]; then
                SHELL_RC="$HOME/.bashrc"
            fi
            
            if [ ! -z "$SHELL_RC" ]; then
                if ! grep -q "flutter/bin" "$SHELL_RC"; then
                    echo 'export PATH="$HOME/flutter/bin:$PATH"' >> "$SHELL_RC"
                    echo -e "${GREEN}✅ Added Flutter to PATH in $SHELL_RC${NC}"
                    echo -e "${YELLOW}⚠️ Please restart your terminal or run: source $SHELL_RC${NC}"
                else
                    echo -e "${BLUE}Flutter already in PATH${NC}"
                fi
            fi
            
            # Set Flutter path for current session
            export PATH="$HOME/flutter/bin:$PATH"
            FLUTTER_ROOT="$HOME/flutter"
            ;;
        3)
            echo -e "${YELLOW}⚠️ Using sudo with Flutter (not recommended)${NC}"
            echo -e "${BLUE}You'll need to use 'sudo flutter' for all commands${NC}"
            ;;
        4)
            echo -e "${BLUE}🔗 Creating symlinks to writable location...${NC}"
            FLUTTER_CACHE_HOME="$HOME/.flutter_cache"
            mkdir -p "$FLUTTER_CACHE_HOME"
            
            # Create symlink for cache directory
            if [ -L "$FLUTTER_ROOT/bin/cache" ]; then
                rm "$FLUTTER_ROOT/bin/cache"
            fi
            
            if [ -d "$FLUTTER_ROOT/bin/cache" ]; then
                cp -r "$FLUTTER_ROOT/bin/cache" "$FLUTTER_CACHE_HOME/"
                rm -rf "$FLUTTER_ROOT/bin/cache"
            fi
            
            ln -s "$FLUTTER_CACHE_HOME/cache" "$FLUTTER_ROOT/bin/cache"
            echo -e "${GREEN}✅ Created symlink for Flutter cache${NC}"
            ;;
        5)
            echo -e "${YELLOW}⚠️ Continuing with potential permission issues${NC}"
            ;;
        *)
            echo -e "${RED}❌ Invalid choice${NC}"
            exit 1
            ;;
    esac
else
    echo -e "${GREEN}✅ Flutter permissions are correct${NC}"
fi

# Test Flutter
echo -e "${BLUE}🧪 Testing Flutter...${NC}"
flutter --version
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Flutter is working correctly${NC}"
else
    echo -e "${RED}❌ Flutter still has issues${NC}"
    echo -e "${BLUE}💡 Try running: flutter doctor -v${NC}"
fi

# Run Flutter doctor
echo -e "${BLUE}🔍 Running Flutter doctor...${NC}"
flutter doctor -v

echo ""
echo -e "${GREEN}🎉 Flutter permission fix complete!${NC}"
echo -e "${BLUE}💡 You can now run Flutter commands normally${NC}"
echo ""
echo -e "${YELLOW}📱 Next steps:${NC}"
echo "1. cd fish_auction_app"
echo "2. flutter pub get"
echo "3. flutter run -d ios"
