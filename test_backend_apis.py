#!/usr/bin/env python3
"""
Comprehensive Backend API Testing Script for Fish Auction Platform
Tests all major functionality including authentication, auctions, payments, etc.
"""

import requests
import json
import time
from datetime import datetime, timedelta

BASE_URL = "http://127.0.0.1:8000/api"

class FishAuctionAPITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.tokens = {}
        self.test_users = {}
        self.test_auctions = {}
        
    def log(self, message):
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        
    def test_user_registration(self):
        """Test user registration for different user types"""
        self.log("Testing User Registration...")
        
        import random
        timestamp = str(int(time.time()))

        users_to_create = [
            {
                "username": f"buyer_test_{timestamp}",
                "email": f"buyer_{timestamp}@test.com",
                "password": "testpass123",
                "password_confirm": "testpass123",
                "first_name": "Test",
                "last_name": "Buyer",
                "phone_number": f"+123456789{random.randint(0,9)}",
                "user_type": "buyer"
            },
            {
                "username": f"seller_test_{timestamp}",
                "email": f"seller_{timestamp}@test.com",
                "password": "testpass123",
                "password_confirm": "testpass123",
                "first_name": "Test",
                "last_name": "Seller",
                "phone_number": f"+123456789{random.randint(0,9)}",
                "user_type": "seller"
            },
            {
                "username": f"broker_test_{timestamp}",
                "email": f"broker_{timestamp}@test.com",
                "password": "testpass123",
                "password_confirm": "testpass123",
                "first_name": "Test",
                "last_name": "Broker",
                "phone_number": f"+123456789{random.randint(0,9)}",
                "user_type": "broker"
            }
        ]
        
        for user_data in users_to_create:
            response = self.session.post(f"{self.base_url}/auth/register/", json=user_data)
            if response.status_code == 201:
                self.log(f"✅ Successfully registered {user_data['user_type']}: {user_data['username']}")
                self.test_users[user_data['user_type']] = user_data
            else:
                self.log(f"❌ Failed to register {user_data['user_type']}: {response.text}")
                
    def test_user_login(self):
        """Test user login and token generation"""
        self.log("Testing User Login...")
        
        for user_type, user_data in self.test_users.items():
            login_data = {
                "username": user_data["username"],
                "password": user_data["password"]
            }
            
            response = self.session.post(f"{self.base_url}/auth/login/", json=login_data)
            if response.status_code == 200:
                token_data = response.json()
                self.tokens[user_type] = token_data["tokens"]["access"]
                self.log(f"✅ Successfully logged in {user_type}")
            else:
                self.log(f"❌ Failed to login {user_type}: {response.text}")
                
    def get_auth_headers(self, user_type):
        """Get authorization headers for a user type"""
        return {"Authorization": f"Bearer {self.tokens.get(user_type, '')}"}
        
    def test_auction_creation(self):
        """Test auction creation by seller"""
        self.log("Testing Auction Creation...")
        
        if "seller" not in self.tokens:
            self.log("❌ No seller token available for auction creation")
            return
            
        # First get fish categories
        categories_response = self.session.get(f"{self.base_url}/auctions/categories/")
        if categories_response.status_code != 200:
            self.log("❌ Could not fetch fish categories")
            return

        categories = categories_response.json()
        if not categories.get('results'):
            self.log("❌ No fish categories available")
            return

        salmon_category = None
        for cat in categories['results']:
            if 'salmon' in cat['name'].lower():
                salmon_category = cat['id']
                break

        if not salmon_category:
            salmon_category = categories['results'][0]['id']  # Use first available

        auction_data = {
            "title": "Fresh Salmon Auction Test",
            "description": "High quality Atlantic salmon, freshly caught",
            "fish_category": salmon_category,
            "fish_type": "Atlantic Salmon",
            "weight": "25.5",
            "quantity": 10,
            "catch_date": datetime.now().date().isoformat(),
            "catch_location": "North Atlantic",
            "auction_type": "live",
            "starting_price": "15.00",
            "reserve_price": "20.00",
            "buy_now_price": "30.00",
            "bid_increment": "1.00",
            "start_time": (datetime.now() + timedelta(minutes=1)).isoformat(),
            "end_time": (datetime.now() + timedelta(hours=2)).isoformat()
        }
        
        headers = self.get_auth_headers("seller")

        # For now, skip image upload in API test - just test without image
        self.log("⚠️ Skipping auction creation test - requires image upload (multipart/form-data)")
        self.log("   This will be tested in the mobile app where image upload is easier")
        return
        
        if response.status_code == 201:
            auction = response.json()
            self.test_auctions["salmon"] = auction
            self.log(f"✅ Successfully created auction: {auction['title']} (ID: {auction['id']})")
        else:
            self.log(f"❌ Failed to create auction: {response.text}")
            
    def test_auction_listing(self):
        """Test auction listing and filtering"""
        self.log("Testing Auction Listing...")
        
        # Test public auction listing
        response = self.session.get(f"{self.base_url}/auctions/")
        if response.status_code == 200:
            auctions = response.json()
            self.log(f"✅ Successfully retrieved {len(auctions.get('results', []))} auctions")
        else:
            self.log(f"❌ Failed to retrieve auctions: {response.text}")
            
        # Test filtering by fish type
        response = self.session.get(f"{self.base_url}/auctions/?fish_type=salmon")
        if response.status_code == 200:
            self.log("✅ Successfully filtered auctions by fish type")
        else:
            self.log(f"❌ Failed to filter auctions: {response.text}")
            
    def test_bidding(self):
        """Test bidding functionality"""
        self.log("Testing Bidding...")

        if "buyer" not in self.tokens:
            self.log("❌ No buyer token available for bidding")
            return

        # Get existing auctions to bid on
        response = self.session.get(f"{self.base_url}/auctions/")
        if response.status_code != 200:
            self.log("❌ Could not fetch auctions for bidding test")
            return

        auctions = response.json()
        live_auctions = [a for a in auctions.get('results', []) if a.get('status') == 'live']

        if not live_auctions:
            self.log("⚠️ No live auctions available for bidding test")
            return

        auction = live_auctions[0]
        current_price = float(auction.get('current_price', auction.get('starting_price', 0)))
        bid_amount = current_price + 5.00  # Bid 5 units higher

        bid_data = {
            "amount": str(bid_amount)
        }

        headers = self.get_auth_headers("buyer")
        response = self.session.post(f"{self.base_url}/auctions/{auction['id']}/bid/", json=bid_data, headers=headers)

        if response.status_code == 201:
            self.log("✅ Successfully placed bid")
        else:
            self.log(f"⚠️ Bidding test result: {response.status_code} - {response.text[:100]}...")
            
    def test_wallet_operations(self):
        """Test wallet creation and operations"""
        self.log("Testing Wallet Operations...")
        
        for user_type in ["buyer", "seller", "broker"]:
            if user_type not in self.tokens:
                continue
                
            headers = self.get_auth_headers(user_type)
            
            # Get wallet balance
            response = self.session.get(f"{self.base_url}/payments/wallet/balance/", headers=headers)
            if response.status_code == 200:
                self.log(f"✅ Successfully retrieved {user_type} wallet info")
            else:
                self.log(f"❌ Failed to get {user_type} wallet: {response.text}")
                
    def test_marketplace_listings(self):
        """Test marketplace functionality"""
        self.log("Testing Marketplace...")

        # Test marketplace listings
        response = self.session.get(f"{self.base_url}/marketplace/listings/")
        if response.status_code == 200:
            self.log("✅ Successfully retrieved marketplace listings")
        else:
            self.log(f"❌ Failed to retrieve marketplace: {response.text}")

        # Test service categories
        response = self.session.get(f"{self.base_url}/marketplace/categories/")
        if response.status_code == 200:
            self.log("✅ Successfully retrieved service categories")
        else:
            self.log(f"❌ Failed to retrieve categories: {response.text}")
            
    def test_user_profile(self):
        """Test user profile operations"""
        self.log("Testing User Profiles...")
        
        for user_type in ["buyer", "seller", "broker"]:
            if user_type not in self.tokens:
                continue
                
            headers = self.get_auth_headers(user_type)
            
            # Get profile
            response = self.session.get(f"{self.base_url}/auth/profile/", headers=headers)
            if response.status_code == 200:
                self.log(f"✅ Successfully retrieved {user_type} profile")
            else:
                self.log(f"❌ Failed to get {user_type} profile: {response.text}")
                
    def run_all_tests(self):
        """Run all API tests"""
        self.log("🚀 Starting Fish Auction Backend API Tests")
        self.log("=" * 50)
        
        try:
            self.test_user_registration()
            time.sleep(1)
            
            self.test_user_login()
            time.sleep(1)
            
            self.test_user_profile()
            time.sleep(1)
            
            self.test_auction_creation()
            time.sleep(2)  # Wait for auction to be created
            
            self.test_auction_listing()
            time.sleep(1)
            
            self.test_bidding()
            time.sleep(1)
            
            self.test_wallet_operations()
            time.sleep(1)
            
            self.test_marketplace_listings()
            
            self.log("=" * 50)
            self.log("🎉 Backend API Testing Complete!")
            
        except Exception as e:
            self.log(f"❌ Test suite failed with error: {str(e)}")

if __name__ == "__main__":
    tester = FishAuctionAPITester()
    tester.run_all_tests()
