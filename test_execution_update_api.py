#!/usr/bin/env python3
"""
Test service execution update API response to debug empty ID issue
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceExecution
from accounts.models import User
import json


def test_execution_update_api():
    """Test service execution update API response"""
    print("🧪 Testing Service Execution Update API Response")
    print("=" * 50)
    
    # Find a service execution to update
    execution = ServiceExecution.objects.filter(status='started').first()
    if not execution:
        print("❌ No service execution with 'started' status found")
        return
    
    print(f"📋 Service Execution: {execution.id}")
    print(f"   Status: {execution.status}")
    print(f"   Broker: {execution.broker.username}")
    print(f"   Service Request: {execution.service_request.id}")
    
    # Create API client and authenticate as broker
    api_client = APIClient()
    refresh = RefreshToken.for_user(execution.broker)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as broker: {execution.broker.username}")
    
    # Test update API
    url = f'/api/broker/executions/{execution.id}/update/'
    update_data = {'status': 'in_progress'}
    
    print(f"\n📤 PATCH {url}")
    print(f"   Data: {update_data}")
    
    response = api_client.patch(url, data=update_data, content_type='application/json')
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Update successful!")
        
        if hasattr(response, 'data'):
            data = response.data
            print(f"   Data Type: {type(data)}")
            print(f"   Raw Response: {data}")
            
            # Check specific fields
            execution_id = data.get('id', 'MISSING')
            status = data.get('status', 'MISSING')
            service_request_info = data.get('service_request_info', {})
            broker_info = data.get('broker_info', {})
            
            print(f"\n🔍 Response Fields:")
            print(f"   ID: {execution_id}")
            print(f"   Status: {status}")
            print(f"   Service Request Info: {service_request_info}")
            print(f"   Broker Info: {broker_info}")
            
            # Check if ID is valid UUID
            if execution_id and execution_id != 'MISSING':
                print(f"   ✅ ID is present in response: {execution_id}")
            else:
                print(f"   ❌ ID is missing from response!")
                
            # Check if this matches the original execution ID
            if str(execution_id) == str(execution.id):
                print(f"   ✅ ID matches original execution ID")
            else:
                print(f"   ❌ ID doesn't match! Original: {execution.id}, Response: {execution_id}")
                
        else:
            print(f"   Content: {response.content.decode()}")
    else:
        print(f"❌ Update failed!")
        if hasattr(response, 'data'):
            print(f"   Error: {response.data}")
        else:
            print(f"   Content: {response.content.decode()}")
    
    # Check database state after update
    execution.refresh_from_db()
    print(f"\n📋 Database State After Update:")
    print(f"   ID: {execution.id}")
    print(f"   Status: {execution.status}")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_execution_update_api()
