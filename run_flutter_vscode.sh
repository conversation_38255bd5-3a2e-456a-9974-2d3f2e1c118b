#!/bin/bash

# Flutter VS Code Runner Script
# Run Flutter app in VS Code terminal with iOS device selection

echo "📱 Flutter VS Code Runner"
echo "========================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Set Flutter path to use the working installation
export PATH="/Users/<USER>/flutter/bin:$PATH"

echo -e "${BLUE}🔧 Setting up Flutter environment...${NC}"

# Check if we're in the right directory
if [ ! -d "fish_auction_app" ]; then
    echo -e "${RED}❌ fish_auction_app directory not found${NC}"
    echo -e "${BLUE}💡 Please run this script from the project root directory${NC}"
    exit 1
fi

# Navigate to Flutter app directory
cd fish_auction_app

echo -e "${GREEN}✅ Using Flutter: $(which flutter)${NC}"
echo -e "${GREEN}✅ Flutter version: $(flutter --version | head -1)${NC}"

# Check Flutter setup
echo -e "${BLUE}🔍 Running Flutter doctor...${NC}"
flutter doctor

# Get dependencies
echo -e "${BLUE}📦 Getting Flutter dependencies...${NC}"
flutter pub get

# Clean build
echo -e "${BLUE}🧹 Cleaning previous builds...${NC}"
flutter clean

# Check for connected devices
echo -e "${BLUE}📱 Checking for connected devices...${NC}"
flutter devices

# Get available devices in JSON format for parsing
DEVICES_JSON=$(flutter devices --machine 2>/dev/null)

if [ $? -ne 0 ] || [ -z "$DEVICES_JSON" ]; then
    echo -e "${YELLOW}⚠️ Could not get device list${NC}"
    echo -e "${BLUE}💡 Make sure your iOS device is connected and trusted${NC}"
    echo -e "${BLUE}🔄 Trying to run anyway...${NC}"
    
    echo -e "${BLUE}🚀 Starting Flutter app...${NC}"
    flutter run --verbose
    exit 0
fi

# Parse devices and show options
echo ""
echo -e "${BLUE}🎯 Available devices:${NC}"
echo "$DEVICES_JSON" | python3 -c "
import sys, json
try:
    devices = json.load(sys.stdin)
    ios_devices = []
    other_devices = []
    
    for i, device in enumerate(devices):
        device_info = f\"{i+1}. {device.get('name', 'Unknown')} ({device.get('id', 'unknown')})\"
        if device.get('category') == 'mobile' and 'ios' in device.get('platformType', '').lower():
            ios_devices.append((i+1, device))
            print(f'📱 {device_info} - iOS Device')
        elif 'simulator' in device.get('name', '').lower() or 'ios' in device.get('platformType', '').lower():
            ios_devices.append((i+1, device))
            print(f'📲 {device_info} - iOS Simulator')
        else:
            other_devices.append((i+1, device))
            print(f'🖥️  {device_info} - {device.get(\"platformType\", \"Other\")}')
    
    if not ios_devices and not other_devices:
        print('❌ No devices found')
        sys.exit(1)
        
except Exception as e:
    print(f'Error parsing devices: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️ Could not parse device list${NC}"
    echo -e "${BLUE}🚀 Running with default device selection...${NC}"
    flutter run --verbose
    exit 0
fi

echo ""
echo -e "${BLUE}🎯 Choose deployment option:${NC}"
echo "1. Auto-select iOS device"
echo "2. Auto-select iOS simulator"
echo "3. Select specific device by number"
echo "4. Run with device selection prompt"
echo "5. Debug mode with maximum verbosity"

read -p "Enter choice (1-5): " CHOICE

case $CHOICE in
    1)
        echo -e "${BLUE}🚀 Auto-selecting iOS device...${NC}"
        # Try to find a physical iOS device
        DEVICE_ID=$(echo "$DEVICES_JSON" | python3 -c "
import sys, json
try:
    devices = json.load(sys.stdin)
    for device in devices:
        if (device.get('category') == 'mobile' and 
            'ios' in device.get('platformType', '').lower() and
            'simulator' not in device.get('name', '').lower()):
            print(device.get('id', ''))
            break
except:
    pass
")
        
        if [ ! -z "$DEVICE_ID" ]; then
            echo -e "${GREEN}✅ Found iOS device: $DEVICE_ID${NC}"
            flutter run -d "$DEVICE_ID" --verbose
        else
            echo -e "${YELLOW}⚠️ No physical iOS device found, trying simulator...${NC}"
            flutter run -d ios --verbose
        fi
        ;;
    2)
        echo -e "${BLUE}🚀 Auto-selecting iOS simulator...${NC}"
        # Open simulator if not already open
        open -a Simulator 2>/dev/null
        sleep 3
        flutter run -d ios --simulator --verbose
        ;;
    3)
        echo -e "${BLUE}📱 Enter device number from the list above:${NC}"
        read DEVICE_NUM
        
        DEVICE_ID=$(echo "$DEVICES_JSON" | python3 -c "
import sys, json
try:
    devices = json.load(sys.stdin)
    device_num = int('$DEVICE_NUM') - 1
    if 0 <= device_num < len(devices):
        print(devices[device_num].get('id', ''))
except:
    pass
")
        
        if [ ! -z "$DEVICE_ID" ]; then
            echo -e "${GREEN}✅ Selected device: $DEVICE_ID${NC}"
            flutter run -d "$DEVICE_ID" --verbose
        else
            echo -e "${RED}❌ Invalid device number${NC}"
            exit 1
        fi
        ;;
    4)
        echo -e "${BLUE}🚀 Running with Flutter device selection...${NC}"
        flutter run --verbose
        ;;
    5)
        echo -e "${BLUE}🐛 Running in debug mode with maximum verbosity...${NC}"
        flutter run --debug --verbose --enable-software-rendering
        ;;
    *)
        echo -e "${RED}❌ Invalid choice${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}🎉 Flutter app deployment complete!${NC}"
echo -e "${BLUE}💡 Use 'r' for hot reload, 'R' for hot restart, 'q' to quit${NC}"
