#!/usr/bin/env python3
"""
Test the exact response format from broker selection API
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceRequest, BrokerQuote
from accounts.models import User
import json


def test_broker_selection_response():
    """Test the exact response format from broker selection API"""
    print("🧪 Testing Broker Selection Response Format")
    print("=" * 50)
    
    # Find a service request that already has a broker selected
    service_request = ServiceRequest.objects.filter(status='broker_selected').first()
    if not service_request:
        print("❌ No service request with broker selected found")
        return
    
    print(f"📋 Service Request: {service_request.id}")
    print(f"   Client: {service_request.client.username}")
    print(f"   Status: {service_request.status}")
    print(f"   Selected Broker: {service_request.selected_broker.username if service_request.selected_broker else 'None'}")
    
    # Get any quote for this request
    quote = service_request.quotes.first()
    if not quote:
        print("❌ No quotes found")
        return
    
    print(f"📝 Quote: {quote.id}")
    print(f"   Broker: {quote.broker.username}")
    print(f"   Status: {quote.status}")
    
    # Create API client and authenticate as client
    api_client = APIClient()
    refresh = RefreshToken.for_user(service_request.client)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as client: {service_request.client.username}")
    
    # Make API request to select broker (should fail with 400)
    url = f'/api/broker/requests/{service_request.id}/select-broker/{quote.id}/'
    print(f"\n📤 POST {url}")
    
    response = api_client.post(url, data={})
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    print(f"   Headers: {dict(response.headers)}")
    
    if hasattr(response, 'data'):
        print(f"   Data Type: {type(response.data)}")
        print(f"   Data: {response.data}")
        
        # Check specific fields
        if isinstance(response.data, dict):
            print(f"\n🔍 Response Fields:")
            for key, value in response.data.items():
                print(f"   {key}: {value}")
    else:
        print(f"   Content: {response.content.decode()}")
        
        # Try to parse as JSON
        try:
            json_data = json.loads(response.content.decode())
            print(f"   Parsed JSON: {json_data}")
        except:
            print("   Could not parse as JSON")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_broker_selection_response()
