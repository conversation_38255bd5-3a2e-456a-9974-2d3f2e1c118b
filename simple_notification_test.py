#!/usr/bin/env python3
"""
Simple notification test to trigger WhatsApp messages
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.utils import timezone
from datetime import timedelta
from accounts.models import User
from notifications.services import NotificationService
from notifications.models import NotificationTemplate


def test_whatsapp_notifications():
    """Test WhatsApp notifications for different scenarios"""
    print("📱 Testing WhatsApp Notifications")
    print("=" * 40)
    
    # Get test user
    user = User.objects.filter(phone_number__isnull=False).exclude(phone_number='').first()
    if not user:
        print("❌ No user with phone number found")
        return
    
    print(f"📞 Testing with user: {user.username} ({user.phone_number})")
    
    service = NotificationService()
    
    # Test 1: Auction Won Notification
    print("\n🏆 Test 1: Auction Won Notification")
    try:
        result = service.send_notification(
            user,
            'auction_won',
            {
                'auction': {
                    'title': 'Fresh Tuna - 5kg',
                    'current_price': 150.00,
                    'payment_deadline': timezone.now() + timedelta(minutes=20)
                },
                'user_name': user.first_name or user.username
            },
            channels=['whatsapp']
        )
        print(f"   ✅ Sent: {len(result)} notifications")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Auction Ending Soon
    print("\n⏰ Test 2: Auction Ending Soon")
    try:
        result = service.send_notification(
            user,
            'auction_ending_soon',
            {
                'auction': {
                    'title': 'Premium Salmon - 3kg',
                    'current_price': 200.00,
                    'end_time': timezone.now() + timedelta(hours=1)
                },
                'user_name': user.first_name or user.username,
                'time_remaining': '1 hour'
            },
            channels=['whatsapp']
        )
        print(f"   ✅ Sent: {len(result)} notifications")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Broker Service Update
    print("\n🤝 Test 3: Broker Service Update")
    try:
        result = service.send_notification(
            user,
            'broker_service_status_update',
            {
                'service_execution': {
                    'service_request': {
                        'service': {'name_ar': 'خدمة الفحص والتقييم'}
                    }
                },
                'old_status': 'started',
                'new_status': 'completed',
                'user_name': user.first_name or user.username
            },
            channels=['whatsapp']
        )
        print(f"   ✅ Sent: {len(result)} notifications")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Check notification status
    print("\n📊 Recent Notifications:")
    from notifications.models import Notification
    recent = Notification.objects.filter(recipient=user).order_by('-created_at')[:5]
    
    for notif in recent:
        status_icon = "✅" if notif.status == 'sent' else "❌" if notif.status == 'failed' else "⏳"
        print(f"   {status_icon} {notif.template.notification_type} - {notif.status}")
        if notif.status == 'failed' and notif.error_message:
            print(f"      Error: {notif.error_message}")
    
    print(f"\n📱 Check WhatsApp number {user.phone_number} for messages!")


if __name__ == '__main__':
    test_whatsapp_notifications()
