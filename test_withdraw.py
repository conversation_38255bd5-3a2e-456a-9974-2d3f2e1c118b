#!/usr/bin/env python3

import os
import sys
import django
import requests
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from payments.models import WalletTransaction

User = get_user_model()

class WithdrawTester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.session = requests.Session()
        self.test_user = None
        self.token = None
        
    def setup_test_user(self):
        """Create or get test user"""
        try:
            self.test_user = User.objects.get(username='withdraw_test_user')
            print(f"✅ Using existing test user: {self.test_user.username}")
        except User.DoesNotExist:
            self.test_user = User.objects.create_user(
                username='withdraw_test_user',
                email='<EMAIL>',
                password='testpass123',
                wallet_balance=Decimal('100.00')
            )
            print(f"✅ Created test user: {self.test_user.username}")
        
        # Ensure user has some balance
        if self.test_user.wallet_balance < 50:
            self.test_user.wallet_balance = Decimal('100.00')
            self.test_user.save()
            print(f"✅ Set test user balance to: ${self.test_user.wallet_balance}")
    
    def authenticate(self):
        """Authenticate test user"""
        try:
            response = self.session.post(f"{self.base_url}/auth/login/", json={
                'username': 'withdraw_test_user',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                data = response.json()
                self.token = data['tokens']['access']
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def test_wallet_balance(self):
        """Test getting wallet balance"""
        try:
            response = self.session.get(f"{self.base_url}/payments/wallet/balance/")
            
            if response.status_code == 200:
                data = response.json()
                balance = data['balance']
                print(f"✅ Current wallet balance: ${balance}")
                return balance
            else:
                print(f"❌ Failed to get balance: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Balance check error: {e}")
            return None
    
    def test_withdraw(self, amount):
        """Test withdraw functionality"""
        try:
            print(f"\n💸 Testing withdrawal of ${amount}...")
            
            # Get balance before withdrawal
            initial_balance = self.test_wallet_balance()
            if initial_balance is None:
                return False
            
            # Attempt withdrawal
            response = self.session.post(f"{self.base_url}/payments/wallet/withdraw/", json={
                'amount': amount
            })
            
            print(f"Withdraw response status: {response.status_code}")
            print(f"Withdraw response: {response.text}")
            
            if response.status_code == 200:
                data = response.json()
                new_balance = data.get('new_balance')
                withdrawn_amount = data.get('amount')
                
                print(f"✅ Withdrawal successful!")
                print(f"   Amount withdrawn: ${withdrawn_amount}")
                print(f"   New balance: ${new_balance}")
                print(f"   Expected balance: ${initial_balance - amount}")
                
                # Verify balance matches expectation
                if abs(new_balance - (initial_balance - amount)) < 0.01:
                    print("✅ Balance calculation correct")
                    return True
                else:
                    print("❌ Balance calculation incorrect")
                    return False
            else:
                data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                error_msg = data.get('error', response.text)
                print(f"❌ Withdrawal failed: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ Withdrawal test error: {e}")
            return False
    
    def test_transaction_history(self):
        """Test transaction history after withdrawal"""
        try:
            response = self.session.get(f"{self.base_url}/payments/wallet/transactions/")
            
            if response.status_code == 200:
                data = response.json()
                transactions = data.get('results', data) if isinstance(data, dict) and 'results' in data else data
                
                print(f"✅ Retrieved {len(transactions)} transactions")
                
                # Look for withdrawal transactions
                withdrawal_transactions = [t for t in transactions if t.get('transaction_type') == 'withdrawal']
                print(f"✅ Found {len(withdrawal_transactions)} withdrawal transactions")
                
                if withdrawal_transactions:
                    latest_withdrawal = withdrawal_transactions[0]
                    print(f"   Latest withdrawal: ${latest_withdrawal.get('amount')} - {latest_withdrawal.get('description')}")
                
                return True
            else:
                print(f"❌ Failed to get transactions: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Transaction history error: {e}")
            return False
    
    def run_tests(self):
        """Run all withdraw tests"""
        print("🚀 Starting Withdraw Functionality Tests")
        print("=" * 50)
        
        # Setup
        self.setup_test_user()
        if not self.authenticate():
            return
        
        # Test cases
        test_results = []
        
        # Test 1: Valid withdrawal
        print("\n📝 Test 1: Valid withdrawal ($25)")
        result1 = self.test_withdraw(25.0)
        test_results.append(("Valid withdrawal", result1))
        
        # Test 2: Check transaction history
        print("\n📝 Test 2: Transaction history")
        result2 = self.test_transaction_history()
        test_results.append(("Transaction history", result2))
        
        # Test 3: Minimum amount validation
        print("\n📝 Test 3: Below minimum withdrawal ($5)")
        result3 = not self.test_withdraw(5.0)  # Should fail, so we invert
        test_results.append(("Minimum validation", result3))
        
        # Test 4: Insufficient balance
        print("\n📝 Test 4: Insufficient balance ($1000)")
        result4 = not self.test_withdraw(1000.0)  # Should fail, so we invert
        test_results.append(("Insufficient balance", result4))
        
        # Results summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            print("🎉 All withdraw tests PASSED!")
        else:
            print("⚠️ Some withdraw tests FAILED!")

if __name__ == "__main__":
    tester = WithdrawTester()
    tester.run_tests()
