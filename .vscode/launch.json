{"version": "0.2.0", "configurations": [{"name": "Fish Auction (Debug)", "cwd": "fish_auction_app", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--verbose"], "flutterMode": "debug"}, {"name": "Fish Auction (Profile)", "cwd": "fish_auction_app", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterMode": "profile"}, {"name": "Fish Auction (Release)", "cwd": "fish_auction_app", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterMode": "release"}, {"name": "Fish Auction (iOS Device)", "cwd": "fish_auction_app", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["-d", "ios", "--verbose"], "flutterMode": "debug"}, {"name": "Fish Auction (iOS Simulator)", "cwd": "fish_auction_app", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["-d", "ios", "--simulator", "--verbose"], "flutterMode": "debug"}]}