{"dart.flutterSdkPath": "/Users/<USER>/flutter", "dart.sdkPath": "/Users/<USER>/flutter/bin/cache/dart-sdk", "flutter.sdkPath": "/Users/<USER>/flutter", "dart.enableSdkFormatter": true, "dart.lineLength": 120, "dart.showTodos": true, "dart.closingLabels": true, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "flutter.hot-reload-on-save": "allIfDirty", "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}}