#!/usr/bin/env python3
"""
Test service execution API responses to debug empty execution ID issue
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from broker_services.models import ServiceExecution
from accounts.models import User
import json


def test_service_execution_api():
    """Test service execution API responses"""
    print("🧪 Testing Service Execution API Responses")
    print("=" * 50)
    
    # Find a broker with service executions
    broker = User.objects.filter(user_type='broker', service_executions__isnull=False).first()
    if not broker:
        print("❌ No broker with service executions found")
        return
    
    print(f"👤 Broker: {broker.username}")
    
    # Get service executions from database
    executions = ServiceExecution.objects.filter(broker=broker)
    print(f"📋 Service executions in database: {executions.count()}")
    
    for execution in executions:
        print(f"   ID: {execution.id}")
        print(f"   Status: {execution.status}")
        print(f"   Service Request: {execution.service_request.id}")
        print(f"   Broker: {execution.broker.username}")
        print()
    
    # Create API client and authenticate as broker
    api_client = APIClient()
    refresh = RefreshToken.for_user(broker)
    access_token = str(refresh.access_token)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
    
    print(f"🔑 Authenticated as broker: {broker.username}")
    
    # Test broker service executions API
    url = '/api/broker/executions/broker/'
    print(f"\n📤 GET {url}")
    
    response = api_client.get(url)
    
    print(f"\n📥 Response:")
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API call successful!")
        
        if hasattr(response, 'data'):
            data = response.data
            print(f"   Data Type: {type(data)}")
            
            if isinstance(data, dict) and 'results' in data:
                # Paginated response
                results = data['results']
                print(f"   Results count: {len(results)}")
                
                for i, execution_data in enumerate(results):
                    print(f"\n   📋 Execution {i+1}:")
                    print(f"      Raw data: {execution_data}")
                    
                    # Check specific fields
                    execution_id = execution_data.get('id', 'MISSING')
                    status = execution_data.get('status', 'MISSING')
                    service_request_info = execution_data.get('service_request_info', {})
                    broker_info = execution_data.get('broker_info', {})
                    
                    print(f"      ID: {execution_id}")
                    print(f"      Status: {status}")
                    print(f"      Service Request Info: {service_request_info}")
                    print(f"      Broker Info: {broker_info}")
                    
                    # Check if ID is valid UUID
                    if execution_id and execution_id != 'MISSING':
                        print(f"      ✅ ID is present: {execution_id}")
                    else:
                        print(f"      ❌ ID is missing or empty!")
                        
            elif isinstance(data, list):
                # Direct list response
                print(f"   Results count: {len(data)}")
                
                for i, execution_data in enumerate(data):
                    print(f"\n   📋 Execution {i+1}:")
                    print(f"      Raw data: {execution_data}")
                    
                    execution_id = execution_data.get('id', 'MISSING')
                    print(f"      ID: {execution_id}")
                    
                    if execution_id and execution_id != 'MISSING':
                        print(f"      ✅ ID is present")
                    else:
                        print(f"      ❌ ID is missing or empty!")
            else:
                print(f"   Unexpected data format: {data}")
        else:
            print(f"   Content: {response.content.decode()}")
    else:
        print(f"❌ API call failed!")
        if hasattr(response, 'data'):
            print(f"   Error: {response.data}")
        else:
            print(f"   Content: {response.content.decode()}")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_service_execution_api()
