#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from django.contrib.auth import get_user_model
from auctions.models import Auction, Bid, FishCategory
from payments.models import Payment
from payments.services import PaymentService
from delivery.models import Delivery
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile
from datetime import timedelta

User = get_user_model()

def test_fresh_delivery():
    print("🔍 Testing Fresh Delivery Creation")
    print("=" * 50)
    
    try:
        # Get test users
        buyer = User.objects.get(username='delivery_test_buyer')
        seller = User.objects.get(username='delivery_test_seller')
        
        # Ensure buyer has sufficient balance
        buyer.wallet_balance = Decimal('300.00')
        buyer.save()
        
        print(f"✅ Buyer: {buyer.username} (Balance: ${buyer.wallet_balance})")
        print(f"✅ Seller: {seller.username}")
        
        # Get or create fish category
        fish_category, _ = FishCategory.objects.get_or_create(
            name="Salmon",
            defaults={'name_ar': 'سلمون'}
        )
        
        # Create a simple test image
        test_image = SimpleUploadedFile(
            "test_salmon.jpg",
            b"fake image content",
            content_type="image/jpeg"
        )
        
        # Create fresh auction
        auction = Auction.objects.create(
            title="Fresh Salmon for Delivery Test",
            description="Fresh test auction for delivery testing",
            fish_category=fish_category,
            fish_type="Salmon",
            quantity=1,
            weight=Decimal('6.0'),
            catch_date=timezone.now().date(),
            catch_location="Fresh Harbor",
            starting_price=Decimal('25.00'),
            current_price=Decimal('40.00'),
            target_price=Decimal('50.00'),
            seller=seller,
            status='ended',
            start_time=timezone.now() - timedelta(hours=2),
            end_time=timezone.now() - timedelta(minutes=30),
            winner=buyer,
            payment_deadline=timezone.now() + timedelta(minutes=20),
            payment_received=False,
            main_image=test_image
        )
        
        print(f"✅ Created fresh auction: {auction.title} (ID: {auction.id})")
        
        # Create payment using service
        payment_service = PaymentService()
        payment = payment_service.create_payment(auction, buyer)
        print(f"✅ Payment created: {payment.id}")
        print(f"   Amount: ${payment.amount}")
        print(f"   Seller amount: ${payment.seller_amount}")
        print(f"   Platform fee: ${payment.platform_fee}")
        
        # Process payment
        print(f"\n💳 Processing wallet payment...")
        try:
            processed_payment = payment_service.process_wallet_payment(payment)
            print(f"✅ Payment processed successfully")
            print(f"   Payment status: {processed_payment.status}")
            
            # Refresh models
            buyer.refresh_from_db()
            auction.refresh_from_db()
            
            print(f"   Buyer balance after: ${buyer.wallet_balance}")
            print(f"   Auction payment received: {auction.payment_received}")
            
            # Check if delivery was created
            try:
                delivery = Delivery.objects.get(auction=auction)
                print(f"✅ Delivery created automatically: {delivery.tracking_number}")
                print(f"   Status: {delivery.status}")
                print(f"   Pickup: {delivery.pickup_address}")
                print(f"   Delivery: {delivery.delivery_address}")
                print(f"   Estimated pickup: {delivery.estimated_pickup_time}")
                print(f"   Estimated delivery: {delivery.estimated_delivery_time}")
                
                return True
            except Delivery.DoesNotExist:
                print("❌ No delivery created automatically")
                return False
                
        except Exception as e:
            print(f"❌ Payment processing failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fresh_delivery()
    if success:
        print("\n🎉 Fresh delivery test PASSED!")
    else:
        print("\n⚠️ Fresh delivery test FAILED!")
