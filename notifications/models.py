from django.db import models
from django.conf import settings
import uuid


class NotificationTemplate(models.Model):
    """Template for different types of notifications"""

    NOTIFICATION_TYPES = [
        # Auction notifications
        ('auction_created', 'Auction Created'),
        ('auction_starting', 'Auction Starting'),
        ('auction_started', 'Auction Started'),  # When scheduled auction goes live
        ('auction_ending_soon', 'Auction Ending Soon'),  # 1 hour before end
        ('auction_ended', 'Auction Ended'),
        ('auction_ended_with_winner', 'Auction Ended with Winner'),  # Seller notification
        ('auction_won', 'Auction Won'),
        ('auction_sold', 'Auction Sold'),  # Seller notification when auction sold
        ('auction_no_bids', 'Auction No Bids'),
        ('auction_failed', 'Auction Failed'),

        # Bidding notifications
        ('bid_placed', 'Bid Placed'),
        ('bid_outbid', 'Outbid'),

        # Payment notifications
        ('payment_reminder', 'Payment Reminder'),
        ('payment_received', 'Payment Received'),
        ('payment_confirmed', 'Payment Confirmed'),
        ('payment_success', 'Payment Success'),  # Both buyer and seller
        ('payment_timeout', 'Payment Timeout'),

        # Delivery notifications
        ('delivery_update', 'Delivery Update'),
        ('delivery_status_changed', 'Delivery Status Changed'),

        # Account notifications
        ('kyc_approved', 'KYC Approved'),
        ('kyc_rejected', 'KYC Rejected'),
        ('account_activated', 'Account Activated'),  # Seller account approved

        # Broker service notifications
        ('broker_offer_received', 'Broker Offer Received'),  # User gets broker offer
        ('broker_offer_accepted', 'Broker Offer Accepted'),  # Broker gets acceptance
        ('broker_service_status_update', 'Broker Service Status Update'),  # Service progress
    ]

    name = models.CharField(max_length=100, unique=True)
    notification_type = models.CharField(max_length=30, choices=NOTIFICATION_TYPES)

    # Email template
    email_subject = models.CharField(max_length=200)
    email_body = models.TextField()

    # WhatsApp template
    whatsapp_message = models.TextField()

    # Push notification template
    push_title = models.CharField(max_length=100)
    push_body = models.CharField(max_length=200)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.get_notification_type_display()}"

    class Meta:
        ordering = ['name']


class Notification(models.Model):
    """Individual notification record"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('read', 'Read'),
    ]

    CHANNEL_CHOICES = [
        ('email', 'Email'),
        ('whatsapp', 'WhatsApp'),
        ('push', 'Push Notification'),
        ('in_app', 'In-App'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    template = models.ForeignKey(NotificationTemplate, on_delete=models.CASCADE)

    # Notification content
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Delivery details
    channel = models.CharField(max_length=20, choices=CHANNEL_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Related objects
    auction = models.ForeignKey('auctions.Auction', on_delete=models.CASCADE, null=True, blank=True)
    payment = models.ForeignKey('payments.Payment', on_delete=models.CASCADE, null=True, blank=True)

    # External service IDs
    external_id = models.CharField(max_length=200, blank=True, null=True)

    # Timestamps
    scheduled_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title} - {self.recipient.username} ({self.channel})"

    class Meta:
        ordering = ['-created_at']


class NotificationPreference(models.Model):
    """User notification preferences"""

    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notification_preferences')

    # Email preferences
    email_auction_updates = models.BooleanField(default=True)
    email_bid_updates = models.BooleanField(default=True)
    email_payment_reminders = models.BooleanField(default=True)
    email_delivery_updates = models.BooleanField(default=True)
    email_marketing = models.BooleanField(default=False)

    # WhatsApp preferences
    whatsapp_auction_updates = models.BooleanField(default=True)
    whatsapp_bid_updates = models.BooleanField(default=True)
    whatsapp_payment_reminders = models.BooleanField(default=True)
    whatsapp_delivery_updates = models.BooleanField(default=True)

    # Push notification preferences
    push_auction_updates = models.BooleanField(default=True)
    push_bid_updates = models.BooleanField(default=True)
    push_payment_reminders = models.BooleanField(default=True)
    push_delivery_updates = models.BooleanField(default=True)

    # Timing preferences
    quiet_hours_start = models.TimeField(default='22:00')
    quiet_hours_end = models.TimeField(default='08:00')
    timezone = models.CharField(max_length=50, default='UTC')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Notification preferences for {self.user.username}"
