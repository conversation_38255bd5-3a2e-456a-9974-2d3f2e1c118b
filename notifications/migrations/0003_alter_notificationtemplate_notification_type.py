# Generated by Django 5.2.3 on 2025-07-09 11:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0002_alter_notificationtemplate_notification_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationtemplate',
            name='notification_type',
            field=models.CharField(choices=[('auction_created', 'Auction Created'), ('auction_starting', 'Auction Starting'), ('auction_started', 'Auction Started'), ('auction_ending_soon', 'Auction Ending Soon'), ('auction_ended', 'Auction Ended'), ('auction_ended_with_winner', 'Auction Ended with Winner'), ('auction_won', 'Auction Won'), ('auction_sold', 'Auction Sold'), ('auction_no_bids', 'Auction No Bids'), ('auction_failed', 'Auction Failed'), ('bid_placed', 'Bid Placed'), ('bid_outbid', 'Outbid'), ('payment_reminder', 'Payment Reminder'), ('payment_received', 'Payment Received'), ('payment_confirmed', 'Payment Confirmed'), ('payment_success', 'Payment Success'), ('payment_timeout', 'Payment Timeout'), ('delivery_update', 'Delivery Update'), ('delivery_status_changed', 'Delivery Status Changed'), ('kyc_approved', 'KYC Approved'), ('kyc_rejected', 'KYC Rejected'), ('account_activated', 'Account Activated'), ('broker_offer_received', 'Broker Offer Received'), ('broker_offer_accepted', 'Broker Offer Accepted'), ('broker_service_status_update', 'Broker Service Status Update')], max_length=30),
        ),
    ]
