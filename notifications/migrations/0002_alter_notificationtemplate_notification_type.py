# Generated by Django 4.2.23 on 2025-06-26 12:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationtemplate',
            name='notification_type',
            field=models.CharField(choices=[('auction_created', 'Auction Created'), ('auction_starting', 'Auction Starting'), ('auction_ending', 'Auction Ending Soon'), ('auction_ended', 'Auction Ended'), ('bid_placed', 'Bid Placed'), ('bid_outbid', 'Outbid'), ('auction_won', 'Auction Won'), ('payment_reminder', 'Payment Reminder'), ('payment_received', 'Payment Received'), ('payment_confirmed', 'Payment Confirmed'), ('delivery_update', 'Delivery Update'), ('kyc_approved', 'KYC Approved'), ('kyc_rejected', 'KYC Rejected')], max_length=30),
        ),
    ]
