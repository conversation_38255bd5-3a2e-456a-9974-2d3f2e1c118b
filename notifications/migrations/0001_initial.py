# Generated by Django 4.2.7 on 2025-06-15 12:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auctions', '0001_initial'),
        ('payments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('notification_type', models.CharField(choices=[('auction_created', 'Auction Created'), ('auction_starting', 'Auction Starting'), ('auction_ending', 'Auction Ending Soon'), ('auction_ended', 'Auction Ended'), ('bid_placed', 'Bid Placed'), ('bid_outbid', 'Outbid'), ('auction_won', 'Auction Won'), ('payment_reminder', 'Payment Reminder'), ('payment_received', 'Payment Received'), ('delivery_update', 'Delivery Update'), ('kyc_approved', 'KYC Approved'), ('kyc_rejected', 'KYC Rejected')], max_length=30)),
                ('email_subject', models.CharField(max_length=200)),
                ('email_body', models.TextField()),
                ('whatsapp_message', models.TextField()),
                ('push_title', models.CharField(max_length=100)),
                ('push_body', models.CharField(max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_auction_updates', models.BooleanField(default=True)),
                ('email_bid_updates', models.BooleanField(default=True)),
                ('email_payment_reminders', models.BooleanField(default=True)),
                ('email_delivery_updates', models.BooleanField(default=True)),
                ('email_marketing', models.BooleanField(default=False)),
                ('whatsapp_auction_updates', models.BooleanField(default=True)),
                ('whatsapp_bid_updates', models.BooleanField(default=True)),
                ('whatsapp_payment_reminders', models.BooleanField(default=True)),
                ('whatsapp_delivery_updates', models.BooleanField(default=True)),
                ('push_auction_updates', models.BooleanField(default=True)),
                ('push_bid_updates', models.BooleanField(default=True)),
                ('push_payment_reminders', models.BooleanField(default=True)),
                ('push_delivery_updates', models.BooleanField(default=True)),
                ('quiet_hours_start', models.TimeField(default='22:00')),
                ('quiet_hours_end', models.TimeField(default='08:00')),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('channel', models.CharField(choices=[('email', 'Email'), ('whatsapp', 'WhatsApp'), ('push', 'Push Notification'), ('in_app', 'In-App')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('read', 'Read')], default='pending', max_length=20)),
                ('external_id', models.CharField(blank=True, max_length=200, null=True)),
                ('scheduled_at', models.DateTimeField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('auction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='auctions.auction')),
                ('payment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='payments.payment')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notifications.notificationtemplate')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
