from django.urls import path
from .views import (
    NotificationListView, NotificationDetailView, NotificationPreferenceView,
    mark_all_read, unread_count, send_test_notification
)

app_name = 'notifications'

urlpatterns = [
    # Notifications
    path('', NotificationListView.as_view(), name='notification_list'),
    path('<uuid:pk>/', NotificationDetailView.as_view(), name='notification_detail'),
    path('preferences/', NotificationPreferenceView.as_view(), name='notification_preferences'),
    path('unread-count/', unread_count, name='unread_count'),
    path('mark-all-read/', mark_all_read, name='mark_all_read'),
    path('test/', send_test_notification, name='send_test_notification'),
]
