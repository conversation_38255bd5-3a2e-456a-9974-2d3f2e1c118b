"""
UltraMsg WhatsApp API Service
Handles sending WhatsApp messages through UltraMsg API
"""

import requests
import logging
from django.conf import settings
from typing import Optional, Dict, Any
import json

logger = logging.getLogger(__name__)


class UltraMsgService:
    """Service for sending WhatsApp messages via UltraMsg API"""
    
    def __init__(self):
        self.instance_id = getattr(settings, 'ULTRAMSG_INSTANCE_ID', None)
        self.token = getattr(settings, 'ULTRAMSG_TOKEN', None)
        self.base_url = f"https://api.ultramsg.com/{self.instance_id}"
        
        if not self.instance_id or not self.token:
            logger.warning("UltraMsg credentials not configured. WhatsApp notifications will be disabled.")
    
    def is_configured(self) -> bool:
        """Check if UltraMsg is properly configured"""
        return bool(self.instance_id and self.token)
    
    def send_text_message(self, phone_number: str, message: str) -> Dict[str, Any]:
        """
        Send a text message via WhatsApp
        
        Args:
            phone_number: Phone number with international format (e.g., +1234567890)
            message: Message text (max 4096 characters)
            
        Returns:
            Dict containing the API response
        """
        if not self.is_configured():
            logger.error("UltraMsg not configured. Cannot send WhatsApp message.")
            return {"success": False, "error": "UltraMsg not configured"}
        
        # Ensure phone number has + prefix
        if not phone_number.startswith('+'):
            phone_number = '+' + phone_number
        
        url = f"{self.base_url}/messages/chat"
        
        payload = {
            'token': self.token,
            'to': phone_number,
            'body': message
        }
        
        try:
            logger.info(f"Sending WhatsApp message to {phone_number}")
            response = requests.post(url, data=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('sent'):
                logger.info(f"✅ WhatsApp message sent successfully to {phone_number}")
                return {
                    "success": True,
                    "message_id": result.get('id'),
                    "response": result
                }
            else:
                logger.error(f"❌ Failed to send WhatsApp message: {result}")
                return {
                    "success": False,
                    "error": result.get('error', 'Unknown error'),
                    "response": result
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Network error sending WhatsApp message: {e}")
            return {
                "success": False,
                "error": f"Network error: {str(e)}"
            }
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON response from UltraMsg: {e}")
            return {
                "success": False,
                "error": f"Invalid response format: {str(e)}"
            }
        except Exception as e:
            logger.error(f"❌ Unexpected error sending WhatsApp message: {e}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}"
            }
    
    def send_image_message(self, phone_number: str, image_url: str, caption: str = "") -> Dict[str, Any]:
        """
        Send an image message via WhatsApp
        
        Args:
            phone_number: Phone number with international format
            image_url: URL of the image to send
            caption: Optional caption for the image
            
        Returns:
            Dict containing the API response
        """
        if not self.is_configured():
            logger.error("UltraMsg not configured. Cannot send WhatsApp image.")
            return {"success": False, "error": "UltraMsg not configured"}
        
        # Ensure phone number has + prefix
        if not phone_number.startswith('+'):
            phone_number = '+' + phone_number
        
        url = f"{self.base_url}/messages/image"
        
        payload = {
            'token': self.token,
            'to': phone_number,
            'image': image_url,
            'caption': caption
        }
        
        try:
            logger.info(f"Sending WhatsApp image to {phone_number}")
            response = requests.post(url, data=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('sent'):
                logger.info(f"✅ WhatsApp image sent successfully to {phone_number}")
                return {
                    "success": True,
                    "message_id": result.get('id'),
                    "response": result
                }
            else:
                logger.error(f"❌ Failed to send WhatsApp image: {result}")
                return {
                    "success": False,
                    "error": result.get('error', 'Unknown error'),
                    "response": result
                }
                
        except Exception as e:
            logger.error(f"❌ Error sending WhatsApp image: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_instance_status(self) -> Dict[str, Any]:
        """
        Get the status of the WhatsApp instance
        
        Returns:
            Dict containing instance status information
        """
        if not self.is_configured():
            return {"success": False, "error": "UltraMsg not configured"}
        
        url = f"{self.base_url}/instance/status"
        
        try:
            response = requests.get(url, params={'token': self.token}, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Instance status: {result}")
            
            return {
                "success": True,
                "status": result
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting instance status: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def check_phone_number(self, phone_number: str) -> Dict[str, Any]:
        """
        Check if a phone number is valid and has WhatsApp
        
        Args:
            phone_number: Phone number to check
            
        Returns:
            Dict containing validation result
        """
        if not self.is_configured():
            return {"success": False, "error": "UltraMsg not configured"}
        
        # Ensure phone number has + prefix
        if not phone_number.startswith('+'):
            phone_number = '+' + phone_number
        
        url = f"{self.base_url}/contacts/check"
        
        try:
            response = requests.get(
                url, 
                params={'token': self.token, 'chatId': phone_number}, 
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            return {
                "success": True,
                "valid": result.get('valid', False),
                "has_whatsapp": result.get('exists', False),
                "response": result
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking phone number: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def send_document_message(self, phone_number: str, document_url: str, caption: str = "") -> Dict[str, Any]:
        """
        Send a document message via WhatsApp

        Args:
            phone_number: Phone number with international format
            document_url: URL of the document to send
            caption: Optional caption for the document

        Returns:
            Dict containing the API response
        """
        if not self.is_configured():
            logger.error("UltraMsg not configured. Cannot send WhatsApp document.")
            return {"success": False, "error": "UltraMsg not configured"}

        # Ensure phone number has + prefix
        if not phone_number.startswith('+'):
            phone_number = '+' + phone_number

        url = f"{self.base_url}/messages/document"

        payload = {
            'token': self.token,
            'to': phone_number,
            'document': document_url,
            'caption': caption
        }

        try:
            logger.info(f"Sending WhatsApp document to {phone_number}")
            response = requests.post(url, data=payload, timeout=30)
            response.raise_for_status()

            result = response.json()

            if result.get('sent'):
                logger.info(f"✅ WhatsApp document sent successfully to {phone_number}")
                return {
                    "success": True,
                    "message_id": result.get('id'),
                    "response": result
                }
            else:
                logger.error(f"❌ Failed to send WhatsApp document: {result}")
                return {
                    "success": False,
                    "error": result.get('error', 'Unknown error'),
                    "response": result
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Network error sending WhatsApp document: {str(e)}")
            return {"success": False, "error": f"Network error: {str(e)}"}
        except Exception as e:
            logger.error(f"❌ Failed to send WhatsApp document: {str(e)}")
            return {"success": False, "error": str(e)}


# Global instance
ultramsg_service = UltraMsgService()
