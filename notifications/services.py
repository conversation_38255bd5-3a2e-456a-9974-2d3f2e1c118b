from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from twilio.rest import Client
from .models import Notification, NotificationTemplate, NotificationPreference
from .ultramsg_service import ultramsg_service
import logging

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for sending notifications through various channels"""
    
    def __init__(self):
        self.twilio_client = None
        if settings.TWILIO_ACCOUNT_SID and settings.TWILIO_AUTH_TOKEN:
            self.twilio_client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
    
    def send_notification(self, user, notification_type, context=None, channels=None):
        """Send notification to user through specified channels"""
        try:
            # Get notification template
            template = NotificationTemplate.objects.get(
                notification_type=notification_type,
                is_active=True
            )
            
            # Get user preferences
            preferences, _ = NotificationPreference.objects.get_or_create(user=user)
            
            # Determine channels to use
            if channels is None:
                channels = self._get_enabled_channels(preferences, notification_type)
            
            context = context or {}
            context.update({
                'user': user,
                'user_name': user.get_full_name() or user.username,
            })
            
            notifications_sent = []
            
            for channel in channels:
                try:
                    if channel == 'email' and self._should_send_email(preferences, notification_type):
                        notification = self._send_email(user, template, context)
                        notifications_sent.append(notification)
                    
                    elif channel == 'whatsapp' and self._should_send_whatsapp(preferences, notification_type):
                        notification = self._send_whatsapp(user, template, context)
                        notifications_sent.append(notification)
                    
                    elif channel == 'in_app':
                        notification = self._create_in_app_notification(user, template, context)
                        notifications_sent.append(notification)
                        
                except Exception as e:
                    logger.error(f"Failed to send {channel} notification to {user.username}: {str(e)}")
            
            return notifications_sent
            
        except NotificationTemplate.DoesNotExist:
            logger.error(f"Notification template not found for type: {notification_type}")
            return []
        except Exception as e:
            logger.error(f"Failed to send notification: {str(e)}")
            return []
    
    def _get_enabled_channels(self, preferences, notification_type):
        """Get enabled channels based on user preferences"""
        channels = []
        
        # Check email preferences
        if getattr(preferences, f'email_{notification_type}', True):
            channels.append('email')
        
        # Check WhatsApp preferences
        if getattr(preferences, f'whatsapp_{notification_type}', True):
            channels.append('whatsapp')
        
        # Always include in-app notifications
        channels.append('in_app')
        
        return channels
    
    def _should_send_email(self, preferences, notification_type):
        """Check if email should be sent based on preferences"""
        return getattr(preferences, f'email_{notification_type}', True)
    
    def _should_send_whatsapp(self, preferences, notification_type):
        """Check if WhatsApp should be sent based on preferences"""
        return getattr(preferences, f'whatsapp_{notification_type}', True)
    
    def _send_email(self, user, template, context):
        """Send email notification"""
        try:
            subject = self._render_template(template.email_subject, context)
            message = self._render_template(template.email_body, context)
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[user.email],
                fail_silently=False,
            )
            
            # Create notification record
            notification = Notification.objects.create(
                recipient=user,
                template=template,
                title=subject,
                message=message,
                channel='email',
                status='sent'
            )
            
            logger.info(f"Email sent to {user.email}")
            return notification
            
        except Exception as e:
            logger.error(f"Failed to send email to {user.email}: {str(e)}")
            # Create failed notification record
            notification = Notification.objects.create(
                recipient=user,
                template=template,
                title=template.email_subject,
                message=str(e),
                channel='email',
                status='failed'
            )
            return notification
    
    def _send_whatsapp(self, user, template, context):
        """Send WhatsApp notification via UltraMsg"""
        try:
            if not ultramsg_service.is_configured():
                raise Exception("UltraMsg WhatsApp service not configured")

            if not user.phone_number:
                raise Exception("User phone number not available")

            message_body = self._render_template(template.whatsapp_message, context)

            # Send via UltraMsg
            result = ultramsg_service.send_text_message(user.phone_number, message_body)

            if result['success']:
                # Create successful notification record
                notification = Notification.objects.create(
                    recipient=user,
                    template=template,
                    title=template.push_title,
                    message=message_body,
                    channel='whatsapp',
                    status='sent',
                    external_id=result.get('message_id')
                )

                logger.info(f"✅ WhatsApp sent to {user.phone_number} via UltraMsg")
                return notification
            else:
                raise Exception(f"UltraMsg error: {result.get('error', 'Unknown error')}")

        except Exception as e:
            logger.error(f"❌ Failed to send WhatsApp to {user.phone_number}: {str(e)}")
            # Create failed notification record
            notification = Notification.objects.create(
                recipient=user,
                template=template,
                title=template.push_title,
                message=str(e),
                channel='whatsapp',
                status='failed'
            )
            return notification
    
    def _create_in_app_notification(self, user, template, context):
        """Create in-app notification"""
        try:
            title = self._render_template(template.push_title, context)
            message = self._render_template(template.push_body, context)
            
            notification = Notification.objects.create(
                recipient=user,
                template=template,
                title=title,
                message=message,
                channel='in_app',
                status='sent'
            )
            
            logger.info(f"In-app notification created for {user.username}")
            return notification
            
        except Exception as e:
            logger.error(f"Failed to create in-app notification for {user.username}: {str(e)}")
            return None
    
    def _render_template(self, template_string, context):
        """Render template string with context"""
        from django.template import Template, Context
        template = Template(template_string)
        return template.render(Context(context))

    def send_whatsapp_message(self, user, message):
        """Send a direct WhatsApp message to user"""
        try:
            if not user.phone_number:
                logger.warning(f"User {user.id} has no phone number for WhatsApp")
                return None

            ultramsg_service = UltraMsgService()

            # Send via UltraMsg
            result = ultramsg_service.send_text_message(user.phone_number, message)

            if result['success']:
                logger.info(f"✅ WhatsApp message sent to {user.phone_number}")
                return result
            else:
                logger.error(f"❌ Failed to send WhatsApp message: {result.get('error', 'Unknown error')}")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to send WhatsApp message to {user.phone_number}: {str(e)}")
            return None

    def send_whatsapp_document(self, user, document_url, caption=""):
        """Send a document via WhatsApp to user"""
        try:
            if not user.phone_number:
                logger.warning(f"User {user.id} has no phone number for WhatsApp")
                return None

            ultramsg_service = UltraMsgService()

            # Send document via UltraMsg
            result = ultramsg_service.send_document_message(user.phone_number, document_url, caption)

            if result['success']:
                logger.info(f"✅ WhatsApp document sent to {user.phone_number}")
                return result
            else:
                logger.error(f"❌ Failed to send WhatsApp document: {result.get('error', 'Unknown error')}")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to send WhatsApp document to {user.phone_number}: {str(e)}")
            return None


# Convenience functions for common notifications
def send_auction_notification(auction, notification_type, additional_context=None):
    """Send auction-related notifications"""
    service = NotificationService()
    context = {
        'auction': auction,
        'auction_title': auction.title,
        'auction_url': f'/auctions/{auction.id}/',
    }
    
    if additional_context:
        context.update(additional_context)
    
    notifications = []
    
    if notification_type == 'auction_created':
        # Notify interested users (e.g., those watching similar auctions)
        # For now, just notify the seller
        notifications.extend(service.send_notification(auction.seller, notification_type, context))
    
    elif notification_type == 'auction_ending':
        # Notify all bidders
        bidders = auction.bids.values_list('bidder', flat=True).distinct()
        for bidder_id in bidders:
            from accounts.models import User
            bidder = User.objects.get(id=bidder_id)
            notifications.extend(service.send_notification(bidder, notification_type, context))
    
    elif notification_type == 'auction_won':
        # Notify winner
        if auction.winner:
            notifications.extend(service.send_notification(auction.winner, notification_type, context))
    
    return notifications


def send_payment_notification(payment, notification_type, additional_context=None):
    """Send payment-related notifications"""
    service = NotificationService()
    context = {
        'payment': payment,
        'auction': payment.auction,
        'amount': payment.amount,
    }
    
    if additional_context:
        context.update(additional_context)
    
    notifications = []
    
    if notification_type == 'payment_reminder':
        notifications.extend(service.send_notification(payment.buyer, notification_type, context))
    
    elif notification_type == 'payment_received':
        notifications.extend(service.send_notification(payment.seller, notification_type, context))
    
    return notifications
