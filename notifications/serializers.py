from rest_framework import serializers
from django.utils import timezone
from .models import (
    NotificationTemplate, Notification, NotificationPreference
)
from accounts.serializers import UserSerializer


class NotificationTemplateSerializer(serializers.ModelSerializer):
    """Serializer for notification templates"""
    
    notification_type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    
    class Meta:
        model = NotificationTemplate
        fields = [
            'id', 'name', 'notification_type', 'notification_type_display',
            'email_subject', 'email_body', 'whatsapp_message',
            'push_title', 'push_body', 'is_active'
        ]


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer for notifications"""
    
    recipient = UserSerializer(read_only=True)
    template = NotificationTemplateSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    channel_display = serializers.CharField(source='get_channel_display', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'recipient', 'template', 'title', 'message', 'channel',
            'channel_display', 'status', 'status_display', 'auction', 'payment',
            'external_id', 'scheduled_at', 'sent_at', 'delivered_at', 'read_at',
            'created_at'
        ]
        read_only_fields = [
            'recipient', 'template', 'title', 'message', 'channel',
            'auction', 'payment', 'external_id', 'scheduled_at', 'sent_at',
            'delivered_at', 'created_at'
        ]
    
    def update(self, instance, validated_data):
        # If marking as read, set read_at timestamp
        if validated_data.get('status') == 'read' and instance.status != 'read':
            validated_data['read_at'] = timezone.now()
        
        return super().update(instance, validated_data)


class NotificationPreferenceSerializer(serializers.ModelSerializer):
    """Serializer for notification preferences"""
    
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = NotificationPreference
        fields = [
            'id', 'user', 'email_auction_updates', 'email_bid_updates',
            'email_payment_reminders', 'email_delivery_updates', 'email_marketing',
            'whatsapp_auction_updates', 'whatsapp_bid_updates',
            'whatsapp_payment_reminders', 'whatsapp_delivery_updates',
            'push_auction_updates', 'push_bid_updates', 'push_payment_reminders',
            'push_delivery_updates', 'quiet_hours_start', 'quiet_hours_end',
            'timezone', 'created_at', 'updated_at'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at']


class NotificationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating notifications"""
    
    class Meta:
        model = Notification
        fields = [
            'recipient', 'template', 'title', 'message', 'channel',
            'auction', 'payment', 'scheduled_at'
        ]
    
    def validate(self, attrs):
        recipient = attrs.get('recipient')
        channel = attrs.get('channel')
        
        # Validate recipient has necessary contact info for channel
        if channel == 'email' and not recipient.email:
            raise serializers.ValidationError("Recipient must have email for email notifications")
        
        if channel == 'whatsapp' and not recipient.phone_number:
            raise serializers.ValidationError("Recipient must have phone number for WhatsApp notifications")
        
        return attrs
