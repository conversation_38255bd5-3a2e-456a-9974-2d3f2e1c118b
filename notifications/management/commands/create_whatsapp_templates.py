"""
Management command to create WhatsApp notification templates for UltraMsg
"""

from django.core.management.base import BaseCommand
from notifications.models import NotificationTemplate


class Command(BaseCommand):
    help = 'Create WhatsApp notification templates for fish auction app'

    def handle(self, *args, **options):
        """Create or update WhatsApp notification templates"""
        
        templates = [
            {
                'name': 'Auction Won WhatsApp',
                'notification_type': 'auction_won',
                'email_subject': '🎉 Congratulations! You won the auction',
                'email_body': '''Dear {{ user_name }},

Congratulations! You have won the auction for "{{ auction.title }}".

Final Price: ${{ auction.current_price }}
Payment Deadline: {{ auction.payment_deadline }}

Please complete your payment within 20 minutes to secure your purchase.

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''🎉 *Congratulations {{ user_name }}!*

You won the auction for *{{ auction.title }}*

💰 Final Price: ${{ auction.current_price }}
⏰ Payment Deadline: {{ auction.payment_deadline|date:"M d, Y H:i" }}

Please complete payment within 20 minutes to secure your purchase.

🐟 Fish Auction Team''',
                'push_title': '🎉 Auction Won!',
                'push_body': 'You won {{ auction.title }} for ${{ auction.current_price }}'
            },
            {
                'name': 'Payment Reminder WhatsApp',
                'notification_type': 'payment_reminder',
                'email_subject': '⏰ Payment Reminder - Action Required',
                'email_body': '''Dear {{ user_name }},

This is a reminder that your payment for "{{ auction.title }}" is due soon.

Amount: ${{ auction.current_price }}
Time Remaining: {{ time_remaining|default:"5 minutes" }}

Please complete your payment to avoid losing your winning bid.

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''⏰ *Payment Reminder*

Hi {{ user_name }}, your payment for *{{ auction.title }}* is due soon!

💰 Amount: ${{ auction.current_price }}
⏰ Time Remaining: {{ time_remaining|default:"5 minutes" }}

Please complete payment now to secure your purchase.

🐟 Fish Auction Team''',
                'push_title': '⏰ Payment Due Soon',
                'push_body': 'Payment for {{ auction.title }} due in {{ time_remaining|default:"5 minutes" }}'
            },
            {
                'name': 'Auction Ending WhatsApp',
                'notification_type': 'auction_ending',
                'email_subject': '⏰ Auction Ending Soon',
                'email_body': '''Dear {{ user_name }},

The auction for "{{ auction.title }}" is ending soon!

Current Price: ${{ auction.current_price }}
Time Remaining: Less than 1 hour

Don't miss your chance to bid!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''⏰ *Auction Ending Soon!*

{{ user_name }}, the auction for *{{ auction.title }}* is ending soon!

💰 Current Price: ${{ auction.current_price }}
⏰ Time Remaining: Less than 1 hour

Don't miss your chance to bid! 🐟

🔗 Bid now in the app''',
                'push_title': '⏰ Auction Ending Soon',
                'push_body': '{{ auction.title }} ends in less than 1 hour'
            },
            {
                'name': 'Bid Outbid WhatsApp',
                'notification_type': 'bid_outbid',
                'email_subject': '📢 You have been outbid',
                'email_body': '''Dear {{ user_name }},

You have been outbid on "{{ auction.title }}".

Your Bid: ${{ user_bid_amount }}
Current Price: ${{ auction.current_price }}

Place a new bid to stay in the competition!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''📢 *You've been outbid!*

{{ user_name }}, someone outbid you on *{{ auction.title }}*

Your Bid: ${{ user_bid_amount }}
💰 Current Price: ${{ auction.current_price }}

Place a new bid to stay in the competition! 🐟

🔗 Bid now in the app''',
                'push_title': '📢 Outbid Alert',
                'push_body': 'You were outbid on {{ auction.title }}'
            },
            {
                'name': 'Auction Created WhatsApp',
                'notification_type': 'auction_created',
                'email_subject': '🐟 New Auction Created',
                'email_body': '''Dear {{ user_name }},

Your auction "{{ auction.title }}" has been created successfully.

Starting Price: ${{ auction.starting_price }}
End Time: {{ auction.end_time }}

Your auction will go live as scheduled.

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''🐟 *Auction Created Successfully!*

{{ user_name }}, your auction *{{ auction.title }}* is ready!

💰 Starting Price: ${{ auction.starting_price }}
⏰ End Time: {{ auction.end_time|date:"M d, Y H:i" }}

Your auction will go live as scheduled. 🎣

🔗 View in the app''',
                'push_title': '🐟 Auction Created',
                'push_body': 'Your auction {{ auction.title }} has been created'
            },
            {
                'name': 'KYC Approved WhatsApp',
                'notification_type': 'kyc_approved',
                'email_subject': '✅ KYC Verification Approved',
                'email_body': '''Dear {{ user_name }},

Your KYC verification has been approved!

You can now participate in all auction activities including bidding and selling.

Welcome to Fish Auction!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''✅ *KYC Approved!*

Great news {{ user_name }}! Your KYC verification is approved.

You can now:
🎣 Participate in auctions
💰 Place bids
🐟 Create your own auctions

Welcome to Fish Auction! 🎉''',
                'push_title': '✅ KYC Approved',
                'push_body': 'Your account is now fully verified!'
            },
            {
                'name': 'KYC Rejected WhatsApp',
                'notification_type': 'kyc_rejected',
                'email_subject': '❌ KYC Verification Rejected',
                'email_body': '''Dear {{ user_name }},

Unfortunately, your KYC verification has been rejected.

Reason: {{ rejection_reason|default:"Please check your documents and try again" }}

You can resubmit your documents for verification.

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''❌ *KYC Verification Rejected*

{{ user_name }}, your KYC verification was rejected.

📝 Reason: {{ rejection_reason|default:"Please check your documents and try again" }}

You can resubmit your documents in the app.

🔗 Try again in the app''',
                'push_title': '❌ KYC Rejected',
                'push_body': 'Please resubmit your verification documents'
            },
            {
                'name': 'Delivery Update WhatsApp',
                'notification_type': 'delivery_update',
                'email_subject': '🚚 Delivery Update',
                'email_body': '''Dear {{ user_name }},

Delivery update for your purchase "{{ auction.title }}":

Status: {{ delivery_status }}
{{ delivery_message }}

Track your delivery in the app.

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''🚚 *Delivery Update*

{{ user_name }}, update on your *{{ auction.title }}* delivery:

📦 Status: {{ delivery_status }}
{{ delivery_message }}

🔗 Track in the app

🐟 Fish Auction Team''',
                'push_title': '🚚 Delivery Update',
                'push_body': '{{ auction.title }} - {{ delivery_status }}'
            }
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    if key != 'name':  # Don't update the name
                        setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'🔄 Updated template: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Summary: {created_count} created, {updated_count} updated'
            )
        )
        self.stdout.write(
            self.style.SUCCESS('🎉 WhatsApp templates are ready for UltraMsg!')
        )
