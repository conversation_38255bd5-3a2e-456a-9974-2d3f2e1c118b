from django.core.management.base import BaseCommand
from notifications.models import NotificationTemplate


class Command(BaseCommand):
    help = 'Set up notification templates for the fish auction app'

    def handle(self, *args, **options):
        templates = [
            {
                'name': 'Auction Won',
                'notification_type': 'auction_won',
                'email_subject': '🎉 Congratulations! You won the auction for {{ auction.title }}',
                'email_body': '''Dear {{ user_name }},

Congratulations! You have won the auction for "{{ auction.title }}".

Auction Details:
- Item: {{ auction.title }}
- Final Price: ${{ auction.current_price }}
- Seller: {{ auction.seller.get_full_name }}

Payment Information:
You have 20 minutes to complete your payment to confirm your winning bid. 
If payment is not received within this time, the auction will be awarded to the next highest bidder.

Please complete your payment as soon as possible to secure your purchase.

Thank you for using our fish auction platform!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''🎉 Congratulations {{ user_name }}! 

You won the auction for "{{ auction.title }}" at ${{ auction.current_price }}.

⏰ IMPORTANT: You have 20 minutes to complete payment to confirm your win.

Complete payment now to secure your purchase!

Fish Auction Team''',
                'push_title': '🎉 Auction Won!',
                'push_body': 'You won {{ auction.title }} for ${{ auction.current_price }}. Pay within 20 minutes!'
            },
            {
                'name': 'Payment Reminder',
                'notification_type': 'payment_reminder',
                'email_subject': '⏰ Payment Reminder - {{ auction.title }}',
                'email_body': '''Dear {{ user_name }},

This is a reminder that your payment for the auction "{{ auction.title }}" is still pending.

Auction Details:
- Item: {{ auction.title }}
- Amount Due: ${{ auction.current_price }}
- Time Remaining: Please pay immediately to avoid losing your winning bid

If you do not complete payment soon, the auction will be awarded to the next highest bidder.

Please complete your payment now to secure your purchase.

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''⏰ URGENT: Payment reminder for {{ auction.title }}

Amount due: ${{ auction.current_price }}

Pay now or lose your winning bid to the next bidder!

Fish Auction Team''',
                'push_title': '⏰ Payment Reminder',
                'push_body': 'Pay for {{ auction.title }} now or lose your winning bid!'
            },
            {
                'name': 'Auction Reassigned',
                'notification_type': 'auction_won',
                'email_subject': '🎉 You are now the winner of {{ auction.title }}!',
                'email_body': '''Dear {{ user_name }},

Great news! The auction for "{{ auction.title }}" has been reassigned to you as the previous winner did not complete payment in time.

Auction Details:
- Item: {{ auction.title }}
- Your Winning Price: ${{ auction.current_price }}
- Seller: {{ auction.seller.get_full_name }}

Payment Information:
You have 20 minutes to complete your payment to confirm your winning bid.

Please complete your payment as soon as possible to secure your purchase.

Thank you for using our fish auction platform!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''🎉 Good news {{ user_name }}! 

You are now the winner of "{{ auction.title }}" at ${{ auction.current_price }}!

The previous winner didn't pay in time.

⏰ You have 20 minutes to complete payment.

Pay now to secure your purchase!

Fish Auction Team''',
                'push_title': '🎉 You Won!',
                'push_body': 'You are now the winner of {{ auction.title }}! Pay within 20 minutes.'
            },
            {
                'name': 'Payment Received - Buyer',
                'notification_type': 'payment_confirmed',
                'email_subject': '✅ Payment Confirmed - {{ auction.title }}',
                'email_body': '''Dear {{ user_name }},

Your payment for "{{ auction.title }}" has been successfully received and confirmed.

Purchase Details:
- Item: {{ auction.title }}
- Amount Paid: ${{ payment.amount }}
- Transaction ID: {{ payment.id }}

Next Steps:
The seller will now prepare your order for delivery. You will receive tracking information and can follow the delivery progress in the app.

You can now view the seller's live location and track your delivery in real-time.

Thank you for your purchase!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''✅ Payment confirmed for {{ auction.title }}!

Amount: ${{ payment.amount }}

Your order is being prepared for delivery. Track progress in the app!

Fish Auction Team''',
                'push_title': '✅ Payment Confirmed',
                'push_body': 'Payment received for {{ auction.title }}. Track your delivery now!'
            },
            {
                'name': 'Payment Received - Seller',
                'notification_type': 'payment_received',
                'email_subject': '💰 Payment Received for {{ auction.title }}',
                'email_body': '''Dear {{ user_name }},

Great news! You have received payment for your auction "{{ auction.title }}".

Sale Details:
- Item: {{ auction.title }}
- Sale Price: ${{ payment.amount }}
- Your Earnings: ${{ payment.seller_amount }}
- Buyer: {{ payment.buyer.username }}
- Transaction ID: {{ payment.id }}

The payment has been added to your wallet balance. You can now prepare the item for delivery.

Next Steps:
1. Prepare the item for pickup/delivery
2. Update delivery status in the app
3. Coordinate with the buyer for delivery

Thank you for selling on our platform!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''💰 Payment received for {{ auction.title }}!

Sale price: ${{ payment.amount }}
Your earnings: ${{ payment.seller_amount }}
Buyer: {{ payment.buyer.username }}

Funds added to your wallet. Prepare item for delivery!

Fish Auction Team''',
                'push_title': '💰 Payment Received',
                'push_body': 'You received ${{ payment.seller_amount }} for {{ auction.title }}!'
            },
            {
                'name': 'Delivery Update',
                'notification_type': 'delivery_update',
                'email_subject': '🚚 Delivery Update - {{ auction.title }}',
                'email_body': '''Dear {{ user_name }},

Your order for "{{ auction.title }}" has been updated.

Delivery Status: {{ delivery_status }}
Estimated Delivery: {{ estimated_delivery }}

You can track the live location of your delivery and see real-time updates in the app.

Thank you for your patience!

Best regards,
Fish Auction Team''',
                'whatsapp_message': '''🚚 Delivery update for {{ auction.title }}

Status: {{ delivery_status }}
ETA: {{ estimated_delivery }}

Track live location in the app!

Fish Auction Team''',
                'push_title': '🚚 Delivery Update',
                'push_body': '{{ auction.title }} - {{ delivery_status }}'
            }
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count + updated_count} templates '
                f'({created_count} created, {updated_count} updated)'
            )
        )
