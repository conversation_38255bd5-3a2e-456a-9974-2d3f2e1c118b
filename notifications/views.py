from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .models import (
    NotificationTemplate, Notification, NotificationPreference
)
from .serializers import (
    NotificationTemplateSerializer, NotificationSerializer,
    NotificationPreferenceSerializer
)
from .services import NotificationService


class NotificationListView(generics.ListAPIView):
    """List user's notifications"""

    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status', 'channel']

    def get_queryset(self):
        return Notification.objects.filter(recipient=self.request.user)

    @extend_schema(
        summary="List my notifications",
        description="Get all notifications for the authenticated user",
        parameters=[
            OpenApiParameter('status', str, description='Filter by notification status'),
            OpenApiParameter('channel', str, description='Filter by notification channel'),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class NotificationDetailView(generics.RetrieveUpdateAPIView):
    """Get and update notification (mark as read)"""

    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Notification.objects.filter(recipient=self.request.user)

    @extend_schema(
        summary="Get notification details",
        description="Get detailed information about a specific notification"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Update notification",
        description="Update notification status (e.g., mark as read)"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class NotificationPreferenceView(generics.RetrieveUpdateAPIView):
    """Get and update notification preferences"""

    serializer_class = NotificationPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        preference, created = NotificationPreference.objects.get_or_create(
            user=self.request.user
        )
        return preference

    @extend_schema(
        summary="Get notification preferences",
        description="Get notification preferences for the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Update notification preferences",
        description="Update notification preferences for the authenticated user"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Mark all notifications as read",
    description="Mark all unread notifications as read for the authenticated user"
)
def mark_all_read(request):
    """Mark all notifications as read"""
    updated_count = Notification.objects.filter(
        recipient=request.user,
        status__in=['sent', 'delivered']
    ).update(status='read')

    return Response({
        'message': f'Marked {updated_count} notifications as read'
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Get unread notification count",
    description="Get the count of unread notifications for the authenticated user"
)
def unread_count(request):
    """Get unread notification count"""
    count = Notification.objects.filter(
        recipient=request.user,
        channel='in_app',
        status__in=['sent', 'delivered']
    ).count()

    return Response({'unread_count': count})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Send test notification",
    description="Send a test notification to verify notification settings"
)
def send_test_notification(request):
    """Send test notification"""
    try:
        notification_service = NotificationService()
        notifications = notification_service.send_notification(
            request.user,
            'test_notification',
            {'test_message': 'This is a test notification from Fish Auction Platform'},
            ['in_app']  # Only send in-app for testing to avoid external service issues
        )
        return Response({
            'message': 'Test notification sent successfully',
            'notifications_sent': len(notifications)
        })
    except Exception as e:
        return Response(
            {'error': f'Failed to send test notification: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
