from celery import shared_task
from django.utils import timezone
from datetime import timedelta
from .services import NotificationService, send_auction_notification, send_payment_notification
import logging

logger = logging.getLogger(__name__)


@shared_task
def send_notification_task(user_id, notification_type, context=None, channels=None):
    """Celery task to send notifications asynchronously"""
    try:
        from accounts.models import User
        user = User.objects.get(id=user_id)
        
        service = NotificationService()
        notifications = service.send_notification(user, notification_type, context, channels)
        
        return f"Sent {len(notifications)} notifications to {user.username}"
        
    except User.DoesNotExist:
        logger.error(f"User with id {user_id} not found")
        return f"User with id {user_id} not found"
    except Exception as e:
        logger.error(f"Failed to send notification: {str(e)}")
        return f"Failed to send notification: {str(e)}"


@shared_task
def send_auction_ending_reminders():
    """Send reminders for auctions ending in 1 hour to bidders"""
    try:
        from auctions.models import Auction
        from django.contrib.auth import get_user_model

        User = get_user_model()
        now = timezone.now()
        ending_soon = now + timedelta(hours=1)  # 1 hour warning

        # Get auctions ending in approximately 1 hour (within 5 minute window)
        auctions = Auction.objects.filter(
            status='live',
            end_time__lte=ending_soon + timedelta(minutes=5),
            end_time__gt=ending_soon - timedelta(minutes=5)
        )

        notifications_sent = 0

        for auction in auctions:
            try:
                # Get all unique bidders for this auction
                bidders = auction.bids.values_list('bidder', flat=True).distinct()

                for bidder_id in bidders:
                    bidder = User.objects.get(id=bidder_id)

                    # Get user's highest bid for context
                    user_bid = auction.bids.filter(bidder=bidder).order_by('-amount').first()

                    # Send notification using the new template
                    service = NotificationService()
                    context = {
                        'auction': auction,
                        'user_bid_amount': user_bid.amount if user_bid else 0,
                        'time_remaining': auction.end_time - now
                    }

                    notifications = service.send_notification(
                        bidder,
                        'auction_ending_soon',
                        context,
                        channels=['whatsapp', 'in_app']  # Both WhatsApp and in-app
                    )
                    notifications_sent += len(notifications)

                    logger.info(f"✅ Sent ending reminder to {bidder.username} for auction {auction.title}")

            except Exception as e:
                logger.error(f"Failed to send ending reminder for auction {auction.id}: {str(e)}")

        logger.info(f"Sent {notifications_sent} auction ending reminders")
        return f"Sent {notifications_sent} auction ending reminders"

    except Exception as e:
        logger.error(f"Failed to send auction ending reminders: {str(e)}")
        return f"Failed to send auction ending reminders: {str(e)}"


@shared_task
def send_payment_reminders():
    """Send payment reminders for overdue payments"""
    try:
        from payments.models import Payment
        
        # Get overdue payments
        now = timezone.now()
        overdue_payments = Payment.objects.filter(
            status='pending',
            payment_deadline__lt=now
        )
        
        notifications_sent = 0
        
        for payment in overdue_payments:
            try:
                notifications = send_payment_notification(payment, 'payment_reminder')
                notifications_sent += len(notifications)
            except Exception as e:
                logger.error(f"Failed to send payment reminder for payment {payment.id}: {str(e)}")
        
        return f"Sent {notifications_sent} payment reminders"
        
    except Exception as e:
        logger.error(f"Failed to send payment reminders: {str(e)}")
        return f"Failed to send payment reminders: {str(e)}"


# REMOVED: Duplicate auto-bid task that was causing same user to bid against themselves
# The correct auto-bid logic is in auctions/tasks.py


@shared_task
def end_expired_auctions():
    """End auctions that have passed their end time"""
    try:
        from auctions.models import Auction
        
        now = timezone.now()
        expired_auctions = Auction.objects.filter(
            status='live',
            end_time__lt=now
        )
        
        auctions_ended = 0
        
        for auction in expired_auctions:
            try:
                # Determine winner (highest bidder)
                winning_bid = auction.bids.order_by('-amount').first()
                if winning_bid:
                    auction.winner = winning_bid.bidder
                    auction.status = 'ended'
                    auction.payment_deadline = now + timedelta(minutes=20)

                    # Send winner notification
                    send_auction_notification(auction, 'auction_won')
                    print(f"✅ Auction {auction.id} ended - Winner: {auction.winner.username} with bid ${winning_bid.amount}")
                else:
                    auction.status = 'ended'
                    print(f"📝 Auction {auction.id} ended - No bids")
                
                auction.save()
                auctions_ended += 1
                
                # Send real-time update
                from channels.layers import get_channel_layer
                from asgiref.sync import async_to_sync
                
                channel_layer = get_channel_layer()
                async_to_sync(channel_layer.group_send)(
                    f'auction_{auction.id}',
                    {
                        'type': 'auction_ended',
                        'auction_data': {
                            'auction_id': auction.id,
                            'status': auction.status,
                            'winner': auction.winner.username if auction.winner else None,
                            'final_price': str(auction.current_price)
                        }
                    }
                )
                
            except Exception as e:
                logger.error(f"Failed to end auction {auction.id}: {str(e)}")
        
        return f"Ended {auctions_ended} expired auctions"
        
    except Exception as e:
        logger.error(f"Failed to end expired auctions: {str(e)}")
        return f"Failed to end expired auctions: {str(e)}"


@shared_task
def handle_payment_timeouts():
    """Handle payment timeouts and reassign auctions to next highest bidders"""
    try:
        from auctions.models import Auction

        now = timezone.now()

        # Get auctions with expired payment deadlines
        expired_payments = Auction.objects.filter(
            status='ended',
            winner__isnull=False,
            payment_deadline__lt=now,
            payment_received=False  # Assuming this field exists
        )

        reassignments = 0

        for auction in expired_payments:
            try:
                # Get the next highest bidder
                current_winner_bid = auction.bids.filter(bidder=auction.winner).first()
                if current_winner_bid:
                    # Find next highest bid from a different bidder
                    next_bid = auction.bids.filter(
                        amount__lt=current_winner_bid.amount
                    ).exclude(bidder=auction.winner).first()

                    if next_bid:
                        # Reassign auction to next highest bidder
                        old_winner = auction.winner
                        auction.winner = next_bid.bidder
                        auction.current_price = next_bid.amount
                        auction.payment_deadline = now + timedelta(minutes=20)
                        auction.save()

                        # Send notification to new winner
                        context = {
                            'auction': auction,
                            'previous_winner': old_winner.get_full_name(),
                        }
                        send_auction_notification(auction, 'auction_won', context)

                        reassignments += 1

                        logger.info(f"Reassigned auction {auction.id} from {old_winner.username} to {next_bid.bidder.username}")
                    else:
                        # No more bidders, mark auction as failed
                        auction.status = 'failed'
                        auction.winner = None
                        auction.save()

                        logger.info(f"No more bidders for auction {auction.id}, marked as failed")

            except Exception as e:
                logger.error(f"Failed to handle payment timeout for auction {auction.id}: {str(e)}")

        return f"Processed {len(expired_payments)} expired payments, {reassignments} reassignments"

    except Exception as e:
        logger.error(f"Failed to handle payment timeouts: {str(e)}")
        return f"Failed to handle payment timeouts: {str(e)}"


@shared_task
def send_payment_deadline_reminders():
    """Send reminders to winners who haven't paid yet"""
    try:
        from auctions.models import Auction

        now = timezone.now()

        # Get auctions where payment deadline is in 5 minutes
        reminder_time = now + timedelta(minutes=5)

        auctions_needing_reminder = Auction.objects.filter(
            status='ended',
            winner__isnull=False,
            payment_deadline__lte=reminder_time,
            payment_deadline__gt=now,
            payment_received=False
        )

        reminders_sent = 0

        for auction in auctions_needing_reminder:
            try:
                context = {
                    'auction': auction,
                    'time_remaining': '5 minutes',
                }
                notifications = send_auction_notification(auction, 'payment_reminder', context)
                reminders_sent += len(notifications)

            except Exception as e:
                logger.error(f"Failed to send payment reminder for auction {auction.id}: {str(e)}")

        return f"Sent {reminders_sent} payment deadline reminders"

    except Exception as e:
        logger.error(f"Failed to send payment deadline reminders: {str(e)}")
        return f"Failed to send payment deadline reminders: {str(e)}"


@shared_task
def cleanup_old_notifications():
    """Clean up old notifications"""
    try:
        from .models import Notification

        # Delete notifications older than 30 days
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count, _ = Notification.objects.filter(
            created_at__lt=cutoff_date
        ).delete()

        return f"Deleted {deleted_count} old notifications"

    except Exception as e:
        logger.error(f"Failed to cleanup old notifications: {str(e)}")
        return f"Failed to cleanup old notifications: {str(e)}"
