#!/usr/bin/env python3
"""
Test script to directly simulate auction close and verify notifications
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, Bid
from accounts.models import User
from notifications.models import Notification
from django.utils import timezone
from datetime import timedelta

def test_direct_auction_close():
    """Test auction close process directly"""
    
    print("🧪 Testing Direct Auction Close Process")
    print("=" * 50)
    
    try:
        # Get test data
        auction = Auction.objects.get(title__icontains='samakat kos ibrahim')
        seller = auction.seller
        buyer = User.objects.get(username='rabie4')
        
        print(f"🎣 Auction: {auction.title}")
        print(f"👤 Seller: {seller.username}")
        print(f"👤 Buyer: {buyer.username}")
        print(f"💰 Current price: ${auction.current_price}")
        print(f"📊 Status: {auction.status}")
        
        # Count initial notifications
        initial_count = Notification.objects.filter(recipient=buyer).count()
        print(f"📱 Initial notifications: {initial_count}")
        print()
        
        # Simulate the close auction process
        print("🔄 Simulating auction close...")
        
        # Close the auction
        auction.status = 'ended'
        auction.end_time = timezone.now()
        
        # Set winner (highest bidder)
        winning_bid = auction.bids.order_by('-amount').first()
        if winning_bid:
            auction.winner = winning_bid.bidder
            auction.payment_deadline = timezone.now() + timedelta(minutes=20)
            print(f"🏆 Winner: {auction.winner.username} with bid ${winning_bid.amount}")
        
        auction.save()
        
        # Send notifications (same code as in the view)
        if winning_bid and auction.winner:
            try:
                # Send in-app notification
                from notifications.services import NotificationService
                notification_service = NotificationService()
                
                print("📨 Sending winner notifications...")
                notifications = notification_service.send_notification(
                    user=auction.winner,
                    notification_type='auction_won',
                    context={'auction': auction, 'bid': winning_bid}
                )
                
                print(f"✅ Notifications sent: {len(notifications)}")
                for notif in notifications:
                    print(f"   📱 {notif.channel}: {notif.status} - {notif.title}")
                
                # Send WhatsApp notification
                from notifications.ultramsg_service import ultramsg_service
                
                message = f"""🎉 Congratulations! You won the auction!

📦 Auction: {auction.title}
💰 Winning Bid: ${auction.current_price}
⏰ Payment Deadline: 20 minutes

Please complete your payment within 20 minutes to secure your purchase.

Thank you for participating!"""

                if ultramsg_service.is_configured():
                    print("📱 Sending WhatsApp message...")
                    result = ultramsg_service.send_text_message(
                        phone_number=auction.winner.phone_number,
                        message=message
                    )
                    if result['success']:
                        print(f"✅ WhatsApp sent successfully!")
                        print(f"   Message ID: {result.get('id', 'N/A')}")
                    else:
                        print(f"❌ WhatsApp failed: {result.get('error')}")
                else:
                    print("❌ WhatsApp not configured")
                    
            except Exception as e:
                print(f"❌ Error sending notifications: {e}")
                import traceback
                traceback.print_exc()
        
        # Check final notification count
        final_count = Notification.objects.filter(recipient=buyer).count()
        added_count = final_count - initial_count
        
        print()
        print(f"📊 Final Results:")
        print(f"   Initial notifications: {initial_count}")
        print(f"   Final notifications: {final_count}")
        print(f"   Added notifications: {added_count}")
        
        # Show recent notifications
        recent = Notification.objects.filter(recipient=buyer).order_by('-created_at')[:3]
        print(f"📱 Most recent notifications:")
        for notif in recent:
            print(f"   {notif.created_at.strftime('%H:%M:%S')}: {notif.title} ({notif.channel}) - {notif.status}")
        
        if added_count > 0:
            print("\n🎉 SUCCESS: Notifications were created!")
            return True
        else:
            print("\n❌ ISSUE: No notifications were added")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_direct_auction_close()
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
