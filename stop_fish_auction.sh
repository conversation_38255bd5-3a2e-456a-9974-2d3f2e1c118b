#!/bin/bash

# Fish Auction App Stop Script
# This script stops all Fish Auction app services

echo "🛑 Stopping Fish Auction App..."

# Get the directory where this script is located
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$DIR"

# Function to stop a service
stop_service() {
    local service_name=$1
    local pid_file="${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo "🛑 Stopping $service_name (PID: $pid)..."
            kill $pid
            sleep 2
            
            # Force kill if still running
            if ps -p $pid > /dev/null 2>&1; then
                echo "⚠️  Force stopping $service_name..."
                kill -9 $pid
            fi
            
            echo "✅ $service_name stopped"
        else
            echo "⚠️  $service_name was not running"
        fi
        rm -f "$pid_file"
    else
        echo "⚠️  No PID file found for $service_name"
    fi
}

# Stop all services
stop_service "celery-beat"
stop_service "celery-worker" 
stop_service "django-server"

# Also kill any remaining processes
echo "🧹 Cleaning up any remaining processes..."
pkill -f "celery.*fish_auction" 2>/dev/null || true
pkill -f "daphne.*fish_auction" 2>/dev/null || true

echo ""
echo "✅ Fish Auction App stopped successfully!"
echo ""
echo "💡 To start again, run: ./start_fish_auction.sh"
