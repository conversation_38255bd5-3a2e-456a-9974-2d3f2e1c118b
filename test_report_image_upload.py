#!/usr/bin/env python3
"""
Test broker report image upload and check if images are properly stored
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from broker_services.models import ServiceExecution
from accounts.models import User
import json


def test_report_image_data():
    """Test what's stored in the report_images field"""
    print("🧪 Testing Report Image Data")
    print("=" * 50)
    
    # Find service executions with reports
    executions = ServiceExecution.objects.all()
    print(f"📋 Total service executions: {executions.count()}")
    
    for execution in executions:
        print(f"\n📋 Execution: {execution.id}")
        print(f"   Status: {execution.status}")
        print(f"   Broker: {execution.broker.username}")
        print(f"   Report Text: '{execution.report_text}'")
        print(f"   Report Images: {execution.report_images}")
        print(f"   Report Images Type: {type(execution.report_images)}")
        print(f"   Report Images Length: {len(execution.report_images) if execution.report_images else 0}")
        
        if execution.report_images:
            print("   📸 Image URLs:")
            for i, image_url in enumerate(execution.report_images):
                print(f"      {i+1}. {image_url}")
                
                # Check if it's a full URL or relative path
                if image_url.startswith('http'):
                    print(f"         ✅ Full URL")
                elif image_url.startswith('/'):
                    print(f"         ⚠️  Relative path - needs base URL")
                else:
                    print(f"         ❓ Unknown format")
        
        # Check if there are any files in the broker_reports directory
        broker_reports_dir = os.path.join('media', 'broker_reports')
        if os.path.exists(broker_reports_dir):
            files = os.listdir(broker_reports_dir)
            print(f"   📁 Files in broker_reports: {files}")
        else:
            print(f"   📁 broker_reports directory doesn't exist")
    
    print(f"\n🎉 Test completed!")


if __name__ == '__main__':
    test_report_image_data()
