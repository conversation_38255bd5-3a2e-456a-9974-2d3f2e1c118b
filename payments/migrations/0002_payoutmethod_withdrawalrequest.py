# Generated by Django 4.2.23 on 2025-06-27 19:03

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PayoutMethod',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payout_type', models.CharField(choices=[('paypal', 'PayPal'), ('stripe', 'Stripe'), ('bank_transfer', 'Bank Transfer'), ('payoneer', 'Payoneer')], max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('is_verified', models.BooleanField(default=False)),
                ('paypal_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('stripe_account_id', models.CharField(blank=True, max_length=255, null=True)),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True)),
                ('account_holder_name', models.CharField(blank=True, max_length=100, null=True)),
                ('account_number', models.CharField(blank=True, max_length=50, null=True)),
                ('routing_number', models.CharField(blank=True, max_length=50, null=True)),
                ('iban', models.CharField(blank=True, max_length=50, null=True)),
                ('swift_code', models.CharField(blank=True, max_length=20, null=True)),
                ('payoneer_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payout_methods', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WithdrawalRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('platform_fee', models.DecimalField(decimal_places=2, default=Decimal('2.00'), max_digits=10)),
                ('payout_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending Admin Approval'), ('approved', 'Approved'), ('processing', 'Processing'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('admin_notes', models.TextField(blank=True)),
                ('external_transaction_id', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('payout_method', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.payoutmethod')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_withdrawals', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
