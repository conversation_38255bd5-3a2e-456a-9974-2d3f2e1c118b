# Generated by Django 4.2.23 on 2025-06-27 21:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0002_payoutmethod_withdrawalrequest'),
    ]

    operations = [
        migrations.AddField(
            model_name='wallettransaction',
            name='external_transaction_id',
            field=models.CharField(blank=True, help_text='External transaction ID from payment processor', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='wallettransaction',
            name='payout_status',
            field=models.CharField(blank=True, choices=[('pending', 'Pending'), ('processing', 'Processing'), ('approved', 'Approved'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('failed', 'Failed')], help_text='Status of payout for withdrawal transactions', max_length=20, null=True),
        ),
    ]
