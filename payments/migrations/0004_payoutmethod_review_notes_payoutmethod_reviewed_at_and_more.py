# Generated by Django 4.2.23 on 2025-06-28 10:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('payments', '0003_wallettransaction_external_transaction_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='payoutmethod',
            name='review_notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='payoutmethod',
            name='reviewed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='payoutmethod',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_payment_methods', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='payoutmethod',
            name='verification_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('verified', 'Verified'), ('rejected', 'Rejected')], default='pending', max_length=20),
        ),
    ]
