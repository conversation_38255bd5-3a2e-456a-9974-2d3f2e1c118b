# Generated by Django 4.2.23 on 2025-07-06 11:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0006_invoice_pdf_file'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='language',
            field=models.CharField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en', max_length=2),
        ),
        migrations.AddField(
            model_name='payoutmethod',
            name='telr_account_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='payoutmethod',
            name='telr_account_holder_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='payoutmethod',
            name='telr_merchant_id',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='payoutmethod',
            name='payout_type',
            field=models.CharField(choices=[('paypal', 'PayPal'), ('stripe', 'Stripe'), ('bank_transfer', 'Bank Transfer'), ('payoneer', 'Payoneer'), ('telr', 'Telr')], max_length=20),
        ),
    ]
