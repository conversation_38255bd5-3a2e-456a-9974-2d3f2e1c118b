# Generated by Django 4.2.23 on 2025-06-28 10:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0004_payoutmethod_review_notes_payoutmethod_reviewed_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='wallettransaction',
            name='withdrawal_request_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='withdrawalrequest',
            name='rejected_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='wallettransaction',
            name='external_transaction_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='wallettransaction',
            name='payout_status',
            field=models.CharField(blank=True, choices=[('pending_approval', 'Pending Admin Approval'), ('pending', 'Pending'), ('processing', 'Processing'), ('approved', 'Approved'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('failed', 'Failed')], help_text='Status of payout for withdrawal transactions', max_length=20, null=True),
        ),
    ]
