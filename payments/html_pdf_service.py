import os
import io
from django.conf import settings
from django.core.files.base import ContentFile
from django.template.loader import render_to_string
from django.utils import timezone
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import logging

logger = logging.getLogger(__name__)


class HTMLInvoicePDFService:
    """Service for generating PDF invoices using HTML/CSS with proper Arabic support"""

    def __init__(self):
        self.font_config = FontConfiguration()
        self.labels = self._get_localized_labels()
    
    def _get_localized_labels(self, language='en'):
        """Get localized labels for the given language"""
        labels = {
            'en': {
                'invoice': 'Invoice',
                'invoice_date': 'Invoice Date',
                'due_date': 'Due Date',
                'paid_date': 'Paid Date',
                'status': 'Status',
                'bill_to': 'Bill To',
                'bill_from': 'Bill From',
                'auction_details': 'Auction Details',
                'item': 'Item',
                'description': 'Description',
                'quantity': 'Quantity',
                'price': 'Price',
                'subtotal': 'Subtotal',
                'platform_fee': 'Platform Fee',
                'tax': 'Tax',
                'total': 'Total',
                'thank_you': 'Thank you for your business!',
                'footer_text': 'For any questions about this invoice, please contact us.',
                'support_text': 'Email: <EMAIL>',
                # Status translations
                'draft': 'Draft',
                'sent': 'Sent',
                'paid': 'Paid',
                'overdue': 'Overdue',
                'cancelled': 'Cancelled',
            },
            'ar': {
                'invoice': 'فاتورة',
                'invoice_date': 'تاريخ الفاتورة',
                'due_date': 'تاريخ الاستحقاق',
                'paid_date': 'تاريخ الدفع',
                'status': 'الحالة',
                'bill_to': 'فاتورة إلى',
                'bill_from': 'فاتورة من',
                'auction_details': 'تفاصيل المزاد',
                'item': 'العنصر',
                'description': 'الوصف',
                'quantity': 'الكمية',
                'price': 'السعر',
                'subtotal': 'المجموع الفرعي',
                'platform_fee': 'رسوم المنصة',
                'tax': 'الضريبة',
                'total': 'المجموع',
                'thank_you': 'شكراً لك على تعاملك معنا!',
                'footer_text': 'لأي استفسارات حول هذه الفاتورة، يرجى الاتصال بنا.',
                'support_text': 'البريد الإلكتروني: <EMAIL>',
                # Status translations
                'draft': 'مسودة',
                'sent': 'مرسلة',
                'paid': 'مدفوع',
                'overdue': 'متأخرة',
                'cancelled': 'ملغية',
            }
        }
        return labels.get(language, labels['en'])

    def save_invoice_pdf(self, invoice, language='en'):
        """Generate and save PDF invoice using HTML/CSS"""
        try:
            logger.info(f"HTMLInvoicePDFService: Generating PDF for invoice {invoice.id} in language {language}")
            
            # Update labels for the specified language
            self.labels = self._get_localized_labels(language)
            
            # Generate HTML content
            html_content = self._generate_html_content(invoice, language)
            
            # Generate CSS styles
            css_content = self._generate_css_styles(language)
            
            # Create PDF from HTML
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content, font_config=self.font_config)
            
            # Generate PDF
            pdf_buffer = io.BytesIO()
            html_doc.write_pdf(pdf_buffer, stylesheets=[css_doc], font_config=self.font_config)
            pdf_buffer.seek(0)
            
            # Save PDF file
            filename = f"invoice_{invoice.invoice_number}_{language}.pdf"
            invoice.pdf_file.save(
                filename,
                ContentFile(pdf_buffer.getvalue()),
                save=True
            )
            
            logger.info(f"HTMLInvoicePDFService: PDF generated successfully for invoice {invoice.id}")
            return True
            
        except Exception as e:
            logger.error(f"HTMLInvoicePDFService: Failed to generate PDF for invoice {invoice.id}: {str(e)}")
            import traceback
            logger.error(f"HTMLInvoicePDFService: Traceback: {traceback.format_exc()}")
            raise

    def _generate_html_content(self, invoice, language):
        """Generate HTML content for the invoice"""
        # Get localized status
        status_text = self.labels.get(invoice.status, invoice.get_status_display())
        
        # Get auction details
        auction = invoice.payment.auction
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="{language}" dir="{'rtl' if language == 'ar' else 'ltr'}">
        <head>
            <meta charset="UTF-8">
            <title>{self.labels['invoice']} #{invoice.invoice_number}</title>
        </head>
        <body>
            <div class="invoice-container">
                <!-- Header -->
                <div class="header">
                    <h1 class="company-name">Fish Auction App</h1>
                    <h2 class="invoice-title">{self.labels['invoice']} #{invoice.invoice_number}</h2>
                </div>
                
                <!-- Invoice Details -->
                <div class="invoice-details">
                    <table class="details-table">
                        <tr>
                            <td class="label">{self.labels['invoice_date']}:</td>
                            <td>{invoice.issue_date.strftime('%B %d, %Y')}</td>
                        </tr>
                        <tr>
                            <td class="label">{self.labels['due_date']}:</td>
                            <td>{invoice.due_date.strftime('%B %d, %Y')}</td>
                        </tr>
                        <tr>
                            <td class="label">{self.labels['status']}:</td>
                            <td class="status-{invoice.status}">{status_text}</td>
                        </tr>
                        {f'<tr><td class="label">{self.labels["paid_date"]}:</td><td>{invoice.paid_date.strftime("%B %d, %Y")}</td></tr>' if invoice.paid_date else ''}
                    </table>
                </div>
                
                <!-- Billing Information -->
                <div class="billing-info">
                    <div class="billing-section">
                        <h3>{self.labels['bill_to']}</h3>
                        <div class="address">
                            <strong>{invoice.buyer_name}</strong><br>
                            {invoice.buyer_email}<br>
                            {invoice.buyer_address or 'N/A'}
                        </div>
                    </div>
                    <div class="billing-section">
                        <h3>{self.labels['bill_from']}</h3>
                        <div class="address">
                            <strong>{invoice.seller_name}</strong><br>
                            {invoice.seller_email}<br>
                            {invoice.seller_address or 'N/A'}
                        </div>
                    </div>
                </div>
                
                <!-- Auction Details -->
                <div class="auction-details">
                    <h3>{self.labels['auction_details']}</h3>
                    <table class="auction-table">
                        <thead>
                            <tr>
                                <th>{self.labels['item']}</th>
                                <th>{self.labels['description']}</th>
                                <th>{self.labels['quantity']}</th>
                                <th>{self.labels['price']}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{auction.title}</td>
                                <td>
                                    {auction.fish_category.name if auction.fish_category else 'N/A'} - {auction.fish_type or 'N/A'}<br>
                                    Weight: {auction.weight}kg<br>
                                    Catch Date: {auction.catch_date.strftime('%Y-%m-%d') if auction.catch_date else 'N/A'}<br>
                                    Location: {auction.catch_location or 'N/A'}
                                </td>
                                <td>{auction.quantity}</td>
                                <td>${invoice.payment.amount:.2f}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Payment Summary -->
                <div class="payment-summary">
                    <table class="summary-table">
                        <tr>
                            <td class="label">{self.labels['subtotal']}:</td>
                            <td class="amount">${invoice.subtotal:.2f}</td>
                        </tr>
                        <tr>
                            <td class="label">{self.labels['platform_fee']}:</td>
                            <td class="amount">${invoice.payment.platform_fee:.2f}</td>
                        </tr>
                        <tr>
                            <td class="label">{self.labels['tax']}:</td>
                            <td class="amount">${invoice.tax_amount:.2f}</td>
                        </tr>
                        <tr class="total-row">
                            <td class="label"><strong>{self.labels['total']}:</strong></td>
                            <td class="amount"><strong>${invoice.total_amount:.2f}</strong></td>
                        </tr>
                    </table>
                </div>
                
                <!-- Footer -->
                <div class="footer">
                    <p class="thank-you">{self.labels['thank_you']}</p>
                    <p>{self.labels['footer_text']}</p>
                    <p>{self.labels['support_text']}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template

    def _generate_css_styles(self, language):
        """Generate CSS styles for the invoice"""
        direction = 'rtl' if language == 'ar' else 'ltr'
        text_align = 'right' if language == 'ar' else 'left'
        
        css_content = f"""
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&family=Roboto:wght@400;700&display=swap');
        
        body {{
            font-family: {'Noto Sans Arabic, Arial, sans-serif' if language == 'ar' else 'Roboto, Arial, sans-serif'};
            direction: {direction};
            text-align: {text_align};
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
        }}
        
        .invoice-container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 20px;
        }}
        
        .company-name {{
            color: #2E86AB;
            font-size: 24px;
            margin: 0 0 10px 0;
            font-weight: 700;
        }}
        
        .invoice-title {{
            color: #2E86AB;
            font-size: 18px;
            margin: 0;
            font-weight: 600;
        }}
        
        .invoice-details {{
            margin-bottom: 30px;
        }}
        
        .details-table {{
            width: 100%;
            border-collapse: collapse;
        }}
        
        .details-table td {{
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .label {{
            font-weight: 600;
            width: 150px;
        }}
        
        .status-paid {{
            color: #28a745;
            font-weight: 600;
        }}
        
        .status-draft {{
            color: #6c757d;
            font-weight: 600;
        }}
        
        .status-sent {{
            color: #007bff;
            font-weight: 600;
        }}
        
        .billing-info {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 30px;
        }}
        
        .billing-section {{
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        
        .billing-section h3 {{
            color: #2E86AB;
            margin: 0 0 10px 0;
            font-size: 14px;
        }}
        
        .address {{
            line-height: 1.8;
        }}
        
        .auction-details {{
            margin-bottom: 30px;
        }}
        
        .auction-details h3 {{
            color: #2E86AB;
            margin: 0 0 15px 0;
            font-size: 16px;
        }}
        
        .auction-table {{
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #ddd;
        }}
        
        .auction-table th {{
            background-color: #2E86AB;
            color: white;
            padding: 12px;
            text-align: {text_align};
            font-weight: 600;
        }}
        
        .auction-table td {{
            padding: 12px;
            border-bottom: 1px solid #ddd;
            vertical-align: top;
        }}
        
        .payment-summary {{
            margin-bottom: 30px;
        }}
        
        .summary-table {{
            width: 100%;
            max-width: 400px;
            margin-left: auto;
            border-collapse: collapse;
        }}
        
        .summary-table td {{
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .summary-table .amount {{
            text-align: right;
            width: 100px;
        }}
        
        .total-row {{
            border-top: 2px solid #2E86AB;
            background-color: #f8f9fa;
        }}
        
        .total-row td {{
            padding: 12px 0;
            font-size: 14px;
        }}
        
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
        
        .thank-you {{
            font-size: 16px;
            font-weight: 600;
            color: #2E86AB;
            margin-bottom: 10px;
        }}
        
        @media print {{
            body {{
                padding: 0;
            }}
            
            .invoice-container {{
                box-shadow: none;
                border: none;
            }}
        }}
        """
        
        return css_content
