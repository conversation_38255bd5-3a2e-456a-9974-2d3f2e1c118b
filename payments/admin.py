from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import WalletTransaction, Payment, Invoice, Refund, PayoutMethod, WithdrawalRequest


@admin.register(WalletTransaction)
class WalletTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'transaction_type', 'amount', 'status', 'created_at']
    list_filter = ['transaction_type', 'status', 'created_at']
    search_fields = ['user__username', 'user__email', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['auction', 'buyer', 'seller', 'amount', 'payment_method', 'status', 'created_at']
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['auction__title', 'buyer__username', 'seller__username']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']


@admin.register(PayoutMethod)
class PayoutMethodAdmin(admin.ModelAdmin):
    list_display = ['user', 'payout_type', 'is_default', 'is_verified', 'created_at']
    list_filter = ['payout_type', 'is_default', 'is_verified', 'created_at']
    search_fields = ['user__username', 'user__email', 'paypal_email', 'payoneer_email']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-created_at']


@admin.register(WithdrawalRequest)
class WithdrawalRequestAdmin(admin.ModelAdmin):
    list_display = [
        'id_short', 'user', 'amount', 'payout_amount', 'payout_method_type',
        'status', 'created_at', 'action_buttons'
    ]
    list_filter = ['status', 'payout_method__payout_type', 'created_at']
    search_fields = ['user__username', 'user__email', 'id']
    readonly_fields = ['id', 'payout_amount', 'created_at', 'updated_at']
    ordering = ['-created_at']

    fieldsets = (
        ('Request Details', {
            'fields': ('id', 'user', 'amount', 'platform_fee', 'payout_amount')
        }),
        ('Payout Method', {
            'fields': ('payout_method',)
        }),
        ('Status & Processing', {
            'fields': ('status', 'admin_notes', 'processed_by', 'external_transaction_id')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'approved_at', 'completed_at')
        }),
    )

    def id_short(self, obj):
        return str(obj.id)[:8]
    id_short.short_description = 'ID'

    def payout_method_type(self, obj):
        return obj.payout_method.get_payout_type_display()
    payout_method_type.short_description = 'Payout Type'

    def action_buttons(self, obj):
        if obj.status == 'pending':
            approve_url = reverse('admin:approve_withdrawal', args=[obj.id])
            reject_url = reverse('admin:reject_withdrawal', args=[obj.id])
            return format_html(
                '<a class="button" href="{}">Approve</a> '
                '<a class="button" href="{}">Reject</a>',
                approve_url, reject_url
            )
        return '-'
    action_buttons.short_description = 'Actions'
    action_buttons.allow_tags = True


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'payment', 'total_amount', 'status', 'issue_date']
    list_filter = ['status', 'issue_date']
    search_fields = ['invoice_number', 'buyer_name', 'seller_name']
    readonly_fields = ['id', 'invoice_number', 'issue_date']
    ordering = ['-issue_date']


@admin.register(Refund)
class RefundAdmin(admin.ModelAdmin):
    list_display = ['payment', 'amount', 'reason', 'status', 'requested_by', 'created_at']
    list_filter = ['reason', 'status', 'created_at']
    search_fields = ['payment__auction__title', 'requested_by__username']
    readonly_fields = ['id', 'created_at', 'processed_at']
    ordering = ['-created_at']
