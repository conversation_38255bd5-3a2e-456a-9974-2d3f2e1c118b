from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Invoice, Payment
from .serializers import InvoiceSerializer
import json


@staff_member_required
def invoice_management(request):
    """Admin interface for invoice management"""
    
    # Get filter parameters
    search_query = request.GET.get('search', '')
    seller_filter = request.GET.get('seller', '')
    buyer_filter = request.GET.get('buyer', '')
    status_filter = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    page = request.GET.get('page', 1)
    
    # Build queryset
    invoices = Invoice.objects.select_related('payment__auction', 'payment__buyer', 'payment__seller').all()
    
    # Apply filters
    if search_query:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search_query) |
            Q(buyer_name__icontains=search_query) |
            Q(seller_name__icontains=search_query) |
            Q(payment__auction__title__icontains=search_query)
        )
    
    if seller_filter:
        invoices = invoices.filter(seller_name__icontains=seller_filter)
    
    if buyer_filter:
        invoices = invoices.filter(buyer_name__icontains=buyer_filter)
    
    if status_filter:
        invoices = invoices.filter(status=status_filter)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            invoices = invoices.filter(issue_date__date__gte=date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            invoices = invoices.filter(issue_date__date__lte=date_to_obj)
        except ValueError:
            pass
    
    # Order by latest first
    invoices = invoices.order_by('-issue_date')
    
    # Pagination
    paginator = Paginator(invoices, 25)  # 25 invoices per page
    page_obj = paginator.get_page(page)
    
    # Get unique sellers and buyers for filter dropdowns
    sellers = Invoice.objects.values_list('seller_name', flat=True).distinct().order_by('seller_name')
    buyers = Invoice.objects.values_list('buyer_name', flat=True).distinct().order_by('buyer_name')
    
    # Status choices
    status_choices = Invoice.STATUS_CHOICES
    
    context = {
        'page_obj': page_obj,
        'sellers': sellers,
        'buyers': buyers,
        'status_choices': status_choices,
        'search_query': search_query,
        'seller_filter': seller_filter,
        'buyer_filter': buyer_filter,
        'status_filter': status_filter,
        'date_from': date_from,
        'date_to': date_to,
        'total_invoices': paginator.count,
    }
    
    return render(request, 'admin/invoice_management.html', context)


@staff_member_required
def invoice_detail(request, invoice_id):
    """Admin view for invoice details"""
    invoice = get_object_or_404(Invoice, id=invoice_id)
    
    context = {
        'invoice': invoice,
        'payment': invoice.payment,
        'auction': invoice.payment.auction,
    }
    
    return render(request, 'admin/invoice_detail.html', context)


@staff_member_required
def download_invoice(request, invoice_id):
    """Download invoice PDF"""
    invoice = get_object_or_404(Invoice, id=invoice_id)
    
    if not invoice.pdf_file:
        # Generate PDF if it doesn't exist
        from .pdf_service import InvoicePDFService
        pdf_service = InvoicePDFService()
        try:
            pdf_service.save_invoice_pdf(invoice, invoice.language)
        except Exception as e:
            return HttpResponse(f"Error generating PDF: {str(e)}", status=500)
    
    if invoice.pdf_file:
        response = HttpResponse(invoice.pdf_file.read(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'
        return response
    else:
        return HttpResponse("PDF not available", status=404)


@staff_member_required
def invoice_stats(request):
    """Get invoice statistics for admin dashboard"""
    
    # Calculate date ranges
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    # Get statistics
    total_invoices = Invoice.objects.count()
    this_week = Invoice.objects.filter(issue_date__date__gte=week_ago).count()
    this_month = Invoice.objects.filter(issue_date__date__gte=month_ago).count()
    
    # Status breakdown
    status_stats = {}
    for status_code, status_name in Invoice.STATUS_CHOICES:
        count = Invoice.objects.filter(status=status_code).count()
        status_stats[status_name] = count
    
    # Revenue statistics
    from django.db.models import Sum
    total_revenue = Invoice.objects.aggregate(total=Sum('total_amount'))['total'] or 0
    week_revenue = Invoice.objects.filter(issue_date__date__gte=week_ago).aggregate(total=Sum('total_amount'))['total'] or 0
    month_revenue = Invoice.objects.filter(issue_date__date__gte=month_ago).aggregate(total=Sum('total_amount'))['total'] or 0
    
    stats = {
        'total_invoices': total_invoices,
        'this_week': this_week,
        'this_month': this_month,
        'status_stats': status_stats,
        'total_revenue': float(total_revenue),
        'week_revenue': float(week_revenue),
        'month_revenue': float(month_revenue),
    }
    
    return JsonResponse(stats)


@staff_member_required
def bulk_invoice_actions(request):
    """Handle bulk actions on invoices"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)
    
    try:
        data = json.loads(request.body)
        action = data.get('action')
        invoice_ids = data.get('invoice_ids', [])
        
        if not action or not invoice_ids:
            return JsonResponse({'error': 'Action and invoice IDs required'}, status=400)
        
        invoices = Invoice.objects.filter(id__in=invoice_ids)
        
        if action == 'regenerate_pdf':
            # Regenerate PDFs for selected invoices
            from .pdf_service import InvoicePDFService
            pdf_service = InvoicePDFService()
            
            success_count = 0
            for invoice in invoices:
                try:
                    pdf_service.save_invoice_pdf(invoice, invoice.language)
                    success_count += 1
                except Exception as e:
                    continue
            
            return JsonResponse({
                'success': True,
                'message': f'Regenerated {success_count} PDFs successfully'
            })
        
        elif action == 'mark_paid':
            # Mark invoices as paid
            updated = invoices.filter(status__in=['draft', 'sent']).update(
                status='paid',
                paid_date=timezone.now()
            )
            
            return JsonResponse({
                'success': True,
                'message': f'Marked {updated} invoices as paid'
            })
        
        elif action == 'resend_whatsapp':
            # Resend WhatsApp notifications
            from .services import PaymentService
            payment_service = PaymentService()
            
            success_count = 0
            for invoice in invoices:
                try:
                    payment_service.send_invoice_whatsapp_notifications(invoice)
                    success_count += 1
                except Exception as e:
                    continue
            
            return JsonResponse({
                'success': True,
                'message': f'Resent WhatsApp notifications for {success_count} invoices'
            })
        
        else:
            return JsonResponse({'error': 'Invalid action'}, status=400)
    
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@staff_member_required
def export_invoices(request):
    """Export invoices to CSV"""
    import csv
    from django.http import HttpResponse
    
    # Get filter parameters (same as invoice_management view)
    search_query = request.GET.get('search', '')
    seller_filter = request.GET.get('seller', '')
    buyer_filter = request.GET.get('buyer', '')
    status_filter = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    # Build queryset with same filters
    invoices = Invoice.objects.select_related('payment__auction', 'payment__buyer', 'payment__seller').all()
    
    # Apply same filters as invoice_management view
    if search_query:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search_query) |
            Q(buyer_name__icontains=search_query) |
            Q(seller_name__icontains=search_query) |
            Q(payment__auction__title__icontains=search_query)
        )
    
    if seller_filter:
        invoices = invoices.filter(seller_name__icontains=seller_filter)
    
    if buyer_filter:
        invoices = invoices.filter(buyer_name__icontains=buyer_filter)
    
    if status_filter:
        invoices = invoices.filter(status=status_filter)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            invoices = invoices.filter(issue_date__date__gte=date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            invoices = invoices.filter(issue_date__date__lte=date_to_obj)
        except ValueError:
            pass
    
    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="invoices_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Invoice Number', 'Buyer Name', 'Seller Name', 'Auction Title',
        'Subtotal', 'Platform Fee', 'Total Amount', 'Status',
        'Issue Date', 'Due Date', 'Paid Date'
    ])
    
    for invoice in invoices.order_by('-issue_date'):
        writer.writerow([
            invoice.invoice_number,
            invoice.buyer_name,
            invoice.seller_name,
            invoice.payment.auction.title,
            invoice.subtotal,
            invoice.payment.platform_fee,
            invoice.total_amount,
            invoice.get_status_display(),
            invoice.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
            invoice.due_date.strftime('%Y-%m-%d %H:%M:%S'),
            invoice.paid_date.strftime('%Y-%m-%d %H:%M:%S') if invoice.paid_date else '',
        ])
    
    return response
