#!/usr/bin/env python3
"""
Test script to verify auto-bidding system fixes
This script tests the continuous auto-bidding competition
"""

import os
import sys
import django
import time
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from auctions.tasks import process_auto_bids_for_auction, monitor_auction_auto_bids

def test_auto_bidding_competition():
    """Test that auto-bids compete continuously"""
    print("🧪 Testing Auto-Bidding Competition Fix")
    print("=" * 50)
    
    # Get live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found. Please create one first.")
        return False
    
    print(f"📍 Testing with auction: {live_auction.title}")
    print(f"💰 Current price: ${live_auction.current_price}")
    print(f"📊 Total bids: {live_auction.total_bids}")
    
    # Get auto-bids for this auction
    auto_bids = AutoBid.objects.filter(auction=live_auction, is_active=True)
    print(f"🤖 Active auto-bids: {auto_bids.count()}")
    
    for auto_bid in auto_bids:
        print(f"   - {auto_bid.bidder.username}: max ${auto_bid.max_amount}")
    
    if auto_bids.count() < 2:
        print("⚠️  Need at least 2 auto-bids to test competition")
        return False
    
    # Record initial state
    initial_price = live_auction.current_price
    initial_bid_count = live_auction.total_bids
    
    print(f"\n🚀 Starting auto-bid competition test...")
    print(f"Initial state: ${initial_price}, {initial_bid_count} bids")
    
    # Test 1: Manual trigger of auto-bid processing
    print("\n📋 Test 1: Manual trigger of auto-bid processing")
    result = process_auto_bids_for_auction(live_auction.id)
    print(f"✅ Manual trigger result: {result} auto-bids placed")
    
    # Refresh auction
    live_auction.refresh_from_db()
    print(f"After manual trigger: ${live_auction.current_price}, {live_auction.total_bids} bids")
    
    # Test 2: Monitor function
    print("\n📋 Test 2: Monitor function trigger")
    monitor_result = monitor_auction_auto_bids()
    print(f"✅ Monitor result: {monitor_result}")
    
    # Wait and check again
    print("\n⏱️  Waiting 6 seconds for periodic monitoring...")
    time.sleep(6)
    
    # Refresh auction again
    live_auction.refresh_from_db()
    final_price = live_auction.current_price
    final_bid_count = live_auction.total_bids
    
    print(f"\n📊 Final Results:")
    print(f"Initial: ${initial_price}, {initial_bid_count} bids")
    print(f"Final:   ${final_price}, {final_bid_count} bids")
    print(f"Change:  +${final_price - initial_price}, +{final_bid_count - initial_bid_count} bids")
    
    # Show recent bids
    recent_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')[:10]
    print(f"\n📝 Recent bids:")
    for bid in recent_bids:
        bid_type_icon = "🤖" if bid.bid_type == 'auto' else "👤"
        print(f"   {bid_type_icon} ${bid.amount} by {bid.bidder.username} ({bid.bid_type}) at {bid.timestamp.strftime('%H:%M:%S')}")
    
    # Check if auto-bids are still active and eligible
    active_auto_bids = AutoBid.objects.filter(
        auction=live_auction,
        is_active=True,
        max_amount__gt=live_auction.current_price
    )
    
    print(f"\n🔍 Auto-bids still eligible: {active_auto_bids.count()}")
    for auto_bid in active_auto_bids:
        remaining = auto_bid.max_amount - live_auction.current_price
        print(f"   - {auto_bid.bidder.username}: ${remaining} remaining")
    
    # Success criteria
    success = final_bid_count > initial_bid_count
    
    if success:
        print(f"\n✅ SUCCESS: Auto-bidding system is working!")
        print(f"   - Bids increased from {initial_bid_count} to {final_bid_count}")
        print(f"   - Price increased from ${initial_price} to ${final_price}")
    else:
        print(f"\n❌ ISSUE: Auto-bidding may not be working properly")
        print(f"   - No new bids were placed during the test")
        
        # Diagnostic information
        print(f"\n🔍 Diagnostics:")
        last_bid = recent_bids.first()
        if last_bid:
            print(f"   - Last bidder: {last_bid.bidder.username}")
            print(f"   - Last bid type: {last_bid.bid_type}")
            print(f"   - Last bid time: {last_bid.timestamp}")
            
            # Check if last bidder has auto-bid
            last_bidder_auto_bid = AutoBid.objects.filter(
                auction=live_auction,
                bidder=last_bid.bidder,
                is_active=True
            ).first()
            
            if last_bidder_auto_bid:
                print(f"   - Last bidder has auto-bid: max ${last_bidder_auto_bid.max_amount}")
            
            # Check other eligible auto-bids
            other_auto_bids = AutoBid.objects.filter(
                auction=live_auction,
                is_active=True,
                max_amount__gt=live_auction.current_price
            ).exclude(bidder=last_bid.bidder)
            
            print(f"   - Other eligible auto-bids: {other_auto_bids.count()}")
    
    return success

if __name__ == "__main__":
    try:
        success = test_auto_bidding_competition()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
