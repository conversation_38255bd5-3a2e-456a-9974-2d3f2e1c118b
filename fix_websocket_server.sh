#!/bin/bash

# <PERSON>ript to fix WebSocket server by using <PERSON> instead of runserver
echo "🔧 Fixing WebSocket Server"
echo "=========================="

# Find and kill Django runserver processes
echo "🛑 Stopping Django runserver..."
DJANGO_PIDS=$(ps aux | grep "manage.py runserver" | grep -v grep | awk '{print $2}')

if [ ! -z "$DJANGO_PIDS" ]; then
    for PID in $DJANGO_PIDS; do
        echo "   Killing process $PID"
        kill $PID
    done
    sleep 2
    echo "✅ Django runserver stopped"
else
    echo "ℹ️  No Django runserver processes found"
fi

# Check if Daphne is available
echo ""
echo "🔍 Checking Daphne availability..."
if command -v daphne &> /dev/null; then
    echo "✅ Daphne is available"
    USE_DAPHNE=true
else
    echo "⚠️ Daphne not found, will use runserver with WebSocket support"
    USE_DAPHNE=false
fi

# Start Django with WebSocket support
echo ""
echo "🚀 Starting Django with WebSocket support..."

if [ "$USE_DAPHNE" = true ]; then
    echo "   Using Daphne ASGI server..."
    cd /Users/<USER>/Downloads/fish-main
    source venv/bin/activate
    daphne -b 0.0.0.0 -p 8000 fish_auction.asgi:application > django_websocket.log 2>&1 &
    DJANGO_PID=$!
    echo "   Daphne started (PID: $DJANGO_PID)"
else
    echo "   Using Django runserver with ASGI support..."
    cd /Users/<USER>/Downloads/fish-main
    source venv/bin/activate
    python3 manage.py runserver 0.0.0.0:8000 --noreload > django_websocket.log 2>&1 &
    DJANGO_PID=$!
    echo "   Django started (PID: $DJANGO_PID)"
fi

# Wait for server to start
echo ""
echo "⏳ Waiting for server to start..."
sleep 5

# Test if server is running
if ps -p $DJANGO_PID > /dev/null; then
    echo "✅ Django server is running (PID: $DJANGO_PID)"
    
    # Test HTTP endpoint
    echo ""
    echo "🧪 Testing HTTP endpoint..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/admin/)
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "302" ]; then
        echo "✅ HTTP endpoint working (HTTP $HTTP_CODE)"
    else
        echo "❌ HTTP endpoint failed (HTTP $HTTP_CODE)"
    fi
    
    # Test WebSocket endpoint (simple check)
    echo ""
    echo "🧪 Testing WebSocket endpoint availability..."
    # We can't easily test WebSocket from bash, but we can check if the port responds
    if nc -z localhost 8000; then
        echo "✅ Server is listening on port 8000"
        echo "🔗 WebSocket URL: ws://localhost:8000/ws/auction/20/"
    else
        echo "❌ Server not responding on port 8000"
    fi
    
else
    echo "❌ Django server failed to start"
    echo "📋 Check logs: tail -f django_websocket.log"
    exit 1
fi

echo ""
echo "🎉 WebSocket Server Setup Complete!"
echo "=================================="
echo "📊 Server Status:"
echo "   🌐 HTTP: http://localhost:8000"
echo "   🔗 WebSocket: ws://localhost:8000/ws"
echo "   📋 Logs: tail -f django_websocket.log"
echo ""
echo "📱 Next Steps:"
echo "1. Restart your Flutter app"
echo "2. Test WebSocket connection"
echo "3. Check for real-time updates"
echo ""
echo "🧪 Test WebSocket:"
echo "   python3 test_websocket_simple.py"
