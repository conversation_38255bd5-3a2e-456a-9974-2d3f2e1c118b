import re
import logging
from django.conf import settings
from django.db.models import Q
from .models import Chat<PERSON>ession, ChatMessage, BotKnowledgeBase
from auctions.models import Auction
from accounts.models import User

logger = logging.getLogger(__name__)


class AIBotService:
    """AI-powered support bot service"""
    
    def __init__(self):
        self.greeting_patterns = [
            r'\b(hi|hello|hey|good morning|good afternoon|good evening)\b',
            r'\b(سلام|مرحبا|أهلا)\b'  # Arabic greetings
        ]
        
        self.farewell_patterns = [
            r'\b(bye|goodbye|see you|thanks|thank you)\b',
            r'\b(وداعا|شكرا|مع السلامة)\b'  # Arabic farewells
        ]
    
    def process_message(self, user, message_content, session_id=None):
        """Process user message and generate bot response"""
        try:
            # Get or create chat session
            if session_id:
                session = ChatSession.objects.get(id=session_id, user=user)
            else:
                session = ChatSession.objects.create(user=user)
            
            # Save user message
            user_message = ChatMessage.objects.create(
                session=session,
                message_type='user',
                content=message_content
            )
            
            # Generate bot response
            bot_response = self.generate_response(user, message_content, session)
            
            # Save bot response
            bot_message = ChatMessage.objects.create(
                session=session,
                message_type='bot',
                content=bot_response
            )
            
            return {
                'session_id': session.id,
                'user_message': user_message,
                'bot_message': bot_message,
                'response': bot_response
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                'error': 'Sorry, I encountered an error. Please try again.'
            }
    
    def generate_response(self, user, message_content, session):
        """Generate appropriate bot response"""
        message_lower = message_content.lower()
        
        # Check for greetings
        if self.is_greeting(message_content):
            return self.get_greeting_response(user)
        
        # Check for farewells
        if self.is_farewell(message_content):
            return self.get_farewell_response(user)
        
        # Check for specific intents
        if 'auction' in message_lower or 'bid' in message_lower:
            return self.handle_auction_query(user, message_content)
        
        if 'payment' in message_lower or 'wallet' in message_lower:
            return self.handle_payment_query(user, message_content)
        
        if 'delivery' in message_lower or 'shipping' in message_lower:
            return self.handle_delivery_query(user, message_content)
        
        if 'account' in message_lower or 'profile' in message_lower:
            return self.handle_account_query(user, message_content)
        
        # Search knowledge base
        kb_response = self.search_knowledge_base(message_content)
        if kb_response:
            return kb_response
        
        # Default response
        return self.get_default_response(user)
    
    def is_greeting(self, message):
        """Check if message is a greeting"""
        for pattern in self.greeting_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        return False
    
    def is_farewell(self, message):
        """Check if message is a farewell"""
        for pattern in self.farewell_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        return False
    
    def get_greeting_response(self, user):
        """Generate greeting response"""
        if user.preferred_language == 'ar':
            return f"مرحباً {user.first_name}! كيف يمكنني مساعدتك اليوم؟"
        else:
            return f"Hello {user.first_name}! How can I help you today?"
    
    def get_farewell_response(self, user):
        """Generate farewell response"""
        if user.preferred_language == 'ar':
            return "شكراً لك! أتمنى لك يوماً سعيداً. لا تتردد في التواصل معي إذا كنت بحاجة لأي مساعدة."
        else:
            return "Thank you! Have a great day. Feel free to reach out if you need any help."
    
    def handle_auction_query(self, user, message):
        """Handle auction-related queries"""
        message_lower = message.lower()
        
        if 'how to bid' in message_lower or 'bidding process' in message_lower:
            if user.preferred_language == 'ar':
                return """لتقديم عرض في المزاد:
1. اختر المزاد الذي تريد المشاركة فيه
2. تأكد من وجود رصيد كافي في محفظتك
3. أدخل مبلغ العرض (يجب أن يكون أعلى من العرض الحالي)
4. اضغط على "تقديم عرض"
5. ستتلقى إشعاراً إذا تم قبول عرضك أو إذا تم تجاوزه"""
            else:
                return """To place a bid in an auction:
1. Select the auction you want to participate in
2. Ensure you have sufficient balance in your wallet
3. Enter your bid amount (must be higher than current bid)
4. Click "Place Bid"
5. You'll receive notifications if your bid is accepted or outbid"""
        
        elif 'live auction' in message_lower:
            live_auctions = Auction.objects.filter(status='live').count()
            if user.preferred_language == 'ar':
                return f"يوجد حالياً {live_auctions} مزاد مباشر. يمكنك مشاهدة المزادات المباشرة من قسم المزادات."
            else:
                return f"There are currently {live_auctions} live auctions. You can view them in the Auctions section."
        
        else:
            if user.preferred_language == 'ar':
                return "يمكنني مساعدتك في أسئلة المزادات. هل تريد معرفة كيفية تقديم عرض أم لديك سؤال آخر؟"
            else:
                return "I can help with auction questions. Would you like to know how to bid or do you have another question?"
    
    def handle_payment_query(self, user, message):
        """Handle payment-related queries"""
        if user.preferred_language == 'ar':
            return f"""معلومات المحفظة:
رصيدك الحالي: ${user.wallet_balance}

يمكنك:
- إضافة رصيد للمحفظة
- مشاهدة تاريخ المعاملات
- سحب الأموال

هل تحتاج مساعدة في شيء محدد؟"""
        else:
            return f"""Wallet Information:
Your current balance: ${user.wallet_balance}

You can:
- Add funds to your wallet
- View transaction history
- Withdraw funds

Do you need help with something specific?"""
    
    def handle_delivery_query(self, user, message):
        """Handle delivery-related queries"""
        if user.preferred_language == 'ar':
            return """معلومات التوصيل:
- يمكنك تتبع طلبك من قسم "طلباتي"
- ستتلقى إشعارات عند تحديث حالة التوصيل
- يمكنك التواصل مع مقدم خدمة التوصيل مباشرة

هل تحتاج مساعدة في تتبع طلب معين؟"""
        else:
            return """Delivery Information:
- You can track your order from "My Orders" section
- You'll receive notifications when delivery status updates
- You can contact the delivery provider directly

Do you need help tracking a specific order?"""
    
    def handle_account_query(self, user, message):
        """Handle account-related queries"""
        kyc_status = user.get_kyc_status_display()
        
        if user.preferred_language == 'ar':
            return f"""معلومات الحساب:
- حالة التحقق: {kyc_status}
- نوع المستخدم: {user.get_user_type_display()}
- عضو منذ: {user.date_joined.strftime('%Y-%m-%d')}

هل تحتاج مساعدة في تحديث ملفك الشخصي أو التحقق من الهوية؟"""
        else:
            return f"""Account Information:
- Verification Status: {kyc_status}
- User Type: {user.get_user_type_display()}
- Member Since: {user.date_joined.strftime('%Y-%m-%d')}

Do you need help updating your profile or identity verification?"""
    
    def search_knowledge_base(self, message):
        """Search knowledge base for relevant answers"""
        try:
            # Simple keyword matching
            knowledge_items = BotKnowledgeBase.objects.filter(
                Q(keywords__icontains=message) | 
                Q(question__icontains=message) |
                Q(answer__icontains=message),
                is_active=True
            )[:3]  # Get top 3 matches
            
            if knowledge_items:
                # Use the most relevant one (first match)
                kb_item = knowledge_items[0]
                kb_item.usage_count += 1
                kb_item.save()
                return kb_item.answer
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {str(e)}")
        
        return None
    
    def get_default_response(self, user):
        """Generate default response when no specific intent is detected"""
        if user.preferred_language == 'ar':
            return """عذراً، لم أفهم سؤالك بوضوح. يمكنني مساعدتك في:

🔹 المزادات والمناقصات
🔹 المدفوعات والمحفظة
🔹 التوصيل والشحن
🔹 إدارة الحساب

يرجى إعادة صياغة سؤالك أو اختيار أحد المواضيع أعلاه."""
        else:
            return """I'm sorry, I didn't quite understand your question. I can help you with:

🔹 Auctions and Bidding
🔹 Payments and Wallet
🔹 Delivery and Shipping
🔹 Account Management

Please rephrase your question or choose one of the topics above."""
