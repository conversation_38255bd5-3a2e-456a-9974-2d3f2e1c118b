[{"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-446655440001", "fields": {"category": "bidding", "question": "How do I place a bid in an auction?", "answer": "To place a bid: 1) Select the auction you want to participate in, 2) Ensure you have sufficient balance in your wallet, 3) Enter your bid amount (must be higher than current bid), 4) Click 'Place Bid', 5) You'll receive notifications about your bid status.", "keywords": "bid, bidding, place bid, how to bid, auction bid", "is_active": true, "usage_count": 0, "created_at": "2025-06-16T09:00:00Z", "updated_at": "2025-06-16T09:00:00Z"}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-446655440002", "fields": {"category": "auctions", "question": "What are the different auction statuses?", "answer": "Auction statuses: Draft (being prepared), Scheduled (waiting to start), Live (currently active), Ended (finished), Cancelled (stopped before completion). You can only bid on Live auctions.", "keywords": "auction status, live auction, ended auction, scheduled auction, draft auction", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"category": "payments", "question": "How do I add money to my wallet?", "answer": "To add funds to your wallet: 1) Go to your Wallet section, 2) Click 'Add Funds', 3) Enter the amount you want to add, 4) Choose your payment method (credit card, bank transfer), 5) Complete the payment process. Funds are usually available immediately.", "keywords": "wallet, add money, add funds, payment, deposit, top up", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"category": "account", "question": "How do I verify my account (KYC)?", "answer": "To verify your account: 1) Go to your Profile section, 2) Click 'Submit KYC Documents', 3) Upload a clear photo of your ID (passport, driver's license, or national ID), 4) Wait for our team to review (usually 1-2 business days), 5) You'll receive a notification once approved.", "keywords": "kyc, verify account, identity verification, upload documents, account verification", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"category": "delivery", "question": "How can I track my delivery?", "answer": "To track your delivery: 1) Go to 'My Orders' section, 2) Find your completed auction, 3) Click on the tracking number, 4) You'll see real-time updates of your delivery status. You'll also receive notifications via WhatsApp/SMS when status changes.", "keywords": "delivery tracking, track order, shipping, delivery status, tracking number", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"category": "general", "question": "What types of fish are available in auctions?", "answer": "We have various fish categories including: Fresh Sea Fish (Tuna, Salmon, Snapper), Shellfish (Shrimp, Crab, Lobster), Freshwater Fish (Tilapia, Catfish), and Processed Fish products. Each auction specifies the fish type, quantity, and quality grade.", "keywords": "fish types, categories, tuna, salmon, shrimp, seafood, fresh fish", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-446655440007", "fields": {"category": "bidding", "question": "What is auto-bidding and how does it work?", "answer": "Auto-bidding automatically places bids for you up to your maximum amount. To set up: 1) Go to the auction, 2) Click 'Set Auto-Bid', 3) Enter your maximum bid amount, 4) The system will automatically bid for you when others bid, staying within your limit.", "keywords": "auto bid, automatic bidding, max bid, auto bidding, automatic bid", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-446655440008", "fields": {"category": "technical", "question": "I'm having trouble logging in, what should I do?", "answer": "If you can't log in: 1) Check your username/email and password, 2) Try resetting your password using 'Forgot Password', 3) Clear your browser cache and cookies, 4) Try a different browser, 5) If still having issues, contact our support team.", "keywords": "login problem, can't login, forgot password, login issues, access problem", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"category": "payments", "question": "What happens if I win an auction but don't pay?", "answer": "If you win an auction but don't pay within the specified time (usually 24 hours): 1) You'll receive payment reminders, 2) After timeout, the auction may be reassigned to the next highest bidder, 3) Your account may be temporarily restricted, 4) Repeated non-payment may result in account suspension.", "keywords": "payment timeout, didn't pay, auction winner, payment deadline, non payment", "is_active": true, "usage_count": 0}}, {"model": "support_bot.botknowledgebase", "pk": "550e8400-e29b-41d4-a716-************", "fields": {"category": "general", "question": "How do I contact customer support?", "answer": "You can contact our support team through: 1) This AI chat bot for instant help, 2) Email: <EMAIL>, 3) WhatsApp: +1-XXX-XXX-XXXX, 4) Phone: +1-XXX-XXX-XXXX (business hours), 5) Live chat on our website. We're here to help 24/7!", "keywords": "contact support, customer service, help, support team, contact us", "is_active": true, "usage_count": 0}}]