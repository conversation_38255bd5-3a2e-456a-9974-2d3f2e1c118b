from rest_framework import serializers
from .models import Chat<PERSON>ess<PERSON>, ChatMessage, BotKnowledgeBase, BotFeedback
from accounts.serializers import UserSerializer


class ChatMessageSerializer(serializers.ModelSerializer):
    """Serializer for chat messages"""
    
    message_type_display = serializers.CharField(source='get_message_type_display', read_only=True)
    
    class Meta:
        model = ChatMessage
        fields = [
            'id', 'message_type', 'message_type_display', 'content',
            'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ChatSessionSerializer(serializers.ModelSerializer):
    """Serializer for chat sessions"""
    
    user = UserSerializer(read_only=True)
    messages = ChatMessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ChatSession
        fields = [
            'id', 'user', 'title', 'is_active', 'message_count',
            'messages', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']
    
    def get_message_count(self, obj):
        return obj.messages.count()


class ChatRequestSerializer(serializers.Serializer):
    """Serializer for chat requests"""
    
    message = serializers.CharField(max_length=1000)
    session_id = serializers.UUIDField(required=False, allow_null=True)
    
    def validate_message(self, value):
        if not value.strip():
            raise serializers.ValidationError("Message cannot be empty")
        return value.strip()


class BotKnowledgeBaseSerializer(serializers.ModelSerializer):
    """Serializer for bot knowledge base"""
    
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    class Meta:
        model = BotKnowledgeBase
        fields = [
            'id', 'category', 'category_display', 'question', 'answer',
            'keywords', 'is_active', 'usage_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'usage_count', 'created_at', 'updated_at']


class BotFeedbackSerializer(serializers.ModelSerializer):
    """Serializer for bot feedback"""
    
    user = UserSerializer(read_only=True)
    message = ChatMessageSerializer(read_only=True)
    message_id = serializers.UUIDField(write_only=True)
    rating_display = serializers.CharField(source='get_rating_display', read_only=True)
    
    class Meta:
        model = BotFeedback
        fields = [
            'id', 'message', 'message_id', 'user', 'rating', 'rating_display',
            'comment', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']
    
    def validate_message_id(self, value):
        try:
            message = ChatMessage.objects.get(id=value, message_type='bot')
            return message
        except ChatMessage.DoesNotExist:
            raise serializers.ValidationError("Bot message not found")
    
    def validate_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value
