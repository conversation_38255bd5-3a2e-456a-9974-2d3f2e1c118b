from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from .models import ChatSession, ChatMessage, BotFeedback
from .serializers import (
    ChatSessionSerializer, ChatMessageSerializer, BotFeedbackSerializer,
    ChatRequestSerializer
)
from .services import AIBotService


class ChatSessionListView(generics.ListAPIView):
    """List user's chat sessions"""

    serializer_class = ChatSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user)

    @extend_schema(
        summary="List chat sessions",
        description="Get all chat sessions for the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ChatSessionDetailView(generics.RetrieveAPIView):
    """Get chat session with messages"""

    serializer_class = ChatSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user)

    @extend_schema(
        summary="Get chat session details",
        description="Get chat session with all messages"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Send message to AI bot",
    description="Send a message to the AI support bot and get a response",
    request=ChatRequestSerializer
)
def chat_with_bot(request):
    """Send message to AI bot"""
    serializer = ChatRequestSerializer(data=request.data)
    if serializer.is_valid():
        message = serializer.validated_data['message']
        session_id = serializer.validated_data.get('session_id')

        bot_service = AIBotService()
        result = bot_service.process_message(request.user, message, session_id)

        if 'error' in result:
            return Response(
                {'error': result['error']},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        return Response({
            'session_id': result['session_id'],
            'user_message': ChatMessageSerializer(result['user_message']).data,
            'bot_message': ChatMessageSerializer(result['bot_message']).data,
            'response': result['response']
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BotFeedbackCreateView(generics.CreateAPIView):
    """Submit feedback for bot response"""

    serializer_class = BotFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @extend_schema(
        summary="Submit bot feedback",
        description="Submit feedback rating for a bot response"
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)
