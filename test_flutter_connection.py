#!/usr/bin/env python3
"""
Test script to verify Flutter app can connect to the backend
This simulates the Flutter app's API calls
"""

import requests
import json
import sys

def test_flutter_connection():
    """Test the connection from Flutter app perspective"""
    
    # Read the current Flutter configuration
    try:
        with open('fish_auction_app/lib/constants/app_constants.dart', 'r') as f:
            content = f.read()
            
        # Extract base URL
        import re
        base_url_match = re.search(r"baseUrl = '([^']+)'", content)
        ws_url_match = re.search(r"wsUrl = '([^']+)'", content)
        
        if not base_url_match:
            print("❌ Could not find baseUrl in Flutter constants")
            return False
            
        base_url = base_url_match.group(1)
        ws_url = ws_url_match.group(1) if ws_url_match else "Not found"
        
        print("🧪 Testing Flutter App Connection")
        print("=" * 40)
        print(f"📱 Flutter API URL: {base_url}")
        print(f"🔗 Flutter WebSocket URL: {ws_url}")
        print()
        
    except Exception as e:
        print(f"❌ Error reading Flutter constants: {e}")
        return False
    
    # Test 1: Basic connectivity
    print("📋 Test 1: Basic API connectivity")
    try:
        response = requests.get(
            f"{base_url.replace('/api', '')}/admin/",
            headers={"ngrok-skip-browser-warning": "true"},
            timeout=10
        )
        print(f"✅ Server responding (HTTP {response.status_code})")
    except Exception as e:
        print(f"❌ Server not responding: {e}")
        return False
    
    # Test 2: Login endpoint
    print("\n📋 Test 2: Login endpoint")
    try:
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        response = requests.post(
            f"{base_url}/auth/login/",
            json=login_data,
            headers={
                "Content-Type": "application/json",
                "ngrok-skip-browser-warning": "true"
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"   User: {data['user']['username']} ({data['user']['user_type']})")
            print(f"   Tokens: Access & Refresh tokens received")
            
            # Store tokens for further tests
            access_token = data['tokens']['access']
            
        else:
            print(f"❌ Login failed (HTTP {response.status_code})")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Login request failed: {e}")
        return False
    
    # Test 3: Authenticated endpoint
    print("\n📋 Test 3: Authenticated API call")
    try:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
            "ngrok-skip-browser-warning": "true"
        }
        
        response = requests.get(
            f"{base_url}/auctions/",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Authenticated API call successful!")
            print(f"   Found {data.get('count', 0)} auctions")
        else:
            print(f"❌ Authenticated API call failed (HTTP {response.status_code})")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Authenticated API request failed: {e}")
    
    # Test 4: User profile
    print("\n📋 Test 4: User profile endpoint")
    try:
        response = requests.get(
            f"{base_url}/auth/profile/",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Profile endpoint working!")
            print(f"   User: {data['username']} - {data['first_name']} {data['last_name']}")
            print(f"   Balance: ${data['wallet_balance']}")
        else:
            print(f"❌ Profile endpoint failed (HTTP {response.status_code})")
            
    except Exception as e:
        print(f"❌ Profile request failed: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 Flutter app should now be able to connect!")
    print()
    print("📱 Next steps:")
    print("1. cd fish_auction_app")
    print("2. flutter pub get")
    print("3. flutter run -d web-server --web-port 8080")
    print()
    print("🔑 Test credentials:")
    print("   Username: testuser")
    print("   Password: testpass123")
    
    return True

if __name__ == "__main__":
    try:
        success = test_flutter_connection()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
