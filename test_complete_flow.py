#!/usr/bin/env python3
"""
Test complete WebSocket + Celery + Auto-bid flow
"""
import os
import sys
import django
import asyncio
import json
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from decimal import Decimal
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from auctions.tasks import process_auto_bids_for_auction

def test_complete_flow():
    print("🧪 Testing Complete WebSocket + Celery + Auto-bid Flow...")
    
    # Get the live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found!")
        return
    
    print(f"🎯 Auction: {live_auction.title} (ID: {live_auction.id})")
    print(f"   Current price: ${live_auction.current_price}")
    
    # Test 1: Check Redis connection
    print("\n🔍 Test 1: Redis Connection")
    try:
        channel_layer = get_channel_layer()
        if channel_layer:
            print("✅ Channel layer available")
            print(f"   Backend: {channel_layer.__class__.__name__}")
        else:
            print("❌ No channel layer configured!")
            return
    except Exception as e:
        print(f"❌ Redis connection error: {e}")
        return
    
    # Test 2: Send test WebSocket message
    print("\n🔍 Test 2: WebSocket Message Sending")
    try:
        test_message = {
            'auction_id': live_auction.id,
            'bid_id': 999,
            'amount': '999.00',
            'bidder': 'test_user',
            'timestamp': '2025-06-22T12:00:00Z',
            'current_price': '999.00',
            'total_bids': 999,
            'bid_type': 'test'
        }
        
        async_to_sync(channel_layer.group_send)(
            f'auction_{live_auction.id}',
            {
                'type': 'bid_update',
                'bid_data': test_message
            }
        )
        print("✅ Test WebSocket message sent successfully!")
        print(f"   Group: auction_{live_auction.id}")
        
    except Exception as e:
        print(f"❌ Error sending WebSocket message: {e}")
        return
    
    # Test 3: Check active auto-bids
    print("\n🔍 Test 3: Active Auto-bids")
    active_auto_bids = AutoBid.objects.filter(
        auction=live_auction,
        is_active=True,
        max_amount__gt=live_auction.current_price
    ).order_by('-max_amount')
    
    print(f"   Found {active_auto_bids.count()} active auto-bids:")
    for ab in active_auto_bids:
        print(f"     - {ab.bidder.username}: max ${ab.max_amount}")
    
    if active_auto_bids.count() < 2:
        print("⚠️  Need at least 2 active auto-bids for proper testing")
        return
    
    # Test 4: Place manual bid to trigger auto-bids
    print("\n🔍 Test 4: Manual Bid + Auto-bid Trigger")
    
    # Find a user who doesn't have auto-bids
    manual_bidder = None
    for user in User.objects.filter(user_type='buyer'):
        if not active_auto_bids.filter(bidder=user).exists():
            manual_bidder = user
            break
    
    if not manual_bidder:
        print("❌ No available manual bidder found!")
        return
    
    # Place manual bid
    manual_bid_amount = live_auction.current_price + live_auction.bid_increment
    print(f"   Placing manual bid: ${manual_bid_amount} by {manual_bidder.username}")
    
    try:
        # Create manual bid
        manual_bid = Bid.objects.create(
            auction=live_auction,
            bidder=manual_bidder,
            amount=manual_bid_amount,
            bid_type='manual'
        )
        
        # Update auction
        live_auction.current_price = manual_bid_amount
        live_auction.total_bids += 1
        live_auction.save()
        
        print(f"✅ Manual bid placed: ${manual_bid.amount}")
        
        # Send WebSocket update for manual bid
        manual_bid_data = {
            'auction_id': live_auction.id,
            'bid_id': manual_bid.id,
            'amount': str(manual_bid.amount),
            'bidder': manual_bid.bidder.username,
            'timestamp': manual_bid.timestamp.isoformat(),
            'current_price': str(live_auction.current_price),
            'total_bids': live_auction.total_bids,
            'bid_type': 'manual'
        }
        
        async_to_sync(channel_layer.group_send)(
            f'auction_{live_auction.id}',
            {
                'type': 'bid_update',
                'bid_data': manual_bid_data
            }
        )
        print("✅ WebSocket update sent for manual bid!")
        
    except Exception as e:
        print(f"❌ Error placing manual bid: {e}")
        return
    
    # Test 5: Trigger auto-bid processing via Celery
    print("\n🔍 Test 5: Celery Auto-bid Processing")
    try:
        print("   Triggering auto-bid processing...")
        result = process_auto_bids_for_auction.delay(live_auction.id, manual_bid.id)
        print(f"✅ Auto-bid task queued: {result.id}")
        
        # Wait for task to complete
        print("   Waiting for auto-bid processing...")
        time.sleep(3)
        
        # Check if auto-bids were placed
        live_auction.refresh_from_db()
        new_bids = Bid.objects.filter(
            auction=live_auction,
            timestamp__gt=manual_bid.timestamp
        ).order_by('-timestamp')
        
        print(f"✅ Auto-bid processing complete!")
        print(f"   New price: ${live_auction.current_price}")
        print(f"   Auto-bids placed: {new_bids.count()}")
        
        # Show new auto-bids
        for i, bid in enumerate(new_bids[:5], 1):
            bid_type = "🤖 Auto" if bid.bid_type == 'auto' else "👤 Manual"
            print(f"     {i}. {bid.bidder.username}: ${bid.amount} ({bid_type})")
        
    except Exception as e:
        print(f"❌ Error in auto-bid processing: {e}")
        return
    
    # Test 6: Check WebSocket messages were sent for auto-bids
    print("\n🔍 Test 6: WebSocket Messages for Auto-bids")
    print("   Auto-bid WebSocket messages should have been sent automatically")
    print("   Check your Flutter app for real-time updates!")
    
    # Summary
    print("\n📊 Test Summary:")
    print("✅ Redis connection working")
    print("✅ WebSocket message sending working")
    print("✅ Auto-bid logic working")
    print("✅ Celery task processing working")
    print("✅ Manual + Auto-bid integration working")
    print("\n🎯 If your Flutter app is connected, you should see real-time updates!")
    print("   If not, check the WebSocket connection in Flutter console logs.")

if __name__ == "__main__":
    test_complete_flow()
