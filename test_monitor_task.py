#!/usr/bin/env python3
"""
Test the monitor task directly to verify it works
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.tasks import monitor_auction_auto_bids
from auctions.models import Auction, AutoBid

def test_monitor_task():
    """Test the monitor task directly"""
    print("🔍 Testing Monitor Task Directly")
    print("=" * 40)
    
    # Show current auction state
    live_auctions = Auction.objects.filter(status='live')
    print(f"📊 Live auctions: {live_auctions.count()}")
    
    for auction in live_auctions:
        auto_bids = AutoBid.objects.filter(auction=auction, is_active=True)
        eligible_auto_bids = auto_bids.filter(max_amount__gt=auction.current_price)
        
        print(f"\n🏷️  {auction.title}")
        print(f"   💰 Current price: ${auction.current_price}")
        print(f"   🤖 Active auto-bids: {auto_bids.count()}")
        print(f"   ✅ Eligible auto-bids: {eligible_auto_bids.count()}")
        
        # Show last bid
        last_bid = auction.bids.order_by('-timestamp').first()
        if last_bid:
            print(f"   📝 Last bid: ${last_bid.amount} by {last_bid.bidder.username} ({last_bid.bid_type})")
        
        # Show eligible auto-bids
        for auto_bid in eligible_auto_bids:
            remaining = auto_bid.max_amount - auction.current_price
            print(f"      - {auto_bid.bidder.username}: ${remaining} remaining")
    
    # Run the monitor task
    print(f"\n🚀 Running monitor task...")
    result = monitor_auction_auto_bids()
    print(f"✅ Monitor result: {result}")
    
    return True

if __name__ == "__main__":
    try:
        test_monitor_task()
        print("\n✅ Monitor task test completed successfully!")
    except Exception as e:
        print(f"❌ Monitor task test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
