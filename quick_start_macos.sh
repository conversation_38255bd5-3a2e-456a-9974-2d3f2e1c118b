#!/bin/bash

# Fish Auction App - Quick Start for macOS
# Enhanced version with debug output and iOS support

echo "🐟 Fish Auction App - Quick Start (macOS) - Enhanced Debug Mode"
echo "================================================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Setup environment paths first
echo -e "${BLUE}🔧 Setting up environment paths...${NC}"
export PATH="/opt/homebrew/bin:$PATH"
export PATH="/Users/<USER>/flutter/bin:$PATH"
eval "$(/opt/homebrew/bin/brew shellenv)" 2>/dev/null || true

# Detect Python command
if command -v python3 >/dev/null 2>&1; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
elif command -v python >/dev/null 2>&1; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
else
    echo -e "${RED}❌ Python not found. Please install Python 3.8+${NC}"
    echo "Install from: https://python.org/downloads/"
    exit 1
fi

echo -e "${GREEN}✅ Using Python: $PYTHON_CMD${NC}"

# Check Redis
if ! command -v redis-cli >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ Redis not found.${NC}"
    if command -v brew >/dev/null 2>&1; then
        echo -e "${BLUE}📦 Installing Redis with Homebrew...${NC}"
        brew install redis
    else
        echo -e "${RED}❌ Redis is required for WebSocket support!${NC}"
        echo ""
        echo "Please install Redis using one of these methods:"
        echo ""
        echo "1. Install Homebrew first, then Redis:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        echo "   brew install redis"
        echo ""
        echo "2. Or download Redis directly:"
        echo "   https://redis.io/download"
        echo ""
        echo "3. Or use Docker:"
        echo "   docker run -d -p 6379:6379 redis:alpine"
        echo ""
        exit 1
    fi
fi

# Start Redis
if ! redis-cli ping >/dev/null 2>&1; then
    echo -e "${BLUE}🚀 Starting Redis...${NC}"
    brew services start redis
    sleep 2
fi
echo -e "${GREEN}✅ Redis is running${NC}"

# Check ngrok
if ! command -v ngrok >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ ngrok not found. Installing with Homebrew...${NC}"
    if command -v brew >/dev/null 2>&1; then
        brew install ngrok
    else
        echo -e "${RED}❌ Please install ngrok manually:${NC}"
        echo "brew install ngrok"
        echo "Or download from: https://ngrok.com/download"
        exit 1
    fi
fi
echo -e "${GREEN}✅ ngrok is available${NC}"

# Check iOS development setup
echo -e "${BLUE}📱 Checking iOS development setup...${NC}"
if command -v flutter >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Flutter is installed${NC}"

    # Check if Xcode is installed
    if command -v xcodebuild >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Xcode is installed${NC}"

        # Check iOS simulators
        if xcrun simctl list devices | grep -q "iPhone"; then
            echo -e "${GREEN}✅ iOS simulators available${NC}"
        else
            echo -e "${YELLOW}⚠️ No iOS simulators found${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Xcode not found - iOS development not available${NC}"
        echo -e "${BLUE}💡 Install Xcode from App Store for iOS development${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Flutter not found${NC}"
    echo -e "${BLUE}💡 Install Flutter: https://flutter.dev/docs/get-started/install${NC}"
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${BLUE}💡 Creating virtual environment...${NC}"
    $PYTHON_CMD -m venv venv
fi

# Activate virtual environment
echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Install dependencies
echo -e "${BLUE}📦 Installing dependencies...${NC}"
pip install -r requirements.txt

# Install WebSocket dependencies
echo -e "${BLUE}📡 Installing WebSocket dependencies...${NC}"
pip install channels_redis daphne

# Verify Django installation
echo -e "${BLUE}🔍 Verifying Django installation...${NC}"
python -c "import django; print(f'Django {django.get_version()} installed')" || {
    echo -e "${RED}❌ Django installation failed. Trying again...${NC}"
    pip install --upgrade pip
    pip install -r requirements.txt --force-reinstall
    pip install channels_redis daphne
}

# Verify Redis channel layer
echo -e "${BLUE}🔍 Verifying Redis channel layer...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
from channels.layers import get_channel_layer
layer = get_channel_layer()
print(f'Channel layer: {layer.__class__.__name__}')
if 'Redis' not in layer.__class__.__name__:
    raise Exception('Redis channel layer not configured!')
print('✅ Redis channel layer working')
" || {
    echo -e "${RED}❌ Redis channel layer not working${NC}"
    exit 1
}

# Fix JWT settings before migrations
echo -e "${BLUE}🔧 Fixing JWT configuration...${NC}"
python -c "
import re

try:
    with open('fish_auction/settings.py', 'r') as f:
        content = f.read()

    # Fix JWT settings to prevent OutstandingToken errors
    jwt_pattern = r'SIMPLE_JWT = \{[^}]*\}'
    new_jwt_config = '''SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=env('JWT_ACCESS_TOKEN_LIFETIME', default=60)),
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=env('JWT_REFRESH_TOKEN_LIFETIME', default=1440)),
    'ROTATE_REFRESH_TOKENS': False,
    'UPDATE_LAST_LOGIN': False,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': env('JWT_SECRET_KEY', default=SECRET_KEY),
}'''

    if 'SIMPLE_JWT' in content:
        content = re.sub(jwt_pattern, new_jwt_config, content, flags=re.DOTALL)
    else:
        # Add JWT config if not present
        content += '\n\n# JWT Settings - Simple configuration without token blacklisting\n' + new_jwt_config + '\n'

    # Remove blacklist app from INSTALLED_APPS if present
    content = re.sub(r\"'rest_framework_simplejwt\.token_blacklist',?\s*\", '', content)

    # Add Stripe test keys
    stripe_pattern = r\"STRIPE_PUBLISHABLE_KEY = env\('STRIPE_PUBLISHABLE_KEY', default='[^']*'\)\"
    stripe_replacement = \"STRIPE_PUBLISHABLE_KEY = env('STRIPE_PUBLISHABLE_KEY', default='pk_test_51OLNPZHIyomRWz3uKNPoOk1439K6zjFyLcBEGoCuzL6JVGfI76Q70kzrxQC4w1y3VvZRirOnv5MntcjNI2cdCYX90016aQVKcs')\"
    content = re.sub(stripe_pattern, stripe_replacement, content)

    stripe_secret_pattern = r\"STRIPE_SECRET_KEY = env\('STRIPE_SECRET_KEY', default='[^']*'\)\"
    stripe_secret_replacement = \"STRIPE_SECRET_KEY = env('STRIPE_SECRET_KEY', default='sk_test_51OLNPZHIyomRWz3uVKGR91iEwmdYt7N4AOuZtA0Cmsf46weg58JNuvWKcFvlV6nO2QSUACiFqk65fPWkyTuS3KEj00usUtxF04')\"
    content = re.sub(stripe_secret_pattern, stripe_secret_replacement, content)

    with open('fish_auction/settings.py', 'w') as f:
        f.write(content)
    print('✅ JWT and Stripe configuration fixed')
except Exception as e:
    print(f'⚠️ Could not fix JWT configuration: {e}')
"

# Create migrations for any model changes
echo -e "${BLUE}🔄 Creating migrations...${NC}"
python manage.py makemigrations

# Run migrations
echo -e "${BLUE}🗄️ Setting up database...${NC}"
python manage.py migrate

# Create notification templates
echo -e "${BLUE}📱 Setting up notification templates...${NC}"
python -c "
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from notifications.models import NotificationTemplate

# Clean up existing templates
NotificationTemplate.objects.all().delete()

# Create notification templates
templates = [
    {
        'name': 'Auction Ending Soon - Bidder Alert',
        'notification_type': 'auction_ending_soon',
        'email_subject': 'Auction Ending Soon - {{auction.title}}',
        'email_body': 'The auction \"{{auction.title}}\" that you have bid on is ending in 1 hour!',
        'whatsapp_message': '🐟 *Auction Ending Soon!* \"{{auction.title}}\" ends in 1 hour! Current Price: \${{auction.current_price}}',
        'push_title': 'Auction Ending Soon!',
        'push_body': '{{auction.title}} ends in 1 hour. Current price: \${{auction.current_price}}'
    },
    {
        'name': 'Scheduled Auction Started - Seller',
        'notification_type': 'auction_started',
        'email_subject': 'Your Auction is Now Live - {{auction.title}}',
        'email_body': 'Your scheduled auction \"{{auction.title}}\" is now live and accepting bids.',
        'whatsapp_message': '🎉 *Your Auction is Live!* \"{{auction.title}}\" is now accepting bids!',
        'push_title': 'Auction Started!',
        'push_body': 'Your auction {{auction.title}} is now live and accepting bids'
    },
    {
        'name': 'Auction Ended with Winner - Seller',
        'notification_type': 'auction_ended_with_winner',
        'email_subject': 'Auction Sold - {{auction.title}}',
        'email_body': 'Your auction \"{{auction.title}}\" has ended successfully. Winner: {{winner.get_full_name}}',
        'whatsapp_message': '🎉 *Auction Sold!* \"{{auction.title}}\" sold to {{winner.get_full_name}} for \${{auction.current_price}}',
        'push_title': 'Auction Sold!',
        'push_body': '{{auction.title}} sold to {{winner.get_full_name}} for \${{auction.current_price}}'
    },
    {
        'name': 'Auction Won - Buyer Payment Required',
        'notification_type': 'auction_won',
        'email_subject': 'Congratulations! You Won - {{auction.title}}',
        'email_body': 'You have won the auction for \"{{auction.title}}\". You have 20 minutes to complete payment.',
        'whatsapp_message': '🎉 *You Won!* \"{{auction.title}}\" - Pay within 20 minutes!',
        'push_title': 'You Won! Payment Required',
        'push_body': 'You won {{auction.title}}. Pay within 20 minutes!'
    },
    {
        'name': 'Payment Success - Buyer',
        'notification_type': 'payment_success',
        'email_subject': 'Payment Confirmed - {{auction.title}}',
        'email_body': 'Your payment for \"{{auction.title}}\" has been successfully processed.',
        'whatsapp_message': '✅ *Payment Successful!* \"{{auction.title}}\" - Your fish is being prepared!',
        'push_title': 'Payment Successful!',
        'push_body': 'Payment confirmed for {{auction.title}}. Delivery preparation started.'
    }
]

# Create templates
created_count = 0
for template_data in templates:
    template = NotificationTemplate.objects.create(**template_data)
    created_count += 1

print(f'✅ Created {created_count} notification templates')
" || {
    echo -e "${YELLOW}⚠️ Could not create notification templates${NC}"
}

# Kill any existing processes
echo -e "${BLUE}🔄 Cleaning up existing processes...${NC}"
pkill -f "runserver" 2>/dev/null || true
pkill -f "daphne.*fish_auction" 2>/dev/null || true
pkill -f "celery.*fish_auction" 2>/dev/null || true
pkill -f "ngrok http" 2>/dev/null || true

# Kill processes on common ports
for port in 8000 8001 8002 8003 8004; do
    lsof -ti:$port | xargs kill -9 2>/dev/null || true
done

# Wait a moment for processes to fully terminate
sleep 2

# Find available port
PORT=8000
for p in 8000 8001 8002 8003 8004; do
    if ! lsof -i :$p >/dev/null 2>&1; then
        PORT=$p
        break
    fi
done

echo -e "${GREEN}✅ Using port: $PORT${NC}"

# Start Django server with WebSocket support (ASGI required for WebSockets)
echo -e "${BLUE}🚀 Starting Django server with WebSocket support on port $PORT...${NC}"
echo -e "${YELLOW}📡 Using Daphne ASGI server for WebSocket support${NC}"
echo -e "${BLUE}🐛 Debug mode: Server output will be displayed in real-time${NC}"

# Start Django with debug output
echo -e "${YELLOW}📋 Starting Django server with verbose output...${NC}"
daphne -b 0.0.0.0 -p $PORT -v 2 fish_auction.asgi:application &
DJANGO_PID=$!

# Wait and check if Django started
echo -e "${BLUE}⏳ Waiting for Django server to start...${NC}"
sleep 5
if ! lsof -i :$PORT >/dev/null 2>&1; then
    echo -e "${RED}❌ Django server failed to start on port $PORT${NC}"
    echo -e "${YELLOW}📋 Checking for errors...${NC}"
    if [ -f django.log ]; then
        echo -e "${YELLOW}📋 Error log:${NC}"
        cat django.log
    fi
    echo -e "${BLUE}💡 Try running: python3 manage.py runserver $PORT --verbosity=2${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Django server running on port $PORT${NC}"
echo -e "${BLUE}🔗 Local access: http://localhost:$PORT${NC}"

# Verify WebSocket support
echo -e "${BLUE}🔍 Verifying WebSocket support...${NC}"
if ps aux | grep -q "daphne.*fish_auction.asgi"; then
    echo -e "${GREEN}✅ Daphne ASGI server running (WebSocket support enabled)${NC}"
else
    echo -e "${YELLOW}⚠️ Daphne not detected, checking for Django runserver...${NC}"
    if ps aux | grep -q "runserver.*8000"; then
        echo -e "${YELLOW}⚠️ Django runserver detected - WebSocket support may be limited${NC}"
        echo -e "${BLUE}💡 For full WebSocket support, ensure Daphne is installed${NC}"
    fi
fi

# Start Celery worker with debug output
echo -e "${BLUE}🔄 Starting Celery worker with debug output...${NC}"
celery -A fish_auction worker --loglevel=debug &
CELERY_WORKER_PID=$!

# Start Celery beat with debug output
echo -e "${BLUE}⏰ Starting Celery beat with debug output...${NC}"
celery -A fish_auction beat --loglevel=debug &
CELERY_BEAT_PID=$!

# Start ngrok with debug output
echo -e "${BLUE}🌐 Starting ngrok tunnel with debug output on port $PORT...${NC}"
ngrok http $PORT --log=stdout &
NGROK_PID=$!

# Wait for services to start
sleep 5

# Test auto-bidding system
echo -e "${BLUE}🧪 Testing auto-bidding system...${NC}"
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
import django
django.setup()
from auctions.models import Auction
live_auctions = Auction.objects.filter(status='live').count()
print(f'✅ Found {live_auctions} live auctions')
print('✅ Auto-bidding system ready')
" || {
    echo -e "${YELLOW}⚠️ No live auctions found - create one in admin panel${NC}"
}

# Get ngrok URL
NGROK_URL=""
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | python -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data.get('tunnels', []):
        if tunnel.get('proto') == 'https':
            print(tunnel.get('public_url', ''))
            break
except:
    pass
" 2>/dev/null)
    
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    sleep 1
done

if [ -z "$NGROK_URL" ]; then
    echo -e "${YELLOW}⚠️ Could not get ngrok URL automatically${NC}"
    echo -e "${BLUE}📍 Check ngrok dashboard: http://localhost:4040${NC}"
    NGROK_URL="https://[your-ngrok-url].ngrok.io"
else
    echo -e "${GREEN}✅ ngrok tunnel: $NGROK_URL${NC}"

    # Update Flutter app configuration automatically
    echo -e "${BLUE}🔄 Updating Flutter app configuration...${NC}"
    FLUTTER_CONSTANTS_FILE="fish_auction_app/lib/constants/app_constants.dart"
    NGROK_HOST=$(echo "$NGROK_URL" | sed 's|https://||' | sed 's|http://||')

    if [ -f "$FLUTTER_CONSTANTS_FILE" ]; then
        # Backup original file
        cp "$FLUTTER_CONSTANTS_FILE" "$FLUTTER_CONSTANTS_FILE.backup" 2>/dev/null || true

        # Update the URLs in the Flutter constants file
        sed -i '' "s|static const String baseUrl = 'https://.*\.ngrok-free\.app/api';|static const String baseUrl = 'https://$NGROK_HOST/api';|g" "$FLUTTER_CONSTANTS_FILE"
        sed -i '' "s|static const String wsUrl = 'wss://.*\.ngrok-free\.app/ws';|static const String wsUrl = 'wss://$NGROK_HOST/ws';|g" "$FLUTTER_CONSTANTS_FILE"

        echo -e "${GREEN}✅ Flutter app updated with new ngrok URL${NC}"
    else
        echo -e "${YELLOW}⚠️ Flutter constants file not found: $FLUTTER_CONSTANTS_FILE${NC}"
    fi

    # Update Django ALLOWED_HOSTS
    echo -e "${BLUE}🔄 Updating Django ALLOWED_HOSTS...${NC}"
    python3 -c "
import re

try:
    with open('fish_auction/settings.py', 'r') as f:
        content = f.read()

    # Update ALLOWED_HOSTS line
    pattern = r\"ALLOWED_HOSTS = \[.*?\]\"
    new_hosts = \"ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', '$NGROK_HOST', '*']\"
    content = re.sub(pattern, new_hosts, content)

    with open('fish_auction/settings.py', 'w') as f:
        f.write(content)
    print('✅ Django ALLOWED_HOSTS updated')
except Exception as e:
    print(f'⚠️ Could not update ALLOWED_HOSTS: {e}')
"
fi

# Display setup instructions
echo ""
echo -e "${YELLOW}🎯 STRIPE WEBHOOK SETUP${NC}"
echo "================================"
echo -e "${BLUE}📍 Webhook URL: $NGROK_URL/api/payments/stripe/webhook/${NC}"
echo ""
echo "📋 Next steps:"
echo "1. Go to: https://dashboard.stripe.com/webhooks"
echo "2. Click 'Add endpoint'"
echo "3. Enter URL: $NGROK_URL/api/payments/stripe/webhook/"
echo "4. Select events: payment_intent.succeeded, payment_intent.payment_failed"
echo "5. Copy signing secret to .env file"
echo ""
echo -e "${GREEN}🎉 Backend is running!${NC}"
echo "================================"
echo "📱 Django: http://localhost:$PORT"
echo "🌐 Public: $NGROK_URL"
echo "📊 ngrok: http://localhost:4040"
echo ""
echo "📁 Debug output: All services running with verbose logging"
echo ""
echo -e "${BLUE}🚀 Now start Flutter app:${NC}"
echo "cd fish_auction_app"
echo "flutter pub get"
echo ""
echo -e "${GREEN}📱 Flutter Run Options:${NC}"
echo "• iOS Device:    flutter run -d ios"
echo "• iOS Simulator: flutter run -d ios --simulator"
echo "• Web:          flutter run -d web-server --web-port 8080"
echo "• Android:      flutter run -d android"
echo ""
echo -e "${YELLOW}💡 For iOS device deployment:${NC}"
echo "1. Connect your iOS device via USB"
echo "2. Trust the computer on your device"
echo "3. Run: flutter devices (to see available devices)"
echo "4. Run: flutter run -d ios"
echo ""
echo -e "${GREEN}📱 Test Credentials:${NC}"
echo "Username: testuser"
echo "Password: testpass123"
echo ""
echo -e "${BLUE}💡 Flutter app is configured with:${NC}"
echo "🌐 API URL: $NGROK_URL/api"
echo "🔗 WebSocket URL: ${NGROK_URL/https/wss}/ws"
echo ""
echo -e "${GREEN}🎯 Auto-bidding Features:${NC}"
echo "✅ Real-time bidding with WebSocket (Daphne ASGI)"
echo "✅ Asynchronous auto-bidding (one by one)"
echo "✅ Server-side processing (works when app closed)"
echo "✅ Redis channel layer for real-time updates"
echo "✅ Celery workers for background tasks"
echo ""
echo -e "${BLUE}🔌 WebSocket Testing:${NC}"
echo "• Green WiFi icon in Flutter app = WebSocket connected"
echo "• Check browser console for WebSocket messages"
echo "• Real-time updates should work without page refresh"
echo ""
echo "🛑 Press Ctrl+C to stop all services"
echo ""
echo -e "${BLUE}🤖 Auto-start Flutter app? (y/n)${NC}"
read -t 10 -n 1 AUTO_START_FLUTTER
echo ""

if [[ $AUTO_START_FLUTTER =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚀 Auto-starting Flutter app...${NC}"

    # Check if we're in the right directory
    if [ -d "fish_auction_app" ]; then
        cd fish_auction_app

        echo -e "${BLUE}📦 Running flutter pub get...${NC}"
        flutter pub get

        echo -e "${BLUE}📱 Checking for connected devices...${NC}"
        flutter devices

        echo -e "${BLUE}🎯 Starting Flutter app on iOS device...${NC}"
        echo -e "${YELLOW}💡 Make sure your iOS device is connected and trusted${NC}"

        # Try to run on iOS device
        flutter run -d ios --verbose &
        FLUTTER_PID=$!

        echo -e "${GREEN}✅ Flutter app starting...${NC}"
        echo -e "${BLUE}📱 Check your iOS device for the app${NC}"
    else
        echo -e "${RED}❌ fish_auction_app directory not found${NC}"
        echo -e "${BLUE}💡 Make sure you're in the correct directory${NC}"
    fi
else
    echo -e "${BLUE}💡 Skipping auto-start. Run Flutter manually when ready.${NC}"
fi

# Cleanup function
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping all services...${NC}"

    # Stop individual processes
    [ ! -z "$DJANGO_PID" ] && kill $DJANGO_PID 2>/dev/null && echo -e "${BLUE}🔄 Stopped Django server${NC}"
    [ ! -z "$CELERY_WORKER_PID" ] && kill $CELERY_WORKER_PID 2>/dev/null && echo -e "${BLUE}🔄 Stopped Celery worker${NC}"
    [ ! -z "$CELERY_BEAT_PID" ] && kill $CELERY_BEAT_PID 2>/dev/null && echo -e "${BLUE}🔄 Stopped Celery beat${NC}"
    [ ! -z "$NGROK_PID" ] && kill $NGROK_PID 2>/dev/null && echo -e "${BLUE}🔄 Stopped ngrok${NC}"
    [ ! -z "$FLUTTER_PID" ] && kill $FLUTTER_PID 2>/dev/null && echo -e "${BLUE}🔄 Stopped Flutter app${NC}"

    # Kill any remaining processes
    pkill -f "runserver 8000" 2>/dev/null || true
    pkill -f "daphne.*fish_auction" 2>/dev/null || true
    pkill -f "celery.*fish_auction" 2>/dev/null || true
    pkill -f "ngrok http 8000" 2>/dev/null || true
    pkill -f "flutter run" 2>/dev/null || true

    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

trap cleanup INT TERM

# Keep running
while true; do
    sleep 1
done
