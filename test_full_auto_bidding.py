#!/usr/bin/env python3
"""
Comprehensive test of the auto-bidding system
This creates a scenario where multiple users can compete
"""

import os
import sys
import django
import time
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from auctions.tasks import process_auto_bids_for_auction, monitor_auction_auto_bids

def setup_competitive_scenario():
    """Set up a scenario where multiple users can compete"""
    print("🔧 Setting up competitive auto-bidding scenario...")
    
    # Get live auction
    live_auction = Auction.objects.filter(status='live').first()
    if not live_auction:
        print("❌ No live auction found")
        return None
    
    # Get users for testing
    users = User.objects.filter(user_type='buyer')[:4]
    if len(users) < 2:
        print("❌ Need at least 2 buyer users")
        return None
    
    print(f"📍 Using auction: {live_auction.title}")
    print(f"💰 Current price: ${live_auction.current_price}")
    
    # Clear existing auto-bids for clean test
    AutoBid.objects.filter(auction=live_auction).delete()
    print("🧹 Cleared existing auto-bids")
    
    # Create new auto-bids with different max amounts
    current_price = live_auction.current_price
    auto_bids_data = [
        (users[0], current_price + 10),  # User 1: +$10
        (users[1], current_price + 15),  # User 2: +$15  
        (users[2], current_price + 8),   # User 3: +$8
        (users[3], current_price + 12),  # User 4: +$12
    ]
    
    created_auto_bids = []
    for user, max_amount in auto_bids_data:
        auto_bid = AutoBid.objects.create(
            auction=live_auction,
            bidder=user,
            max_amount=max_amount,
            increment=live_auction.bid_increment,
            is_active=True
        )
        created_auto_bids.append(auto_bid)
        print(f"✅ Created auto-bid: {user.username} max ${max_amount}")
    
    return live_auction, created_auto_bids

def test_continuous_competition():
    """Test that auto-bids compete continuously"""
    print("\n🧪 Testing Continuous Auto-Bid Competition")
    print("=" * 50)
    
    # Setup scenario
    result = setup_competitive_scenario()
    if not result:
        return False
    
    live_auction, auto_bids = result
    
    # Record initial state
    initial_price = live_auction.current_price
    initial_bid_count = live_auction.total_bids
    
    print(f"\n📊 Initial state:")
    print(f"   💰 Price: ${initial_price}")
    print(f"   📝 Bids: {initial_bid_count}")
    
    # Test multiple rounds of auto-bidding
    print(f"\n🚀 Starting auto-bid competition rounds...")
    
    for round_num in range(1, 4):  # 3 rounds
        print(f"\n🔄 Round {round_num}:")
        
        # Trigger auto-bid processing
        result = process_auto_bids_for_auction(live_auction.id)
        print(f"   📈 Auto-bids placed: {result}")
        
        # Refresh auction
        live_auction.refresh_from_db()
        print(f"   💰 New price: ${live_auction.current_price}")
        print(f"   📝 Total bids: {live_auction.total_bids}")
        
        # Show recent bids
        recent_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')[:3]
        for bid in recent_bids:
            bid_type_icon = "🤖" if bid.bid_type == 'auto' else "👤"
            print(f"      {bid_type_icon} ${bid.amount} by {bid.bidder.username}")
        
        # Check remaining eligible auto-bids
        last_bid = recent_bids.first()
        last_bidder = last_bid.bidder if last_bid else None
        
        eligible_auto_bids = AutoBid.objects.filter(
            auction=live_auction,
            is_active=True,
            max_amount__gt=live_auction.current_price
        )
        
        if last_bidder:
            eligible_auto_bids = eligible_auto_bids.exclude(bidder=last_bidder)
        
        print(f"   🎯 Eligible for next round: {eligible_auto_bids.count()}")
        
        if eligible_auto_bids.count() == 0:
            print(f"   🏁 No more eligible auto-bids - competition complete!")
            break
        
        # Small delay between rounds
        time.sleep(1)
    
    # Test monitor function
    print(f"\n🔍 Testing monitor function...")
    monitor_result = monitor_auction_auto_bids()
    print(f"   📊 Monitor result: {monitor_result}")
    
    # Final results
    live_auction.refresh_from_db()
    final_price = live_auction.current_price
    final_bid_count = live_auction.total_bids
    
    print(f"\n📊 Final Results:")
    print(f"   💰 Price change: ${initial_price} → ${final_price} (+${final_price - initial_price})")
    print(f"   📝 Bid change: {initial_bid_count} → {final_bid_count} (+{final_bid_count - initial_bid_count})")
    
    # Show final auto-bid states
    print(f"\n🤖 Final auto-bid states:")
    for auto_bid in auto_bids:
        auto_bid.refresh_from_db()
        status = "✅ Active" if auto_bid.is_active else "❌ Maxed out"
        remaining = max(0, auto_bid.max_amount - final_price)
        print(f"   {auto_bid.bidder.username}: {status}, ${remaining} remaining")
    
    # Show bid history
    print(f"\n📝 Recent bid history:")
    recent_bids = Bid.objects.filter(auction=live_auction).order_by('-timestamp')[:10]
    for i, bid in enumerate(recent_bids):
        bid_type_icon = "🤖" if bid.bid_type == 'auto' else "👤"
        time_str = bid.timestamp.strftime('%H:%M:%S')
        print(f"   {i+1:2d}. {bid_type_icon} ${bid.amount} by {bid.bidder.username} at {time_str}")
    
    # Success criteria
    success = (final_bid_count > initial_bid_count and 
               final_price > initial_price)
    
    if success:
        print(f"\n✅ SUCCESS: Auto-bidding competition is working!")
        print(f"   - Multiple rounds of bidding occurred")
        print(f"   - Price increased through competition")
        print(f"   - System correctly excluded last bidders")
    else:
        print(f"\n❌ ISSUE: Auto-bidding competition may have problems")
    
    return success

if __name__ == "__main__":
    try:
        success = test_continuous_competition()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
