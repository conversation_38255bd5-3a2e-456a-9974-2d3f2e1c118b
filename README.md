# Fish Auction & Services Platform - Backend API

A comprehensive Django REST Framework backend for a Fish Auction & Services platform with real-time bidding, payment processing, delivery tracking, and automated notifications.

## 🚀 Features

### Core Functionality
- **User Management**: Multi-role system (Buyers, Sellers, Brokers, Service Providers)
- **Real-time Auctions**: WebSocket-powered live bidding with automatic bid agents
- **Payment System**: Integrated wallet system with Stripe payment processing
- **Delivery Tracking**: GPS-based vessel tracking and delivery status updates
- **Notifications**: Multi-channel notifications (Email, WhatsApp, In-app)
- **Marketplace**: Buy-now listings for fish and services
- **KYC Verification**: Automated user verification system

### Advanced Features
- **Automatic Bidding**: AI-powered bidding agents with configurable limits
- **Real-time Updates**: WebSocket connections for live auction updates
- **Background Tasks**: Celery-powered async processing
- **API Documentation**: Auto-generated Swagger/OpenAPI documentation
- **Comprehensive Testing**: Unit and integration tests

## 🛠 Tech Stack

- **Backend**: Django 4.2.7 + Django REST Framework
- **Database**: PostgreSQL (SQLite for development)
- **Cache/Message Broker**: Redis
- **Real-time**: Django Channels + WebSockets
- **Background Tasks**: Celery + Celery Beat
- **Payments**: Stripe Integration
- **Notifications**: Twilio (WhatsApp), SMTP (Email)
- **Documentation**: drf-spectacular (Swagger/OpenAPI)
- **Testing**: pytest + factory-boy

## 📋 Prerequisites

- Python 3.9+
- Redis Server
- PostgreSQL (optional, SQLite included for development)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd fish-auction-backend
```

### 2. Create Virtual Environment

```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Update the following in `.env`:
- `SECRET_KEY`: Django secret key
- `DATABASE_URL`: Database connection string
- `REDIS_URL`: Redis connection string
- `STRIPE_SECRET_KEY`: Stripe secret key
- `TWILIO_ACCOUNT_SID`: Twilio account SID (for WhatsApp)
- `TWILIO_AUTH_TOKEN`: Twilio auth token

### 5. Database Setup

```bash
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
```

### 6. Create Sample Data

```bash
python manage.py create_sample_data
```

### 7. Run Development Server

```bash
python manage.py runserver
```

The API will be available at `http://localhost:8000`

## 📚 API Documentation

- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/token/refresh/` - Refresh JWT token
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/` - Update user profile

### Auctions
- `GET /api/auctions/` - List auctions
- `POST /api/auctions/create/` - Create auction (sellers only)
- `GET /api/auctions/{id}/` - Get auction details
- `POST /api/auctions/{id}/bid/` - Place bid
- `POST /api/auctions/{id}/auto-bid/` - Setup automatic bidding
- `GET /api/auctions/categories/` - List fish categories

### Payments
- `GET /api/payments/wallet/balance/` - Get wallet balance
- `POST /api/payments/wallet/topup/` - Top up wallet
- `GET /api/payments/wallet/transactions/` - Get transaction history
- `POST /api/payments/process/{auction_id}/` - Process payment

### WebSocket Endpoints
- `ws://localhost:8000/ws/auction/{auction_id}/` - Real-time auction updates

## 🔧 Background Services

### Start Celery Worker

```bash
celery -A fish_auction worker --loglevel=info
```

### Start Celery Beat (Scheduler)

```bash
celery -A fish_auction beat --loglevel=info
```

### Scheduled Tasks
- **Auction Ending Reminders**: Every 15 minutes
- **Payment Reminders**: Every 30 minutes
- **Auto-bid Processing**: Every 5 minutes
- **Auction Expiration**: Every 10 minutes
- **Notification Cleanup**: Daily at 2 AM

## 🧪 Testing

### Run All Tests

```bash
python manage.py test
```

### Run Specific App Tests

```bash
python manage.py test accounts
python manage.py test auctions
python manage.py test payments
```

### Run with Coverage

```bash
coverage run --source='.' manage.py test
coverage report
coverage html
```

## 📱 User Roles & Permissions

### Buyer
- Browse and search auctions
- Place manual and automatic bids
- Manage wallet and payments
- Track delivery status
- Rate sellers and delivery services

### Seller
- Create and manage auctions
- Update delivery status
- Receive payments
- View analytics and reports

### Broker
- Access to wholesale features
- Bulk auction management
- Advanced reporting

### Service Provider
- Manage service listings
- Handle service orders
- Delivery management

## 🔐 Security Features

- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- CORS protection
- Rate limiting (configurable)
- Secure payment processing

## 📊 Database Models

### Core Models
- **User**: Extended user model with roles and wallet
- **Auction**: Auction details with bidding logic
- **Bid**: Individual bids with auto-bid support
- **Payment**: Payment processing and tracking
- **Notification**: Multi-channel notification system

### Supporting Models
- **FishCategory**: Fish type categorization
- **Delivery**: Delivery tracking and management
- **MarketplaceListing**: Buy-now marketplace items
- **WalletTransaction**: Wallet transaction history

## 🚀 Deployment

### Production Settings

1. Set `DEBUG=False` in `.env`
2. Configure production database
3. Set up Redis cluster
4. Configure email and WhatsApp services
5. Set up SSL certificates
6. Configure static file serving

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 📞 WhatsApp Integration Setup

For WhatsApp notifications, you need a Twilio account:

1. Sign up at [Twilio](https://www.twilio.com/)
2. Get your Account SID and Auth Token
3. Set up WhatsApp sandbox or get approved for production
4. Update `.env` with Twilio credentials

**Note**: WhatsApp sandbox is free for testing but has limitations.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation
- Review the test files for usage examples

---

**Status**: ✅ **Phase 1 Complete** - Backend API with core auction functionality, payment system, and real-time features fully implemented and tested.

**Next Steps**: 
- Mobile app development (Flutter)
- Web portal for brokers/service providers
- Advanced analytics dashboard
- AI-powered features enhancement
