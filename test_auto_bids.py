#!/usr/bin/env python3
"""
Test script to check auto-bid functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fish_auction.settings')
django.setup()

from auctions.models import Auction, AutoBid, Bid
from accounts.models import User
from decimal import Decimal

def main():
    print("🔍 Checking Auto-Bid System...")
    
    # Check live auctions
    live_auctions = Auction.objects.filter(status='live')
    print(f"📊 Live auctions: {live_auctions.count()}")
    
    # Check all auctions
    all_auctions = Auction.objects.all()
    print(f"📊 Total auctions: {all_auctions.count()}")
    
    if all_auctions.exists():
        print("\n🎯 Recent auctions:")
        for auction in all_auctions[:5]:
            print(f"  - {auction.title}: {auction.status} - ${auction.current_price}")
            
            # Check auto-bids for this auction
            auto_bids = AutoBid.objects.filter(auction=auction)
            if auto_bids.exists():
                print(f"    🤖 Auto-bids ({auto_bids.count()}):")
                for ab in auto_bids:
                    status = "✅ Active" if ab.is_active else "❌ Inactive"
                    print(f"      - {ab.bidder.username}: max ${ab.max_amount} ({status})")
            
            # Check recent bids
            recent_bids = Bid.objects.filter(auction=auction)[:3]
            if recent_bids.exists():
                print(f"    💰 Recent bids ({recent_bids.count()}):")
                for bid in recent_bids:
                    bid_type = "🤖 Auto" if bid.bid_type == 'auto' else "👤 Manual"
                    print(f"      - {bid.bidder.username}: ${bid.amount} ({bid_type})")
            print()
    
    # Check users
    users = User.objects.all()
    print(f"👥 Total users: {users.count()}")
    
    buyers = User.objects.filter(user_type='buyer')
    sellers = User.objects.filter(user_type='seller')
    print(f"  - Buyers: {buyers.count()}")
    print(f"  - Sellers: {sellers.count()}")
    
    # Check for any auto-bids
    all_auto_bids = AutoBid.objects.all()
    active_auto_bids = AutoBid.objects.filter(is_active=True)
    print(f"\n🤖 Auto-bids: {all_auto_bids.count()} total, {active_auto_bids.count()} active")
    
    if active_auto_bids.exists():
        print("  Active auto-bids:")
        for ab in active_auto_bids:
            print(f"    - {ab.bidder.username} on '{ab.auction.title}': max ${ab.max_amount}")

if __name__ == "__main__":
    main()
